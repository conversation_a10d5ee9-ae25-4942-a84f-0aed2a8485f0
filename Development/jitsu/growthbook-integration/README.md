# Jitsu + GrowthBook Integration

Цей проект демонструє інтеграцію між Jitsu (платформа збору даних) та GrowthBook (платформа feature flags та A/B тестування).

## 🚀 Що було зроблено

### 1. Розгорнуто Jitsu
- Склоновано репозиторій Jitsu з GitHub
- Запущено базові сервіси (PostgreSQL + ClickHouse) через Docker
- Налаштовано локальне середовище розробки

### 2. Підключено GrowthBook
- Створено інтеграційний сервер на Node.js + Express
- Підключено GrowthBook SDK з API ключем: `secret_admin_fwgOZQYG797Z6PnsshabJ8nSnFTsXCLVBVzJbOCME`
- Реалізовано відправку подій до Jitsu

### 3. Створено веб-інтерфейс
- Інтерактивна демо-сторінка для тестування інтеграції
- Можливість тестувати feature flags та A/B тести
- Відстеження подій та експериментів

## 📋 Запущені сервіси

### База даних
- **PostgreSQL**: `localhost:5432` (пароль: `postgres_secure_2024`)
- **ClickHouse**: `localhost:8123` (пароль: `clickhouse_secure_2024`)

### Додатки
- **Інтеграційний сервер**: `http://localhost:3001`
- **Веб-інтерфейс**: `http://localhost:3001`

## 🛠️ API Endpoints

### GET /health
Перевірка стану сервера

### GET /features
Отримання всіх feature flags з GrowthBook

### POST /evaluate
Оцінка feature flag для користувача
```json
{
  "userId": "user123",
  "featureKey": "welcome-message",
  "attributes": {"country": "US"}
}
```

### POST /track-experiment
Відстеження A/B тесту
```json
{
  "userId": "user123",
  "experimentKey": "my-experiment",
  "variationId": "control",
  "attributes": {"source": "web"}
}
```

## 🎯 Демо Feature Flags

### welcome-message
- **Значення за замовчуванням**: "Welcome!"
- **Правило**: Якщо country = "US", то "Welcome to our US site!"

### button-color
- **Значення за замовчуванням**: "blue"
- **Правило**: Якщо premium = true, то "gold"

## 📊 Відстеження подій

Всі взаємодії з feature flags та експериментами автоматично відстежуються та відправляються до Jitsu:

- `feature_evaluated` - коли оцінюється feature flag
- `experiment_viewed` - коли користувач бачить експеримент
- `experiment_tracked` - коли відстежується участь в експерименті

## 🔧 Налаштування

### Змінні середовища (.env)
```
GROWTHBOOK_API_KEY=secret_admin_fwgOZQYG797Z6PnsshabJ8nSnFTsXCLVBVzJbOCME
GROWTHBOOK_API_HOST=https://api.growthbook.io
JITSU_INGEST_URL=http://localhost:8080
PORT=3001
```

## 🚀 Запуск

1. Запустити базові сервіси:
```bash
cd jitsu
docker-compose -f docker-compose-simple.yml up -d
```

2. Запустити інтеграційний сервер:
```bash
cd growthbook-integration
npm install
npm start
```

3. Відкрити веб-інтерфейс: http://localhost:3001

## 📈 Наступні кроки

1. Налаштувати повний Jitsu stack з усіма сервісами
2. Підключити реальні feature flags з GrowthBook
3. Налаштувати дашборди для аналізу даних
4. Додати більше типів подій та метрик
