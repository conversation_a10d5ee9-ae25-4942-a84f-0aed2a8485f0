{"name": "@growthbook/growthbook", "version": "0.33.0", "license": "MIT", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "types": "dist/index.d.ts", "typings": "dist/index.d.ts", "exports": {"require": "./dist/cjs/index.js", "import": "./dist/esm/index.js", "types": "./dist/index.d.ts"}, "files": ["dist", "src"], "engines": {"node": ">=10"}, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/growthbook/growthbook", "directory": "packages/sdk-js"}, "scripts": {"build:clean": "<PERSON><PERSON><PERSON> dist", "build:esm": "cross-env BABEL_ENV=esmUnbundled babel src --extensions '.ts' --out-dir 'dist/esm' --source-maps", "build:cjs": "cross-env BABEL_ENV=cjs babel src --extensions '.ts' --out-dir 'dist/cjs' --source-maps", "build:bundles": "rollup -c", "build:types": "tsc --emitDeclarationOnly", "build": "yarn build:clean && npm-run-all --parallel build:types build:esm build:cjs build:bundles", "test": "jest", "clean": "rimraf node_modules", "type-safe-tests": "node scripts/type-safe-tests.js", "type-check": "tsc --pretty --noEmit", "size": "gzip-size ./dist/bundles/index.min.js --include-original"}, "author": "<PERSON>", "dependencies": {"dom-mutator": "^0.6.0"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.6", "@babel/preset-env": "^7.19.4", "@babel/preset-typescript": "^7.18.6", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-node-resolve": "^13.0.4", "@rollup/plugin-replace": "^3.0.0", "@types/jest": "^27.0.1", "@types/js-cookie": "^3.0.5", "babel-plugin-minify-replace": "^0.5.0", "cross-env": "^7.0.3", "gzip-size-cli": "^5.0.0", "ioredis": "^5.3.2", "ioredis-mock": "^8.9.0", "jest": "^27.1.1", "jest-localstorage-mock": "^2.4.26", "js-cookie": "^3.0.5", "mocksse": "^1.0.4", "npm-run-all": "^4.1.5", "rimraf": "^3.0.2", "rollup": "^2.56.3", "rollup-plugin-terser": "^7.0.2", "ts-jest": "^27.0.5", "typescript": "4.7.4"}, "browserslist": ["defaults", ">0.35%", "not IE 11", "maintained node versions"]}