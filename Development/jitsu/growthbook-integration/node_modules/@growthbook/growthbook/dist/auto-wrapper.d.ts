import { Context, GrowthBook } from "./index";
declare global {
    interface Window {
        _growthbook?: GrowthBook;
        growthbook_config?: Context;
        dataLayer: any[];
        analytics?: {
            track?: (name: string, props?: Record<string, unknown>) => void;
        };
    }
}
declare const gb: GrowthBook<Record<string, any>>;
export default gb;
//# sourceMappingURL=auto-wrapper.d.ts.map