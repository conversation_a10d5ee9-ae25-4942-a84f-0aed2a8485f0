import { CacheSettings, Helpers, Polyfills } from "./types/growthbook";
import type { GrowthBook } from ".";
export declare const helpers: Helpers;
export declare function setPolyfills(overrides: Partial<Polyfills>): void;
export declare function configureCache(overrides: Partial<CacheSettings>): void;
export declare function clearCache(): Promise<void>;
export declare function refreshFeatures(instance: GrowthBook, timeout?: number, skipCache?: boolean, allowStale?: boolean, updateInstance?: boolean, backgroundSync?: boolean): Promise<void>;
export declare function subscribe(instance: GrowthBook): void;
export declare function unsubscribe(instance: GrowthBook): void;
export declare function onHidden(): void;
export declare function onVisible(): void;
//# sourceMappingURL=feature-repository.d.ts.map