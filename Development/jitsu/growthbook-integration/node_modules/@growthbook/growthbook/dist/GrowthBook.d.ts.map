{"version": 3, "file": "GrowthBook.d.ts", "sourceRoot": "", "sources": ["../src/GrowthBook.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,OAAO,EACP,UAAU,EACV,cAAc,EACd,uBAAuB,EACvB,SAAS,EACT,OAAO,EACP,UAAU,EACV,kBAAkB,EAClB,iBAAiB,EACjB,aAAa,EAGb,mBAAmB,EAEnB,sBAAsB,EACtB,MAAM,EAEN,yBAAyB,EAGzB,oBAAoB,EAGpB,eAAe,EAChB,MAAM,oBAAoB,CAAC;AAwB5B,qBAAa,UAAU,CAErB,WAAW,SAAS,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAI7D,OAAO,CAAC,OAAO,CAAU;IAClB,KAAK,EAAE,OAAO,CAAC;IACf,KAAK,EAAE,OAAO,CAAC;IACf,OAAO,EAAE,MAAM,CAAC;IAGvB,OAAO,CAAC,IAAI,CAAU;IACtB,OAAO,CAAC,SAAS,CAAsB;IACvC,OAAO,CAAC,mBAAmB,CAAe;IAC1C,OAAO,CAAC,gBAAgB,CAAyB;IACjD,OAAO,CAAC,cAAc,CAA4B;IAClD,OAAO,CAAC,QAAQ,CAAsB;IACtC,OAAO,CAAC,QAAQ,CAAS;IACzB,OAAO,CAAC,SAAS,CAQf;IAEF,OAAO,CAAC,oBAAoB,CAAmB;IAC/C,OAAO,CAAC,mBAAmB,CAAa;IACxC,OAAO,CAAC,sBAAsB,CAG5B;IACF,OAAO,CAAC,iBAAiB,CAAc;IACvC,OAAO,CAAC,mBAAmB,CAAU;gBAEzB,OAAO,CAAC,EAAE,OAAO;IAgEhB,YAAY,CAAC,OAAO,CAAC,EAAE,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC;IAc1D,eAAe,CAC1B,OAAO,CAAC,EAAE,sBAAsB,GAC/B,OAAO,CAAC,IAAI,CAAC;IAIT,UAAU,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC;IAGlC,WAAW,IAAI;QACpB,OAAO,EAAE,MAAM,CAAC;QAChB,aAAa,EAAE,MAAM,CAAC;QACtB,iBAAiB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QAC3C,2BAA2B,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;KACtD;IAYM,YAAY,IAAI,MAAM;IAItB,YAAY,IAAI,OAAO;IAIvB,qBAAqB,IAAI,CAAC,MAAM,UAAU,CAAC,EAAE,GAAG,SAAS;YAIlD,QAAQ;IAmBtB,OAAO,CAAC,OAAO;IAMR,WAAW,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC;IAMjD,oBAAoB,CAC/B,eAAe,EAAE,MAAM,EACvB,aAAa,CAAC,EAAE,MAAM,EACtB,MAAM,CAAC,EAAE,YAAY,GACpB,OAAO,CAAC,IAAI,CAAC;IAWT,cAAc,CAAC,WAAW,EAAE,cAAc,EAAE,GAAG,IAAI;IAM7C,uBAAuB,CAClC,eAAe,EAAE,MAAM,EACvB,aAAa,CAAC,EAAE,MAAM,EACtB,MAAM,CAAC,EAAE,YAAY,GACpB,OAAO,CAAC,IAAI,CAAC;IASH,cAAc,CACzB,IAAI,EAAE,kBAAkB,EACxB,aAAa,CAAC,EAAE,MAAM,EACtB,MAAM,CAAC,EAAE,YAAY,GACpB,OAAO,CAAC,kBAAkB,CAAC;IAwBjB,aAAa,CAAC,UAAU,EAAE,UAAU;IAapC,qBAAqB,CAAC,SAAS,EAAE,UAAU;IAa3C,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC;IAWtD,iBAAiB,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;IAKjC,MAAM,CAAC,GAAG,EAAE,MAAM;IAUxB,aAAa;;;IAIb,mBAAmB;IAInB,iBAAiB;IAKjB,6BAA6B;IAI7B,MAAM;IAIN,WAAW;IAIX,cAAc;IAId,SAAS,CAAC,EAAE,EAAE,oBAAoB,GAAG,MAAM,IAAI;IAOtD,OAAO,CAAC,aAAa;YAIP,qBAAqB;IAQ5B,aAAa;;;;IAIb,OAAO;IAwBP,WAAW,CAAC,QAAQ,EAAE,MAAM,IAAI;IAIhC,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM;IAW7C,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;IAM5C,iBAAiB,CAAC,GAAG,EAAE,MAAM;IAYpC,OAAO,CAAC,kBAAkB;IA4C1B,OAAO,CAAC,yBAAyB;IAQjC,OAAO,CAAC,yBAAyB;IAkBjC,OAAO,CAAC,kBAAkB;IAsB1B,OAAO,CAAC,kBAAkB;IAoD1B,OAAO,CAAC,iBAAiB;IAwBlB,IAAI,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,WAAW,GAAG,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO;IAIpE,KAAK,CAAC,CAAC,SAAS,MAAM,GAAG,MAAM,WAAW,GAAG,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,OAAO;IAIrE,eAAe,CACpB,CAAC,SAAS,WAAW,CAAC,CAAC,CAAC,EACxB,CAAC,SAAS,MAAM,GAAG,MAAM,WAAW,GAAG,MAAM,EAC7C,GAAG,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;IAK9C;;;OAGG;IAEI,OAAO,CACZ,CAAC,SAAS,WAAW,CAAC,CAAC,CAAC,EACxB,CAAC,SAAS,MAAM,GAAG,MAAM,WAAW,GAAG,MAAM,EAC7C,EAAE,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC;IAI1B,WAAW,CAChB,CAAC,SAAS,WAAW,CAAC,CAAC,CAAC,EACxB,CAAC,SAAS,MAAM,GAAG,MAAM,WAAW,GAAG,MAAM,EAC7C,EAAE,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC;IAwJjC,OAAO,CAAC,oBAAoB;IA4B5B,OAAO,CAAC,gBAAgB;IAIxB,OAAO,CAAC,cAAc;IAUtB,OAAO,CAAC,IAAI;IAmSZ,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC;IAM7C,OAAO,CAAC,MAAM;IAkBd,OAAO,CAAC,eAAe;IAgBvB,OAAO,CAAC,iBAAiB;IA8BzB,OAAO,CAAC,UAAU;IA6ClB,OAAO,CAAC,cAAc;IAItB,OAAO,CAAC,WAAW;IAWnB,OAAO,CAAC,gBAAgB;IAQxB,OAAO,CAAC,gBAAgB;IAyBxB,OAAO,CAAC,uCAAuC;IA2BlC,oBAAoB,CAAC,IAAI,CAAC,EAAE,kBAAkB;IAS3D,OAAO,CAAC,2BAA2B;IAQnC,OAAO,CAAC,yBAAyB;IA0CjC,OAAO,CAAC,6BAA6B;IAQrC,OAAO,CAAC,0BAA0B;IAelC,OAAO,CAAC,kCAAkC;CA6B3C"}