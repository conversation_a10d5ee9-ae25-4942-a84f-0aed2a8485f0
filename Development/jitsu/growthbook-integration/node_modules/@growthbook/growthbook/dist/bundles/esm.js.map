{"version": 3, "file": "esm.js", "sources": ["../../src/feature-repository.ts", "../../../../node_modules/dom-mutator/dist/dom-mutator.esm.js", "../../src/util.ts", "../../src/mongrule.ts", "../../src/GrowthBook.ts", "../../src/sticky-bucket-service.ts"], "sourcesContent": ["import {\n  Attributes,\n  CacheSettings,\n  FeatureApiResponse,\n  Helpers,\n  Polyfills,\n} from \"./types/growthbook\";\nimport type { GrowthBook } from \".\";\n\ntype CacheEntry = {\n  data: FeatureApiResponse;\n  sse?: boolean;\n  version: string;\n  staleAt: Date;\n};\ntype ScopedChannel = {\n  src: EventSource | null;\n  cb: (event: MessageEvent<string>) => void;\n  host: string;\n  clientKey: string;\n  headers?: Record<string, string>;\n  errors: number;\n  state: \"active\" | \"idle\" | \"disabled\";\n};\n\n// Config settings\nconst cacheSettings: CacheSettings = {\n  // Consider a fetch stale after 1 minute\n  staleTTL: 1000 * 60,\n  // Max time to keep a fetch in cache (24 hours default)\n  maxAge: 1000 * 60 * 60 * 24,\n  cacheKey: \"gbFeaturesCache\",\n  backgroundSync: true,\n  maxEntries: 10,\n  disableIdleStreams: false,\n  idleStreamInterval: 20000,\n};\nconst polyfills: Polyfills = {\n  fetch: globalThis.fetch ? globalThis.fetch.bind(globalThis) : undefined,\n  SubtleCrypto: globalThis.crypto ? globalThis.crypto.subtle : undefined,\n  EventSource: globalThis.EventSource,\n};\nexport const helpers: Helpers = {\n  fetchFeaturesCall: ({ host, clientKey, headers }) => {\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/features/${clientKey}`,\n      { headers }\n    );\n  },\n  fetchRemoteEvalCall: ({ host, clientKey, payload, headers }) => {\n    const options = {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\", ...headers },\n      body: JSON.stringify(payload),\n    };\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/eval/${clientKey}`,\n      options\n    );\n  },\n  eventSourceCall: ({ host, clientKey, headers }) => {\n    if (headers) {\n      return new polyfills.EventSource(`${host}/sub/${clientKey}`, {\n        headers,\n      });\n    }\n    return new polyfills.EventSource(`${host}/sub/${clientKey}`);\n  },\n  startIdleListener: () => {\n    let idleTimeout: number | undefined;\n    const isBrowser =\n      typeof window !== \"undefined\" && typeof document !== \"undefined\";\n    if (!isBrowser) return;\n    const onVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        window.clearTimeout(idleTimeout);\n        onVisible();\n      } else if (document.visibilityState === \"hidden\") {\n        idleTimeout = window.setTimeout(\n          onHidden,\n          cacheSettings.idleStreamInterval\n        );\n      }\n    };\n    document.addEventListener(\"visibilitychange\", onVisibilityChange);\n    return () =>\n      document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n  },\n  stopIdleListener: () => {\n    // No-op, replaced by startIdleListener\n  },\n};\n\ntry {\n  if (globalThis.localStorage) {\n    polyfills.localStorage = globalThis.localStorage;\n  }\n} catch (e) {\n  // Ignore localStorage errors\n}\n\n// Global state\nconst subscribedInstances: Map<string, Set<GrowthBook>> = new Map();\nlet cacheInitialized = false;\nconst cache: Map<string, CacheEntry> = new Map();\nconst activeFetches: Map<string, Promise<FeatureApiResponse>> = new Map();\nconst streams: Map<string, ScopedChannel> = new Map();\nconst supportsSSE: Set<string> = new Set();\n\n// Public functions\nexport function setPolyfills(overrides: Partial<Polyfills>): void {\n  Object.assign(polyfills, overrides);\n}\nexport function configureCache(overrides: Partial<CacheSettings>): void {\n  Object.assign(cacheSettings, overrides);\n  if (!cacheSettings.backgroundSync) {\n    clearAutoRefresh();\n  }\n}\n\nexport async function clearCache(): Promise<void> {\n  cache.clear();\n  activeFetches.clear();\n  clearAutoRefresh();\n  cacheInitialized = false;\n  await updatePersistentCache();\n}\n\nexport async function refreshFeatures(\n  instance: GrowthBook,\n  timeout?: number,\n  skipCache?: boolean,\n  allowStale?: boolean,\n  updateInstance?: boolean,\n  backgroundSync?: boolean\n): Promise<void> {\n  if (!backgroundSync) {\n    cacheSettings.backgroundSync = false;\n  }\n\n  const data = await fetchFeaturesWithCache(\n    instance,\n    allowStale,\n    timeout,\n    skipCache\n  );\n  updateInstance && data && (await refreshInstance(instance, data));\n}\n\n// Subscribe a GrowthBook instance to feature changes\nexport function subscribe(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const subs = subscribedInstances.get(key) || new Set();\n  subs.add(instance);\n  subscribedInstances.set(key, subs);\n}\nexport function unsubscribe(instance: GrowthBook): void {\n  subscribedInstances.forEach((s) => s.delete(instance));\n}\n\nexport function onHidden() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    channel.state = \"idle\";\n    disableChannel(channel);\n  });\n}\n\nexport function onVisible() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    if (channel.state !== \"idle\") return;\n    enableChannel(channel);\n  });\n}\n\n// Private functions\n\nasync function updatePersistentCache() {\n  try {\n    if (!polyfills.localStorage) return;\n    await polyfills.localStorage.setItem(\n      cacheSettings.cacheKey,\n      JSON.stringify(Array.from(cache.entries()))\n    );\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n}\n\nasync function fetchFeaturesWithCache(\n  instance: GrowthBook,\n  allowStale?: boolean,\n  timeout?: number,\n  skipCache?: boolean\n): Promise<FeatureApiResponse | null> {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const now = new Date();\n\n  const minStaleAt = new Date(\n    now.getTime() - cacheSettings.maxAge + cacheSettings.staleTTL\n  );\n\n  await initializeCache();\n  const existing = cache.get(cacheKey);\n  if (\n    existing &&\n    !skipCache &&\n    (allowStale || existing.staleAt > now) &&\n    existing.staleAt > minStaleAt\n  ) {\n    // Restore from cache whether SSE is supported\n    if (existing.sse) supportsSSE.add(key);\n\n    // Reload features in the background if stale\n    if (existing.staleAt < now) {\n      fetchFeatures(instance);\n    }\n    // Otherwise, if we don't need to refresh now, start a background sync\n    else {\n      startAutoRefresh(instance);\n    }\n    return existing.data;\n  } else {\n    return await promiseTimeout(fetchFeatures(instance), timeout);\n  }\n}\n\nfunction getKey(instance: GrowthBook): string {\n  const [apiHost, clientKey] = instance.getApiInfo();\n  return `${apiHost}||${clientKey}`;\n}\n\nfunction getCacheKey(instance: GrowthBook): string {\n  const baseKey = getKey(instance);\n  if (!instance.isRemoteEval()) return baseKey;\n\n  const attributes = instance.getAttributes();\n  const cacheKeyAttributes =\n    instance.getCacheKeyAttributes() || Object.keys(instance.getAttributes());\n  const ca: Attributes = {};\n  cacheKeyAttributes.forEach((key) => {\n    ca[key] = attributes[key];\n  });\n\n  const fv = instance.getForcedVariations();\n  const url = instance.getUrl();\n\n  return `${baseKey}||${JSON.stringify({\n    ca,\n    fv,\n    url,\n  })}`;\n}\n\n// Guarantee the promise always resolves within {timeout} ms\n// Resolved value will be `null` when there's an error or it takes too long\n// Note: The promise will continue running in the background, even if the timeout is hit\nfunction promiseTimeout<T>(\n  promise: Promise<T>,\n  timeout?: number\n): Promise<T | null> {\n  return new Promise((resolve) => {\n    let resolved = false;\n    let timer: unknown;\n    const finish = (data?: T) => {\n      if (resolved) return;\n      resolved = true;\n      timer && clearTimeout(timer as NodeJS.Timer);\n      resolve(data || null);\n    };\n\n    if (timeout) {\n      timer = setTimeout(() => finish(), timeout);\n    }\n\n    promise.then((data) => finish(data)).catch(() => finish());\n  });\n}\n\n// Populate cache from localStorage (if available)\nasync function initializeCache(): Promise<void> {\n  if (cacheInitialized) return;\n  cacheInitialized = true;\n  try {\n    if (polyfills.localStorage) {\n      const value = await polyfills.localStorage.getItem(\n        cacheSettings.cacheKey\n      );\n      if (value) {\n        const parsed: [string, CacheEntry][] = JSON.parse(value);\n        if (parsed && Array.isArray(parsed)) {\n          parsed.forEach(([key, data]) => {\n            cache.set(key, {\n              ...data,\n              staleAt: new Date(data.staleAt),\n            });\n          });\n        }\n        cleanupCache();\n      }\n    }\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n  if (!cacheSettings.disableIdleStreams) {\n    const cleanupFn = helpers.startIdleListener();\n    if (cleanupFn) {\n      helpers.stopIdleListener = cleanupFn;\n    }\n  }\n}\n\n// Enforce the maxEntries limit\nfunction cleanupCache() {\n  const entriesWithTimestamps = Array.from(cache.entries())\n    .map(([key, value]) => ({\n      key,\n      staleAt: value.staleAt.getTime(),\n    }))\n    .sort((a, b) => a.staleAt - b.staleAt);\n\n  const entriesToRemoveCount = Math.min(\n    Math.max(0, cache.size - cacheSettings.maxEntries),\n    cache.size\n  );\n\n  for (let i = 0; i < entriesToRemoveCount; i++) {\n    cache.delete(entriesWithTimestamps[i].key);\n  }\n}\n\n// Called whenever new features are fetched from the API\nfunction onNewFeatureData(\n  key: string,\n  cacheKey: string,\n  data: FeatureApiResponse\n): void {\n  // If contents haven't changed, ignore the update, extend the stale TTL\n  const version = data.dateUpdated || \"\";\n  const staleAt = new Date(Date.now() + cacheSettings.staleTTL);\n  const existing = cache.get(cacheKey);\n  if (existing && version && existing.version === version) {\n    existing.staleAt = staleAt;\n    updatePersistentCache();\n    return;\n  }\n\n  // Update in-memory cache\n  cache.set(cacheKey, {\n    data,\n    version,\n    staleAt,\n    sse: supportsSSE.has(key),\n  });\n  cleanupCache();\n  // Update local storage (don't await this, just update asynchronously)\n  updatePersistentCache();\n\n  // Update features for all subscribed GrowthBook instances\n  const instances = subscribedInstances.get(key);\n  instances && instances.forEach((instance) => refreshInstance(instance, data));\n}\n\nasync function refreshInstance(\n  instance: GrowthBook,\n  data: FeatureApiResponse\n): Promise<void> {\n  data = await instance.decryptPayload(data, undefined, polyfills.SubtleCrypto);\n\n  await instance.refreshStickyBuckets(data);\n  instance.setExperiments(data.experiments || instance.getExperiments());\n  instance.setFeatures(data.features || instance.getFeatures());\n}\n\nasync function fetchFeatures(\n  instance: GrowthBook\n): Promise<FeatureApiResponse> {\n  const { apiHost, apiRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  const remoteEval = instance.isRemoteEval();\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n\n  let promise = activeFetches.get(cacheKey);\n  if (!promise) {\n    const fetcher: Promise<Response> = remoteEval\n      ? helpers.fetchRemoteEvalCall({\n          host: apiHost,\n          clientKey,\n          payload: {\n            attributes: instance.getAttributes(),\n            forcedVariations: instance.getForcedVariations(),\n            forcedFeatures: Array.from(instance.getForcedFeatures().entries()),\n            url: instance.getUrl(),\n          },\n          headers: apiRequestHeaders,\n        })\n      : helpers.fetchFeaturesCall({\n          host: apiHost,\n          clientKey,\n          headers: apiRequestHeaders,\n        });\n\n    // TODO: auto-retry if status code indicates a temporary error\n    promise = fetcher\n      .then((res) => {\n        if (res.headers.get(\"x-sse-support\") === \"enabled\") {\n          supportsSSE.add(key);\n        }\n        return res.json();\n      })\n      .then((data: FeatureApiResponse) => {\n        onNewFeatureData(key, cacheKey, data);\n        startAutoRefresh(instance);\n        activeFetches.delete(cacheKey);\n        return data;\n      })\n      .catch((e) => {\n        process.env.NODE_ENV !== \"production\" &&\n          instance.log(\"Error fetching features\", {\n            apiHost,\n            clientKey,\n            error: e ? e.message : null,\n          });\n        activeFetches.delete(cacheKey);\n        return Promise.resolve({});\n      });\n    activeFetches.set(cacheKey, promise);\n  }\n  return await promise;\n}\n\n// Watch a feature endpoint for changes\n// Will prefer SSE if enabled, otherwise fall back to cron\nfunction startAutoRefresh(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const { streamingHost, streamingHostRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  if (\n    cacheSettings.backgroundSync &&\n    supportsSSE.has(key) &&\n    polyfills.EventSource\n  ) {\n    if (streams.has(key)) return;\n    const channel: ScopedChannel = {\n      src: null,\n      host: streamingHost,\n      clientKey,\n      headers: streamingHostRequestHeaders,\n      cb: (event: MessageEvent<string>) => {\n        try {\n          if (event.type === \"features-updated\") {\n            const instances = subscribedInstances.get(key);\n            instances &&\n              instances.forEach((instance) => {\n                fetchFeatures(instance);\n              });\n          } else if (event.type === \"features\") {\n            const json: FeatureApiResponse = JSON.parse(event.data);\n            onNewFeatureData(key, cacheKey, json);\n          }\n          // Reset error count on success\n          channel.errors = 0;\n        } catch (e) {\n          process.env.NODE_ENV !== \"production\" &&\n            instance.log(\"SSE Error\", {\n              streamingHost,\n              clientKey,\n              error: e ? (e as Error).message : null,\n            });\n          onSSEError(channel);\n        }\n      },\n      errors: 0,\n      state: \"active\",\n    };\n    streams.set(key, channel);\n    enableChannel(channel);\n  }\n}\n\nfunction onSSEError(channel: ScopedChannel) {\n  if (channel.state === \"idle\") return;\n  channel.errors++;\n  if (channel.errors > 3 || (channel.src && channel.src.readyState === 2)) {\n    // exponential backoff after 4 errors, with jitter\n    const delay =\n      Math.pow(3, channel.errors - 3) * (1000 + Math.random() * 1000);\n    disableChannel(channel);\n    setTimeout(() => {\n      if ([\"idle\", \"active\"].includes(channel.state)) return;\n      enableChannel(channel);\n    }, Math.min(delay, 300000)); // 5 minutes max\n  }\n}\n\nfunction disableChannel(channel: ScopedChannel) {\n  if (!channel.src) return;\n  channel.src.onopen = null;\n  channel.src.onerror = null;\n  channel.src.close();\n  channel.src = null;\n  if (channel.state === \"active\") {\n    channel.state = \"disabled\";\n  }\n}\n\nfunction enableChannel(channel: ScopedChannel) {\n  channel.src = helpers.eventSourceCall({\n    host: channel.host,\n    clientKey: channel.clientKey,\n    headers: channel.headers,\n  }) as EventSource;\n  channel.state = \"active\";\n  channel.src.addEventListener(\"features\", channel.cb);\n  channel.src.addEventListener(\"features-updated\", channel.cb);\n  channel.src.onerror = () => onSSEError(channel);\n  channel.src.onopen = () => {\n    channel.errors = 0;\n  };\n}\n\nfunction destroyChannel(channel: ScopedChannel, key: string) {\n  disableChannel(channel);\n  streams.delete(key);\n}\n\nfunction clearAutoRefresh() {\n  // Clear list of which keys are auto-updated\n  supportsSSE.clear();\n\n  // Stop listening for any SSE events\n  streams.forEach(destroyChannel);\n\n  // Remove all references to GrowthBook instances\n  subscribedInstances.clear();\n\n  // Run the idle stream cleanup function\n  helpers.stopIdleListener();\n}\n", "var validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nvar nullController = {\n  revert: function revert() {}\n};\nvar elements = /*#__PURE__*/new Map();\nvar mutations = /*#__PURE__*/new Set();\n\nfunction getObserverInit(attr) {\n  return attr === 'html' ? {\n    childList: true,\n    subtree: true,\n    attributes: true,\n    characterData: true\n  } : {\n    childList: false,\n    subtree: false,\n    attributes: true,\n    attributeFilter: [attr]\n  };\n}\n\nfunction getElementRecord(element) {\n  var record = elements.get(element);\n\n  if (!record) {\n    record = {\n      element: element,\n      attributes: {}\n    };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(el, attr, getCurrentValue, setValue, mutationRunner) {\n  var currentValue = getCurrentValue(el);\n  var record = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el: el,\n    _positionTimeout: null,\n    observer: new MutationObserver(function () {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;else if (attr === 'position') record._positionTimeout = setTimeout(function () {\n        record._positionTimeout = null;\n      }, 1000);\n      var currentValue = getCurrentValue(el);\n      if (attr === 'position' && currentValue.parentNode === record.virtualValue.parentNode && currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode) return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner: mutationRunner,\n    setValue: setValue,\n    getCurrentValue: getCurrentValue\n  };\n\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n\n  return record;\n}\n\nfunction queueIfNeeded(val, record) {\n  var currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n\n  if (val && typeof val !== 'string') {\n    if (!currentVal || val.parentNode !== currentVal.parentNode || val.insertBeforeNode !== currentVal.insertBeforeNode) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(getTransformedHTML(val), record);\n}\n\nfunction classMutationRunner(record) {\n  var val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(function (m) {\n    return m.mutate(val);\n  });\n  queueIfNeeded(Array.from(val).filter(Boolean).join(' '), record);\n}\n\nfunction attrMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes(_ref) {\n  var parentSelector = _ref.parentSelector,\n      insertBeforeSelector = _ref.insertBeforeSelector;\n  var parentNode = document.querySelector(parentSelector);\n  if (!parentNode) return null;\n  var insertBeforeNode = insertBeforeSelector ? document.querySelector(insertBeforeSelector) : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode: parentNode,\n    insertBeforeNode: insertBeforeNode\n  };\n}\n\nfunction positionMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    var selectors = m.mutate();\n\n    var newNodes = _loadDOMNodes(selectors);\n\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nvar getHTMLValue = function getHTMLValue(el) {\n  return el.innerHTML;\n};\n\nvar setHTMLValue = function setHTMLValue(el, value) {\n  return el.innerHTML = value;\n};\n\nfunction getElementHTMLRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(element, 'html', getHTMLValue, setHTMLValue, htmlMutationRunner);\n  }\n\n  return elementRecord.html;\n}\n\nvar getElementPosition = function getElementPosition(el) {\n  return {\n    parentNode: el.parentElement,\n    insertBeforeNode: el.nextElementSibling\n  };\n};\n\nvar setElementPosition = function setElementPosition(el, value) {\n  if (value.insertBeforeNode && !value.parentNode.contains(value.insertBeforeNode)) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\n\nfunction getElementPositionRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(element, 'position', getElementPosition, setElementPosition, positionMutationRunner);\n  }\n\n  return elementRecord.position;\n}\n\nvar setClassValue = function setClassValue(el, val) {\n  return val ? el.className = val : el.removeAttribute('class');\n};\n\nvar getClassValue = function getClassValue(el) {\n  return el.className;\n};\n\nfunction getElementClassRecord(el) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(el, 'class', getClassValue, setClassValue, classMutationRunner);\n  }\n\n  return elementRecord.classes;\n}\n\nvar getAttrValue = function getAttrValue(attrName) {\n  return function (el) {\n    var _el$getAttribute;\n\n    return (_el$getAttribute = el.getAttribute(attrName)) != null ? _el$getAttribute : null;\n  };\n};\n\nvar setAttrValue = function setAttrValue(attrName) {\n  return function (el, val) {\n    return val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\n  };\n};\n\nfunction getElementAttributeRecord(el, attr) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(el, attr, getAttrValue(attr), setAttrValue(attr), attrMutationRunner);\n  }\n\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el, attr) {\n  var element = elements.get(el);\n  if (!element) return;\n\n  if (attr === 'html') {\n    var _element$html, _element$html$observe;\n\n    (_element$html = element.html) == null ? void 0 : (_element$html$observe = _element$html.observer) == null ? void 0 : _element$html$observe.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    var _element$classes, _element$classes$obse;\n\n    (_element$classes = element.classes) == null ? void 0 : (_element$classes$obse = _element$classes.observer) == null ? void 0 : _element$classes$obse.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    var _element$position, _element$position$obs;\n\n    (_element$position = element.position) == null ? void 0 : (_element$position$obs = _element$position.observer) == null ? void 0 : _element$position$obs.disconnect();\n    delete element.position;\n  } else {\n    var _element$attributes, _element$attributes$a, _element$attributes$a2;\n\n    (_element$attributes = element.attributes) == null ? void 0 : (_element$attributes$a = _element$attributes[attr]) == null ? void 0 : (_element$attributes$a2 = _element$attributes$a.observer) == null ? void 0 : _element$attributes$a2.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nvar transformContainer;\n\nfunction getTransformedHTML(html) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue(el, attr, m) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  var val = m.virtualValue;\n\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n\n  m.setValue(el, val);\n}\n\nfunction setValue(m, el) {\n  m.html && setPropertyValue(el, 'html', m.html);\n  m.classes && setPropertyValue(el, 'class', m.classes);\n  m.position && setPropertyValue(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(function (attr) {\n    setPropertyValue(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n} // find or create ElementPropertyRecord, add mutation to it, then run\n\n\nfunction startMutating(mutation, element) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n} // get (existing) ElementPropertyRecord, remove mutation from it, then run\n\n\nfunction stopMutating(mutation, el) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n\n  if (!record) return;\n  var index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n} // maintain list of elements associated with mutation\n\n\nfunction refreshElementsSet(mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n  var existingElements = new Set(mutation.elements);\n  var matchingElements = document.querySelectorAll(mutation.selector);\n  matchingElements.forEach(function (el) {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation) {\n  mutation.elements.forEach(function (el) {\n    return stopMutating(mutation, el);\n  });\n  mutation.elements.clear();\n  mutations[\"delete\"](mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n} // Observer for elements that don't exist in the DOM yet\n\n\nvar observer;\nfunction disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nfunction connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(function () {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false\n  });\n} // run on init\n\nconnectGlobalObserver();\n\nfunction newMutation(m) {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController; // add to global index of mutations\n\n  mutations.add(m); // run refresh on init to establish list of elements associated w/ mutation\n\n  refreshElementsSet(m);\n  return {\n    revert: function revert() {\n      revertMutation(m);\n    }\n  };\n}\n\nfunction html(selector, mutate) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction position(selector, mutate) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction classes(selector, mutate) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction attribute(selector, attribute, mutate) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, function (classnames) {\n      var mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames.split(/\\s+/g).filter(Boolean).forEach(function (c) {\n        return classnames.add(c);\n      });\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute: attribute,\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction declarative(_ref2) {\n  var selector = _ref2.selector,\n      action = _ref2.action,\n      value = _ref2.value,\n      attr = _ref2.attribute,\n      parentSelector = _ref2.parentSelector,\n      insertBeforeSelector = _ref2.insertBeforeSelector;\n\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, function (val) {\n        return val + (value != null ? value : '');\n      });\n    } else if (action === 'set') {\n      return html(selector, function () {\n        return value != null ? value : '';\n      });\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, function (val) {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, function (val) {\n        if (value) val[\"delete\"](value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, function (val) {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, function () {\n        return {\n          insertBeforeSelector: insertBeforeSelector,\n          parentSelector: parentSelector\n        };\n      });\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, function (val) {\n        return val !== null ? val + (value != null ? value : '') : value != null ? value : '';\n      });\n    } else if (action === 'set') {\n      return attribute(selector, attr, function () {\n        return value != null ? value : '';\n      });\n    } else if (action === 'remove') {\n      return attribute(selector, attr, function () {\n        return null;\n      });\n    }\n  }\n\n  return nullController;\n}\n\nvar index = {\n  html: html,\n  classes: classes,\n  attribute: attribute,\n  position: position,\n  declarative: declarative\n};\n\nexport default index;\nexport { connectGlobalObserver, disconnectGlobalObserver, validAttributeName };\n//# sourceMappingURL=dom-mutator.esm.js.map\n", "import { UrlTarget, UrlTargetType, VariationRange } from \"./types/growthbook\";\n\nfunction hashFnv32a(str: string): number {\n  let hval = 0x811c9dc5;\n  const l = str.length;\n\n  for (let i = 0; i < l; i++) {\n    hval ^= str.charCodeAt(i);\n    hval +=\n      (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);\n  }\n  return hval >>> 0;\n}\n\nexport function hash(\n  seed: string,\n  value: string,\n  version: number\n): number | null {\n  // New unbiased hashing algorithm\n  if (version === 2) {\n    return (hashFnv32a(hashFnv32a(seed + value) + \"\") % 10000) / 10000;\n  }\n  // Original biased hashing algorithm (keep for backwards compatibility)\n  if (version === 1) {\n    return (hashFnv32a(value + seed) % 1000) / 1000;\n  }\n\n  // Unknown hash version\n  return null;\n}\n\nexport function getEqualWeights(n: number): number[] {\n  if (n <= 0) return [];\n  return new Array(n).fill(1 / n);\n}\n\nexport function inRange(n: number, range: VariationRange): boolean {\n  return n >= range[0] && n < range[1];\n}\n\nexport function inNamespace(\n  hashValue: string,\n  namespace: [string, number, number]\n): boolean {\n  const n = hash(\"__\" + namespace[0], hashValue, 1);\n  if (n === null) return false;\n  return n >= namespace[1] && n < namespace[2];\n}\n\nexport function chooseVariation(n: number, ranges: VariationRange[]): number {\n  for (let i = 0; i < ranges.length; i++) {\n    if (inRange(n, ranges[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nexport function getUrlRegExp(regexString: string): RegExp | undefined {\n  try {\n    const escaped = regexString.replace(/([^\\\\])\\//g, \"$1\\\\/\");\n    return new RegExp(escaped);\n  } catch (e) {\n    console.error(e);\n    return undefined;\n  }\n}\n\nexport function isURLTargeted(url: string, targets: UrlTarget[]) {\n  if (!targets.length) return false;\n  let hasIncludeRules = false;\n  let isIncluded = false;\n\n  for (let i = 0; i < targets.length; i++) {\n    const match = _evalURLTarget(url, targets[i].type, targets[i].pattern);\n    if (targets[i].include === false) {\n      if (match) return false;\n    } else {\n      hasIncludeRules = true;\n      if (match) isIncluded = true;\n    }\n  }\n\n  return isIncluded || !hasIncludeRules;\n}\n\nfunction _evalSimpleUrlPart(\n  actual: string,\n  pattern: string,\n  isPath: boolean\n): boolean {\n  try {\n    // Escape special regex characters and change wildcard `_____` to `.*`\n    let escaped = pattern\n      .replace(/[*.+?^${}()|[\\]\\\\]/g, \"\\\\$&\")\n      .replace(/_____/g, \".*\");\n\n    if (isPath) {\n      // When matching pathname, make leading/trailing slashes optional\n      escaped = \"\\\\/?\" + escaped.replace(/(^\\/|\\/$)/g, \"\") + \"\\\\/?\";\n    }\n\n    const regex = new RegExp(\"^\" + escaped + \"$\", \"i\");\n    return regex.test(actual);\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalSimpleUrlTarget(actual: URL, pattern: string) {\n  try {\n    // If a protocol is missing, but a host is specified, add `https://` to the front\n    // Use \"_____\" as the wildcard since `*` is not a valid hostname in some browsers\n    const expected = new URL(\n      pattern.replace(/^([^:/?]*)\\./i, \"https://$1.\").replace(/\\*/g, \"_____\"),\n      \"https://_____\"\n    );\n\n    // Compare each part of the URL separately\n    const comps: Array<[string, string, boolean]> = [\n      [actual.host, expected.host, false],\n      [actual.pathname, expected.pathname, true],\n    ];\n    // We only want to compare hashes if it's explicitly being targeted\n    if (expected.hash) {\n      comps.push([actual.hash, expected.hash, false]);\n    }\n\n    expected.searchParams.forEach((v, k) => {\n      comps.push([actual.searchParams.get(k) || \"\", v, false]);\n    });\n\n    // If any comparisons fail, the whole thing fails\n    return !comps.some(\n      (data) => !_evalSimpleUrlPart(data[0], data[1], data[2])\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalURLTarget(\n  url: string,\n  type: UrlTargetType,\n  pattern: string\n): boolean {\n  try {\n    const parsed = new URL(url, \"https://_\");\n\n    if (type === \"regex\") {\n      const regex = getUrlRegExp(pattern);\n      if (!regex) return false;\n      return (\n        regex.test(parsed.href) ||\n        regex.test(parsed.href.substring(parsed.origin.length))\n      );\n    } else if (type === \"simple\") {\n      return _evalSimpleUrlTarget(parsed, pattern);\n    }\n\n    return false;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function getBucketRanges(\n  numVariations: number,\n  coverage: number | undefined,\n  weights?: number[]\n): VariationRange[] {\n  coverage = coverage === undefined ? 1 : coverage;\n\n  // Make sure coverage is within bounds\n  if (coverage < 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be greater than or equal to 0\");\n    }\n    coverage = 0;\n  } else if (coverage > 1) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be less than or equal to 1\");\n    }\n    coverage = 1;\n  }\n\n  // Default to equal weights if missing or invalid\n  const equal = getEqualWeights(numVariations);\n  weights = weights || equal;\n  if (weights.length !== numVariations) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\n        \"Experiment.weights array must be the same length as Experiment.variations\"\n      );\n    }\n    weights = equal;\n  }\n\n  // If weights don't add up to 1 (or close to it), default to equal weights\n  const totalWeight = weights.reduce((w, sum) => sum + w, 0);\n  if (totalWeight < 0.99 || totalWeight > 1.01) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.weights must add up to 1\");\n    }\n    weights = equal;\n  }\n\n  // Covert weights to ranges\n  let cumulative = 0;\n  return weights.map((w) => {\n    const start = cumulative;\n    cumulative += w;\n    return [start, start + (coverage as number) * w];\n  }) as VariationRange[];\n}\n\nexport function getQueryStringOverride(\n  id: string,\n  url: string,\n  numVariations: number\n) {\n  if (!url) {\n    return null;\n  }\n\n  const search = url.split(\"?\")[1];\n  if (!search) {\n    return null;\n  }\n\n  const match = search\n    .replace(/#.*/, \"\") // Get rid of anchor\n    .split(\"&\") // Split into key/value pairs\n    .map((kv) => kv.split(\"=\", 2))\n    .filter(([k]) => k === id) // Look for key that matches the experiment id\n    .map(([, v]) => parseInt(v)); // Parse the value into an integer\n\n  if (match.length > 0 && match[0] >= 0 && match[0] < numVariations)\n    return match[0];\n\n  return null;\n}\n\nexport function isIncluded(include: () => boolean) {\n  try {\n    return include();\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\n\nconst base64ToBuf = (b: string) =>\n  Uint8Array.from(atob(b), (c) => c.charCodeAt(0));\n\nexport async function decrypt(\n  encryptedString: string,\n  decryptionKey?: string,\n  subtle?: SubtleCrypto\n): Promise<string> {\n  decryptionKey = decryptionKey || \"\";\n  subtle = subtle || (globalThis.crypto && globalThis.crypto.subtle);\n  if (!subtle) {\n    throw new Error(\"No SubtleCrypto implementation found\");\n  }\n  try {\n    const key = await subtle.importKey(\n      \"raw\",\n      base64ToBuf(decryptionKey),\n      { name: \"AES-CBC\", length: 128 },\n      true,\n      [\"encrypt\", \"decrypt\"]\n    );\n    const [iv, cipherText] = encryptedString.split(\".\");\n    const plainTextBuffer = await subtle.decrypt(\n      { name: \"AES-CBC\", iv: base64ToBuf(iv) },\n      key,\n      base64ToBuf(cipherText)\n    );\n\n    return new TextDecoder().decode(plainTextBuffer);\n  } catch (e) {\n    throw new Error(\"Failed to decrypt\");\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function toString(input: any): string {\n  if (typeof input === \"string\") return input;\n  return JSON.stringify(input);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function paddedVersionString(input: any): string {\n  if (typeof input === \"number\") {\n    input = input + \"\";\n  }\n  if (!input || typeof input !== \"string\") {\n    input = \"0\";\n  }\n  // Remove build info and leading `v` if any\n  // Split version into parts (both core version numbers and pre-release tags)\n  // \"v1.2.3-rc.1+build123\" -> [\"1\",\"2\",\"3\",\"rc\",\"1\"]\n  const parts = (input as string).replace(/(^v|\\+.*$)/g, \"\").split(/[-.]/);\n\n  // If it's SemVer without a pre-release, add `~` to the end\n  // [\"1\",\"0\",\"0\"] -> [\"1\",\"0\",\"0\",\"~\"]\n  // \"~\" is the largest ASCII character, so this will make \"1.0.0\" greater than \"1.0.0-beta\" for example\n  if (parts.length === 3) {\n    parts.push(\"~\");\n  }\n\n  // Left pad each numeric part with spaces so string comparisons will work (\"9\">\"10\", but \" 9\"<\"10\")\n  // Then, join back together into a single string\n  return parts\n    .map((v) => (v.match(/^[0-9]+$/) ? v.padStart(5, \" \") : v))\n    .join(\"-\");\n}\n\nexport function loadSDKVersion(): string {\n  let version: string;\n  try {\n    // @ts-expect-error right-hand value to be replaced by build with string literal\n    version = __SDK_VERSION__;\n  } catch (e) {\n    version = \"\";\n  }\n  return version;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport {\n  ConditionInterface,\n  TestedObj,\n  ConditionValue,\n  Operator,\n  OperatorConditionValue,\n  VarType,\n} from \"./types/mongrule\";\nimport { paddedVersionString } from \"./util\";\n\nconst _regexCache: { [key: string]: RegExp } = {};\n\n// The top-level condition evaluation function\nexport function evalCondition(\n  obj: TestedObj,\n  condition: ConditionInterface\n): boolean {\n  // Recursive condition\n  if (\"$or\" in condition) {\n    return evalOr(obj, condition[\"$or\"] as ConditionInterface[]);\n  }\n  if (\"$nor\" in condition) {\n    return !evalOr(obj, condition[\"$nor\"] as ConditionInterface[]);\n  }\n  if (\"$and\" in condition) {\n    return evalAnd(obj, condition[\"$and\"] as ConditionInterface[]);\n  }\n  if (\"$not\" in condition) {\n    return !evalCondition(obj, condition[\"$not\"] as ConditionInterface);\n  }\n\n  // Condition is an object, keys are object paths, values are the condition for that path\n  for (const [k, v] of Object.entries(condition)) {\n    if (!evalConditionValue(v, getPath(obj, k))) return false;\n  }\n  return true;\n}\n\n// Return value at dot-separated path of an object\nfunction getPath(obj: TestedObj, path: string) {\n  const parts = path.split(\".\");\n  let current: any = obj;\n  for (let i = 0; i < parts.length; i++) {\n    if (current && typeof current === \"object\" && parts[i] in current) {\n      current = current[parts[i]];\n    } else {\n      return null;\n    }\n  }\n  return current;\n}\n\n// Transform a regex string into a real RegExp object\nfunction getRegex(regex: string): RegExp {\n  if (!_regexCache[regex]) {\n    _regexCache[regex] = new RegExp(regex.replace(/([^\\\\])\\//g, \"$1\\\\/\"));\n  }\n  return _regexCache[regex];\n}\n\n// Evaluate a single value against a condition\nfunction evalConditionValue(condition: ConditionValue, value: any) {\n  // Simple equality comparisons\n  if (typeof condition === \"string\") {\n    return value + \"\" === condition;\n  }\n  if (typeof condition === \"number\") {\n    return value * 1 === condition;\n  }\n  if (typeof condition === \"boolean\") {\n    return !!value === condition;\n  }\n\n  if (condition === null) {\n    return value === null;\n  }\n\n  if (Array.isArray(condition) || !isOperatorObject(condition)) {\n    return JSON.stringify(value) === JSON.stringify(condition);\n  }\n\n  // This is a special operator condition and we should evaluate each one separately\n  for (const op in condition) {\n    if (\n      !evalOperatorCondition(\n        op as Operator,\n        value,\n        condition[op as keyof OperatorConditionValue]\n      )\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// If the object has only keys that start with '$'\nfunction isOperatorObject(obj: any): boolean {\n  const keys = Object.keys(obj);\n  return (\n    keys.length > 0 && keys.filter((k) => k[0] === \"$\").length === keys.length\n  );\n}\n\n// Return the data type of a value\nfunction getType(v: any): VarType | \"unknown\" {\n  if (v === null) return \"null\";\n  if (Array.isArray(v)) return \"array\";\n  const t = typeof v;\n  if ([\"string\", \"number\", \"boolean\", \"object\", \"undefined\"].includes(t)) {\n    return t as VarType;\n  }\n  return \"unknown\";\n}\n\n// At least one element of actual must match the expected condition/value\nfunction elemMatch(actual: any, expected: any) {\n  if (!Array.isArray(actual)) return false;\n  const check = isOperatorObject(expected)\n    ? (v: any) => evalConditionValue(expected, v)\n    : (v: any) => evalCondition(v, expected);\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i] && check(actual[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isIn(actual: any, expected: Array<any>): boolean {\n  // Do an intersection is attribute is an array\n  if (Array.isArray(actual)) {\n    return actual.some((el) => expected.includes(el));\n  }\n  return expected.includes(actual);\n}\n\n// Evaluate a single operator condition\nfunction evalOperatorCondition(\n  operator: Operator,\n  actual: any,\n  expected: any\n): boolean {\n  switch (operator) {\n    case \"$veq\":\n      return paddedVersionString(actual) === paddedVersionString(expected);\n    case \"$vne\":\n      return paddedVersionString(actual) !== paddedVersionString(expected);\n    case \"$vgt\":\n      return paddedVersionString(actual) > paddedVersionString(expected);\n    case \"$vgte\":\n      return paddedVersionString(actual) >= paddedVersionString(expected);\n    case \"$vlt\":\n      return paddedVersionString(actual) < paddedVersionString(expected);\n    case \"$vlte\":\n      return paddedVersionString(actual) <= paddedVersionString(expected);\n    case \"$eq\":\n      return actual === expected;\n    case \"$ne\":\n      return actual !== expected;\n    case \"$lt\":\n      return actual < expected;\n    case \"$lte\":\n      return actual <= expected;\n    case \"$gt\":\n      return actual > expected;\n    case \"$gte\":\n      return actual >= expected;\n    case \"$exists\":\n      // Using `!=` and `==` instead of strict checks so it also matches for undefined\n      return expected ? actual != null : actual == null;\n    case \"$in\":\n      if (!Array.isArray(expected)) return false;\n      return isIn(actual, expected);\n    case \"$nin\":\n      if (!Array.isArray(expected)) return false;\n      return !isIn(actual, expected);\n    case \"$not\":\n      return !evalConditionValue(expected, actual);\n    case \"$size\":\n      if (!Array.isArray(actual)) return false;\n      return evalConditionValue(expected, actual.length);\n    case \"$elemMatch\":\n      return elemMatch(actual, expected);\n    case \"$all\":\n      if (!Array.isArray(actual)) return false;\n      for (let i = 0; i < expected.length; i++) {\n        let passed = false;\n        for (let j = 0; j < actual.length; j++) {\n          if (evalConditionValue(expected[i], actual[j])) {\n            passed = true;\n            break;\n          }\n        }\n        if (!passed) return false;\n      }\n      return true;\n    case \"$regex\":\n      try {\n        return getRegex(expected).test(actual);\n      } catch (e) {\n        return false;\n      }\n    case \"$type\":\n      return getType(actual) === expected;\n    default:\n      console.error(\"Unknown operator: \" + operator);\n      return false;\n  }\n}\n\n// Recursive $or rule\nfunction evalOr(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  if (!conditions.length) return true;\n  for (let i = 0; i < conditions.length; i++) {\n    if (evalCondition(obj, conditions[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// Recursive $and rule\nfunction evalAnd(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  for (let i = 0; i < conditions.length; i++) {\n    if (!evalCondition(obj, conditions[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n", "import mutate, { DeclarativeMutation } from \"dom-mutator\";\nimport type {\n  ApiHost,\n  Attributes,\n  AutoExperiment,\n  AutoExperimentVariation,\n  ClientKey,\n  Context,\n  Experiment,\n  FeatureApiResponse,\n  FeatureDefinition,\n  FeatureResult,\n  FeatureResultSource,\n  Filter,\n  LoadFeaturesOptions,\n  RealtimeUsageData,\n  RefreshFeaturesOptions,\n  Result,\n  StickyAssignments,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n  StickyExperimentKey,\n  SubscriptionFunction,\n  VariationMeta,\n  VariationRange,\n  WidenPrimitives,\n} from \"./types/growthbook\";\nimport type { ConditionInterface } from \"./types/mongrule\";\nimport {\n  chooseVariation,\n  decrypt,\n  getBucketRanges,\n  getQueryStringOverride,\n  getUrlRegExp,\n  hash,\n  inNamespace,\n  inRange,\n  isIncluded,\n  isURLTargeted,\n  loadSDKVersion,\n  toString,\n} from \"./util\";\nimport { evalCondition } from \"./mongrule\";\nimport { refreshFeatures, subscribe, unsubscribe } from \"./feature-repository\";\n\nconst isBrowser =\n  typeof window !== \"undefined\" && typeof document !== \"undefined\";\n\nconst SDK_VERSION = loadSDKVersion();\n\nexport class GrowthBook<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  AppFeatures extends Record<string, any> = Record<string, any>\n> {\n  // context is technically private, but some tools depend on it so we can't mangle the name\n  // _ctx below is a clone of this property that we use internally\n  private context: Context;\n  public debug: boolean;\n  public ready: boolean;\n  public version: string;\n\n  // Properties and methods that start with \"_\" are mangled by Terser (saves ~150 bytes)\n  private _ctx: Context;\n  private _renderer: null | (() => void);\n  private _trackedExperiments: Set<unknown>;\n  private _trackedFeatures: Record<string, string>;\n  private _subscriptions: Set<SubscriptionFunction>;\n  private _rtQueue: RealtimeUsageData[];\n  private _rtTimer: number;\n  private _assigned: Map<\n    string,\n    {\n      // eslint-disable-next-line\n      experiment: Experiment<any>;\n      // eslint-disable-next-line\n      result: Result<any>;\n    }\n  >;\n  // eslint-disable-next-line\n  private _forcedFeatureValues: Map<string, any>;\n  private _attributeOverrides: Attributes;\n  private _activeAutoExperiments: Map<\n    AutoExperiment,\n    { valueHash: string; undo: () => void }\n  >;\n  private _triggeredExpKeys: Set<string>;\n  private _loadFeaturesCalled: boolean;\n\n  constructor(context?: Context) {\n    context = context || {};\n    // These properties are all initialized in the constructor instead of above\n    // This saves ~80 bytes in the final output\n    this.version = SDK_VERSION;\n    this._ctx = this.context = context;\n    this._renderer = null;\n    this._trackedExperiments = new Set();\n    this._trackedFeatures = {};\n    this.debug = false;\n    this._subscriptions = new Set();\n    this._rtQueue = [];\n    this._rtTimer = 0;\n    this.ready = false;\n    this._assigned = new Map();\n    this._forcedFeatureValues = new Map();\n    this._attributeOverrides = {};\n    this._activeAutoExperiments = new Map();\n    this._triggeredExpKeys = new Set();\n    this._loadFeaturesCalled = false;\n\n    if (context.remoteEval) {\n      if (context.decryptionKey) {\n        throw new Error(\"Encryption is not available for remoteEval\");\n      }\n      if (!context.clientKey) {\n        throw new Error(\"Missing clientKey\");\n      }\n      let isGbHost = false;\n      try {\n        isGbHost = !!new URL(context.apiHost || \"\").hostname.match(\n          /growthbook\\.io$/i\n        );\n      } catch (e) {\n        // ignore invalid URLs\n      }\n      if (isGbHost) {\n        throw new Error(\"Cannot use remoteEval on GrowthBook Cloud\");\n      }\n    } else {\n      if (context.cacheKeyAttributes) {\n        throw new Error(\"cacheKeyAttributes are only used for remoteEval\");\n      }\n    }\n\n    if (context.features) {\n      this.ready = true;\n    }\n\n    if (isBrowser && context.enableDevMode) {\n      window._growthbook = this;\n      document.dispatchEvent(new Event(\"gbloaded\"));\n    }\n\n    if (context.experiments) {\n      this.ready = true;\n      this._updateAllAutoExperiments();\n    }\n\n    if (context.clientKey && !context.remoteEval) {\n      this._refresh({}, true, false);\n    }\n  }\n\n  public async loadFeatures(options?: LoadFeaturesOptions): Promise<void> {\n    if (options && options.autoRefresh) {\n      // interpret deprecated autoRefresh option as subscribeToChanges\n      this._ctx.subscribeToChanges = true;\n    }\n    this._loadFeaturesCalled = true;\n\n    await this._refresh(options, true, true);\n\n    if (this._canSubscribe()) {\n      subscribe(this);\n    }\n  }\n\n  public async refreshFeatures(\n    options?: RefreshFeaturesOptions\n  ): Promise<void> {\n    await this._refresh(options, false, true);\n  }\n\n  public getApiInfo(): [ApiHost, ClientKey] {\n    return [this.getApiHosts().apiHost, this.getClientKey()];\n  }\n  public getApiHosts(): {\n    apiHost: string;\n    streamingHost: string;\n    apiRequestHeaders?: Record<string, string>;\n    streamingHostRequestHeaders?: Record<string, string>;\n  } {\n    const defaultHost = this._ctx.apiHost || \"https://cdn.growthbook.io\";\n    return {\n      apiHost: defaultHost.replace(/\\/*$/, \"\"),\n      streamingHost: (this._ctx.streamingHost || defaultHost).replace(\n        /\\/*$/,\n        \"\"\n      ),\n      apiRequestHeaders: this._ctx.apiHostRequestHeaders,\n      streamingHostRequestHeaders: this._ctx.streamingHostRequestHeaders,\n    };\n  }\n  public getClientKey(): string {\n    return this._ctx.clientKey || \"\";\n  }\n\n  public isRemoteEval(): boolean {\n    return this._ctx.remoteEval || false;\n  }\n\n  public getCacheKeyAttributes(): (keyof Attributes)[] | undefined {\n    return this._ctx.cacheKeyAttributes;\n  }\n\n  private async _refresh(\n    options?: RefreshFeaturesOptions,\n    allowStale?: boolean,\n    updateInstance?: boolean\n  ) {\n    options = options || {};\n    if (!this._ctx.clientKey) {\n      throw new Error(\"Missing clientKey\");\n    }\n    await refreshFeatures(\n      this,\n      options.timeout,\n      options.skipCache || this._ctx.enableDevMode,\n      allowStale,\n      updateInstance,\n      this._ctx.backgroundSync !== false\n    );\n  }\n\n  private _render() {\n    if (this._renderer) {\n      this._renderer();\n    }\n  }\n\n  public setFeatures(features: Record<string, FeatureDefinition>) {\n    this._ctx.features = features;\n    this.ready = true;\n    this._render();\n  }\n\n  public async setEncryptedFeatures(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const featuresJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setFeatures(\n      JSON.parse(featuresJSON) as Record<string, FeatureDefinition>\n    );\n  }\n\n  public setExperiments(experiments: AutoExperiment[]): void {\n    this._ctx.experiments = experiments;\n    this.ready = true;\n    this._updateAllAutoExperiments();\n  }\n\n  public async setEncryptedExperiments(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const experimentsJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setExperiments(JSON.parse(experimentsJSON) as AutoExperiment[]);\n  }\n\n  public async decryptPayload(\n    data: FeatureApiResponse,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<FeatureApiResponse> {\n    if (data.encryptedFeatures) {\n      data.features = JSON.parse(\n        await decrypt(\n          data.encryptedFeatures,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedFeatures;\n    }\n    if (data.encryptedExperiments) {\n      data.experiments = JSON.parse(\n        await decrypt(\n          data.encryptedExperiments,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedExperiments;\n    }\n    return data;\n  }\n\n  public async setAttributes(attributes: Attributes) {\n    this._ctx.attributes = attributes;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setAttributeOverrides(overrides: Attributes) {\n    this._attributeOverrides = overrides;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setForcedVariations(vars: Record<string, number>) {\n    this._ctx.forcedVariations = vars || {};\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  // eslint-disable-next-line\n  public setForcedFeatures(map: Map<string, any>) {\n    this._forcedFeatureValues = map;\n    this._render();\n  }\n\n  public async setURL(url: string) {\n    this._ctx.url = url;\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      this._updateAllAutoExperiments(true);\n      return;\n    }\n    this._updateAllAutoExperiments(true);\n  }\n\n  public getAttributes() {\n    return { ...this._ctx.attributes, ...this._attributeOverrides };\n  }\n\n  public getForcedVariations() {\n    return this._ctx.forcedVariations || {};\n  }\n\n  public getForcedFeatures() {\n    // eslint-disable-next-line\n    return this._forcedFeatureValues || new Map<string, any>();\n  }\n\n  public getStickyBucketAssignmentDocs() {\n    return this._ctx.stickyBucketAssignmentDocs || {};\n  }\n\n  public getUrl() {\n    return this._ctx.url || \"\";\n  }\n\n  public getFeatures() {\n    return this._ctx.features || {};\n  }\n\n  public getExperiments() {\n    return this._ctx.experiments || [];\n  }\n\n  public subscribe(cb: SubscriptionFunction): () => void {\n    this._subscriptions.add(cb);\n    return () => {\n      this._subscriptions.delete(cb);\n    };\n  }\n\n  private _canSubscribe() {\n    return this._ctx.backgroundSync !== false && this._ctx.subscribeToChanges;\n  }\n\n  private async _refreshForRemoteEval() {\n    if (!this._ctx.remoteEval) return;\n    if (!this._loadFeaturesCalled) return;\n    await this._refresh({}, false, true).catch(() => {\n      // Ignore errors\n    });\n  }\n\n  public getAllResults() {\n    return new Map(this._assigned);\n  }\n\n  public destroy() {\n    // Release references to save memory\n    this._subscriptions.clear();\n    this._assigned.clear();\n    this._trackedExperiments.clear();\n    this._trackedFeatures = {};\n    this._rtQueue = [];\n    if (this._rtTimer) {\n      clearTimeout(this._rtTimer);\n    }\n    unsubscribe(this);\n\n    if (isBrowser && window._growthbook === this) {\n      delete window._growthbook;\n    }\n\n    // Undo any active auto experiments\n    this._activeAutoExperiments.forEach((exp) => {\n      exp.undo();\n    });\n    this._activeAutoExperiments.clear();\n    this._triggeredExpKeys.clear();\n  }\n\n  public setRenderer(renderer: () => void) {\n    this._renderer = renderer;\n  }\n\n  public forceVariation(key: string, variation: number) {\n    this._ctx.forcedVariations = this._ctx.forcedVariations || {};\n    this._ctx.forcedVariations[key] = variation;\n    if (this._ctx.remoteEval) {\n      this._refreshForRemoteEval();\n      return;\n    }\n    this._updateAllAutoExperiments();\n    this._render();\n  }\n\n  public run<T>(experiment: Experiment<T>): Result<T> {\n    const result = this._run(experiment, null);\n    this._fireSubscriptions(experiment, result);\n    return result;\n  }\n\n  public triggerExperiment(key: string) {\n    this._triggeredExpKeys.add(key);\n    if (!this._ctx.experiments) return null;\n    const experiments = this._ctx.experiments.filter((exp) => exp.key === key);\n    return experiments\n      .map((exp) => {\n        if (!exp.manual) return null;\n        return this._runAutoExperiment(exp);\n      })\n      .filter((res) => res !== null);\n  }\n\n  private _runAutoExperiment(experiment: AutoExperiment, forceRerun?: boolean) {\n    const existing = this._activeAutoExperiments.get(experiment);\n\n    // If this is a manual experiment and it's not already running, skip\n    if (\n      experiment.manual &&\n      !this._triggeredExpKeys.has(experiment.key) &&\n      !existing\n    )\n      return null;\n\n    // Run the experiment\n    const result = this.run(experiment);\n\n    // A hash to quickly tell if the assigned value changed\n    const valueHash = JSON.stringify(result.value);\n\n    // If the changes are already active, no need to re-apply them\n    if (\n      !forceRerun &&\n      result.inExperiment &&\n      existing &&\n      existing.valueHash === valueHash\n    ) {\n      return result;\n    }\n\n    // Undo any existing changes\n    if (existing) this._undoActiveAutoExperiment(experiment);\n\n    // Apply new changes\n    if (result.inExperiment) {\n      const undo = this._applyDOMChanges(result.value);\n      if (undo) {\n        this._activeAutoExperiments.set(experiment, {\n          undo,\n          valueHash,\n        });\n      }\n    }\n\n    return result;\n  }\n\n  private _undoActiveAutoExperiment(exp: AutoExperiment) {\n    const data = this._activeAutoExperiments.get(exp);\n    if (data) {\n      data.undo();\n      this._activeAutoExperiments.delete(exp);\n    }\n  }\n\n  private _updateAllAutoExperiments(forceRerun?: boolean) {\n    const experiments = this._ctx.experiments || [];\n\n    // Stop any experiments that are no longer defined\n    const keys = new Set(experiments);\n    this._activeAutoExperiments.forEach((v, k) => {\n      if (!keys.has(k)) {\n        v.undo();\n        this._activeAutoExperiments.delete(k);\n      }\n    });\n\n    // Re-run all new/updated experiments\n    experiments.forEach((exp) => {\n      this._runAutoExperiment(exp, forceRerun);\n    });\n  }\n\n  private _fireSubscriptions<T>(experiment: Experiment<T>, result: Result<T>) {\n    const key = experiment.key;\n\n    // If assigned variation has changed, fire subscriptions\n    const prev = this._assigned.get(key);\n    // TODO: what if the experiment definition has changed?\n    if (\n      !prev ||\n      prev.result.inExperiment !== result.inExperiment ||\n      prev.result.variationId !== result.variationId\n    ) {\n      this._assigned.set(key, { experiment, result });\n      this._subscriptions.forEach((cb) => {\n        try {\n          cb(experiment, result);\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }\n\n  private _trackFeatureUsage(key: string, res: FeatureResult): void {\n    // Don't track feature usage that was forced via an override\n    if (res.source === \"override\") return;\n\n    // Only track a feature once, unless the assigned value changed\n    const stringifiedValue = JSON.stringify(res.value);\n    if (this._trackedFeatures[key] === stringifiedValue) return;\n    this._trackedFeatures[key] = stringifiedValue;\n\n    // Fire user-supplied callback\n    if (this._ctx.onFeatureUsage) {\n      try {\n        this._ctx.onFeatureUsage(key, res);\n      } catch (e) {\n        // Ignore feature usage callback errors\n      }\n    }\n\n    // In browser environments, queue up feature usage to be tracked in batches\n    if (!isBrowser || !window.fetch) return;\n    this._rtQueue.push({\n      key,\n      on: res.on,\n    });\n    if (!this._rtTimer) {\n      this._rtTimer = window.setTimeout(() => {\n        // Reset the queue\n        this._rtTimer = 0;\n        const q = [...this._rtQueue];\n        this._rtQueue = [];\n\n        // Skip logging if a real-time usage key is not configured\n        if (!this._ctx.realtimeKey) return;\n\n        window\n          .fetch(\n            `https://rt.growthbook.io/?key=${\n              this._ctx.realtimeKey\n            }&events=${encodeURIComponent(JSON.stringify(q))}`,\n\n            {\n              cache: \"no-cache\",\n              mode: \"no-cors\",\n            }\n          )\n          .catch(() => {\n            // TODO: retry in case of network errors?\n          });\n      }, this._ctx.realtimeInterval || 2000);\n    }\n  }\n\n  private _getFeatureResult<T>(\n    key: string,\n    value: T,\n    source: FeatureResultSource,\n    ruleId?: string,\n    experiment?: Experiment<T>,\n    result?: Result<T>\n  ): FeatureResult<T> {\n    const ret: FeatureResult = {\n      value,\n      on: !!value,\n      off: !value,\n      source,\n      ruleId: ruleId || \"\",\n    };\n    if (experiment) ret.experiment = experiment;\n    if (result) ret.experimentResult = result;\n\n    // Track the usage of this feature in real-time\n    this._trackFeatureUsage(key, ret);\n\n    return ret;\n  }\n\n  public isOn<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).on;\n  }\n\n  public isOff<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).off;\n  }\n\n  public getFeatureValue<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(key: K, defaultValue: V): WidenPrimitives<V> {\n    const value = this.evalFeature<WidenPrimitives<V>, K>(key).value;\n    return value === null ? (defaultValue as WidenPrimitives<V>) : value;\n  }\n\n  /**\n   * @deprecated Use {@link evalFeature}\n   * @param id\n   */\n  // eslint-disable-next-line\n  public feature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    return this.evalFeature(id);\n  }\n\n  public evalFeature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    // Global override\n    if (this._forcedFeatureValues.has(id)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Global override\", {\n          id,\n          value: this._forcedFeatureValues.get(id),\n        });\n      return this._getFeatureResult(\n        id,\n        this._forcedFeatureValues.get(id),\n        \"override\"\n      );\n    }\n\n    // Unknown feature id\n    if (!this._ctx.features || !this._ctx.features[id]) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Unknown feature\", { id });\n      return this._getFeatureResult(id, null, \"unknownFeature\");\n    }\n\n    // Get the feature\n    const feature: FeatureDefinition<V> = this._ctx.features[id];\n\n    // Loop through the rules\n    if (feature.rules) {\n      for (const rule of feature.rules) {\n        // If there are filters for who is included (e.g. namespaces)\n        if (rule.filters && this._isFilteredOut(rule.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip rule because of filters\", {\n              id,\n              rule,\n            });\n          continue;\n        }\n\n        // Feature value is being forced\n        if (\"force\" in rule) {\n          // If it's a conditional rule, skip if the condition doesn't pass\n          if (rule.condition && !this._conditionPasses(rule.condition)) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because of condition ff\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          // If this is a percentage rollout, skip if not included\n          if (\n            !this._isIncludedInRollout(\n              rule.seed || id,\n              rule.hashAttribute,\n              this._ctx.stickyBucketService && !rule.disableStickyBucketing\n                ? rule.fallbackAttribute\n                : undefined,\n              rule.range,\n              rule.coverage,\n              rule.hashVersion\n            )\n          ) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because user not included in rollout\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Force value from rule\", {\n              id,\n              rule,\n            });\n\n          // If this was a remotely evaluated experiment, fire the tracking callbacks\n          if (rule.tracks) {\n            rule.tracks.forEach((t) => {\n              this._track(t.experiment, t.result);\n            });\n          }\n\n          return this._getFeatureResult(id, rule.force as V, \"force\", rule.id);\n        }\n        if (!rule.variations) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip invalid rule\", {\n              id,\n              rule,\n            });\n\n          continue;\n        }\n\n        // For experiment rules, run an experiment\n        const exp: Experiment<V> = {\n          variations: rule.variations as [V, V, ...V[]],\n          key: rule.key || id,\n        };\n        if (\"coverage\" in rule) exp.coverage = rule.coverage;\n        if (rule.weights) exp.weights = rule.weights;\n        if (rule.hashAttribute) exp.hashAttribute = rule.hashAttribute;\n        if (rule.fallbackAttribute)\n          exp.fallbackAttribute = rule.fallbackAttribute;\n        if (rule.disableStickyBucketing)\n          exp.disableStickyBucketing = rule.disableStickyBucketing;\n        if (rule.bucketVersion !== undefined)\n          exp.bucketVersion = rule.bucketVersion;\n        if (rule.minBucketVersion !== undefined)\n          exp.minBucketVersion = rule.minBucketVersion;\n        if (rule.namespace) exp.namespace = rule.namespace;\n        if (rule.meta) exp.meta = rule.meta;\n        if (rule.ranges) exp.ranges = rule.ranges;\n        if (rule.name) exp.name = rule.name;\n        if (rule.phase) exp.phase = rule.phase;\n        if (rule.seed) exp.seed = rule.seed;\n        if (rule.hashVersion) exp.hashVersion = rule.hashVersion;\n        if (rule.filters) exp.filters = rule.filters;\n        if (rule.condition) exp.condition = rule.condition;\n\n        // Only return a value if the user is part of the experiment\n        const res = this._run(exp, id);\n        this._fireSubscriptions(exp, res);\n        if (res.inExperiment && !res.passthrough) {\n          return this._getFeatureResult(\n            id,\n            res.value,\n            \"experiment\",\n            rule.id,\n            exp,\n            res\n          );\n        }\n      }\n    }\n\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"Use default value\", {\n        id,\n        value: feature.defaultValue,\n      });\n\n    // Fall back to using the default value\n    return this._getFeatureResult(\n      id,\n      feature.defaultValue === undefined ? null : feature.defaultValue,\n      \"defaultValue\"\n    );\n  }\n\n  private _isIncludedInRollout(\n    seed: string,\n    hashAttribute: string | undefined,\n    fallbackAttribute: string | undefined,\n    range: VariationRange | undefined,\n    coverage: number | undefined,\n    hashVersion: number | undefined\n  ): boolean {\n    if (!range && coverage === undefined) return true;\n\n    const { hashValue } = this._getHashAttribute(\n      hashAttribute,\n      fallbackAttribute\n    );\n    if (!hashValue) {\n      return false;\n    }\n\n    const n = hash(seed, hashValue, hashVersion || 1);\n    if (n === null) return false;\n\n    return range\n      ? inRange(n, range)\n      : coverage !== undefined\n      ? n <= coverage\n      : true;\n  }\n\n  private _conditionPasses(condition: ConditionInterface): boolean {\n    return evalCondition(this.getAttributes(), condition);\n  }\n\n  private _isFilteredOut(filters: Filter[]): boolean {\n    return filters.some((filter) => {\n      const { hashValue } = this._getHashAttribute(filter.attribute);\n      if (!hashValue) return true;\n      const n = hash(filter.seed, hashValue, filter.hashVersion || 2);\n      if (n === null) return true;\n      return !filter.ranges.some((r) => inRange(n, r));\n    });\n  }\n\n  private _run<T>(\n    experiment: Experiment<T>,\n    featureId: string | null\n  ): Result<T> {\n    const key = experiment.key;\n    const numVariations = experiment.variations.length;\n\n    // 1. If experiment has less than 2 variations, return immediately\n    if (numVariations < 2) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Invalid experiment\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2. If the context is disabled, return immediately\n    if (this._ctx.enabled === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Context disabled\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2.5. Merge in experiment overrides from the context\n    experiment = this._mergeOverrides(experiment);\n\n    // 2.6 New, more powerful URL targeting\n    if (\n      experiment.urlPatterns &&\n      !isURLTargeted(this._getContextUrl(), experiment.urlPatterns)\n    ) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url targeting\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 3. If a variation is forced from a querystring, return the forced variation\n    const qsOverride = getQueryStringOverride(\n      key,\n      this._getContextUrl(),\n      numVariations\n    );\n    if (qsOverride !== null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via querystring\", {\n          id: key,\n          variation: qsOverride,\n        });\n      return this._getResult(experiment, qsOverride, false, featureId);\n    }\n\n    // 4. If a variation is forced in the context, return the forced variation\n    if (this._ctx.forcedVariations && key in this._ctx.forcedVariations) {\n      const variation = this._ctx.forcedVariations[key];\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via dev tools\", {\n          id: key,\n          variation,\n        });\n      return this._getResult(experiment, variation, false, featureId);\n    }\n\n    // 5. Exclude if a draft experiment or not active\n    if (experiment.status === \"draft\" || experiment.active === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because inactive\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 6. Get the hash attribute and return if empty\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n    if (!hashValue) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because missing hashAttribute\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    let assigned = -1;\n\n    let foundStickyBucket = false;\n    let stickyBucketVersionIsBlocked = false;\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const { variation, versionIsBlocked } = this._getStickyBucketVariation(\n        experiment.key,\n        experiment.bucketVersion,\n        experiment.minBucketVersion,\n        experiment.meta\n      );\n      foundStickyBucket = variation >= 0;\n      assigned = variation;\n      stickyBucketVersionIsBlocked = !!versionIsBlocked;\n    }\n\n    // Some checks are not needed if we already have a sticky bucket\n    if (!foundStickyBucket) {\n      // 7. Exclude if user is filtered out (used to be called \"namespace\")\n      if (experiment.filters) {\n        if (this._isFilteredOut(experiment.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip because of filters\", {\n              id: key,\n            });\n          return this._getResult(experiment, -1, false, featureId);\n        }\n      } else if (\n        experiment.namespace &&\n        !inNamespace(hashValue, experiment.namespace)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of namespace\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 7.5. Exclude if experiment.include returns false or throws\n      if (experiment.include && !isIncluded(experiment.include)) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of include function\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8. Exclude if condition is false\n      if (\n        experiment.condition &&\n        !this._conditionPasses(experiment.condition)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of condition exp\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8.1. Exclude if user is not in a required group\n      if (\n        experiment.groups &&\n        !this._hasGroupOverlap(experiment.groups as string[])\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of groups\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n    }\n\n    // 8.2. Old style URL targeting\n    if (experiment.url && !this._urlIsValid(experiment.url as RegExp)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 9. Get the variation from the sticky bucket or get bucket ranges and choose variation\n    const n = hash(\n      experiment.seed || key,\n      hashValue,\n      experiment.hashVersion || 1\n    );\n    if (n === null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of invalid hash version\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    if (!foundStickyBucket) {\n      const ranges =\n        experiment.ranges ||\n        getBucketRanges(\n          numVariations,\n          experiment.coverage === undefined ? 1 : experiment.coverage,\n          experiment.weights\n        );\n      assigned = chooseVariation(n, ranges);\n    }\n\n    // 9.5 Unenroll if any prior sticky buckets are blocked by version\n    if (stickyBucketVersionIsBlocked) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because sticky bucket version is blocked\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId, undefined, true);\n    }\n\n    // 10. Return if not in experiment\n    if (assigned < 0) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of coverage\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 11. Experiment has a forced variation\n    if (\"force\" in experiment) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force variation\", {\n          id: key,\n          variation: experiment.force,\n        });\n      return this._getResult(\n        experiment,\n        experiment.force === undefined ? -1 : experiment.force,\n        false,\n        featureId\n      );\n    }\n\n    // 12. Exclude if in QA mode\n    if (this._ctx.qaMode) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because QA mode\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 12.5. Exclude if experiment is stopped\n    if (experiment.status === \"stopped\") {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because stopped\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 13. Build the result object\n    const result = this._getResult(\n      experiment,\n      assigned,\n      true,\n      featureId,\n      n,\n      foundStickyBucket\n    );\n\n    // 13.5. Persist sticky bucket\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const {\n        changed,\n        key: attrKey,\n        doc,\n      } = this._generateStickyBucketAssignmentDoc(\n        hashAttribute,\n        toString(hashValue),\n        {\n          [this._getStickyBucketExperimentKey(\n            experiment.key,\n            experiment.bucketVersion\n          )]: result.key,\n        }\n      );\n      if (changed) {\n        // update local docs\n        this._ctx.stickyBucketAssignmentDocs =\n          this._ctx.stickyBucketAssignmentDocs || {};\n        this._ctx.stickyBucketAssignmentDocs[attrKey] = doc;\n        // save doc\n        this._ctx.stickyBucketService.saveAssignments(doc);\n      }\n    }\n\n    // 14. Fire the tracking callback\n    this._track(experiment, result);\n\n    // 15. Return the result\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"In experiment\", {\n        id: key,\n        variation: result.variationId,\n      });\n    return result;\n  }\n\n  log(msg: string, ctx: Record<string, unknown>) {\n    if (!this.debug) return;\n    if (this._ctx.log) this._ctx.log(msg, ctx);\n    else console.log(msg, ctx);\n  }\n\n  private _track<T>(experiment: Experiment<T>, result: Result<T>) {\n    if (!this._ctx.trackingCallback) return;\n\n    const key = experiment.key;\n\n    // Make sure a tracking callback is only fired once per unique experiment\n    const k =\n      result.hashAttribute + result.hashValue + key + result.variationId;\n    if (this._trackedExperiments.has(k)) return;\n    this._trackedExperiments.add(k);\n\n    try {\n      this._ctx.trackingCallback(experiment, result);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  private _mergeOverrides<T>(experiment: Experiment<T>): Experiment<T> {\n    const key = experiment.key;\n    const o = this._ctx.overrides;\n    if (o && o[key]) {\n      experiment = Object.assign({}, experiment, o[key]);\n      if (typeof experiment.url === \"string\") {\n        experiment.url = getUrlRegExp(\n          // eslint-disable-next-line\n          experiment.url as any\n        );\n      }\n    }\n\n    return experiment;\n  }\n\n  private _getHashAttribute(attr?: string, fallback?: string) {\n    let hashAttribute = attr || \"id\";\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let hashValue: any = \"\";\n\n    if (this._attributeOverrides[hashAttribute]) {\n      hashValue = this._attributeOverrides[hashAttribute];\n    } else if (this._ctx.attributes) {\n      hashValue = this._ctx.attributes[hashAttribute] || \"\";\n    } else if (this._ctx.user) {\n      hashValue = this._ctx.user[hashAttribute] || \"\";\n    }\n\n    // if no match, try fallback\n    if (!hashValue && fallback) {\n      if (this._attributeOverrides[fallback]) {\n        hashValue = this._attributeOverrides[fallback];\n      } else if (this._ctx.attributes) {\n        hashValue = this._ctx.attributes[fallback] || \"\";\n      } else if (this._ctx.user) {\n        hashValue = this._ctx.user[fallback] || \"\";\n      }\n      if (hashValue) {\n        hashAttribute = fallback;\n      }\n    }\n\n    return { hashAttribute, hashValue };\n  }\n\n  private _getResult<T>(\n    experiment: Experiment<T>,\n    variationIndex: number,\n    hashUsed: boolean,\n    featureId: string | null,\n    bucket?: number,\n    stickyBucketUsed?: boolean\n  ): Result<T> {\n    let inExperiment = true;\n    // If assigned variation is not valid, use the baseline and mark the user as not in the experiment\n    if (variationIndex < 0 || variationIndex >= experiment.variations.length) {\n      variationIndex = 0;\n      inExperiment = false;\n    }\n\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n\n    const meta: Partial<VariationMeta> = experiment.meta\n      ? experiment.meta[variationIndex]\n      : {};\n\n    const res: Result<T> = {\n      key: meta.key || \"\" + variationIndex,\n      featureId,\n      inExperiment,\n      hashUsed,\n      variationId: variationIndex,\n      value: experiment.variations[variationIndex],\n      hashAttribute,\n      hashValue,\n      stickyBucketUsed: !!stickyBucketUsed,\n    };\n\n    if (meta.name) res.name = meta.name;\n    if (bucket !== undefined) res.bucket = bucket;\n    if (meta.passthrough) res.passthrough = meta.passthrough;\n\n    return res;\n  }\n\n  private _getContextUrl() {\n    return this._ctx.url || (isBrowser ? window.location.href : \"\");\n  }\n\n  private _urlIsValid(urlRegex: RegExp): boolean {\n    const url = this._getContextUrl();\n    if (!url) return false;\n\n    const pathOnly = url.replace(/^https?:\\/\\//, \"\").replace(/^[^/]*\\//, \"/\");\n\n    if (urlRegex.test(url)) return true;\n    if (urlRegex.test(pathOnly)) return true;\n    return false;\n  }\n\n  private _hasGroupOverlap(expGroups: string[]): boolean {\n    const groups = this._ctx.groups || {};\n    for (let i = 0; i < expGroups.length; i++) {\n      if (groups[expGroups[i]]) return true;\n    }\n    return false;\n  }\n\n  private _applyDOMChanges(changes: AutoExperimentVariation) {\n    if (!isBrowser) return;\n    const undo: (() => void)[] = [];\n    if (changes.css) {\n      const s = document.createElement(\"style\");\n      s.innerHTML = changes.css;\n      document.head.appendChild(s);\n      undo.push(() => s.remove());\n    }\n    if (changes.js) {\n      const script = document.createElement(\"script\");\n      script.innerHTML = changes.js;\n      document.head.appendChild(script);\n      undo.push(() => script.remove());\n    }\n    if (changes.domMutations) {\n      changes.domMutations.forEach((mutation) => {\n        undo.push(mutate.declarative(mutation as DeclarativeMutation).revert);\n      });\n    }\n    return () => {\n      undo.forEach((fn) => fn());\n    };\n  }\n\n  private _deriveStickyBucketIdentifierAttributes(data?: FeatureApiResponse) {\n    const attributes = new Set<string>();\n    const features = data && data.features ? data.features : this.getFeatures();\n    const experiments =\n      data && data.experiments ? data.experiments : this.getExperiments();\n    Object.keys(features).forEach((id) => {\n      const feature = features[id];\n      if (feature.rules) {\n        for (const rule of feature.rules) {\n          if (rule.variations) {\n            attributes.add(rule.hashAttribute || \"id\");\n            if (rule.fallbackAttribute) {\n              attributes.add(rule.fallbackAttribute);\n            }\n          }\n        }\n      }\n    });\n    experiments.map((experiment) => {\n      attributes.add(experiment.hashAttribute || \"id\");\n      if (experiment.fallbackAttribute) {\n        attributes.add(experiment.fallbackAttribute);\n      }\n    });\n    return Array.from(attributes);\n  }\n\n  public async refreshStickyBuckets(data?: FeatureApiResponse) {\n    if (this._ctx.stickyBucketService) {\n      const attributes = this._getStickyBucketAttributes(data);\n      this._ctx.stickyBucketAssignmentDocs = await this._ctx.stickyBucketService.getAllAssignments(\n        attributes\n      );\n    }\n  }\n\n  private _getStickyBucketAssignments(): StickyAssignments {\n    const mergedAssignments: StickyAssignments = {};\n    Object.values(this._ctx.stickyBucketAssignmentDocs || {}).forEach((doc) => {\n      if (doc.assignments) Object.assign(mergedAssignments, doc.assignments);\n    });\n    return mergedAssignments;\n  }\n\n  private _getStickyBucketVariation(\n    experimentKey: string,\n    experimentBucketVersion?: number,\n    minExperimentBucketVersion?: number,\n    meta?: VariationMeta[]\n  ): {\n    variation: number;\n    versionIsBlocked?: boolean;\n  } {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    minExperimentBucketVersion = minExperimentBucketVersion || 0;\n    meta = meta || [];\n    const id = this._getStickyBucketExperimentKey(\n      experimentKey,\n      experimentBucketVersion\n    );\n    const assignments = this._getStickyBucketAssignments();\n\n    // users with any blocked bucket version (0 to minExperimentBucketVersion) are excluded from the test\n    if (minExperimentBucketVersion > 0) {\n      for (let i = 0; i <= minExperimentBucketVersion; i++) {\n        const blockedKey = this._getStickyBucketExperimentKey(experimentKey, i);\n        if (assignments[blockedKey] !== undefined) {\n          return {\n            variation: -1,\n            versionIsBlocked: true,\n          };\n        }\n      }\n    }\n    const variationKey = assignments[id];\n    if (variationKey === undefined)\n      // no assignment found\n      return { variation: -1 };\n    const variation = meta.findIndex((m) => m.key === variationKey);\n    if (variation < 0)\n      // invalid assignment, treat as \"no assignment found\"\n      return { variation: -1 };\n\n    return { variation };\n  }\n\n  private _getStickyBucketExperimentKey(\n    experimentKey: string,\n    experimentBucketVersion?: number\n  ): StickyExperimentKey {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    return `${experimentKey}__${experimentBucketVersion}`;\n  }\n\n  private _getStickyBucketAttributes(\n    data?: FeatureApiResponse\n  ): Record<string, string> {\n    const attributes: Record<string, string> = {};\n    this._ctx.stickyBucketIdentifierAttributes = !this._ctx\n      .stickyBucketIdentifierAttributes\n      ? this._deriveStickyBucketIdentifierAttributes(data)\n      : this._ctx.stickyBucketIdentifierAttributes;\n    this._ctx.stickyBucketIdentifierAttributes.forEach((attr) => {\n      const { hashValue } = this._getHashAttribute(attr);\n      attributes[attr] = toString(hashValue);\n    });\n    return attributes;\n  }\n\n  private _generateStickyBucketAssignmentDoc(\n    attributeName: string,\n    attributeValue: string,\n    assignments: StickyAssignments\n  ): {\n    key: StickyAttributeKey;\n    doc: StickyAssignmentsDocument;\n    changed: boolean;\n  } {\n    const key = `${attributeName}||${attributeValue}`;\n    const existingAssignments =\n      this._ctx.stickyBucketAssignmentDocs &&\n      this._ctx.stickyBucketAssignmentDocs[key]\n        ? this._ctx.stickyBucketAssignmentDocs[key].assignments || {}\n        : {};\n    const newAssignments = { ...existingAssignments, ...assignments };\n    const changed =\n      JSON.stringify(existingAssignments) !== JSON.stringify(newAssignments);\n\n    return {\n      key,\n      doc: {\n        attributeName,\n        attributeValue,\n        assignments: newAssignments,\n      },\n      changed,\n    };\n  }\n}\n", "import {\n  LocalStorageCompat,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n} from \"./types/growthbook\";\n\nexport interface CookieAttributes {\n  expires?: number | Date | undefined;\n  path?: string | undefined;\n  domain?: string | undefined;\n  secure?: boolean | undefined;\n  sameSite?: \"strict\" | \"Strict\" | \"lax\" | \"Lax\" | \"none\" | \"None\" | undefined;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [property: string]: any;\n}\nexport interface JsCookiesCompat<T = string> {\n  set(\n    name: string,\n    value: string | T,\n    options?: CookieAttributes\n  ): string | undefined;\n  get(name: string): string | T | undefined;\n  get(): { [key: string]: string };\n  remove(name: string, options?: CookieAttributes): void;\n}\n\nexport interface IORedisCompat {\n  mget(...keys: string[]): Promise<string[]>;\n  set(key: string, value: string): Promise<string>;\n}\n\nexport interface RequestCompat {\n  cookies: Record<string, string>;\n  [key: string]: unknown;\n}\nexport interface ResponseCompat {\n  cookie(\n    name: string,\n    value: string,\n    options?: CookieAttributes\n  ): ResponseCompat;\n  [key: string]: unknown;\n}\n\n/**\n * Responsible for reading and writing documents which describe sticky bucket assignments.\n */\nexport abstract class StickyBucketService {\n  abstract getAssignments(\n    attributeName: string,\n    attributeValue: string\n  ): Promise<StickyAssignmentsDocument | null>;\n\n  abstract saveAssignments(doc: StickyAssignmentsDocument): Promise<unknown>;\n\n  /**\n   * The SDK calls getAllAssignments to populate sticky buckets. This in turn will\n   * typically loop through individual getAssignments calls. However, some StickyBucketService\n   * instances (i.e. Redis) will instead perform a multi-query inside getAllAssignments instead.\n   */\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<string, StickyAssignmentsDocument> = {};\n    (\n      await Promise.all(\n        Object.entries(attributes).map(([attributeName, attributeValue]) =>\n          this.getAssignments(attributeName, attributeValue)\n        )\n      )\n    ).forEach((doc) => {\n      if (doc) {\n        const key = `${doc.attributeName}||${doc.attributeValue}`;\n        docs[key] = doc;\n      }\n    });\n    return docs;\n  }\n}\n\nexport class LocalStorageStickyBucketService extends StickyBucketService {\n  private prefix: string;\n  private localStorage: LocalStorageCompat | undefined;\n  constructor(opts?: { prefix?: string; localStorage?: LocalStorageCompat }) {\n    opts = opts || {};\n    super();\n    this.prefix = opts.prefix || \"gbStickyBuckets__\";\n    try {\n      this.localStorage = opts.localStorage || globalThis.localStorage;\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.localStorage) return doc;\n    try {\n      const raw = (await this.localStorage.getItem(this.prefix + key)) || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.localStorage) return;\n    try {\n      await this.localStorage.setItem(this.prefix + key, JSON.stringify(doc));\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n}\n\nexport class ExpressCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with cookieParser() middleware from npm: 'cookie-parser'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value must be manually encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private req: RequestCompat;\n  private res: ResponseCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    req,\n    res,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    req: RequestCompat;\n    res: ResponseCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.req = req;\n    this.res = res;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.req) return doc;\n    try {\n      const raw = this.req.cookies[this.prefix + key] || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.res) return;\n    const str = JSON.stringify(doc);\n    this.res.cookie(\n      encodeURIComponent(this.prefix + key),\n      encodeURIComponent(str),\n      this.cookieAttributes\n    );\n  }\n}\n\nexport class BrowserCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with npm: 'js-cookie'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value is automatically encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private jsCookie: JsCookiesCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    jsCookie,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    jsCookie: JsCookiesCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.jsCookie = jsCookie;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.jsCookie) return doc;\n    try {\n      const raw = this.jsCookie.get(this.prefix + key);\n      const data = JSON.parse(raw || \"{}\");\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.jsCookie) return;\n    const str = JSON.stringify(doc);\n    this.jsCookie.set(this.prefix + key, str, this.cookieAttributes);\n  }\n}\n\nexport class RedisStickyBucketService extends StickyBucketService {\n  /** Intended to be used with npm: 'ioredis'. **/\n  private redis: IORedisCompat | undefined;\n  constructor({ redis }: { redis: IORedisCompat }) {\n    super();\n    this.redis = redis;\n  }\n\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<StickyAttributeKey, StickyAssignmentsDocument> = {};\n    const keys = Object.entries(attributes).map(\n      ([attributeName, attributeValue]) => `${attributeName}||${attributeValue}`\n    );\n    if (!this.redis) return docs;\n    this.redis.mget(...keys).then((values) => {\n      values.forEach((raw) => {\n        try {\n          const data = JSON.parse(raw || \"{}\");\n          if (data.attributeName && data.attributeValue && data.assignments) {\n            const key = `${data.attributeName}||${data.attributeValue}`;\n            docs[key] = data;\n          }\n        } catch (e) {\n          // ignore redis doc parse errors\n        }\n      });\n    });\n    return docs;\n  }\n\n  async getAssignments(_attributeName: string, _attributeValue: string) {\n    // not implemented\n    return null;\n  }\n\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.redis) return;\n    await this.redis.set(key, JSON.stringify(doc));\n  }\n}\n"], "names": ["cacheSettings", "staleTTL", "maxAge", "cache<PERSON>ey", "backgroundSync", "maxEntries", "disableIdleStreams", "idleStreamInterval", "polyfills", "fetch", "globalThis", "bind", "undefined", "SubtleCrypto", "crypto", "subtle", "EventSource", "helpers", "fetchFeaturesCall", "host", "client<PERSON>ey", "headers", "fetchRemoteEvalCall", "payload", "options", "method", "body", "JSON", "stringify", "eventSourceCall", "startIdleListener", "idleTimeout", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "onVisibilityChange", "visibilityState", "clearTimeout", "onVisible", "setTimeout", "onHidden", "addEventListener", "removeEventListener", "stopIdleListener", "localStorage", "e", "subscribedInstances", "Map", "cacheInitialized", "cache", "activeFetches", "streams", "supportsSSE", "Set", "setPolyfills", "overrides", "Object", "assign", "configure<PERSON>ache", "clearAutoRefresh", "clearCache", "clear", "updatePersistentCache", "refreshFeatures", "instance", "timeout", "<PERSON><PERSON><PERSON>", "allowStale", "updateInstance", "data", "fetchFeaturesWithCache", "refreshInstance", "subscribe", "key", "<PERSON><PERSON><PERSON>", "subs", "get", "add", "set", "unsubscribe", "for<PERSON>ach", "s", "delete", "channel", "state", "disableChannel", "enableChannel", "setItem", "Array", "from", "entries", "get<PERSON><PERSON><PERSON><PERSON>", "now", "Date", "minStaleAt", "getTime", "initializeCache", "existing", "staleAt", "sse", "fetchFeatures", "startAutoRefresh", "promiseTimeout", "apiHost", "getApiInfo", "baseKey", "isRemoteEval", "attributes", "getAttributes", "cacheKeyAttributes", "getCacheKeyAttributes", "keys", "ca", "fv", "getForcedVariations", "url", "getUrl", "promise", "Promise", "resolve", "resolved", "timer", "finish", "then", "catch", "value", "getItem", "parsed", "parse", "isArray", "cleanupCache", "cleanupFn", "entriesWithTimestamps", "map", "sort", "a", "b", "entriesToRemoveCount", "Math", "min", "max", "size", "i", "onNewFeatureData", "version", "dateUpdated", "has", "instances", "decryptPayload", "refreshStickyBuckets", "setExperiments", "experiments", "getExperiments", "setFeatures", "features", "getFeatures", "apiRequestHeaders", "getApiHosts", "getClientKey", "remoteEval", "fetcher", "forcedVariations", "forcedFeatures", "getForcedFeatures", "res", "json", "streamingHost", "streamingHostRequestHeaders", "src", "cb", "event", "type", "errors", "onSSEError", "readyState", "delay", "pow", "random", "includes", "onopen", "onerror", "close", "destroyChannel", "validAttributeName", "nullController", "revert", "elements", "mutations", "getObserverInit", "attr", "childList", "subtree", "characterData", "attributeFilter", "getElementRecord", "element", "record", "createElementPropertyRecord", "el", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "parentNode", "insertBeforeNode", "observe", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "m", "mutate", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "join", "attrMutationRunner", "_loadDOMNodes", "parentSelector", "insertBeforeSelector", "querySelector", "positionMutationRunner", "selectors", "newNodes", "getHTMLValue", "innerHTML", "setHTMLValue", "getElementHTMLRecord", "elementRecord", "html", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getAttrValue", "attrName", "getAttribute", "setAttrValue", "setAttribute", "getElementAttributeRecord", "deleteElementPropertyRecord", "disconnect", "transformContainer", "createElement", "setPropertyV<PERSON>ue", "length", "startMutating", "mutation", "kind", "attribute", "push", "stopMutating", "index", "indexOf", "splice", "refreshElementsSet", "existingElements", "matchingElements", "querySelectorAll", "selector", "revertMutation", "refreshAllElementSets", "connectGlobalObserver", "documentElement", "newMutation", "test", "mutatedClassnames", "classnames", "c", "declarative", "action", "hashFnv32a", "str", "hval", "l", "charCodeAt", "hash", "seed", "getEqualWeights", "n", "fill", "inRange", "range", "inNamespace", "hashValue", "namespace", "chooseVariation", "ranges", "getUrlRegExp", "regexString", "escaped", "replace", "RegExp", "console", "error", "isURLTargeted", "targets", "hasIncludeRules", "isIncluded", "match", "_evalURLTarget", "pattern", "include", "_evalSimpleUrlPart", "actual", "isPath", "regex", "_evalSimpleUrlTarget", "expected", "URL", "comps", "pathname", "searchParams", "v", "k", "some", "href", "substring", "origin", "getBucketRanges", "numVariations", "coverage", "weights", "equal", "totalWeight", "reduce", "w", "sum", "cumulative", "start", "getQueryStringOverride", "id", "search", "kv", "parseInt", "base64ToBuf", "Uint8Array", "atob", "decrypt", "encryptedString", "decryptionKey", "Error", "importKey", "name", "iv", "cipherText", "plainTextBuffer", "TextDecoder", "decode", "toString", "input", "paddedVersionString", "parts", "padStart", "loadSDKVersion", "_regexCache", "evalCondition", "obj", "condition", "evalOr", "evalAnd", "evalConditionValue", "<PERSON><PERSON><PERSON>", "path", "current", "getRegex", "isOperatorObject", "op", "evalOperatorCondition", "getType", "t", "elemMatch", "check", "isIn", "operator", "passed", "j", "conditions", "SDK_VERSION", "GrowthBook", "constructor", "context", "_ctx", "_renderer", "_trackedExperiments", "_trackedFeatures", "debug", "_subscriptions", "_rtQueue", "_rtTimer", "ready", "_assigned", "_forcedFeatureV<PERSON>ues", "_attributeOverrides", "_activeAutoExperiments", "_triggeredExpKeys", "_loadFeaturesCalled", "isGbHost", "hostname", "enableDevMode", "_growthbook", "dispatchEvent", "Event", "_updateAllAutoExperiments", "_refresh", "loadFeatures", "autoRefresh", "subscribeToChanges", "_canSubscribe", "defaultHost", "apiHostRequestHeaders", "_render", "setEncryptedFeatures", "featuresJSON", "setEncryptedExperiments", "experimentsJSON", "encryptedFeatures", "encryptedExperiments", "setAttributes", "stickyBucketService", "_refreshForRemoteEval", "setAttributeOverrides", "setForcedVariations", "vars", "setForcedFeatures", "setURL", "getStickyBucketAssignmentDocs", "stickyBucketAssignmentDocs", "getAllResults", "destroy", "exp", "undo", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "forceVariation", "variation", "run", "experiment", "result", "_run", "_fireSubscriptions", "triggerExperiment", "manual", "_runAutoExperiment", "forceRerun", "valueHash", "inExperiment", "_undoActiveAutoExperiment", "_applyDOMChanges", "prev", "variationId", "_trackFeatureUsage", "source", "stringifiedValue", "onFeatureUsage", "on", "q", "realtimeKey", "encodeURIComponent", "mode", "realtimeInterval", "_getFeatureResult", "ruleId", "ret", "off", "experimentResult", "isOn", "evalFeature", "isOff", "getFeatureValue", "defaultValue", "feature", "rules", "rule", "filters", "_isFilteredOut", "_conditionPasses", "_isIncludedInRollout", "hashAttribute", "disableStickyBucketing", "fallbackAttribute", "hashVersion", "tracks", "_track", "force", "variations", "bucketVersion", "minBucketVersion", "meta", "phase", "passthrough", "_getHashAttribute", "r", "featureId", "_getResult", "enabled", "_mergeOverrides", "urlPatterns", "_getContextUrl", "qsOverride", "status", "active", "assigned", "foundStickyBucket", "stickyBucketVersionIsBlocked", "versionIsBlocked", "_getStickyBucketVariation", "groups", "_hasGroupOverlap", "_urlIsValid", "qaMode", "changed", "attrKey", "doc", "_generateStickyBucketAssignmentDoc", "_getStickyBucketExperimentKey", "saveAssignments", "log", "msg", "ctx", "trackingCallback", "o", "fallback", "user", "variationIndex", "hashUsed", "bucket", "stickyBucketUsed", "location", "urlRegex", "pathOnly", "expGroups", "changes", "css", "head", "append<PERSON><PERSON><PERSON>", "remove", "js", "script", "domMutations", "fn", "_deriveStickyBucketIdentifierAttributes", "_getStickyBucketAttributes", "getAllAssignments", "_getStickyBucketAssignments", "mergedAssignments", "values", "assignments", "<PERSON><PERSON><PERSON>", "experimentBucketVersion", "minExperimentBucketVersion", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "findIndex", "stickyBucketIdentifierAttributes", "attributeName", "attributeValue", "existingAssignments", "newAssignments", "StickyBucketService", "docs", "all", "getAssignments", "LocalStorageStickyBucketService", "opts", "prefix", "raw", "ExpressCookieStickyBucketService", "req", "cookieAttributes", "cookies", "cookie", "BrowserCookieStickyBucketService", "js<PERSON><PERSON><PERSON>", "RedisStickyBucketService", "redis", "mget", "_attributeName", "_attributeValue"], "mappings": "AAyBA;AACA,MAAMA,aAA4B,GAAG;AACnC;EACAC,QAAQ,EAAE,IAAI,GAAG,EAAE;AACnB;AACAC,EAAAA,MAAM,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3BC,EAAAA,QAAQ,EAAE,iBAAiB;AAC3BC,EAAAA,cAAc,EAAE,IAAI;AACpBC,EAAAA,UAAU,EAAE,EAAE;AACdC,EAAAA,kBAAkB,EAAE,KAAK;AACzBC,EAAAA,kBAAkB,EAAE,KAAA;AACtB,CAAC,CAAA;AACD,MAAMC,SAAoB,GAAG;AAC3BC,EAAAA,KAAK,EAAEC,UAAU,CAACD,KAAK,GAAGC,UAAU,CAACD,KAAK,CAACE,IAAI,CAACD,UAAU,CAAC,GAAGE,SAAS;EACvEC,YAAY,EAAEH,UAAU,CAACI,MAAM,GAAGJ,UAAU,CAACI,MAAM,CAACC,MAAM,GAAGH,SAAS;EACtEI,WAAW,EAAEN,UAAU,CAACM,WAAAA;AAC1B,CAAC,CAAA;AACM,MAAMC,OAAgB,GAAG;AAC9BC,EAAAA,iBAAiB,EAAE,IAAkC,IAAA;IAAA,IAAjC;MAAEC,IAAI;MAAEC,SAAS;AAAEC,MAAAA,OAAAA;KAAS,GAAA,IAAA,CAAA;AAC9C,IAAA,OAAQb,SAAS,CAACC,KAAK,WAClBU,IAAI,EAAA,gBAAA,CAAA,CAAA,MAAA,CAAiBC,SAAS,CACjC,EAAA;AAAEC,MAAAA,OAAAA;AAAQ,KAAC,CACZ,CAAA;GACF;AACDC,EAAAA,mBAAmB,EAAE,KAA2C,IAAA;IAAA,IAA1C;MAAEH,IAAI;MAAEC,SAAS;MAAEG,OAAO;AAAEF,MAAAA,OAAAA;KAAS,GAAA,KAAA,CAAA;AACzD,IAAA,MAAMG,OAAO,GAAG;AACdC,MAAAA,MAAM,EAAE,MAAM;AACdJ,MAAAA,OAAO,EAAE;AAAE,QAAA,cAAc,EAAE,kBAAkB;QAAE,GAAGA,OAAAA;OAAS;AAC3DK,MAAAA,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAA;KAC7B,CAAA;IACD,OAAQf,SAAS,CAACC,KAAK,CAAA,EAAA,CAAA,MAAA,CAClBU,IAAI,EAAaC,YAAAA,CAAAA,CAAAA,MAAAA,CAAAA,SAAS,CAC7BI,EAAAA,OAAO,CACR,CAAA;GACF;AACDK,EAAAA,eAAe,EAAE,KAAkC,IAAA;IAAA,IAAjC;MAAEV,IAAI;MAAEC,SAAS;AAAEC,MAAAA,OAAAA;KAAS,GAAA,KAAA,CAAA;AAC5C,IAAA,IAAIA,OAAO,EAAE;MACX,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,EAAA,OAAA,CAAA,CAAA,MAAA,CAAQC,SAAS,CAAI,EAAA;AAC3DC,QAAAA,OAAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;IACA,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,EAAA,OAAA,CAAA,CAAA,MAAA,CAAQC,SAAS,CAAG,CAAA,CAAA;GAC7D;AACDU,EAAAA,iBAAiB,EAAE,MAAM;AACvB,IAAA,IAAIC,WAA+B,CAAA;IACnC,MAAMC,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,CAAA;IAClE,IAAI,CAACF,SAAS,EAAE,OAAA;IAChB,MAAMG,kBAAkB,GAAG,MAAM;AAC/B,MAAA,IAAID,QAAQ,CAACE,eAAe,KAAK,SAAS,EAAE;AAC1CH,QAAAA,MAAM,CAACI,YAAY,CAACN,WAAW,CAAC,CAAA;AAChCO,QAAAA,SAAS,EAAE,CAAA;AACb,OAAC,MAAM,IAAIJ,QAAQ,CAACE,eAAe,KAAK,QAAQ,EAAE;QAChDL,WAAW,GAAGE,MAAM,CAACM,UAAU,CAC7BC,QAAQ,EACRxC,aAAa,CAACO,kBAAkB,CACjC,CAAA;AACH,OAAA;KACD,CAAA;AACD2B,IAAAA,QAAQ,CAACO,gBAAgB,CAAC,kBAAkB,EAAEN,kBAAkB,CAAC,CAAA;IACjE,OAAO,MACLD,QAAQ,CAACQ,mBAAmB,CAAC,kBAAkB,EAAEP,kBAAkB,CAAC,CAAA;GACvE;AACDQ,EAAAA,gBAAgB,EAAE,MAAM;AACtB;AAAA,GAAA;AAEJ,EAAC;AAED,IAAI;EACF,IAAIjC,UAAU,CAACkC,YAAY,EAAE;AAC3BpC,IAAAA,SAAS,CAACoC,YAAY,GAAGlC,UAAU,CAACkC,YAAY,CAAA;AAClD,GAAA;AACF,CAAC,CAAC,OAAOC,CAAC,EAAE;AACV;AAAA,CAAA;;AAGF;AACA,MAAMC,mBAAiD,GAAG,IAAIC,GAAG,EAAE,CAAA;AACnE,IAAIC,gBAAgB,GAAG,KAAK,CAAA;AAC5B,MAAMC,KAA8B,GAAG,IAAIF,GAAG,EAAE,CAAA;AAChD,MAAMG,aAAuD,GAAG,IAAIH,GAAG,EAAE,CAAA;AACzE,MAAMI,OAAmC,GAAG,IAAIJ,GAAG,EAAE,CAAA;AACrD,MAAMK,WAAwB,GAAG,IAAIC,GAAG,EAAE,CAAA;;AAE1C;AACO,SAASC,YAAY,CAACC,SAA6B,EAAQ;AAChEC,EAAAA,MAAM,CAACC,MAAM,CAACjD,SAAS,EAAE+C,SAAS,CAAC,CAAA;AACrC,CAAA;AACO,SAASG,cAAc,CAACH,SAAiC,EAAQ;AACtEC,EAAAA,MAAM,CAACC,MAAM,CAACzD,aAAa,EAAEuD,SAAS,CAAC,CAAA;AACvC,EAAA,IAAI,CAACvD,aAAa,CAACI,cAAc,EAAE;AACjCuD,IAAAA,gBAAgB,EAAE,CAAA;AACpB,GAAA;AACF,CAAA;AAEO,eAAeC,UAAU,GAAkB;EAChDX,KAAK,CAACY,KAAK,EAAE,CAAA;EACbX,aAAa,CAACW,KAAK,EAAE,CAAA;AACrBF,EAAAA,gBAAgB,EAAE,CAAA;AAClBX,EAAAA,gBAAgB,GAAG,KAAK,CAAA;AACxB,EAAA,MAAMc,qBAAqB,EAAE,CAAA;AAC/B,CAAA;AAEO,eAAeC,eAAe,CACnCC,QAAoB,EACpBC,OAAgB,EAChBC,SAAmB,EACnBC,UAAoB,EACpBC,cAAwB,EACxBhE,cAAwB,EACT;EACf,IAAI,CAACA,cAAc,EAAE;IACnBJ,aAAa,CAACI,cAAc,GAAG,KAAK,CAAA;AACtC,GAAA;AAEA,EAAA,MAAMiE,IAAI,GAAG,MAAMC,sBAAsB,CACvCN,QAAQ,EACRG,UAAU,EACVF,OAAO,EACPC,SAAS,CACV,CAAA;EACDE,cAAc,IAAIC,IAAI,KAAK,MAAME,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC,CAAA;AACnE,CAAA;;AAEA;AACO,SAASG,SAAS,CAACR,QAAoB,EAAQ;AACpD,EAAA,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAC5B,MAAMW,IAAI,GAAG7B,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC,IAAI,IAAIpB,GAAG,EAAE,CAAA;AACtDsB,EAAAA,IAAI,CAACE,GAAG,CAACb,QAAQ,CAAC,CAAA;AAClBlB,EAAAA,mBAAmB,CAACgC,GAAG,CAACL,GAAG,EAAEE,IAAI,CAAC,CAAA;AACpC,CAAA;AACO,SAASI,WAAW,CAACf,QAAoB,EAAQ;EACtDlB,mBAAmB,CAACkC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClB,QAAQ,CAAC,CAAC,CAAA;AACxD,CAAA;AAEO,SAASxB,QAAQ,GAAG;AACzBW,EAAAA,OAAO,CAAC6B,OAAO,CAAEG,OAAO,IAAK;IAC3B,IAAI,CAACA,OAAO,EAAE,OAAA;IACdA,OAAO,CAACC,KAAK,GAAG,MAAM,CAAA;IACtBC,cAAc,CAACF,OAAO,CAAC,CAAA;AACzB,GAAC,CAAC,CAAA;AACJ,CAAA;AAEO,SAAS7C,SAAS,GAAG;AAC1Ba,EAAAA,OAAO,CAAC6B,OAAO,CAAEG,OAAO,IAAK;IAC3B,IAAI,CAACA,OAAO,EAAE,OAAA;AACd,IAAA,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE,OAAA;IAC9BE,aAAa,CAACH,OAAO,CAAC,CAAA;AACxB,GAAC,CAAC,CAAA;AACJ,CAAA;;AAEA;;AAEA,eAAerB,qBAAqB,GAAG;EACrC,IAAI;AACF,IAAA,IAAI,CAACtD,SAAS,CAACoC,YAAY,EAAE,OAAA;IAC7B,MAAMpC,SAAS,CAACoC,YAAY,CAAC2C,OAAO,CAClCvF,aAAa,CAACG,QAAQ,EACtBwB,IAAI,CAACC,SAAS,CAAC4D,KAAK,CAACC,IAAI,CAACxC,KAAK,CAACyC,OAAO,EAAE,CAAC,CAAC,CAC5C,CAAA;GACF,CAAC,OAAO7C,CAAC,EAAE;AACV;AAAA,GAAA;AAEJ,CAAA;AAEA,eAAeyB,sBAAsB,CACnCN,QAAoB,EACpBG,UAAoB,EACpBF,OAAgB,EAChBC,SAAmB,EACiB;AACpC,EAAA,MAAMO,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;AAC5B,EAAA,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC,CAAA;AACtC,EAAA,MAAM4B,GAAG,GAAG,IAAIC,IAAI,EAAE,CAAA;AAEtB,EAAA,MAAMC,UAAU,GAAG,IAAID,IAAI,CACzBD,GAAG,CAACG,OAAO,EAAE,GAAG/F,aAAa,CAACE,MAAM,GAAGF,aAAa,CAACC,QAAQ,CAC9D,CAAA;AAED,EAAA,MAAM+F,eAAe,EAAE,CAAA;AACvB,EAAA,MAAMC,QAAQ,GAAGhD,KAAK,CAAC2B,GAAG,CAACzE,QAAQ,CAAC,CAAA;AACpC,EAAA,IACE8F,QAAQ,IACR,CAAC/B,SAAS,KACTC,UAAU,IAAI8B,QAAQ,CAACC,OAAO,GAAGN,GAAG,CAAC,IACtCK,QAAQ,CAACC,OAAO,GAAGJ,UAAU,EAC7B;AACA;IACA,IAAIG,QAAQ,CAACE,GAAG,EAAE/C,WAAW,CAACyB,GAAG,CAACJ,GAAG,CAAC,CAAA;;AAEtC;AACA,IAAA,IAAIwB,QAAQ,CAACC,OAAO,GAAGN,GAAG,EAAE;MAC1BQ,aAAa,CAACpC,QAAQ,CAAC,CAAA;AACzB,KAAA;AACA;SACK;MACHqC,gBAAgB,CAACrC,QAAQ,CAAC,CAAA;AAC5B,KAAA;IACA,OAAOiC,QAAQ,CAAC5B,IAAI,CAAA;AACtB,GAAC,MAAM;IACL,OAAO,MAAMiC,cAAc,CAACF,aAAa,CAACpC,QAAQ,CAAC,EAAEC,OAAO,CAAC,CAAA;AAC/D,GAAA;AACF,CAAA;AAEA,SAASS,MAAM,CAACV,QAAoB,EAAU;EAC5C,MAAM,CAACuC,OAAO,EAAEnF,SAAS,CAAC,GAAG4C,QAAQ,CAACwC,UAAU,EAAE,CAAA;EAClD,OAAUD,EAAAA,CAAAA,MAAAA,CAAAA,OAAO,eAAKnF,SAAS,CAAA,CAAA;AACjC,CAAA;AAEA,SAASuE,WAAW,CAAC3B,QAAoB,EAAU;AACjD,EAAA,MAAMyC,OAAO,GAAG/B,MAAM,CAACV,QAAQ,CAAC,CAAA;AAChC,EAAA,IAAI,CAACA,QAAQ,CAAC0C,YAAY,EAAE,EAAE,OAAOD,OAAO,CAAA;AAE5C,EAAA,MAAME,UAAU,GAAG3C,QAAQ,CAAC4C,aAAa,EAAE,CAAA;AAC3C,EAAA,MAAMC,kBAAkB,GACtB7C,QAAQ,CAAC8C,qBAAqB,EAAE,IAAItD,MAAM,CAACuD,IAAI,CAAC/C,QAAQ,CAAC4C,aAAa,EAAE,CAAC,CAAA;EAC3E,MAAMI,EAAc,GAAG,EAAE,CAAA;AACzBH,EAAAA,kBAAkB,CAAC7B,OAAO,CAAEP,GAAG,IAAK;AAClCuC,IAAAA,EAAE,CAACvC,GAAG,CAAC,GAAGkC,UAAU,CAAClC,GAAG,CAAC,CAAA;AAC3B,GAAC,CAAC,CAAA;AAEF,EAAA,MAAMwC,EAAE,GAAGjD,QAAQ,CAACkD,mBAAmB,EAAE,CAAA;AACzC,EAAA,MAAMC,GAAG,GAAGnD,QAAQ,CAACoD,MAAM,EAAE,CAAA;AAE7B,EAAA,OAAA,EAAA,CAAA,MAAA,CAAUX,OAAO,EAAA,IAAA,CAAA,CAAA,MAAA,CAAK9E,IAAI,CAACC,SAAS,CAAC;IACnCoF,EAAE;IACFC,EAAE;AACFE,IAAAA,GAAAA;AACF,GAAC,CAAC,CAAA,CAAA;AACJ,CAAA;;AAEA;AACA;AACA;AACA,SAASb,cAAc,CACrBe,OAAmB,EACnBpD,OAAgB,EACG;AACnB,EAAA,OAAO,IAAIqD,OAAO,CAAEC,OAAO,IAAK;IAC9B,IAAIC,QAAQ,GAAG,KAAK,CAAA;AACpB,IAAA,IAAIC,KAAc,CAAA;IAClB,MAAMC,MAAM,GAAIrD,IAAQ,IAAK;AAC3B,MAAA,IAAImD,QAAQ,EAAE,OAAA;AACdA,MAAAA,QAAQ,GAAG,IAAI,CAAA;AACfC,MAAAA,KAAK,IAAIpF,YAAY,CAACoF,KAAK,CAAiB,CAAA;AAC5CF,MAAAA,OAAO,CAAClD,IAAI,IAAI,IAAI,CAAC,CAAA;KACtB,CAAA;AAED,IAAA,IAAIJ,OAAO,EAAE;MACXwD,KAAK,GAAGlF,UAAU,CAAC,MAAMmF,MAAM,EAAE,EAAEzD,OAAO,CAAC,CAAA;AAC7C,KAAA;AAEAoD,IAAAA,OAAO,CAACM,IAAI,CAAEtD,IAAI,IAAKqD,MAAM,CAACrD,IAAI,CAAC,CAAC,CAACuD,KAAK,CAAC,MAAMF,MAAM,EAAE,CAAC,CAAA;AAC5D,GAAC,CAAC,CAAA;AACJ,CAAA;;AAEA;AACA,eAAe1B,eAAe,GAAkB;AAC9C,EAAA,IAAIhD,gBAAgB,EAAE,OAAA;AACtBA,EAAAA,gBAAgB,GAAG,IAAI,CAAA;EACvB,IAAI;IACF,IAAIxC,SAAS,CAACoC,YAAY,EAAE;AAC1B,MAAA,MAAMiF,KAAK,GAAG,MAAMrH,SAAS,CAACoC,YAAY,CAACkF,OAAO,CAChD9H,aAAa,CAACG,QAAQ,CACvB,CAAA;AACD,MAAA,IAAI0H,KAAK,EAAE;AACT,QAAA,MAAME,MAA8B,GAAGpG,IAAI,CAACqG,KAAK,CAACH,KAAK,CAAC,CAAA;QACxD,IAAIE,MAAM,IAAIvC,KAAK,CAACyC,OAAO,CAACF,MAAM,CAAC,EAAE;UACnCA,MAAM,CAAC/C,OAAO,CAAC,KAAiB,IAAA;AAAA,YAAA,IAAhB,CAACP,GAAG,EAAEJ,IAAI,CAAC,GAAA,KAAA,CAAA;AACzBpB,YAAAA,KAAK,CAAC6B,GAAG,CAACL,GAAG,EAAE;AACb,cAAA,GAAGJ,IAAI;AACP6B,cAAAA,OAAO,EAAE,IAAIL,IAAI,CAACxB,IAAI,CAAC6B,OAAO,CAAA;AAChC,aAAC,CAAC,CAAA;AACJ,WAAC,CAAC,CAAA;AACJ,SAAA;AACAgC,QAAAA,YAAY,EAAE,CAAA;AAChB,OAAA;AACF,KAAA;GACD,CAAC,OAAOrF,CAAC,EAAE;AACV;AAAA,GAAA;AAEF,EAAA,IAAI,CAAC7C,aAAa,CAACM,kBAAkB,EAAE;AACrC,IAAA,MAAM6H,SAAS,GAAGlH,OAAO,CAACa,iBAAiB,EAAE,CAAA;AAC7C,IAAA,IAAIqG,SAAS,EAAE;MACblH,OAAO,CAAC0B,gBAAgB,GAAGwF,SAAS,CAAA;AACtC,KAAA;AACF,GAAA;AACF,CAAA;;AAEA;AACA,SAASD,YAAY,GAAG;AACtB,EAAA,MAAME,qBAAqB,GAAG5C,KAAK,CAACC,IAAI,CAACxC,KAAK,CAACyC,OAAO,EAAE,CAAC,CACtD2C,GAAG,CAAC,KAAA,IAAA;AAAA,IAAA,IAAC,CAAC5D,GAAG,EAAEoD,KAAK,CAAC,GAAA,KAAA,CAAA;IAAA,OAAM;MACtBpD,GAAG;AACHyB,MAAAA,OAAO,EAAE2B,KAAK,CAAC3B,OAAO,CAACH,OAAO,EAAA;KAC/B,CAAA;AAAA,GAAC,CAAC,CACFuC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrC,OAAO,GAAGsC,CAAC,CAACtC,OAAO,CAAC,CAAA;EAExC,MAAMuC,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CACnCD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE3F,KAAK,CAAC4F,IAAI,GAAG7I,aAAa,CAACK,UAAU,CAAC,EAClD4C,KAAK,CAAC4F,IAAI,CACX,CAAA;EAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,oBAAoB,EAAEK,CAAC,EAAE,EAAE;IAC7C7F,KAAK,CAACiC,MAAM,CAACkD,qBAAqB,CAACU,CAAC,CAAC,CAACrE,GAAG,CAAC,CAAA;AAC5C,GAAA;AACF,CAAA;;AAEA;AACA,SAASsE,gBAAgB,CACvBtE,GAAW,EACXtE,QAAgB,EAChBkE,IAAwB,EAClB;AACN;AACA,EAAA,MAAM2E,OAAO,GAAG3E,IAAI,CAAC4E,WAAW,IAAI,EAAE,CAAA;AACtC,EAAA,MAAM/C,OAAO,GAAG,IAAIL,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG5F,aAAa,CAACC,QAAQ,CAAC,CAAA;AAC7D,EAAA,MAAMgG,QAAQ,GAAGhD,KAAK,CAAC2B,GAAG,CAACzE,QAAQ,CAAC,CAAA;EACpC,IAAI8F,QAAQ,IAAI+C,OAAO,IAAI/C,QAAQ,CAAC+C,OAAO,KAAKA,OAAO,EAAE;IACvD/C,QAAQ,CAACC,OAAO,GAAGA,OAAO,CAAA;AAC1BpC,IAAAA,qBAAqB,EAAE,CAAA;AACvB,IAAA,OAAA;AACF,GAAA;;AAEA;AACAb,EAAAA,KAAK,CAAC6B,GAAG,CAAC3E,QAAQ,EAAE;IAClBkE,IAAI;IACJ2E,OAAO;IACP9C,OAAO;AACPC,IAAAA,GAAG,EAAE/C,WAAW,CAAC8F,GAAG,CAACzE,GAAG,CAAA;AAC1B,GAAC,CAAC,CAAA;AACFyD,EAAAA,YAAY,EAAE,CAAA;AACd;AACApE,EAAAA,qBAAqB,EAAE,CAAA;;AAEvB;AACA,EAAA,MAAMqF,SAAS,GAAGrG,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC,CAAA;AAC9C0E,EAAAA,SAAS,IAAIA,SAAS,CAACnE,OAAO,CAAEhB,QAAQ,IAAKO,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC,CAAA;AAC/E,CAAA;AAEA,eAAeE,eAAe,CAC5BP,QAAoB,EACpBK,IAAwB,EACT;AACfA,EAAAA,IAAI,GAAG,MAAML,QAAQ,CAACoF,cAAc,CAAC/E,IAAI,EAAEzD,SAAS,EAAEJ,SAAS,CAACK,YAAY,CAAC,CAAA;AAE7E,EAAA,MAAMmD,QAAQ,CAACqF,oBAAoB,CAAChF,IAAI,CAAC,CAAA;EACzCL,QAAQ,CAACsF,cAAc,CAACjF,IAAI,CAACkF,WAAW,IAAIvF,QAAQ,CAACwF,cAAc,EAAE,CAAC,CAAA;EACtExF,QAAQ,CAACyF,WAAW,CAACpF,IAAI,CAACqF,QAAQ,IAAI1F,QAAQ,CAAC2F,WAAW,EAAE,CAAC,CAAA;AAC/D,CAAA;AAEA,eAAevD,aAAa,CAC1BpC,QAAoB,EACS;EAC7B,MAAM;IAAEuC,OAAO;AAAEqD,IAAAA,iBAAAA;AAAkB,GAAC,GAAG5F,QAAQ,CAAC6F,WAAW,EAAE,CAAA;AAC7D,EAAA,MAAMzI,SAAS,GAAG4C,QAAQ,CAAC8F,YAAY,EAAE,CAAA;AACzC,EAAA,MAAMC,UAAU,GAAG/F,QAAQ,CAAC0C,YAAY,EAAE,CAAA;AAC1C,EAAA,MAAMjC,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;AAC5B,EAAA,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC,CAAA;AAEtC,EAAA,IAAIqD,OAAO,GAAGnE,aAAa,CAAC0B,GAAG,CAACzE,QAAQ,CAAC,CAAA;EACzC,IAAI,CAACkH,OAAO,EAAE;AACZ,IAAA,MAAM2C,OAA0B,GAAGD,UAAU,GACzC9I,OAAO,CAACK,mBAAmB,CAAC;AAC1BH,MAAAA,IAAI,EAAEoF,OAAO;MACbnF,SAAS;AACTG,MAAAA,OAAO,EAAE;AACPoF,QAAAA,UAAU,EAAE3C,QAAQ,CAAC4C,aAAa,EAAE;AACpCqD,QAAAA,gBAAgB,EAAEjG,QAAQ,CAACkD,mBAAmB,EAAE;AAChDgD,QAAAA,cAAc,EAAE1E,KAAK,CAACC,IAAI,CAACzB,QAAQ,CAACmG,iBAAiB,EAAE,CAACzE,OAAO,EAAE,CAAC;QAClEyB,GAAG,EAAEnD,QAAQ,CAACoD,MAAM,EAAA;OACrB;AACD/F,MAAAA,OAAO,EAAEuI,iBAAAA;AACX,KAAC,CAAC,GACF3I,OAAO,CAACC,iBAAiB,CAAC;AACxBC,MAAAA,IAAI,EAAEoF,OAAO;MACbnF,SAAS;AACTC,MAAAA,OAAO,EAAEuI,iBAAAA;AACX,KAAC,CAAC,CAAA;;AAEN;AACAvC,IAAAA,OAAO,GAAG2C,OAAO,CACdrC,IAAI,CAAEyC,GAAG,IAAK;MACb,IAAIA,GAAG,CAAC/I,OAAO,CAACuD,GAAG,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE;AAClDxB,QAAAA,WAAW,CAACyB,GAAG,CAACJ,GAAG,CAAC,CAAA;AACtB,OAAA;MACA,OAAO2F,GAAG,CAACC,IAAI,EAAE,CAAA;AACnB,KAAC,CAAC,CACD1C,IAAI,CAAEtD,IAAwB,IAAK;AAClC0E,MAAAA,gBAAgB,CAACtE,GAAG,EAAEtE,QAAQ,EAAEkE,IAAI,CAAC,CAAA;MACrCgC,gBAAgB,CAACrC,QAAQ,CAAC,CAAA;AAC1Bd,MAAAA,aAAa,CAACgC,MAAM,CAAC/E,QAAQ,CAAC,CAAA;AAC9B,MAAA,OAAOkE,IAAI,CAAA;AACb,KAAC,CAAC,CACDuD,KAAK,CAAE/E,CAAC,IAAK;AAOZK,MAAAA,aAAa,CAACgC,MAAM,CAAC/E,QAAQ,CAAC,CAAA;AAC9B,MAAA,OAAOmH,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,CAAA;AAC5B,KAAC,CAAC,CAAA;AACJrE,IAAAA,aAAa,CAAC4B,GAAG,CAAC3E,QAAQ,EAAEkH,OAAO,CAAC,CAAA;AACtC,GAAA;AACA,EAAA,OAAO,MAAMA,OAAO,CAAA;AACtB,CAAA;;AAEA;AACA;AACA,SAAShB,gBAAgB,CAACrC,QAAoB,EAAQ;AACpD,EAAA,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;AAC5B,EAAA,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC,CAAA;EACtC,MAAM;IAAEsG,aAAa;AAAEC,IAAAA,2BAAAA;AAA4B,GAAC,GAAGvG,QAAQ,CAAC6F,WAAW,EAAE,CAAA;AAC7E,EAAA,MAAMzI,SAAS,GAAG4C,QAAQ,CAAC8F,YAAY,EAAE,CAAA;AACzC,EAAA,IACE9J,aAAa,CAACI,cAAc,IAC5BgD,WAAW,CAAC8F,GAAG,CAACzE,GAAG,CAAC,IACpBjE,SAAS,CAACQ,WAAW,EACrB;AACA,IAAA,IAAImC,OAAO,CAAC+F,GAAG,CAACzE,GAAG,CAAC,EAAE,OAAA;AACtB,IAAA,MAAMU,OAAsB,GAAG;AAC7BqF,MAAAA,GAAG,EAAE,IAAI;AACTrJ,MAAAA,IAAI,EAAEmJ,aAAa;MACnBlJ,SAAS;AACTC,MAAAA,OAAO,EAAEkJ,2BAA2B;MACpCE,EAAE,EAAGC,KAA2B,IAAK;QACnC,IAAI;AACF,UAAA,IAAIA,KAAK,CAACC,IAAI,KAAK,kBAAkB,EAAE;AACrC,YAAA,MAAMxB,SAAS,GAAGrG,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC,CAAA;AAC9C0E,YAAAA,SAAS,IACPA,SAAS,CAACnE,OAAO,CAAEhB,QAAQ,IAAK;cAC9BoC,aAAa,CAACpC,QAAQ,CAAC,CAAA;AACzB,aAAC,CAAC,CAAA;AACN,WAAC,MAAM,IAAI0G,KAAK,CAACC,IAAI,KAAK,UAAU,EAAE;YACpC,MAAMN,IAAwB,GAAG1I,IAAI,CAACqG,KAAK,CAAC0C,KAAK,CAACrG,IAAI,CAAC,CAAA;AACvD0E,YAAAA,gBAAgB,CAACtE,GAAG,EAAEtE,QAAQ,EAAEkK,IAAI,CAAC,CAAA;AACvC,WAAA;AACA;UACAlF,OAAO,CAACyF,MAAM,GAAG,CAAC,CAAA;SACnB,CAAC,OAAO/H,CAAC,EAAE;UAOVgI,UAAU,CAAC1F,OAAO,CAAC,CAAA;AACrB,SAAA;OACD;AACDyF,MAAAA,MAAM,EAAE,CAAC;AACTxF,MAAAA,KAAK,EAAE,QAAA;KACR,CAAA;AACDjC,IAAAA,OAAO,CAAC2B,GAAG,CAACL,GAAG,EAAEU,OAAO,CAAC,CAAA;IACzBG,aAAa,CAACH,OAAO,CAAC,CAAA;AACxB,GAAA;AACF,CAAA;AAEA,SAAS0F,UAAU,CAAC1F,OAAsB,EAAE;AAC1C,EAAA,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE,OAAA;EAC9BD,OAAO,CAACyF,MAAM,EAAE,CAAA;AAChB,EAAA,IAAIzF,OAAO,CAACyF,MAAM,GAAG,CAAC,IAAKzF,OAAO,CAACqF,GAAG,IAAIrF,OAAO,CAACqF,GAAG,CAACM,UAAU,KAAK,CAAE,EAAE;AACvE;IACA,MAAMC,KAAK,GACTrC,IAAI,CAACsC,GAAG,CAAC,CAAC,EAAE7F,OAAO,CAACyF,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,GAAGlC,IAAI,CAACuC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAA;IACjE5F,cAAc,CAACF,OAAO,CAAC,CAAA;AACvB5C,IAAAA,UAAU,CAAC,MAAM;AACf,MAAA,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC2I,QAAQ,CAAC/F,OAAO,CAACC,KAAK,CAAC,EAAE,OAAA;MAChDE,aAAa,CAACH,OAAO,CAAC,CAAA;KACvB,EAAEuD,IAAI,CAACC,GAAG,CAACoC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;AAC9B,GAAA;AACF,CAAA;;AAEA,SAAS1F,cAAc,CAACF,OAAsB,EAAE;AAC9C,EAAA,IAAI,CAACA,OAAO,CAACqF,GAAG,EAAE,OAAA;AAClBrF,EAAAA,OAAO,CAACqF,GAAG,CAACW,MAAM,GAAG,IAAI,CAAA;AACzBhG,EAAAA,OAAO,CAACqF,GAAG,CAACY,OAAO,GAAG,IAAI,CAAA;AAC1BjG,EAAAA,OAAO,CAACqF,GAAG,CAACa,KAAK,EAAE,CAAA;EACnBlG,OAAO,CAACqF,GAAG,GAAG,IAAI,CAAA;AAClB,EAAA,IAAIrF,OAAO,CAACC,KAAK,KAAK,QAAQ,EAAE;IAC9BD,OAAO,CAACC,KAAK,GAAG,UAAU,CAAA;AAC5B,GAAA;AACF,CAAA;AAEA,SAASE,aAAa,CAACH,OAAsB,EAAE;AAC7CA,EAAAA,OAAO,CAACqF,GAAG,GAAGvJ,OAAO,CAACY,eAAe,CAAC;IACpCV,IAAI,EAAEgE,OAAO,CAAChE,IAAI;IAClBC,SAAS,EAAE+D,OAAO,CAAC/D,SAAS;IAC5BC,OAAO,EAAE8D,OAAO,CAAC9D,OAAAA;AACnB,GAAC,CAAgB,CAAA;EACjB8D,OAAO,CAACC,KAAK,GAAG,QAAQ,CAAA;EACxBD,OAAO,CAACqF,GAAG,CAAC/H,gBAAgB,CAAC,UAAU,EAAE0C,OAAO,CAACsF,EAAE,CAAC,CAAA;EACpDtF,OAAO,CAACqF,GAAG,CAAC/H,gBAAgB,CAAC,kBAAkB,EAAE0C,OAAO,CAACsF,EAAE,CAAC,CAAA;EAC5DtF,OAAO,CAACqF,GAAG,CAACY,OAAO,GAAG,MAAMP,UAAU,CAAC1F,OAAO,CAAC,CAAA;AAC/CA,EAAAA,OAAO,CAACqF,GAAG,CAACW,MAAM,GAAG,MAAM;IACzBhG,OAAO,CAACyF,MAAM,GAAG,CAAC,CAAA;GACnB,CAAA;AACH,CAAA;AAEA,SAASU,cAAc,CAACnG,OAAsB,EAAEV,GAAW,EAAE;EAC3DY,cAAc,CAACF,OAAO,CAAC,CAAA;AACvBhC,EAAAA,OAAO,CAAC+B,MAAM,CAACT,GAAG,CAAC,CAAA;AACrB,CAAA;AAEA,SAASd,gBAAgB,GAAG;AAC1B;EACAP,WAAW,CAACS,KAAK,EAAE,CAAA;;AAEnB;AACAV,EAAAA,OAAO,CAAC6B,OAAO,CAACsG,cAAc,CAAC,CAAA;;AAE/B;EACAxI,mBAAmB,CAACe,KAAK,EAAE,CAAA;;AAE3B;EACA5C,OAAO,CAAC0B,gBAAgB,EAAE,CAAA;AAC5B;;AC9hBa4I,IAAAA,kBAAkB,GAAG,8BAAA,CAAA;AAClC,IAAMC,cAAc,GAAuB;AACzCC,EAAAA,MAAM,EAAE,SAAA,MAAA,GAAA,EAAA;AADiC,CAA3C,CAAA;AAIA,IAAMC,QAAQ,gBAAgC,IAAI3I,GAAJ,EAA9C,CAAA;AACA,IAAM4I,SAAS,gBAAkB,IAAItI,GAAJ,EAAjC,CAAA;AAEA,SAASuI,eAAT,CAAyBC,IAAzB,EAAA;EACE,OAAOA,IAAI,KAAK,MAAT,GACH;AACEC,IAAAA,SAAS,EAAE,IADb;AAEEC,IAAAA,OAAO,EAAE,IAFX;AAGEpF,IAAAA,UAAU,EAAE,IAHd;AAIEqF,IAAAA,aAAa,EAAE,IAAA;AAJjB,GADG,GAOH;AACEF,IAAAA,SAAS,EAAE,KADb;AAEEC,IAAAA,OAAO,EAAE,KAFX;AAGEpF,IAAAA,UAAU,EAAE,IAHd;IAIEsF,eAAe,EAAE,CAACJ,IAAD,CAAA;GAXvB,CAAA;AAaD,CAAA;AAED,SAASK,gBAAT,CAA0BC,OAA1B,EAAA;AACE,EAAA,IAAIC,MAAM,GAAGV,QAAQ,CAAC9G,GAAT,CAAauH,OAAb,CAAb,CAAA;EAEA,IAAI,CAACC,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAG;AAAED,MAAAA,OAAO,EAAPA,OAAF;AAAWxF,MAAAA,UAAU,EAAE,EAAA;KAAhC,CAAA;AACA+E,IAAAA,QAAQ,CAAC5G,GAAT,CAAaqH,OAAb,EAAsBC,MAAtB,CAAA,CAAA;AACD,GAAA;AAED,EAAA,OAAOA,MAAP,CAAA;AACD,CAAA;AAED,SAASC,2BAAT,CACEC,EADF,EAEET,IAFF,EAGEU,eAHF,EAIEC,QAJF,EAKEC,cALF,EAAA;AAOE,EAAA,IAAMC,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC,CAAA;AACA,EAAA,IAAMF,MAAM,GAAoC;AAC9CO,IAAAA,OAAO,EAAE,KADqC;AAE9CC,IAAAA,aAAa,EAAEF,YAF+B;AAG9CG,IAAAA,YAAY,EAAEH,YAHgC;AAI9Cf,IAAAA,SAAS,EAAE,EAJmC;AAK9CW,IAAAA,EAAE,EAAFA,EAL8C;AAM9CQ,IAAAA,gBAAgB,EAAE,IAN4B;IAO9CC,QAAQ,EAAE,IAAIC,gBAAJ,CAAqB,YAAA;AAC7B;AACA;AACA;AACA;MACA,IAAInB,IAAI,KAAK,UAAT,IAAuBO,MAAM,CAACU,gBAAlC,EAAoD,OAApD,KACK,IAAIjB,IAAI,KAAK,UAAb,EACHO,MAAM,CAACU,gBAAP,GAA0BvK,UAAU,CAAC,YAAA;QACnC6J,MAAM,CAACU,gBAAP,GAA0B,IAA1B,CAAA;OADkC,EAEjC,IAFiC,CAApC,CAAA;AAIF,MAAA,IAAMJ,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC,CAAA;MACA,IACET,IAAI,KAAK,UAAT,IACAa,YAAY,CAACO,UAAb,KAA4Bb,MAAM,CAACS,YAAP,CAAoBI,UADhD,IAEAP,YAAY,CAACQ,gBAAb,KAAkCd,MAAM,CAACS,YAAP,CAAoBK,gBAHxD,EAKE,OAAA;AACF,MAAA,IAAIR,YAAY,KAAKN,MAAM,CAACS,YAA5B,EAA0C,OAAA;MAC1CT,MAAM,CAACQ,aAAP,GAAuBF,YAAvB,CAAA;MACAD,cAAc,CAACL,MAAD,CAAd,CAAA;AACD,KArBS,CAPoC;AA6B9CK,IAAAA,cAAc,EAAdA,cA7B8C;AA8B9CD,IAAAA,QAAQ,EAARA,QA9B8C;AA+B9CD,IAAAA,eAAe,EAAfA,eAAAA;GA/BF,CAAA;AAiCA,EAAA,IAAIV,IAAI,KAAK,UAAT,IAAuBS,EAAE,CAACW,UAA9B,EAA0C;IACxCb,MAAM,CAACW,QAAP,CAAgBI,OAAhB,CAAwBb,EAAE,CAACW,UAA3B,EAAuC;AACrCnB,MAAAA,SAAS,EAAE,IAD0B;AAErCC,MAAAA,OAAO,EAAE,IAF4B;AAGrCpF,MAAAA,UAAU,EAAE,KAHyB;AAIrCqF,MAAAA,aAAa,EAAE,KAAA;KAJjB,CAAA,CAAA;AAMD,GAPD,MAOO;IACLI,MAAM,CAACW,QAAP,CAAgBI,OAAhB,CAAwBb,EAAxB,EAA4BV,eAAe,CAACC,IAAD,CAA3C,CAAA,CAAA;AACD,GAAA;AACD,EAAA,OAAOO,MAAP,CAAA;AACD,CAAA;AAED,SAASgB,aAAT,CACEC,GADF,EAEEjB,MAFF,EAAA;EAIE,IAAMkB,UAAU,GAAGlB,MAAM,CAACG,eAAP,CAAuBH,MAAM,CAACE,EAA9B,CAAnB,CAAA;EACAF,MAAM,CAACS,YAAP,GAAsBQ,GAAtB,CAAA;AACA,EAAA,IAAIA,GAAG,IAAI,OAAOA,GAAP,KAAe,QAA1B,EAAoC;AAClC,IAAA,IACE,CAACC,UAAD,IACAD,GAAG,CAACJ,UAAJ,KAAmBK,UAAU,CAACL,UAD9B,IAEAI,GAAG,CAACH,gBAAJ,KAAyBI,UAAU,CAACJ,gBAHtC,EAIE;MACAd,MAAM,CAACO,OAAP,GAAiB,IAAjB,CAAA;MACAY,aAAa,EAAA,CAAA;AACd,KAAA;AACF,GATD,MASO,IAAIF,GAAG,KAAKC,UAAZ,EAAwB;IAC7BlB,MAAM,CAACO,OAAP,GAAiB,IAAjB,CAAA;IACAY,aAAa,EAAA,CAAA;AACd,GAAA;AACF,CAAA;AAED,SAASC,kBAAT,CAA4BpB,MAA5B,EAAA;AACE,EAAA,IAAIiB,GAAG,GAAGjB,MAAM,CAACQ,aAAjB,CAAA;AACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;AAAA,IAAA,OAAKqI,GAAG,GAAGI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAX,CAAA;GAA1B,CAAA,CAAA;AACAD,EAAAA,aAAa,CAACO,kBAAkB,CAACN,GAAD,CAAnB,EAA0BjB,MAA1B,CAAb,CAAA;AACD,CAAA;AACD,SAASwB,mBAAT,CAA6BxB,MAA7B,EAAA;AACE,EAAA,IAAMiB,GAAG,GAAG,IAAIhK,GAAJ,CAAQ+I,MAAM,CAACQ,aAAP,CAAqBiB,KAArB,CAA2B,KAA3B,CAAA,CAAkCC,MAAlC,CAAyCC,OAAzC,CAAR,CAAZ,CAAA;AACA3B,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;AAAA,IAAA,OAAIyI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAJ,CAAA;GAA1B,CAAA,CAAA;AACAD,EAAAA,aAAa,CACX5H,KAAK,CAACC,IAAN,CAAW4H,GAAX,CACGS,CAAAA,MADH,CACUC,OADV,EAEGC,IAFH,CAEQ,GAFR,CADW,EAIX5B,MAJW,CAAb,CAAA;AAMD,CAAA;AAED,SAAS6B,kBAAT,CAA4B7B,MAA5B,EAAA;AACE,EAAA,IAAIiB,GAAG,GAAkBjB,MAAM,CAACQ,aAAhC,CAAA;AACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;AAAA,IAAA,OAAKqI,GAAG,GAAGI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAX,CAAA;GAA1B,CAAA,CAAA;AACAD,EAAAA,aAAa,CAACC,GAAD,EAAMjB,MAAN,CAAb,CAAA;AACD,CAAA;AAED,SAAS8B,aAAT,CAAA,IAAA,EAAA;AACEC,EAAAA,IAAAA,cAAAA,GAAAA,IAAAA,CAAAA,cAAAA;AACAC,IAAAA,oBAAAA,GAAAA,IAAAA,CAAAA,oBAAAA,CAAAA;AAEA,EAAA,IAAMnB,UAAU,GAAG/K,QAAQ,CAACmM,aAAT,CAAoCF,cAApC,CAAnB,CAAA;AACA,EAAA,IAAI,CAAClB,UAAL,EAAiB,OAAO,IAAP,CAAA;EACjB,IAAMC,gBAAgB,GAAGkB,oBAAoB,GACzClM,QAAQ,CAACmM,aAAT,CAAoCD,oBAApC,CADyC,GAEzC,IAFJ,CAAA;AAGA,EAAA,IAAIA,oBAAoB,IAAI,CAAClB,gBAA7B,EAA+C,OAAO,IAAP,CAAA;EAC/C,OAAO;AACLD,IAAAA,UAAU,EAAVA,UADK;AAELC,IAAAA,gBAAgB,EAAhBA,gBAAAA;GAFF,CAAA;AAID,CAAA;AAED,SAASoB,sBAAT,CAAgClC,MAAhC,EAAA;AACE,EAAA,IAAIiB,GAAG,GAAGjB,MAAM,CAACQ,aAAjB,CAAA;AACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;AACxB,IAAA,IAAMuJ,SAAS,GAAGd,CAAC,CAACC,MAAF,EAAlB,CAAA;AACA,IAAA,IAAMc,QAAQ,GAAGN,aAAa,CAACK,SAAD,CAA9B,CAAA;IACAlB,GAAG,GAAGmB,QAAQ,IAAInB,GAAlB,CAAA;GAHF,CAAA,CAAA;AAKAD,EAAAA,aAAa,CAACC,GAAD,EAAMjB,MAAN,CAAb,CAAA;AACD,CAAA;AAED,IAAMqC,YAAY,GAAG,SAAfA,YAAe,CAACnC,EAAD,EAAA;EAAA,OAAiBA,EAAE,CAACoC,SAApB,CAAA;AAAA,CAArB,CAAA;AACA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACrC,EAAD,EAAczE,KAAd,EAAA;AAAA,EAAA,OAAiCyE,EAAE,CAACoC,SAAH,GAAe7G,KAAhD,CAAA;AAAA,CAArB,CAAA;AACA,SAAS+G,oBAAT,CAA8BzC,OAA9B,EAAA;AACE,EAAA,IAAM0C,aAAa,GAAG3C,gBAAgB,CAACC,OAAD,CAAtC,CAAA;AACA,EAAA,IAAI,CAAC0C,aAAa,CAACC,IAAnB,EAAyB;AACvBD,IAAAA,aAAa,CAACC,IAAd,GAAqBzC,2BAA2B,CAC9CF,OAD8C,EAE9C,MAF8C,EAG9CsC,YAH8C,EAI9CE,YAJ8C,EAK9CnB,kBAL8C,CAAhD,CAAA;AAOD,GAAA;EACD,OAAOqB,aAAa,CAACC,IAArB,CAAA;AACD,CAAA;AAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAACzC,EAAD,EAAA;EACzB,OAAO;IACLW,UAAU,EAAEX,EAAE,CAAC0C,aADV;IAEL9B,gBAAgB,EAAEZ,EAAE,CAAC2C,kBAAAA;GAFvB,CAAA;AAID,CALD,CAAA;AAMA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC5C,EAAD,EAAczE,KAAd,EAAA;AACzB,EAAA,IACEA,KAAK,CAACqF,gBAAN,IACA,CAACrF,KAAK,CAACoF,UAAN,CAAiBkC,QAAjB,CAA0BtH,KAAK,CAACqF,gBAAhC,CAFH,EAGE;AACA;AACA;AACA,IAAA,OAAA;AACD,GAAA;EACDrF,KAAK,CAACoF,UAAN,CAAiBmC,YAAjB,CAA8B9C,EAA9B,EAAkCzE,KAAK,CAACqF,gBAAxC,CAAA,CAAA;AACD,CAVD,CAAA;AAWA,SAASmC,wBAAT,CAAkClD,OAAlC,EAAA;AACE,EAAA,IAAM0C,aAAa,GAAG3C,gBAAgB,CAACC,OAAD,CAAtC,CAAA;AACA,EAAA,IAAI,CAAC0C,aAAa,CAACS,QAAnB,EAA6B;AAC3BT,IAAAA,aAAa,CAACS,QAAd,GAAyBjD,2BAA2B,CAClDF,OADkD,EAElD,UAFkD,EAGlD4C,kBAHkD,EAIlDG,kBAJkD,EAKlDZ,sBALkD,CAApD,CAAA;AAOD,GAAA;EACD,OAAOO,aAAa,CAACS,QAArB,CAAA;AACD,CAAA;AAED,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACjD,EAAD,EAAce,GAAd,EAAA;AAAA,EAAA,OACpBA,GAAG,GAAIf,EAAE,CAACkD,SAAH,GAAenC,GAAnB,GAA0Bf,EAAE,CAACmD,eAAH,CAAmB,OAAnB,CADT,CAAA;AAAA,CAAtB,CAAA;AAEA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACpD,EAAD,EAAA;EAAA,OAAiBA,EAAE,CAACkD,SAApB,CAAA;AAAA,CAAtB,CAAA;AACA,SAASG,qBAAT,CAA+BrD,EAA/B,EAAA;AACE,EAAA,IAAMuC,aAAa,GAAG3C,gBAAgB,CAACI,EAAD,CAAtC,CAAA;AACA,EAAA,IAAI,CAACuC,aAAa,CAACe,OAAnB,EAA4B;AAC1Bf,IAAAA,aAAa,CAACe,OAAd,GAAwBvD,2BAA2B,CACjDC,EADiD,EAEjD,OAFiD,EAGjDoD,aAHiD,EAIjDH,aAJiD,EAKjD3B,mBALiD,CAAnD,CAAA;AAOD,GAAA;EACD,OAAOiB,aAAa,CAACe,OAArB,CAAA;AACD,CAAA;AAED,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACC,QAAD,EAAA;AAAA,EAAA,OAAsB,UAACxD,EAAD,EAAA;AAAA,IAAA,IAAA,gBAAA,CAAA;AAAA,IAAA,OAAA,CAAA,gBAAA,GACzCA,EAAE,CAACyD,YAAH,CAAgBD,QAAhB,CADyC,+BACZ,IADY,CAAA;GAAtB,CAAA;AAAA,CAArB,CAAA;AAEA,IAAME,YAAY,GAAG,SAAfA,YAAe,CAACF,QAAD,EAAA;EAAA,OAAsB,UAACxD,EAAD,EAAce,GAAd,EAAA;AAAA,IAAA,OACzCA,GAAG,KAAK,IAAR,GAAef,EAAE,CAAC2D,YAAH,CAAgBH,QAAhB,EAA0BzC,GAA1B,CAAf,GAAgDf,EAAE,CAACmD,eAAH,CAAmBK,QAAnB,CADP,CAAA;GAAtB,CAAA;AAAA,CAArB,CAAA;AAEA,SAASI,yBAAT,CAAmC5D,EAAnC,EAAgDT,IAAhD,EAAA;AACE,EAAA,IAAMgD,aAAa,GAAG3C,gBAAgB,CAACI,EAAD,CAAtC,CAAA;AACA,EAAA,IAAI,CAACuC,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,CAAL,EAAqC;IACnCgD,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,IAAiCQ,2BAA2B,CAC1DC,EAD0D,EAE1DT,IAF0D,EAG1DgE,YAAY,CAAChE,IAAD,CAH8C,EAI1DmE,YAAY,CAACnE,IAAD,CAJ8C,EAK1DoC,kBAL0D,CAA5D,CAAA;AAOD,GAAA;AACD,EAAA,OAAOY,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,CAAP,CAAA;AACD,CAAA;AAED,SAASsE,2BAAT,CAAqC7D,EAArC,EAAkDT,IAAlD,EAAA;AACE,EAAA,IAAMM,OAAO,GAAGT,QAAQ,CAAC9G,GAAT,CAAa0H,EAAb,CAAhB,CAAA;EACA,IAAI,CAACH,OAAL,EAAc,OAAA;EACd,IAAIN,IAAI,KAAK,MAAb,EAAqB;AAAA,IAAA,IAAA,aAAA,EAAA,qBAAA,CAAA;AACnB,IAAA,CAAA,aAAA,GAAA,OAAO,CAACiD,IAAR,KAAc/B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,aAAAA,CAAAA,QAAd,2CAAwBqD,UAAxB,EAAA,CAAA;IACA,OAAOjE,OAAO,CAAC2C,IAAf,CAAA;AACD,GAHD,MAGO,IAAIjD,IAAI,KAAK,OAAb,EAAsB;AAAA,IAAA,IAAA,gBAAA,EAAA,qBAAA,CAAA;AAC3B,IAAA,CAAA,gBAAA,GAAA,OAAO,CAAC+D,OAAR,KAAiB7C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,gBAAAA,CAAAA,QAAjB,2CAA2BqD,UAA3B,EAAA,CAAA;IACA,OAAOjE,OAAO,CAACyD,OAAf,CAAA;AACD,GAHM,MAGA,IAAI/D,IAAI,KAAK,UAAb,EAAyB;AAAA,IAAA,IAAA,iBAAA,EAAA,qBAAA,CAAA;AAC9B,IAAA,CAAA,iBAAA,GAAA,OAAO,CAACyD,QAAR,KAAkBvC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,iBAAAA,CAAAA,QAAlB,2CAA4BqD,UAA5B,EAAA,CAAA;IACA,OAAOjE,OAAO,CAACmD,QAAf,CAAA;AACD,GAHM,MAGA;AAAA,IAAA,IAAA,mBAAA,EAAA,qBAAA,EAAA,sBAAA,CAAA;AACL,IAAA,CAAA,mBAAA,GAAA,OAAO,CAAC3I,UAAR,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,GAAA,mBAAA,CAAqBkF,IAArB,CAA4BkB,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,sBAAAA,GAAAA,qBAAAA,CAAAA,QAA5B,4CAAsCqD,UAAtC,EAAA,CAAA;AACA,IAAA,OAAOjE,OAAO,CAACxF,UAAR,CAAmBkF,IAAnB,CAAP,CAAA;AACD,GAAA;AACF,CAAA;AAED,IAAIwE,kBAAJ,CAAA;AACA,SAAS1C,kBAAT,CAA4BmB,IAA5B,EAAA;EACE,IAAI,CAACuB,kBAAL,EAAyB;AACvBA,IAAAA,kBAAkB,GAAGnO,QAAQ,CAACoO,aAAT,CAAuB,KAAvB,CAArB,CAAA;AACD,GAAA;EACDD,kBAAkB,CAAC3B,SAAnB,GAA+BI,IAA/B,CAAA;EACA,OAAOuB,kBAAkB,CAAC3B,SAA1B,CAAA;AACD,CAAA;AAED,SAAS6B,gBAAT,CACEjE,EADF,EAEET,IAFF,EAGE4B,CAHF,EAAA;AAKE,EAAA,IAAI,CAACA,CAAC,CAACd,OAAP,EAAgB,OAAA;EAChBc,CAAC,CAACd,OAAF,GAAY,KAAZ,CAAA;AACA,EAAA,IAAMU,GAAG,GAAGI,CAAC,CAACZ,YAAd,CAAA;AACA,EAAA,IAAI,CAACY,CAAC,CAAC9B,SAAF,CAAY6E,MAAjB,EAAyB;AACvBL,IAAAA,2BAA2B,CAAC7D,EAAD,EAAKT,IAAL,CAA3B,CAAA;AACD,GAAA;AACD4B,EAAAA,CAAC,CAACjB,QAAF,CAAWF,EAAX,EAAee,GAAf,CAAA,CAAA;AACD,CAAA;AAED,SAASb,QAAT,CAAkBiB,CAAlB,EAAoCnB,EAApC,EAAA;AACEmB,EAAAA,CAAC,CAACqB,IAAF,IAAUyB,gBAAgB,CAAajE,EAAb,EAAiB,MAAjB,EAAyBmB,CAAC,CAACqB,IAA3B,CAA1B,CAAA;AACArB,EAAAA,CAAC,CAACmC,OAAF,IAAaW,gBAAgB,CAAkBjE,EAAlB,EAAsB,OAAtB,EAA+BmB,CAAC,CAACmC,OAAjC,CAA7B,CAAA;AACAnC,EAAAA,CAAC,CAAC6B,QAAF,IAAciB,gBAAgB,CAAiBjE,EAAjB,EAAqB,UAArB,EAAiCmB,CAAC,CAAC6B,QAAnC,CAA9B,CAAA;EACA9L,MAAM,CAACuD,IAAP,CAAY0G,CAAC,CAAC9G,UAAd,CAAA,CAA0B3B,OAA1B,CAAkC,UAAI,IAAA,EAAA;IACpCuL,gBAAgB,CAAkBjE,EAAlB,EAAsBT,IAAtB,EAA4B4B,CAAC,CAAC9G,UAAF,CAAakF,IAAb,CAA5B,CAAhB,CAAA;GADF,CAAA,CAAA;AAGD,CAAA;AAED,SAAS0B,aAAT,GAAA;EACE7B,QAAQ,CAAC1G,OAAT,CAAiBwH,QAAjB,CAAA,CAAA;AACD,CAAA;;AAGD,SAASiE,aAAT,CAAuBC,QAAvB,EAA2CvE,OAA3C,EAAA;EACE,IAAIC,MAAM,GAA2C,IAArD,CAAA;AACA,EAAA,IAAIsE,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;AAC5BvE,IAAAA,MAAM,GAAGwC,oBAAoB,CAACzC,OAAD,CAA7B,CAAA;AACD,GAFD,MAEO,IAAIuE,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;AACpCvE,IAAAA,MAAM,GAAGuD,qBAAqB,CAACxD,OAAD,CAA9B,CAAA;AACD,GAFM,MAEA,IAAIuE,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;IACxCvE,MAAM,GAAG8D,yBAAyB,CAAC/D,OAAD,EAAUuE,QAAQ,CAACE,SAAnB,CAAlC,CAAA;AACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;AACvCvE,IAAAA,MAAM,GAAGiD,wBAAwB,CAAClD,OAAD,CAAjC,CAAA;AACD,GAAA;EACD,IAAI,CAACC,MAAL,EAAa,OAAA;AACbA,EAAAA,MAAM,CAACT,SAAP,CAAiBkF,IAAjB,CAAsBH,QAAtB,CAAA,CAAA;EACAtE,MAAM,CAACK,cAAP,CAAsBL,MAAtB,CAAA,CAAA;AACD,CAAA;;AAGD,SAAS0E,YAAT,CAAsBJ,QAAtB,EAA0CpE,EAA1C,EAAA;EACE,IAAIF,MAAM,GAA2C,IAArD,CAAA;AACA,EAAA,IAAIsE,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;AAC5BvE,IAAAA,MAAM,GAAGwC,oBAAoB,CAACtC,EAAD,CAA7B,CAAA;AACD,GAFD,MAEO,IAAIoE,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;AACpCvE,IAAAA,MAAM,GAAGuD,qBAAqB,CAACrD,EAAD,CAA9B,CAAA;AACD,GAFM,MAEA,IAAIoE,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;IACxCvE,MAAM,GAAG8D,yBAAyB,CAAC5D,EAAD,EAAKoE,QAAQ,CAACE,SAAd,CAAlC,CAAA;AACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;AACvCvE,IAAAA,MAAM,GAAGiD,wBAAwB,CAAC/C,EAAD,CAAjC,CAAA;AACD,GAAA;EACD,IAAI,CAACF,MAAL,EAAa,OAAA;EACb,IAAM2E,KAAK,GAAG3E,MAAM,CAACT,SAAP,CAAiBqF,OAAjB,CAAyBN,QAAzB,CAAd,CAAA;AACA,EAAA,IAAIK,KAAK,KAAK,CAAC,CAAf,EAAkB3E,MAAM,CAACT,SAAP,CAAiBsF,MAAjB,CAAwBF,KAAxB,EAA+B,CAA/B,CAAA,CAAA;EAClB3E,MAAM,CAACK,cAAP,CAAsBL,MAAtB,CAAA,CAAA;AACD,CAAA;;AAGD,SAAS8E,kBAAT,CAA4BR,QAA5B,EAAA;AACE;AACA;AACA,EAAA,IAAIA,QAAQ,CAACC,IAAT,KAAkB,UAAlB,IAAgCD,QAAQ,CAAChF,QAAT,CAAkB7C,IAAlB,KAA2B,CAA/D,EAAkE,OAAA;EAElE,IAAMsI,gBAAgB,GAAG,IAAI9N,GAAJ,CAAQqN,QAAQ,CAAChF,QAAjB,CAAzB,CAAA;EACA,IAAM0F,gBAAgB,GAAGlP,QAAQ,CAACmP,gBAAT,CAA0BX,QAAQ,CAACY,QAAnC,CAAzB,CAAA;EAEAF,gBAAgB,CAACpM,OAAjB,CAAyB,UAAE,EAAA,EAAA;AACzB,IAAA,IAAI,CAACmM,gBAAgB,CAACjI,GAAjB,CAAqBoD,EAArB,CAAL,EAA+B;AAC7BoE,MAAAA,QAAQ,CAAChF,QAAT,CAAkB7G,GAAlB,CAAsByH,EAAtB,CAAA,CAAA;AACAmE,MAAAA,aAAa,CAACC,QAAD,EAAWpE,EAAX,CAAb,CAAA;AACD,KAAA;GAJH,CAAA,CAAA;AAMD,CAAA;AAED,SAASiF,cAAT,CAAwBb,QAAxB,EAAA;AACEA,EAAAA,QAAQ,CAAChF,QAAT,CAAkB1G,OAAlB,CAA0B,UAAE,EAAA,EAAA;AAAA,IAAA,OAAI8L,YAAY,CAACJ,QAAD,EAAWpE,EAAX,CAAhB,CAAA;GAA5B,CAAA,CAAA;EACAoE,QAAQ,CAAChF,QAAT,CAAkB7H,KAAlB,EAAA,CAAA;EACA8H,SAAS,CAAA,QAAA,CAAT,CAAiB+E,QAAjB,CAAA,CAAA;AACD,CAAA;AAED,SAASc,qBAAT,GAAA;EACE7F,SAAS,CAAC3G,OAAV,CAAkBkM,kBAAlB,CAAA,CAAA;AACD,CAAA;;AAGD,IAAInE,QAAJ,CAAA;AAIgB0E,SAAAA,qBAAAA,GAAAA;AACd,EAAA,IAAI,OAAOvP,QAAP,KAAoB,WAAxB,EAAqC,OAAA;EAErC,IAAI,CAAC6K,QAAL,EAAe;IACbA,QAAQ,GAAG,IAAIC,gBAAJ,CAAqB,YAAA;MAC9BwE,qBAAqB,EAAA,CAAA;AACtB,KAFU,CAAX,CAAA;AAGD,GAAA;EAEDA,qBAAqB,EAAA,CAAA;AACrBzE,EAAAA,QAAQ,CAACI,OAAT,CAAiBjL,QAAQ,CAACwP,eAA1B,EAA2C;AACzC5F,IAAAA,SAAS,EAAE,IAD8B;AAEzCC,IAAAA,OAAO,EAAE,IAFgC;AAGzCpF,IAAAA,UAAU,EAAE,KAH6B;AAIzCqF,IAAAA,aAAa,EAAE,KAAA;GAJjB,CAAA,CAAA;AAMD,CAAA;;AAGDyF,qBAAqB,EAAA,CAAA;AAErB,SAASE,WAAT,CAAqBlE,CAArB,EAAA;AACE;AACA,EAAA,IAAI,OAAOvL,QAAP,KAAoB,WAAxB,EAAqC,OAAOsJ,cAAP,CAAA;;EAErCG,SAAS,CAAC9G,GAAV,CAAc4I,CAAd,CAAA,CAAA;;EAEAyD,kBAAkB,CAACzD,CAAD,CAAlB,CAAA;EACA,OAAO;AACLhC,IAAAA,MAAM,EAAE,SAAA,MAAA,GAAA;MACN8F,cAAc,CAAC9D,CAAD,CAAd,CAAA;AACD,KAAA;GAHH,CAAA;AAKD,CAAA;AAED,SAASqB,IAAT,CACEwC,QADF,EAEE5D,MAFF,EAAA;AAIE,EAAA,OAAOiE,WAAW,CAAC;AACjBhB,IAAAA,IAAI,EAAE,MADW;IAEjBjF,QAAQ,EAAE,IAAIrI,GAAJ,EAFO;AAGjBqK,IAAAA,MAAM,EAANA,MAHiB;AAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;AAJiB,GAAD,CAAlB,CAAA;AAMD,CAAA;AAED,SAAShC,QAAT,CACEgC,QADF,EAEE5D,MAFF,EAAA;AAIE,EAAA,OAAOiE,WAAW,CAAC;AACjBhB,IAAAA,IAAI,EAAE,UADW;IAEjBjF,QAAQ,EAAE,IAAIrI,GAAJ,EAFO;AAGjBqK,IAAAA,MAAM,EAANA,MAHiB;AAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;AAJiB,GAAD,CAAlB,CAAA;AAMD,CAAA;AAED,SAAS1B,OAAT,CACE0B,QADF,EAEE5D,MAFF,EAAA;AAIE,EAAA,OAAOiE,WAAW,CAAC;AACjBhB,IAAAA,IAAI,EAAE,OADW;IAEjBjF,QAAQ,EAAE,IAAIrI,GAAJ,EAFO;AAGjBqK,IAAAA,MAAM,EAANA,MAHiB;AAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;AAJiB,GAAD,CAAlB,CAAA;AAMD,CAAA;AAED,SAASV,SAAT,CACEU,QADF,EAEEV,SAFF,EAGElD,MAHF,EAAA;EAKE,IAAI,CAACnC,kBAAkB,CAACqG,IAAnB,CAAwBhB,SAAxB,CAAL,EAAyC,OAAOpF,cAAP,CAAA;AAEzC,EAAA,IAAIoF,SAAS,KAAK,OAAd,IAAyBA,SAAS,KAAK,WAA3C,EAAwD;AACtD,IAAA,OAAOhB,OAAO,CAAC0B,QAAD,EAAW,UAAU,UAAA,EAAA;AACjC,MAAA,IAAMO,iBAAiB,GAAGnE,MAAM,CAAClI,KAAK,CAACC,IAAN,CAAWqM,UAAX,CAAuB9D,CAAAA,IAAvB,CAA4B,GAA5B,CAAD,CAAhC,CAAA;AACA8D,MAAAA,UAAU,CAACjO,KAAX,EAAA,CAAA;MACA,IAAI,CAACgO,iBAAL,EAAwB,OAAA;MACxBA,iBAAiB,CACdhE,KADH,CACS,MADT,CAAA,CAEGC,MAFH,CAEUC,OAFV,CAAA,CAGG/I,OAHH,CAGW,UAAC,CAAA,EAAA;AAAA,QAAA,OAAI8M,UAAU,CAACjN,GAAX,CAAekN,CAAf,CAAJ,CAAA;OAHZ,CAAA,CAAA;AAID,KARa,CAAd,CAAA;AASD,GAAA;AAED,EAAA,OAAOJ,WAAW,CAAC;AACjBhB,IAAAA,IAAI,EAAE,WADW;AAEjBC,IAAAA,SAAS,EAATA,SAFiB;IAGjBlF,QAAQ,EAAE,IAAIrI,GAAJ,EAHO;AAIjBqK,IAAAA,MAAM,EAANA,MAJiB;AAKjB4D,IAAAA,QAAQ,EAARA,QAAAA;AALiB,GAAD,CAAlB,CAAA;AAOD,CAAA;AAED,SAASU,WAAT,CAAA,KAAA,EAAA;AACEV,EAAAA,IAAAA,QAAAA,GAAAA,KAAAA,CAAAA,QAAAA;AACAW,IAAAA,MAAAA,GAAAA,KAAAA,CAAAA,MAAAA;AACApK,IAAAA,KAAAA,GAAAA,KAAAA,CAAAA,KAAAA;IACWgE,IAAX+E,GAAAA,KAAAA,CAAAA,SAAAA;AACAzC,IAAAA,cAAAA,GAAAA,KAAAA,CAAAA,cAAAA;AACAC,IAAAA,oBAAAA,GAAAA,KAAAA,CAAAA,oBAAAA,CAAAA;EAEA,IAAIvC,IAAI,KAAK,MAAb,EAAqB;IACnB,IAAIoG,MAAM,KAAK,QAAf,EAAyB;AACvB,MAAA,OAAOnD,IAAI,CAACwC,QAAD,EAAW,UAAG,GAAA,EAAA;AAAA,QAAA,OAAIjE,GAAG,IAAIxF,KAAJ,WAAIA,KAAJ,GAAa,EAAb,CAAP,CAAA;AAAA,OAAd,CAAX,CAAA;AACD,KAFD,MAEO,IAAIoK,MAAM,KAAK,KAAf,EAAsB;MAC3B,OAAOnD,IAAI,CAACwC,QAAD,EAAW,YAAA;AAAA,QAAA,OAAMzJ,KAAN,IAAA,IAAA,GAAMA,KAAN,GAAe,EAAf,CAAA;AAAA,OAAX,CAAX,CAAA;AACD,KAAA;AACF,GAND,MAMO,IAAIgE,IAAI,KAAK,OAAb,EAAsB;IAC3B,IAAIoG,MAAM,KAAK,QAAf,EAAyB;AACvB,MAAA,OAAOrC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;AAC1B,QAAA,IAAIzJ,KAAJ,EAAWwF,GAAG,CAACxI,GAAJ,CAAQgD,KAAR,CAAA,CAAA;AACZ,OAFa,CAAd,CAAA;AAGD,KAJD,MAIO,IAAIoK,MAAM,KAAK,QAAf,EAAyB;AAC9B,MAAA,OAAOrC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;AAC1B,QAAA,IAAIzJ,KAAJ,EAAWwF,GAAG,CAAA,QAAA,CAAH,CAAWxF,KAAX,CAAA,CAAA;AACZ,OAFa,CAAd,CAAA;AAGD,KAJM,MAIA,IAAIoK,MAAM,KAAK,KAAf,EAAsB;AAC3B,MAAA,OAAOrC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;AAC1BjE,QAAAA,GAAG,CAACxJ,KAAJ,EAAA,CAAA;AACA,QAAA,IAAIgE,KAAJ,EAAWwF,GAAG,CAACxI,GAAJ,CAAQgD,KAAR,CAAA,CAAA;AACZ,OAHa,CAAd,CAAA;AAID,KAAA;AACF,GAfM,MAeA,IAAIgE,IAAI,KAAK,UAAb,EAAyB;AAC9B,IAAA,IAAIoG,MAAM,KAAK,KAAX,IAAoB9D,cAAxB,EAAwC;MACtC,OAAOmB,QAAQ,CAACgC,QAAD,EAAW,YAAA;QAAA,OAAO;AAC/BlD,UAAAA,oBAAoB,EAApBA,oBAD+B;AAE/BD,UAAAA,cAAc,EAAdA,cAAAA;SAFwB,CAAA;AAAA,OAAX,CAAf,CAAA;AAID,KAAA;AACF,GAPM,MAOA;IACL,IAAI8D,MAAM,KAAK,QAAf,EAAyB;AACvB,MAAA,OAAOrB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,UAAG,GAAA,EAAA;AAAA,QAAA,OAClCwB,GAAG,KAAK,IAAR,GAAeA,GAAG,IAAIxF,KAAJ,IAAA,IAAA,GAAIA,KAAJ,GAAa,EAAb,CAAlB,GAAqCA,KAArC,IAAqCA,IAAAA,GAAAA,KAArC,GAA8C,EADZ,CAAA;AAAA,OAApB,CAAhB,CAAA;AAGD,KAJD,MAIO,IAAIoK,MAAM,KAAK,KAAf,EAAsB;AAC3B,MAAA,OAAOrB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,YAAA;AAAA,QAAA,OAAMhE,KAAN,IAAA,IAAA,GAAMA,KAAN,GAAe,EAAf,CAAA;AAAA,OAAjB,CAAhB,CAAA;AACD,KAFM,MAEA,IAAIoK,MAAM,KAAK,QAAf,EAAyB;AAC9B,MAAA,OAAOrB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,YAAA;AAAA,QAAA,OAAM,IAAN,CAAA;AAAA,OAAjB,CAAhB,CAAA;AACD,KAAA;AACF,GAAA;AACD,EAAA,OAAOL,cAAP,CAAA;AACD,CAAA;AAeD,IAAe,KAAA,GAAA;AACbsD,EAAAA,IAAI,EAAJA,IADa;AAEbc,EAAAA,OAAO,EAAPA,OAFa;AAGbgB,EAAAA,SAAS,EAATA,SAHa;AAIbtB,EAAAA,QAAQ,EAARA,QAJa;AAKb0C,EAAAA,WAAW,EAAXA,WAAAA;AALa,CAAf;;ACzgBA,SAASE,UAAU,CAACC,GAAW,EAAU;EACvC,IAAIC,IAAI,GAAG,UAAU,CAAA;AACrB,EAAA,MAAMC,CAAC,GAAGF,GAAG,CAAC3B,MAAM,CAAA;EAEpB,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuJ,CAAC,EAAEvJ,CAAC,EAAE,EAAE;AAC1BsJ,IAAAA,IAAI,IAAID,GAAG,CAACG,UAAU,CAACxJ,CAAC,CAAC,CAAA;IACzBsJ,IAAI,IACF,CAACA,IAAI,IAAI,CAAC,KAAKA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,EAAE,CAAC,CAAA;AACxE,GAAA;EACA,OAAOA,IAAI,KAAK,CAAC,CAAA;AACnB,CAAA;AAEO,SAASG,IAAI,CAClBC,IAAY,EACZ3K,KAAa,EACbmB,OAAe,EACA;AACf;EACA,IAAIA,OAAO,KAAK,CAAC,EAAE;AACjB,IAAA,OAAQkJ,UAAU,CAACA,UAAU,CAACM,IAAI,GAAG3K,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,GAAI,KAAK,CAAA;AACpE,GAAA;AACA;EACA,IAAImB,OAAO,KAAK,CAAC,EAAE;IACjB,OAAQkJ,UAAU,CAACrK,KAAK,GAAG2K,IAAI,CAAC,GAAG,IAAI,GAAI,IAAI,CAAA;AACjD,GAAA;;AAEA;AACA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEO,SAASC,eAAe,CAACC,CAAS,EAAY;AACnD,EAAA,IAAIA,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAA;EACrB,OAAO,IAAIlN,KAAK,CAACkN,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAA;AACjC,CAAA;AAEO,SAASE,OAAO,CAACF,CAAS,EAAEG,KAAqB,EAAW;AACjE,EAAA,OAAOH,CAAC,IAAIG,KAAK,CAAC,CAAC,CAAC,IAAIH,CAAC,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAA;AACtC,CAAA;AAEO,SAASC,WAAW,CACzBC,SAAiB,EACjBC,SAAmC,EAC1B;AACT,EAAA,MAAMN,CAAC,GAAGH,IAAI,CAAC,IAAI,GAAGS,SAAS,CAAC,CAAC,CAAC,EAAED,SAAS,EAAE,CAAC,CAAC,CAAA;AACjD,EAAA,IAAIL,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,CAAA;AAC5B,EAAA,OAAOA,CAAC,IAAIM,SAAS,CAAC,CAAC,CAAC,IAAIN,CAAC,GAAGM,SAAS,CAAC,CAAC,CAAC,CAAA;AAC9C,CAAA;AAEO,SAASC,eAAe,CAACP,CAAS,EAAEQ,MAAwB,EAAU;AAC3E,EAAA,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoK,MAAM,CAAC1C,MAAM,EAAE1H,CAAC,EAAE,EAAE;IACtC,IAAI8J,OAAO,CAACF,CAAC,EAAEQ,MAAM,CAACpK,CAAC,CAAC,CAAC,EAAE;AACzB,MAAA,OAAOA,CAAC,CAAA;AACV,KAAA;AACF,GAAA;AACA,EAAA,OAAO,CAAC,CAAC,CAAA;AACX,CAAA;AAEO,SAASqK,YAAY,CAACC,WAAmB,EAAsB;EACpE,IAAI;IACF,MAAMC,OAAO,GAAGD,WAAW,CAACE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;AAC1D,IAAA,OAAO,IAAIC,MAAM,CAACF,OAAO,CAAC,CAAA;GAC3B,CAAC,OAAOxQ,CAAC,EAAE;AACV2Q,IAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;AAChB,IAAA,OAAOjC,SAAS,CAAA;AAClB,GAAA;AACF,CAAA;AAEO,SAAS8S,aAAa,CAACvM,GAAW,EAAEwM,OAAoB,EAAE;AAC/D,EAAA,IAAI,CAACA,OAAO,CAACnD,MAAM,EAAE,OAAO,KAAK,CAAA;EACjC,IAAIoD,eAAe,GAAG,KAAK,CAAA;EAC3B,IAAIC,UAAU,GAAG,KAAK,CAAA;AAEtB,EAAA,KAAK,IAAI/K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6K,OAAO,CAACnD,MAAM,EAAE1H,CAAC,EAAE,EAAE;AACvC,IAAA,MAAMgL,KAAK,GAAGC,cAAc,CAAC5M,GAAG,EAAEwM,OAAO,CAAC7K,CAAC,CAAC,CAAC6B,IAAI,EAAEgJ,OAAO,CAAC7K,CAAC,CAAC,CAACkL,OAAO,CAAC,CAAA;IACtE,IAAIL,OAAO,CAAC7K,CAAC,CAAC,CAACmL,OAAO,KAAK,KAAK,EAAE;MAChC,IAAIH,KAAK,EAAE,OAAO,KAAK,CAAA;AACzB,KAAC,MAAM;AACLF,MAAAA,eAAe,GAAG,IAAI,CAAA;AACtB,MAAA,IAAIE,KAAK,EAAED,UAAU,GAAG,IAAI,CAAA;AAC9B,KAAA;AACF,GAAA;EAEA,OAAOA,UAAU,IAAI,CAACD,eAAe,CAAA;AACvC,CAAA;AAEA,SAASM,kBAAkB,CACzBC,MAAc,EACdH,OAAe,EACfI,MAAe,EACN;EACT,IAAI;AACF;AACA,IAAA,IAAIf,OAAO,GAAGW,OAAO,CAClBV,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CACtCA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;AAE1B,IAAA,IAAIc,MAAM,EAAE;AACV;AACAf,MAAAA,OAAO,GAAG,MAAM,GAAGA,OAAO,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,GAAG,MAAM,CAAA;AAC/D,KAAA;AAEA,IAAA,MAAMe,KAAK,GAAG,IAAId,MAAM,CAAC,GAAG,GAAGF,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;AAClD,IAAA,OAAOgB,KAAK,CAACzC,IAAI,CAACuC,MAAM,CAAC,CAAA;GAC1B,CAAC,OAAOtR,CAAC,EAAE;AACV,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAEA,SAASyR,oBAAoB,CAACH,MAAW,EAAEH,OAAe,EAAE;EAC1D,IAAI;AACF;AACA;IACA,MAAMO,QAAQ,GAAG,IAAIC,GAAG,CACtBR,OAAO,CAACV,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,EACvE,eAAe,CAChB,CAAA;;AAED;IACA,MAAMmB,KAAuC,GAAG,CAC9C,CAACN,MAAM,CAAChT,IAAI,EAAEoT,QAAQ,CAACpT,IAAI,EAAE,KAAK,CAAC,EACnC,CAACgT,MAAM,CAACO,QAAQ,EAAEH,QAAQ,CAACG,QAAQ,EAAE,IAAI,CAAC,CAC3C,CAAA;AACD;IACA,IAAIH,QAAQ,CAAChC,IAAI,EAAE;AACjBkC,MAAAA,KAAK,CAAC5D,IAAI,CAAC,CAACsD,MAAM,CAAC5B,IAAI,EAAEgC,QAAQ,CAAChC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;AACjD,KAAA;IAEAgC,QAAQ,CAACI,YAAY,CAAC3P,OAAO,CAAC,CAAC4P,CAAC,EAAEC,CAAC,KAAK;AACtCJ,MAAAA,KAAK,CAAC5D,IAAI,CAAC,CAACsD,MAAM,CAACQ,YAAY,CAAC/P,GAAG,CAACiQ,CAAC,CAAC,IAAI,EAAE,EAAED,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;AAC1D,KAAC,CAAC,CAAA;;AAEF;IACA,OAAO,CAACH,KAAK,CAACK,IAAI,CACfzQ,IAAI,IAAK,CAAC6P,kBAAkB,CAAC7P,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD,CAAA;GACF,CAAC,OAAOxB,CAAC,EAAE;AACV,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAEA,SAASkR,cAAc,CACrB5M,GAAW,EACXwD,IAAmB,EACnBqJ,OAAe,EACN;EACT,IAAI;IACF,MAAMjM,MAAM,GAAG,IAAIyM,GAAG,CAACrN,GAAG,EAAE,WAAW,CAAC,CAAA;IAExC,IAAIwD,IAAI,KAAK,OAAO,EAAE;AACpB,MAAA,MAAM0J,KAAK,GAAGlB,YAAY,CAACa,OAAO,CAAC,CAAA;AACnC,MAAA,IAAI,CAACK,KAAK,EAAE,OAAO,KAAK,CAAA;MACxB,OACEA,KAAK,CAACzC,IAAI,CAAC7J,MAAM,CAACgN,IAAI,CAAC,IACvBV,KAAK,CAACzC,IAAI,CAAC7J,MAAM,CAACgN,IAAI,CAACC,SAAS,CAACjN,MAAM,CAACkN,MAAM,CAACzE,MAAM,CAAC,CAAC,CAAA;AAE3D,KAAC,MAAM,IAAI7F,IAAI,KAAK,QAAQ,EAAE;AAC5B,MAAA,OAAO2J,oBAAoB,CAACvM,MAAM,EAAEiM,OAAO,CAAC,CAAA;AAC9C,KAAA;AAEA,IAAA,OAAO,KAAK,CAAA;GACb,CAAC,OAAOnR,CAAC,EAAE;AACV,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAEO,SAASqS,eAAe,CAC7BC,aAAqB,EACrBC,QAA4B,EAC5BC,OAAkB,EACA;AAClBD,EAAAA,QAAQ,GAAGA,QAAQ,KAAKxU,SAAS,GAAG,CAAC,GAAGwU,QAAQ,CAAA;;AAEhD;EACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;AAIhBA,IAAAA,QAAQ,GAAG,CAAC,CAAA;AACd,GAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;AAIvBA,IAAAA,QAAQ,GAAG,CAAC,CAAA;AACd,GAAA;;AAEA;AACA,EAAA,MAAME,KAAK,GAAG7C,eAAe,CAAC0C,aAAa,CAAC,CAAA;EAC5CE,OAAO,GAAGA,OAAO,IAAIC,KAAK,CAAA;AAC1B,EAAA,IAAID,OAAO,CAAC7E,MAAM,KAAK2E,aAAa,EAAE;AAMpCE,IAAAA,OAAO,GAAGC,KAAK,CAAA;AACjB,GAAA;;AAEA;AACA,EAAA,MAAMC,WAAW,GAAGF,OAAO,CAACG,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKA,GAAG,GAAGD,CAAC,EAAE,CAAC,CAAC,CAAA;AAC1D,EAAA,IAAIF,WAAW,GAAG,IAAI,IAAIA,WAAW,GAAG,IAAI,EAAE;AAI5CF,IAAAA,OAAO,GAAGC,KAAK,CAAA;AACjB,GAAA;;AAEA;EACA,IAAIK,UAAU,GAAG,CAAC,CAAA;AAClB,EAAA,OAAON,OAAO,CAAChN,GAAG,CAAEoN,CAAC,IAAK;IACxB,MAAMG,KAAK,GAAGD,UAAU,CAAA;AACxBA,IAAAA,UAAU,IAAIF,CAAC,CAAA;IACf,OAAO,CAACG,KAAK,EAAEA,KAAK,GAAIR,QAAQ,GAAcK,CAAC,CAAC,CAAA;AAClD,GAAC,CAAC,CAAA;AACJ,CAAA;AAEO,SAASI,sBAAsB,CACpCC,EAAU,EACV3O,GAAW,EACXgO,aAAqB,EACrB;EACA,IAAI,CAAChO,GAAG,EAAE;AACR,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA,MAAM4O,MAAM,GAAG5O,GAAG,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;EAChC,IAAI,CAACkI,MAAM,EAAE;AACX,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA,MAAMjC,KAAK,GAAGiC,MAAM,CACjBzC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAAC,GACnBzF,KAAK,CAAC,GAAG,CAAC;AAAC,GACXxF,GAAG,CAAE2N,EAAE,IAAKA,EAAE,CAACnI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAC7BC,MAAM,CAAC,IAAA,IAAA;IAAA,IAAC,CAAC+G,CAAC,CAAC,GAAA,IAAA,CAAA;IAAA,OAAKA,CAAC,KAAKiB,EAAE,CAAA;AAAA,GAAA,CAAC;AAAC,GAC1BzN,GAAG,CAAC,KAAA,IAAA;IAAA,IAAC,GAAGuM,CAAC,CAAC,GAAA,KAAA,CAAA;IAAA,OAAKqB,QAAQ,CAACrB,CAAC,CAAC,CAAA;AAAA,GAAA,CAAC,CAAC;;EAE/B,IAAId,KAAK,CAACtD,MAAM,GAAG,CAAC,IAAIsD,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGqB,aAAa,EAC/D,OAAOrB,KAAK,CAAC,CAAC,CAAC,CAAA;AAEjB,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEO,SAASD,UAAU,CAACI,OAAsB,EAAE;EACjD,IAAI;AACF,IAAA,OAAOA,OAAO,EAAE,CAAA;GACjB,CAAC,OAAOpR,CAAC,EAAE;AACV2Q,IAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;AAChB,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AACF,CAAA;AAEA,MAAMqT,WAAW,GAAI1N,CAAS,IAC5B2N,UAAU,CAAC1Q,IAAI,CAAC2Q,IAAI,CAAC5N,CAAC,CAAC,EAAGuJ,CAAC,IAAKA,CAAC,CAACO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;AAE3C,eAAe+D,OAAO,CAC3BC,eAAuB,EACvBC,aAAsB,EACtBxV,MAAqB,EACJ;EACjBwV,aAAa,GAAGA,aAAa,IAAI,EAAE,CAAA;EACnCxV,MAAM,GAAGA,MAAM,IAAKL,UAAU,CAACI,MAAM,IAAIJ,UAAU,CAACI,MAAM,CAACC,MAAO,CAAA;EAClE,IAAI,CAACA,MAAM,EAAE;AACX,IAAA,MAAM,IAAIyV,KAAK,CAAC,sCAAsC,CAAC,CAAA;AACzD,GAAA;EACA,IAAI;AACF,IAAA,MAAM/R,GAAG,GAAG,MAAM1D,MAAM,CAAC0V,SAAS,CAChC,KAAK,EACLP,WAAW,CAACK,aAAa,CAAC,EAC1B;AAAEG,MAAAA,IAAI,EAAE,SAAS;AAAElG,MAAAA,MAAM,EAAE,GAAA;KAAK,EAChC,IAAI,EACJ,CAAC,SAAS,EAAE,SAAS,CAAC,CACvB,CAAA;IACD,MAAM,CAACmG,EAAE,EAAEC,UAAU,CAAC,GAAGN,eAAe,CAACzI,KAAK,CAAC,GAAG,CAAC,CAAA;AACnD,IAAA,MAAMgJ,eAAe,GAAG,MAAM9V,MAAM,CAACsV,OAAO,CAC1C;AAAEK,MAAAA,IAAI,EAAE,SAAS;MAAEC,EAAE,EAAET,WAAW,CAACS,EAAE,CAAA;AAAE,KAAC,EACxClS,GAAG,EACHyR,WAAW,CAACU,UAAU,CAAC,CACxB,CAAA;AAED,IAAA,OAAO,IAAIE,WAAW,EAAE,CAACC,MAAM,CAACF,eAAe,CAAC,CAAA;GACjD,CAAC,OAAOhU,CAAC,EAAE;AACV,IAAA,MAAM,IAAI2T,KAAK,CAAC,mBAAmB,CAAC,CAAA;AACtC,GAAA;AACF,CAAA;;AAEA;AACO,SAASQ,QAAQ,CAACC,KAAU,EAAU;AAC3C,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK,CAAA;AAC3C,EAAA,OAAOtV,IAAI,CAACC,SAAS,CAACqV,KAAK,CAAC,CAAA;AAC9B,CAAA;;AAEA;AACO,SAASC,mBAAmB,CAACD,KAAU,EAAU;AACtD,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGA,KAAK,GAAG,EAAE,CAAA;AACpB,GAAA;AACA,EAAA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;AACvCA,IAAAA,KAAK,GAAG,GAAG,CAAA;AACb,GAAA;AACA;AACA;AACA;AACA,EAAA,MAAME,KAAK,GAAIF,KAAK,CAAY3D,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAACzF,KAAK,CAAC,MAAM,CAAC,CAAA;;AAExE;AACA;AACA;AACA,EAAA,IAAIsJ,KAAK,CAAC3G,MAAM,KAAK,CAAC,EAAE;AACtB2G,IAAAA,KAAK,CAACtG,IAAI,CAAC,GAAG,CAAC,CAAA;AACjB,GAAA;;AAEA;AACA;AACA,EAAA,OAAOsG,KAAK,CACT9O,GAAG,CAAEuM,CAAC,IAAMA,CAAC,CAACd,KAAK,CAAC,UAAU,CAAC,GAAGc,CAAC,CAACwC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGxC,CAAE,CAAC,CAC1D5G,IAAI,CAAC,GAAG,CAAC,CAAA;AACd,CAAA;AAEO,SAASqJ,cAAc,GAAW;AACvC,EAAA,IAAIrO,OAAe,CAAA;EACnB,IAAI;AACF;AACAA,IAAAA,OAAO,GAAkB,QAAA,CAAA;GAC1B,CAAC,OAAOnG,CAAC,EAAE;AACVmG,IAAAA,OAAO,GAAG,EAAE,CAAA;AACd,GAAA;AACA,EAAA,OAAOA,OAAO,CAAA;AAChB;;ACzUA;AAYA,MAAMsO,WAAsC,GAAG,EAAE,CAAA;;AAEjD;AACO,SAASC,aAAa,CAC3BC,GAAc,EACdC,SAA6B,EACpB;AACT;EACA,IAAI,KAAK,IAAIA,SAAS,EAAE;IACtB,OAAOC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,KAAK,CAAC,CAAyB,CAAA;AAC9D,GAAA;EACA,IAAI,MAAM,IAAIA,SAAS,EAAE;IACvB,OAAO,CAACC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB,CAAA;AAChE,GAAA;EACA,IAAI,MAAM,IAAIA,SAAS,EAAE;IACvB,OAAOE,OAAO,CAACH,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB,CAAA;AAChE,GAAA;EACA,IAAI,MAAM,IAAIA,SAAS,EAAE;IACvB,OAAO,CAACF,aAAa,CAACC,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAuB,CAAA;AACrE,GAAA;;AAEA;AACA,EAAA,KAAK,MAAM,CAAC5C,CAAC,EAAED,CAAC,CAAC,IAAIpR,MAAM,CAACkC,OAAO,CAAC+R,SAAS,CAAC,EAAE;AAC9C,IAAA,IAAI,CAACG,kBAAkB,CAAChD,CAAC,EAAEiD,OAAO,CAACL,GAAG,EAAE3C,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAA;AAC3D,GAAA;AACA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACA,SAASgD,OAAO,CAACL,GAAc,EAAEM,IAAY,EAAE;AAC7C,EAAA,MAAMX,KAAK,GAAGW,IAAI,CAACjK,KAAK,CAAC,GAAG,CAAC,CAAA;EAC7B,IAAIkK,OAAY,GAAGP,GAAG,CAAA;AACtB,EAAA,KAAK,IAAI1O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,KAAK,CAAC3G,MAAM,EAAE1H,CAAC,EAAE,EAAE;AACrC,IAAA,IAAIiP,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIZ,KAAK,CAACrO,CAAC,CAAC,IAAIiP,OAAO,EAAE;AACjEA,MAAAA,OAAO,GAAGA,OAAO,CAACZ,KAAK,CAACrO,CAAC,CAAC,CAAC,CAAA;AAC7B,KAAC,MAAM;AACL,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AACA,EAAA,OAAOiP,OAAO,CAAA;AAChB,CAAA;;AAEA;AACA,SAASC,QAAQ,CAAC3D,KAAa,EAAU;AACvC,EAAA,IAAI,CAACiD,WAAW,CAACjD,KAAK,CAAC,EAAE;AACvBiD,IAAAA,WAAW,CAACjD,KAAK,CAAC,GAAG,IAAId,MAAM,CAACc,KAAK,CAACf,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAA;AACvE,GAAA;EACA,OAAOgE,WAAW,CAACjD,KAAK,CAAC,CAAA;AAC3B,CAAA;;AAEA;AACA,SAASuD,kBAAkB,CAACH,SAAyB,EAAE5P,KAAU,EAAE;AACjE;AACA,EAAA,IAAI,OAAO4P,SAAS,KAAK,QAAQ,EAAE;AACjC,IAAA,OAAO5P,KAAK,GAAG,EAAE,KAAK4P,SAAS,CAAA;AACjC,GAAA;AACA,EAAA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;AACjC,IAAA,OAAO5P,KAAK,GAAG,CAAC,KAAK4P,SAAS,CAAA;AAChC,GAAA;AACA,EAAA,IAAI,OAAOA,SAAS,KAAK,SAAS,EAAE;AAClC,IAAA,OAAO,CAAC,CAAC5P,KAAK,KAAK4P,SAAS,CAAA;AAC9B,GAAA;EAEA,IAAIA,SAAS,KAAK,IAAI,EAAE;IACtB,OAAO5P,KAAK,KAAK,IAAI,CAAA;AACvB,GAAA;AAEA,EAAA,IAAIrC,KAAK,CAACyC,OAAO,CAACwP,SAAS,CAAC,IAAI,CAACQ,gBAAgB,CAACR,SAAS,CAAC,EAAE;AAC5D,IAAA,OAAO9V,IAAI,CAACC,SAAS,CAACiG,KAAK,CAAC,KAAKlG,IAAI,CAACC,SAAS,CAAC6V,SAAS,CAAC,CAAA;AAC5D,GAAA;;AAEA;AACA,EAAA,KAAK,MAAMS,EAAE,IAAIT,SAAS,EAAE;AAC1B,IAAA,IACE,CAACU,qBAAqB,CACpBD,EAAE,EACFrQ,KAAK,EACL4P,SAAS,CAACS,EAAE,CAAiC,CAC9C,EACD;AACA,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AACF,GAAA;AACA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;;AAEA;AACA,SAASD,gBAAgB,CAACT,GAAQ,EAAW;AAC3C,EAAA,MAAMzQ,IAAI,GAAGvD,MAAM,CAACuD,IAAI,CAACyQ,GAAG,CAAC,CAAA;EAC7B,OACEzQ,IAAI,CAACyJ,MAAM,GAAG,CAAC,IAAIzJ,IAAI,CAAC+G,MAAM,CAAE+G,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAACrE,MAAM,KAAKzJ,IAAI,CAACyJ,MAAM,CAAA;AAE9E,CAAA;;AAEA;AACA,SAAS4H,OAAO,CAACxD,CAAM,EAAuB;AAC5C,EAAA,IAAIA,CAAC,KAAK,IAAI,EAAE,OAAO,MAAM,CAAA;EAC7B,IAAIpP,KAAK,CAACyC,OAAO,CAAC2M,CAAC,CAAC,EAAE,OAAO,OAAO,CAAA;EACpC,MAAMyD,CAAC,GAAG,OAAOzD,CAAC,CAAA;AAClB,EAAA,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC1J,QAAQ,CAACmN,CAAC,CAAC,EAAE;AACtE,IAAA,OAAOA,CAAC,CAAA;AACV,GAAA;AACA,EAAA,OAAO,SAAS,CAAA;AAClB,CAAA;;AAEA;AACA,SAASC,SAAS,CAACnE,MAAW,EAAEI,QAAa,EAAE;EAC7C,IAAI,CAAC/O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;EACxC,MAAMoE,KAAK,GAAGN,gBAAgB,CAAC1D,QAAQ,CAAC,GACnCK,CAAM,IAAKgD,kBAAkB,CAACrD,QAAQ,EAAEK,CAAC,CAAC,GAC1CA,CAAM,IAAK2C,aAAa,CAAC3C,CAAC,EAAEL,QAAQ,CAAC,CAAA;AAC1C,EAAA,KAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqL,MAAM,CAAC3D,MAAM,EAAE1H,CAAC,EAAE,EAAE;AACtC,IAAA,IAAIqL,MAAM,CAACrL,CAAC,CAAC,IAAIyP,KAAK,CAACpE,MAAM,CAACrL,CAAC,CAAC,CAAC,EAAE;AACjC,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;AAEA,SAAS0P,IAAI,CAACrE,MAAW,EAAEI,QAAoB,EAAW;AACxD;AACA,EAAA,IAAI/O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE;AACzB,IAAA,OAAOA,MAAM,CAACW,IAAI,CAAExI,EAAE,IAAKiI,QAAQ,CAACrJ,QAAQ,CAACoB,EAAE,CAAC,CAAC,CAAA;AACnD,GAAA;AACA,EAAA,OAAOiI,QAAQ,CAACrJ,QAAQ,CAACiJ,MAAM,CAAC,CAAA;AAClC,CAAA;;AAEA;AACA,SAASgE,qBAAqB,CAC5BM,QAAkB,EAClBtE,MAAW,EACXI,QAAa,EACJ;AACT,EAAA,QAAQkE,QAAQ;AACd,IAAA,KAAK,MAAM;MACT,OAAOvB,mBAAmB,CAAC/C,MAAM,CAAC,KAAK+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;AACtE,IAAA,KAAK,MAAM;MACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,KAAK+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;AACtE,IAAA,KAAK,MAAM;MACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,GAAG+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;AACpE,IAAA,KAAK,OAAO;MACV,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,IAAI+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;AACrE,IAAA,KAAK,MAAM;MACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,GAAG+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;AACpE,IAAA,KAAK,OAAO;MACV,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,IAAI+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;AACrE,IAAA,KAAK,KAAK;MACR,OAAOJ,MAAM,KAAKI,QAAQ,CAAA;AAC5B,IAAA,KAAK,KAAK;MACR,OAAOJ,MAAM,KAAKI,QAAQ,CAAA;AAC5B,IAAA,KAAK,KAAK;MACR,OAAOJ,MAAM,GAAGI,QAAQ,CAAA;AAC1B,IAAA,KAAK,MAAM;MACT,OAAOJ,MAAM,IAAII,QAAQ,CAAA;AAC3B,IAAA,KAAK,KAAK;MACR,OAAOJ,MAAM,GAAGI,QAAQ,CAAA;AAC1B,IAAA,KAAK,MAAM;MACT,OAAOJ,MAAM,IAAII,QAAQ,CAAA;AAC3B,IAAA,KAAK,SAAS;AACZ;MACA,OAAOA,QAAQ,GAAGJ,MAAM,IAAI,IAAI,GAAGA,MAAM,IAAI,IAAI,CAAA;AACnD,IAAA,KAAK,KAAK;MACR,IAAI,CAAC3O,KAAK,CAACyC,OAAO,CAACsM,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAA;AAC1C,MAAA,OAAOiE,IAAI,CAACrE,MAAM,EAAEI,QAAQ,CAAC,CAAA;AAC/B,IAAA,KAAK,MAAM;MACT,IAAI,CAAC/O,KAAK,CAACyC,OAAO,CAACsM,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAA;AAC1C,MAAA,OAAO,CAACiE,IAAI,CAACrE,MAAM,EAAEI,QAAQ,CAAC,CAAA;AAChC,IAAA,KAAK,MAAM;AACT,MAAA,OAAO,CAACqD,kBAAkB,CAACrD,QAAQ,EAAEJ,MAAM,CAAC,CAAA;AAC9C,IAAA,KAAK,OAAO;MACV,IAAI,CAAC3O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;AACxC,MAAA,OAAOyD,kBAAkB,CAACrD,QAAQ,EAAEJ,MAAM,CAAC3D,MAAM,CAAC,CAAA;AACpD,IAAA,KAAK,YAAY;AACf,MAAA,OAAO8H,SAAS,CAACnE,MAAM,EAAEI,QAAQ,CAAC,CAAA;AACpC,IAAA,KAAK,MAAM;MACT,IAAI,CAAC/O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;AACxC,MAAA,KAAK,IAAIrL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyL,QAAQ,CAAC/D,MAAM,EAAE1H,CAAC,EAAE,EAAE;QACxC,IAAI4P,MAAM,GAAG,KAAK,CAAA;AAClB,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxE,MAAM,CAAC3D,MAAM,EAAEmI,CAAC,EAAE,EAAE;AACtC,UAAA,IAAIf,kBAAkB,CAACrD,QAAQ,CAACzL,CAAC,CAAC,EAAEqL,MAAM,CAACwE,CAAC,CAAC,CAAC,EAAE;AAC9CD,YAAAA,MAAM,GAAG,IAAI,CAAA;AACb,YAAA,MAAA;AACF,WAAA;AACF,SAAA;AACA,QAAA,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK,CAAA;AAC3B,OAAA;AACA,MAAA,OAAO,IAAI,CAAA;AACb,IAAA,KAAK,QAAQ;MACX,IAAI;QACF,OAAOV,QAAQ,CAACzD,QAAQ,CAAC,CAAC3C,IAAI,CAACuC,MAAM,CAAC,CAAA;OACvC,CAAC,OAAOtR,CAAC,EAAE;AACV,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AACF,IAAA,KAAK,OAAO;AACV,MAAA,OAAOuV,OAAO,CAACjE,MAAM,CAAC,KAAKI,QAAQ,CAAA;AACrC,IAAA;AACEf,MAAAA,OAAO,CAACC,KAAK,CAAC,oBAAoB,GAAGgF,QAAQ,CAAC,CAAA;AAC9C,MAAA,OAAO,KAAK,CAAA;AAAC,GAAA;AAEnB,CAAA;;AAEA;AACA,SAASf,MAAM,CAACF,GAAc,EAAEoB,UAAgC,EAAW;AACzE,EAAA,IAAI,CAACA,UAAU,CAACpI,MAAM,EAAE,OAAO,IAAI,CAAA;AACnC,EAAA,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,UAAU,CAACpI,MAAM,EAAE1H,CAAC,EAAE,EAAE;IAC1C,IAAIyO,aAAa,CAACC,GAAG,EAAEoB,UAAU,CAAC9P,CAAC,CAAC,CAAC,EAAE;AACrC,MAAA,OAAO,IAAI,CAAA;AACb,KAAA;AACF,GAAA;AACA,EAAA,OAAO,KAAK,CAAA;AACd,CAAA;;AAEA;AACA,SAAS6O,OAAO,CAACH,GAAc,EAAEoB,UAAgC,EAAW;AAC1E,EAAA,KAAK,IAAI9P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,UAAU,CAACpI,MAAM,EAAE1H,CAAC,EAAE,EAAE;IAC1C,IAAI,CAACyO,aAAa,CAACC,GAAG,EAAEoB,UAAU,CAAC9P,CAAC,CAAC,CAAC,EAAE;AACtC,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;AACF,GAAA;AACA,EAAA,OAAO,IAAI,CAAA;AACb;;AC3LA,MAAM9G,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,CAAA;AAElE,MAAM2W,WAAW,GAAGxB,cAAc,EAAE,CAAA;AAE7B,MAAMyB,UAAU,CAGrB;AACA;AACA;;AAMA;;AAiBA;;EAUAC,WAAW,CAACC,OAAiB,EAAE;AAC7BA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;AACvB;AACA;IACA,IAAI,CAAChQ,OAAO,GAAG6P,WAAW,CAAA;AAC1B,IAAA,IAAI,CAACI,IAAI,GAAG,IAAI,CAACD,OAAO,GAAGA,OAAO,CAAA;IAClC,IAAI,CAACE,SAAS,GAAG,IAAI,CAAA;AACrB,IAAA,IAAI,CAACC,mBAAmB,GAAG,IAAI9V,GAAG,EAAE,CAAA;AACpC,IAAA,IAAI,CAAC+V,gBAAgB,GAAG,EAAE,CAAA;IAC1B,IAAI,CAACC,KAAK,GAAG,KAAK,CAAA;AAClB,IAAA,IAAI,CAACC,cAAc,GAAG,IAAIjW,GAAG,EAAE,CAAA;IAC/B,IAAI,CAACkW,QAAQ,GAAG,EAAE,CAAA;IAClB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAA;IACjB,IAAI,CAACC,KAAK,GAAG,KAAK,CAAA;AAClB,IAAA,IAAI,CAACC,SAAS,GAAG,IAAI3W,GAAG,EAAE,CAAA;AAC1B,IAAA,IAAI,CAAC4W,oBAAoB,GAAG,IAAI5W,GAAG,EAAE,CAAA;AACrC,IAAA,IAAI,CAAC6W,mBAAmB,GAAG,EAAE,CAAA;AAC7B,IAAA,IAAI,CAACC,sBAAsB,GAAG,IAAI9W,GAAG,EAAE,CAAA;AACvC,IAAA,IAAI,CAAC+W,iBAAiB,GAAG,IAAIzW,GAAG,EAAE,CAAA;IAClC,IAAI,CAAC0W,mBAAmB,GAAG,KAAK,CAAA;IAEhC,IAAIf,OAAO,CAACjP,UAAU,EAAE;MACtB,IAAIiP,OAAO,CAACzC,aAAa,EAAE;AACzB,QAAA,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC,CAAA;AAC/D,OAAA;AACA,MAAA,IAAI,CAACwC,OAAO,CAAC5X,SAAS,EAAE;AACtB,QAAA,MAAM,IAAIoV,KAAK,CAAC,mBAAmB,CAAC,CAAA;AACtC,OAAA;MACA,IAAIwD,QAAQ,GAAG,KAAK,CAAA;MACpB,IAAI;AACFA,QAAAA,QAAQ,GAAG,CAAC,CAAC,IAAIxF,GAAG,CAACwE,OAAO,CAACzS,OAAO,IAAI,EAAE,CAAC,CAAC0T,QAAQ,CAACnG,KAAK,CACxD,kBAAkB,CACnB,CAAA;OACF,CAAC,OAAOjR,CAAC,EAAE;AACV;AAAA,OAAA;AAEF,MAAA,IAAImX,QAAQ,EAAE;AACZ,QAAA,MAAM,IAAIxD,KAAK,CAAC,2CAA2C,CAAC,CAAA;AAC9D,OAAA;AACF,KAAC,MAAM;MACL,IAAIwC,OAAO,CAACnS,kBAAkB,EAAE;AAC9B,QAAA,MAAM,IAAI2P,KAAK,CAAC,iDAAiD,CAAC,CAAA;AACpE,OAAA;AACF,KAAA;IAEA,IAAIwC,OAAO,CAACtP,QAAQ,EAAE;MACpB,IAAI,CAAC+P,KAAK,GAAG,IAAI,CAAA;AACnB,KAAA;AAEA,IAAA,IAAIzX,SAAS,IAAIgX,OAAO,CAACkB,aAAa,EAAE;MACtCjY,MAAM,CAACkY,WAAW,GAAG,IAAI,CAAA;MACzBjY,QAAQ,CAACkY,aAAa,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;AAC/C,KAAA;IAEA,IAAIrB,OAAO,CAACzP,WAAW,EAAE;MACvB,IAAI,CAACkQ,KAAK,GAAG,IAAI,CAAA;MACjB,IAAI,CAACa,yBAAyB,EAAE,CAAA;AAClC,KAAA;IAEA,IAAItB,OAAO,CAAC5X,SAAS,IAAI,CAAC4X,OAAO,CAACjP,UAAU,EAAE;MAC5C,IAAI,CAACwQ,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;AAChC,KAAA;AACF,GAAA;EAEA,MAAaC,YAAY,CAAChZ,OAA6B,EAAiB;AACtE,IAAA,IAAIA,OAAO,IAAIA,OAAO,CAACiZ,WAAW,EAAE;AAClC;AACA,MAAA,IAAI,CAACxB,IAAI,CAACyB,kBAAkB,GAAG,IAAI,CAAA;AACrC,KAAA;IACA,IAAI,CAACX,mBAAmB,GAAG,IAAI,CAAA;IAE/B,MAAM,IAAI,CAACQ,QAAQ,CAAC/Y,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;AAExC,IAAA,IAAI,IAAI,CAACmZ,aAAa,EAAE,EAAE;MACxBnW,SAAS,CAAC,IAAI,CAAC,CAAA;AACjB,KAAA;AACF,GAAA;EAEA,MAAaT,eAAe,CAC1BvC,OAAgC,EACjB;IACf,MAAM,IAAI,CAAC+Y,QAAQ,CAAC/Y,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;AAC3C,GAAA;AAEOgF,EAAAA,UAAU,GAAyB;AACxC,IAAA,OAAO,CAAC,IAAI,CAACqD,WAAW,EAAE,CAACtD,OAAO,EAAE,IAAI,CAACuD,YAAY,EAAE,CAAC,CAAA;AAC1D,GAAA;AACOD,EAAAA,WAAW,GAKhB;IACA,MAAM+Q,WAAW,GAAG,IAAI,CAAC3B,IAAI,CAAC1S,OAAO,IAAI,2BAA2B,CAAA;IACpE,OAAO;MACLA,OAAO,EAAEqU,WAAW,CAACtH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;AACxChJ,MAAAA,aAAa,EAAE,CAAC,IAAI,CAAC2O,IAAI,CAAC3O,aAAa,IAAIsQ,WAAW,EAAEtH,OAAO,CAC7D,MAAM,EACN,EAAE,CACH;AACD1J,MAAAA,iBAAiB,EAAE,IAAI,CAACqP,IAAI,CAAC4B,qBAAqB;AAClDtQ,MAAAA,2BAA2B,EAAE,IAAI,CAAC0O,IAAI,CAAC1O,2BAAAA;KACxC,CAAA;AACH,GAAA;AACOT,EAAAA,YAAY,GAAW;AAC5B,IAAA,OAAO,IAAI,CAACmP,IAAI,CAAC7X,SAAS,IAAI,EAAE,CAAA;AAClC,GAAA;AAEOsF,EAAAA,YAAY,GAAY;AAC7B,IAAA,OAAO,IAAI,CAACuS,IAAI,CAAClP,UAAU,IAAI,KAAK,CAAA;AACtC,GAAA;AAEOjD,EAAAA,qBAAqB,GAAqC;AAC/D,IAAA,OAAO,IAAI,CAACmS,IAAI,CAACpS,kBAAkB,CAAA;AACrC,GAAA;AAEA,EAAA,MAAc0T,QAAQ,CACpB/Y,OAAgC,EAChC2C,UAAoB,EACpBC,cAAwB,EACxB;AACA5C,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;AACvB,IAAA,IAAI,CAAC,IAAI,CAACyX,IAAI,CAAC7X,SAAS,EAAE;AACxB,MAAA,MAAM,IAAIoV,KAAK,CAAC,mBAAmB,CAAC,CAAA;AACtC,KAAA;AACA,IAAA,MAAMzS,eAAe,CACnB,IAAI,EACJvC,OAAO,CAACyC,OAAO,EACfzC,OAAO,CAAC0C,SAAS,IAAI,IAAI,CAAC+U,IAAI,CAACiB,aAAa,EAC5C/V,UAAU,EACVC,cAAc,EACd,IAAI,CAAC6U,IAAI,CAAC7Y,cAAc,KAAK,KAAK,CACnC,CAAA;AACH,GAAA;AAEQ0a,EAAAA,OAAO,GAAG;IAChB,IAAI,IAAI,CAAC5B,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,EAAE,CAAA;AAClB,KAAA;AACF,GAAA;EAEOzP,WAAW,CAACC,QAA2C,EAAE;AAC9D,IAAA,IAAI,CAACuP,IAAI,CAACvP,QAAQ,GAAGA,QAAQ,CAAA;IAC7B,IAAI,CAAC+P,KAAK,GAAG,IAAI,CAAA;IACjB,IAAI,CAACqB,OAAO,EAAE,CAAA;AAChB,GAAA;AAEA,EAAA,MAAaC,oBAAoB,CAC/BzE,eAAuB,EACvBC,aAAsB,EACtBxV,MAAqB,EACN;AACf,IAAA,MAAMia,YAAY,GAAG,MAAM3E,OAAO,CAChCC,eAAe,EACfC,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CAAA;IACD,IAAI,CAAC0I,WAAW,CACd9H,IAAI,CAACqG,KAAK,CAACgT,YAAY,CAAC,CACzB,CAAA;AACH,GAAA;EAEO1R,cAAc,CAACC,WAA6B,EAAQ;AACzD,IAAA,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,GAAGA,WAAW,CAAA;IACnC,IAAI,CAACkQ,KAAK,GAAG,IAAI,CAAA;IACjB,IAAI,CAACa,yBAAyB,EAAE,CAAA;AAClC,GAAA;AAEA,EAAA,MAAaW,uBAAuB,CAClC3E,eAAuB,EACvBC,aAAsB,EACtBxV,MAAqB,EACN;AACf,IAAA,MAAMma,eAAe,GAAG,MAAM7E,OAAO,CACnCC,eAAe,EACfC,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CAAA;IACD,IAAI,CAACuI,cAAc,CAAC3H,IAAI,CAACqG,KAAK,CAACkT,eAAe,CAAC,CAAqB,CAAA;AACtE,GAAA;AAEA,EAAA,MAAa9R,cAAc,CACzB/E,IAAwB,EACxBkS,aAAsB,EACtBxV,MAAqB,EACQ;IAC7B,IAAIsD,IAAI,CAAC8W,iBAAiB,EAAE;MAC1B9W,IAAI,CAACqF,QAAQ,GAAG/H,IAAI,CAACqG,KAAK,CACxB,MAAMqO,OAAO,CACXhS,IAAI,CAAC8W,iBAAiB,EACtB5E,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CACF,CAAA;MACD,OAAOsD,IAAI,CAAC8W,iBAAiB,CAAA;AAC/B,KAAA;IACA,IAAI9W,IAAI,CAAC+W,oBAAoB,EAAE;MAC7B/W,IAAI,CAACkF,WAAW,GAAG5H,IAAI,CAACqG,KAAK,CAC3B,MAAMqO,OAAO,CACXhS,IAAI,CAAC+W,oBAAoB,EACzB7E,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CACF,CAAA;MACD,OAAOsD,IAAI,CAAC+W,oBAAoB,CAAA;AAClC,KAAA;AACA,IAAA,OAAO/W,IAAI,CAAA;AACb,GAAA;EAEA,MAAagX,aAAa,CAAC1U,UAAsB,EAAE;AACjD,IAAA,IAAI,CAACsS,IAAI,CAACtS,UAAU,GAAGA,UAAU,CAAA;AACjC,IAAA,IAAI,IAAI,CAACsS,IAAI,CAACqC,mBAAmB,EAAE;MACjC,MAAM,IAAI,CAACjS,oBAAoB,EAAE,CAAA;AACnC,KAAA;AACA,IAAA,IAAI,IAAI,CAAC4P,IAAI,CAAClP,UAAU,EAAE;MACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;AAClC,MAAA,OAAA;AACF,KAAA;IACA,IAAI,CAACT,OAAO,EAAE,CAAA;IACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;AAClC,GAAA;EAEA,MAAakB,qBAAqB,CAACjY,SAAqB,EAAE;IACxD,IAAI,CAACqW,mBAAmB,GAAGrW,SAAS,CAAA;AACpC,IAAA,IAAI,IAAI,CAAC0V,IAAI,CAACqC,mBAAmB,EAAE;MACjC,MAAM,IAAI,CAACjS,oBAAoB,EAAE,CAAA;AACnC,KAAA;AACA,IAAA,IAAI,IAAI,CAAC4P,IAAI,CAAClP,UAAU,EAAE;MACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;AAClC,MAAA,OAAA;AACF,KAAA;IACA,IAAI,CAACT,OAAO,EAAE,CAAA;IACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;AAClC,GAAA;EAEA,MAAamB,mBAAmB,CAACC,IAA4B,EAAE;IAC7D,IAAI,CAACzC,IAAI,CAAChP,gBAAgB,GAAGyR,IAAI,IAAI,EAAE,CAAA;AACvC,IAAA,IAAI,IAAI,CAACzC,IAAI,CAAClP,UAAU,EAAE;MACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;AAClC,MAAA,OAAA;AACF,KAAA;IACA,IAAI,CAACT,OAAO,EAAE,CAAA;IACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;AAClC,GAAA;;AAEA;EACOqB,iBAAiB,CAACtT,GAAqB,EAAE;IAC9C,IAAI,CAACsR,oBAAoB,GAAGtR,GAAG,CAAA;IAC/B,IAAI,CAACyS,OAAO,EAAE,CAAA;AAChB,GAAA;EAEA,MAAac,MAAM,CAACzU,GAAW,EAAE;AAC/B,IAAA,IAAI,CAAC8R,IAAI,CAAC9R,GAAG,GAAGA,GAAG,CAAA;AACnB,IAAA,IAAI,IAAI,CAAC8R,IAAI,CAAClP,UAAU,EAAE;MACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;AAClC,MAAA,IAAI,CAACjB,yBAAyB,CAAC,IAAI,CAAC,CAAA;AACpC,MAAA,OAAA;AACF,KAAA;AACA,IAAA,IAAI,CAACA,yBAAyB,CAAC,IAAI,CAAC,CAAA;AACtC,GAAA;AAEO1T,EAAAA,aAAa,GAAG;IACrB,OAAO;AAAE,MAAA,GAAG,IAAI,CAACqS,IAAI,CAACtS,UAAU;AAAE,MAAA,GAAG,IAAI,CAACiT,mBAAAA;KAAqB,CAAA;AACjE,GAAA;AAEO1S,EAAAA,mBAAmB,GAAG;AAC3B,IAAA,OAAO,IAAI,CAAC+R,IAAI,CAAChP,gBAAgB,IAAI,EAAE,CAAA;AACzC,GAAA;AAEOE,EAAAA,iBAAiB,GAAG;AACzB;AACA,IAAA,OAAO,IAAI,CAACwP,oBAAoB,IAAI,IAAI5W,GAAG,EAAe,CAAA;AAC5D,GAAA;AAEO8Y,EAAAA,6BAA6B,GAAG;AACrC,IAAA,OAAO,IAAI,CAAC5C,IAAI,CAAC6C,0BAA0B,IAAI,EAAE,CAAA;AACnD,GAAA;AAEO1U,EAAAA,MAAM,GAAG;AACd,IAAA,OAAO,IAAI,CAAC6R,IAAI,CAAC9R,GAAG,IAAI,EAAE,CAAA;AAC5B,GAAA;AAEOwC,EAAAA,WAAW,GAAG;AACnB,IAAA,OAAO,IAAI,CAACsP,IAAI,CAACvP,QAAQ,IAAI,EAAE,CAAA;AACjC,GAAA;AAEOF,EAAAA,cAAc,GAAG;AACtB,IAAA,OAAO,IAAI,CAACyP,IAAI,CAAC1P,WAAW,IAAI,EAAE,CAAA;AACpC,GAAA;EAEO/E,SAAS,CAACiG,EAAwB,EAAc;AACrD,IAAA,IAAI,CAAC6O,cAAc,CAACzU,GAAG,CAAC4F,EAAE,CAAC,CAAA;AAC3B,IAAA,OAAO,MAAM;AACX,MAAA,IAAI,CAAC6O,cAAc,CAACpU,MAAM,CAACuF,EAAE,CAAC,CAAA;KAC/B,CAAA;AACH,GAAA;AAEQkQ,EAAAA,aAAa,GAAG;AACtB,IAAA,OAAO,IAAI,CAAC1B,IAAI,CAAC7Y,cAAc,KAAK,KAAK,IAAI,IAAI,CAAC6Y,IAAI,CAACyB,kBAAkB,CAAA;AAC3E,GAAA;AAEA,EAAA,MAAca,qBAAqB,GAAG;AACpC,IAAA,IAAI,CAAC,IAAI,CAACtC,IAAI,CAAClP,UAAU,EAAE,OAAA;AAC3B,IAAA,IAAI,CAAC,IAAI,CAACgQ,mBAAmB,EAAE,OAAA;AAC/B,IAAA,MAAM,IAAI,CAACQ,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC3S,KAAK,CAAC,MAAM;AAC/C;AAAA,KACD,CAAC,CAAA;AACJ,GAAA;AAEOmU,EAAAA,aAAa,GAAG;AACrB,IAAA,OAAO,IAAIhZ,GAAG,CAAC,IAAI,CAAC2W,SAAS,CAAC,CAAA;AAChC,GAAA;AAEOsC,EAAAA,OAAO,GAAG;AACf;AACA,IAAA,IAAI,CAAC1C,cAAc,CAACzV,KAAK,EAAE,CAAA;AAC3B,IAAA,IAAI,CAAC6V,SAAS,CAAC7V,KAAK,EAAE,CAAA;AACtB,IAAA,IAAI,CAACsV,mBAAmB,CAACtV,KAAK,EAAE,CAAA;AAChC,IAAA,IAAI,CAACuV,gBAAgB,GAAG,EAAE,CAAA;IAC1B,IAAI,CAACG,QAAQ,GAAG,EAAE,CAAA;IAClB,IAAI,IAAI,CAACC,QAAQ,EAAE;AACjBnX,MAAAA,YAAY,CAAC,IAAI,CAACmX,QAAQ,CAAC,CAAA;AAC7B,KAAA;IACAzU,WAAW,CAAC,IAAI,CAAC,CAAA;AAEjB,IAAA,IAAI/C,SAAS,IAAIC,MAAM,CAACkY,WAAW,KAAK,IAAI,EAAE;MAC5C,OAAOlY,MAAM,CAACkY,WAAW,CAAA;AAC3B,KAAA;;AAEA;AACA,IAAA,IAAI,CAACN,sBAAsB,CAAC7U,OAAO,CAAEiX,GAAG,IAAK;MAC3CA,GAAG,CAACC,IAAI,EAAE,CAAA;AACZ,KAAC,CAAC,CAAA;AACF,IAAA,IAAI,CAACrC,sBAAsB,CAAChW,KAAK,EAAE,CAAA;AACnC,IAAA,IAAI,CAACiW,iBAAiB,CAACjW,KAAK,EAAE,CAAA;AAChC,GAAA;EAEOsY,WAAW,CAACC,QAAoB,EAAE;IACvC,IAAI,CAAClD,SAAS,GAAGkD,QAAQ,CAAA;AAC3B,GAAA;AAEOC,EAAAA,cAAc,CAAC5X,GAAW,EAAE6X,SAAiB,EAAE;AACpD,IAAA,IAAI,CAACrD,IAAI,CAAChP,gBAAgB,GAAG,IAAI,CAACgP,IAAI,CAAChP,gBAAgB,IAAI,EAAE,CAAA;IAC7D,IAAI,CAACgP,IAAI,CAAChP,gBAAgB,CAACxF,GAAG,CAAC,GAAG6X,SAAS,CAAA;AAC3C,IAAA,IAAI,IAAI,CAACrD,IAAI,CAAClP,UAAU,EAAE;MACxB,IAAI,CAACwR,qBAAqB,EAAE,CAAA;AAC5B,MAAA,OAAA;AACF,KAAA;IACA,IAAI,CAACjB,yBAAyB,EAAE,CAAA;IAChC,IAAI,CAACQ,OAAO,EAAE,CAAA;AAChB,GAAA;EAEOyB,GAAG,CAAIC,UAAyB,EAAa;IAClD,MAAMC,MAAM,GAAG,IAAI,CAACC,IAAI,CAACF,UAAU,EAAE,IAAI,CAAC,CAAA;AAC1C,IAAA,IAAI,CAACG,kBAAkB,CAACH,UAAU,EAAEC,MAAM,CAAC,CAAA;AAC3C,IAAA,OAAOA,MAAM,CAAA;AACf,GAAA;EAEOG,iBAAiB,CAACnY,GAAW,EAAE;AACpC,IAAA,IAAI,CAACqV,iBAAiB,CAACjV,GAAG,CAACJ,GAAG,CAAC,CAAA;IAC/B,IAAI,CAAC,IAAI,CAACwU,IAAI,CAAC1P,WAAW,EAAE,OAAO,IAAI,CAAA;AACvC,IAAA,MAAMA,WAAW,GAAG,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,CAACuE,MAAM,CAAEmO,GAAG,IAAKA,GAAG,CAACxX,GAAG,KAAKA,GAAG,CAAC,CAAA;AAC1E,IAAA,OAAO8E,WAAW,CACflB,GAAG,CAAE4T,GAAG,IAAK;AACZ,MAAA,IAAI,CAACA,GAAG,CAACY,MAAM,EAAE,OAAO,IAAI,CAAA;AAC5B,MAAA,OAAO,IAAI,CAACC,kBAAkB,CAACb,GAAG,CAAC,CAAA;KACpC,CAAC,CACDnO,MAAM,CAAE1D,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAAA;AAClC,GAAA;AAEQ0S,EAAAA,kBAAkB,CAACN,UAA0B,EAAEO,UAAoB,EAAE;IAC3E,MAAM9W,QAAQ,GAAG,IAAI,CAAC4T,sBAAsB,CAACjV,GAAG,CAAC4X,UAAU,CAAC,CAAA;;AAE5D;IACA,IACEA,UAAU,CAACK,MAAM,IACjB,CAAC,IAAI,CAAC/C,iBAAiB,CAAC5Q,GAAG,CAACsT,UAAU,CAAC/X,GAAG,CAAC,IAC3C,CAACwB,QAAQ,EAET,OAAO,IAAI,CAAA;;AAEb;AACA,IAAA,MAAMwW,MAAM,GAAG,IAAI,CAACF,GAAG,CAACC,UAAU,CAAC,CAAA;;AAEnC;IACA,MAAMQ,SAAS,GAAGrb,IAAI,CAACC,SAAS,CAAC6a,MAAM,CAAC5U,KAAK,CAAC,CAAA;;AAE9C;AACA,IAAA,IACE,CAACkV,UAAU,IACXN,MAAM,CAACQ,YAAY,IACnBhX,QAAQ,IACRA,QAAQ,CAAC+W,SAAS,KAAKA,SAAS,EAChC;AACA,MAAA,OAAOP,MAAM,CAAA;AACf,KAAA;;AAEA;AACA,IAAA,IAAIxW,QAAQ,EAAE,IAAI,CAACiX,yBAAyB,CAACV,UAAU,CAAC,CAAA;;AAExD;IACA,IAAIC,MAAM,CAACQ,YAAY,EAAE;MACvB,MAAMf,IAAI,GAAG,IAAI,CAACiB,gBAAgB,CAACV,MAAM,CAAC5U,KAAK,CAAC,CAAA;AAChD,MAAA,IAAIqU,IAAI,EAAE;AACR,QAAA,IAAI,CAACrC,sBAAsB,CAAC/U,GAAG,CAAC0X,UAAU,EAAE;UAC1CN,IAAI;AACJc,UAAAA,SAAAA;AACF,SAAC,CAAC,CAAA;AACJ,OAAA;AACF,KAAA;AAEA,IAAA,OAAOP,MAAM,CAAA;AACf,GAAA;EAEQS,yBAAyB,CAACjB,GAAmB,EAAE;IACrD,MAAM5X,IAAI,GAAG,IAAI,CAACwV,sBAAsB,CAACjV,GAAG,CAACqX,GAAG,CAAC,CAAA;AACjD,IAAA,IAAI5X,IAAI,EAAE;MACRA,IAAI,CAAC6X,IAAI,EAAE,CAAA;AACX,MAAA,IAAI,CAACrC,sBAAsB,CAAC3U,MAAM,CAAC+W,GAAG,CAAC,CAAA;AACzC,KAAA;AACF,GAAA;EAEQ3B,yBAAyB,CAACyC,UAAoB,EAAE;IACtD,MAAMxT,WAAW,GAAG,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,IAAI,EAAE,CAAA;;AAE/C;AACA,IAAA,MAAMxC,IAAI,GAAG,IAAI1D,GAAG,CAACkG,WAAW,CAAC,CAAA;IACjC,IAAI,CAACsQ,sBAAsB,CAAC7U,OAAO,CAAC,CAAC4P,CAAC,EAAEC,CAAC,KAAK;AAC5C,MAAA,IAAI,CAAC9N,IAAI,CAACmC,GAAG,CAAC2L,CAAC,CAAC,EAAE;QAChBD,CAAC,CAACsH,IAAI,EAAE,CAAA;AACR,QAAA,IAAI,CAACrC,sBAAsB,CAAC3U,MAAM,CAAC2P,CAAC,CAAC,CAAA;AACvC,OAAA;AACF,KAAC,CAAC,CAAA;;AAEF;AACAtL,IAAAA,WAAW,CAACvE,OAAO,CAAEiX,GAAG,IAAK;AAC3B,MAAA,IAAI,CAACa,kBAAkB,CAACb,GAAG,EAAEc,UAAU,CAAC,CAAA;AAC1C,KAAC,CAAC,CAAA;AACJ,GAAA;AAEQJ,EAAAA,kBAAkB,CAAIH,UAAyB,EAAEC,MAAiB,EAAE;AAC1E,IAAA,MAAMhY,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;;AAE1B;IACA,MAAM2Y,IAAI,GAAG,IAAI,CAAC1D,SAAS,CAAC9U,GAAG,CAACH,GAAG,CAAC,CAAA;AACpC;IACA,IACE,CAAC2Y,IAAI,IACLA,IAAI,CAACX,MAAM,CAACQ,YAAY,KAAKR,MAAM,CAACQ,YAAY,IAChDG,IAAI,CAACX,MAAM,CAACY,WAAW,KAAKZ,MAAM,CAACY,WAAW,EAC9C;AACA,MAAA,IAAI,CAAC3D,SAAS,CAAC5U,GAAG,CAACL,GAAG,EAAE;QAAE+X,UAAU;AAAEC,QAAAA,MAAAA;AAAO,OAAC,CAAC,CAAA;AAC/C,MAAA,IAAI,CAACnD,cAAc,CAACtU,OAAO,CAAEyF,EAAE,IAAK;QAClC,IAAI;AACFA,UAAAA,EAAE,CAAC+R,UAAU,EAAEC,MAAM,CAAC,CAAA;SACvB,CAAC,OAAO5Z,CAAC,EAAE;AACV2Q,UAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;AAClB,SAAA;AACF,OAAC,CAAC,CAAA;AACJ,KAAA;AACF,GAAA;AAEQya,EAAAA,kBAAkB,CAAC7Y,GAAW,EAAE2F,GAAkB,EAAQ;AAChE;AACA,IAAA,IAAIA,GAAG,CAACmT,MAAM,KAAK,UAAU,EAAE,OAAA;;AAE/B;IACA,MAAMC,gBAAgB,GAAG7b,IAAI,CAACC,SAAS,CAACwI,GAAG,CAACvC,KAAK,CAAC,CAAA;IAClD,IAAI,IAAI,CAACuR,gBAAgB,CAAC3U,GAAG,CAAC,KAAK+Y,gBAAgB,EAAE,OAAA;AACrD,IAAA,IAAI,CAACpE,gBAAgB,CAAC3U,GAAG,CAAC,GAAG+Y,gBAAgB,CAAA;;AAE7C;AACA,IAAA,IAAI,IAAI,CAACvE,IAAI,CAACwE,cAAc,EAAE;MAC5B,IAAI;QACF,IAAI,CAACxE,IAAI,CAACwE,cAAc,CAAChZ,GAAG,EAAE2F,GAAG,CAAC,CAAA;OACnC,CAAC,OAAOvH,CAAC,EAAE;AACV;AAAA,OAAA;AAEJ,KAAA;;AAEA;AACA,IAAA,IAAI,CAACb,SAAS,IAAI,CAACC,MAAM,CAACxB,KAAK,EAAE,OAAA;AACjC,IAAA,IAAI,CAAC8Y,QAAQ,CAAC1I,IAAI,CAAC;MACjBpM,GAAG;MACHiZ,EAAE,EAAEtT,GAAG,CAACsT,EAAAA;AACV,KAAC,CAAC,CAAA;AACF,IAAA,IAAI,CAAC,IAAI,CAAClE,QAAQ,EAAE;AAClB,MAAA,IAAI,CAACA,QAAQ,GAAGvX,MAAM,CAACM,UAAU,CAAC,MAAM;AACtC;QACA,IAAI,CAACiX,QAAQ,GAAG,CAAC,CAAA;AACjB,QAAA,MAAMmE,CAAC,GAAG,CAAC,GAAG,IAAI,CAACpE,QAAQ,CAAC,CAAA;QAC5B,IAAI,CAACA,QAAQ,GAAG,EAAE,CAAA;;AAElB;AACA,QAAA,IAAI,CAAC,IAAI,CAACN,IAAI,CAAC2E,WAAW,EAAE,OAAA;AAE5B3b,QAAAA,MAAM,CACHxB,KAAK,CAAA,gCAAA,CAAA,MAAA,CAEF,IAAI,CAACwY,IAAI,CAAC2E,WAAW,EAAA,UAAA,CAAA,CAAA,MAAA,CACZC,kBAAkB,CAAClc,IAAI,CAACC,SAAS,CAAC+b,CAAC,CAAC,CAAC,CAEhD,EAAA;AACE1a,UAAAA,KAAK,EAAE,UAAU;AACjB6a,UAAAA,IAAI,EAAE,SAAA;AACR,SAAC,CACF,CACAlW,KAAK,CAAC,MAAM;AACX;AAAA,SACD,CAAC,CAAA;OACL,EAAE,IAAI,CAACqR,IAAI,CAAC8E,gBAAgB,IAAI,IAAI,CAAC,CAAA;AACxC,KAAA;AACF,GAAA;AAEQC,EAAAA,iBAAiB,CACvBvZ,GAAW,EACXoD,KAAQ,EACR0V,MAA2B,EAC3BU,MAAe,EACfzB,UAA0B,EAC1BC,MAAkB,EACA;AAClB,IAAA,MAAMyB,GAAkB,GAAG;MACzBrW,KAAK;MACL6V,EAAE,EAAE,CAAC,CAAC7V,KAAK;MACXsW,GAAG,EAAE,CAACtW,KAAK;MACX0V,MAAM;MACNU,MAAM,EAAEA,MAAM,IAAI,EAAA;KACnB,CAAA;AACD,IAAA,IAAIzB,UAAU,EAAE0B,GAAG,CAAC1B,UAAU,GAAGA,UAAU,CAAA;AAC3C,IAAA,IAAIC,MAAM,EAAEyB,GAAG,CAACE,gBAAgB,GAAG3B,MAAM,CAAA;;AAEzC;AACA,IAAA,IAAI,CAACa,kBAAkB,CAAC7Y,GAAG,EAAEyZ,GAAG,CAAC,CAAA;AAEjC,IAAA,OAAOA,GAAG,CAAA;AACZ,GAAA;EAEOG,IAAI,CAAgD5Z,GAAM,EAAW;AAC1E,IAAA,OAAO,IAAI,CAAC6Z,WAAW,CAAC7Z,GAAG,CAAC,CAACiZ,EAAE,CAAA;AACjC,GAAA;EAEOa,KAAK,CAAgD9Z,GAAM,EAAW;AAC3E,IAAA,OAAO,IAAI,CAAC6Z,WAAW,CAAC7Z,GAAG,CAAC,CAAC0Z,GAAG,CAAA;AAClC,GAAA;AAEOK,EAAAA,eAAe,CAGpB/Z,GAAM,EAAEga,YAAe,EAAsB;IAC7C,MAAM5W,KAAK,GAAG,IAAI,CAACyW,WAAW,CAAwB7Z,GAAG,CAAC,CAACoD,KAAK,CAAA;AAChE,IAAA,OAAOA,KAAK,KAAK,IAAI,GAAI4W,YAAY,GAA0B5W,KAAK,CAAA;AACtE,GAAA;;AAEA;AACF;AACA;AACA;AACE;EACO6W,OAAO,CAGZ5I,EAAK,EAA2B;AAChC,IAAA,OAAO,IAAI,CAACwI,WAAW,CAACxI,EAAE,CAAC,CAAA;AAC7B,GAAA;EAEOwI,WAAW,CAGhBxI,EAAK,EAA2B;AAChC;IACA,IAAI,IAAI,CAAC6D,oBAAoB,CAACzQ,GAAG,CAAC4M,EAAE,CAAC,EAAE;AAMrC,MAAA,OAAO,IAAI,CAACkI,iBAAiB,CAC3BlI,EAAE,EACF,IAAI,CAAC6D,oBAAoB,CAAC/U,GAAG,CAACkR,EAAE,CAAC,EACjC,UAAU,CACX,CAAA;AACH,KAAA;;AAEA;AACA,IAAA,IAAI,CAAC,IAAI,CAACmD,IAAI,CAACvP,QAAQ,IAAI,CAAC,IAAI,CAACuP,IAAI,CAACvP,QAAQ,CAACoM,EAAE,CAAC,EAAE;MAGlD,OAAO,IAAI,CAACkI,iBAAiB,CAAClI,EAAE,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAA;AAC3D,KAAA;;AAEA;IACA,MAAM4I,OAA6B,GAAG,IAAI,CAACzF,IAAI,CAACvP,QAAQ,CAACoM,EAAE,CAAC,CAAA;;AAE5D;IACA,IAAI4I,OAAO,CAACC,KAAK,EAAE;AACjB,MAAA,KAAK,MAAMC,IAAI,IAAIF,OAAO,CAACC,KAAK,EAAE;AAChC;AACA,QAAA,IAAIC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,cAAc,CAACF,IAAI,CAACC,OAAO,CAAC,EAAE;AAMrD,UAAA,SAAA;AACF,SAAA;;AAEA;QACA,IAAI,OAAO,IAAID,IAAI,EAAE;AACnB;AACA,UAAA,IAAIA,IAAI,CAACnH,SAAS,IAAI,CAAC,IAAI,CAACsH,gBAAgB,CAACH,IAAI,CAACnH,SAAS,CAAC,EAAE;AAM5D,YAAA,SAAA;AACF,WAAA;;AAEA;UACA,IACE,CAAC,IAAI,CAACuH,oBAAoB,CACxBJ,IAAI,CAACpM,IAAI,IAAIsD,EAAE,EACf8I,IAAI,CAACK,aAAa,EAClB,IAAI,CAAChG,IAAI,CAACqC,mBAAmB,IAAI,CAACsD,IAAI,CAACM,sBAAsB,GACzDN,IAAI,CAACO,iBAAiB,GACtBve,SAAS,EACbge,IAAI,CAAC/L,KAAK,EACV+L,IAAI,CAACxJ,QAAQ,EACbwJ,IAAI,CAACQ,WAAW,CACjB,EACD;AAMA,YAAA,SAAA;AACF,WAAA;;AAQA;UACA,IAAIR,IAAI,CAACS,MAAM,EAAE;AACfT,YAAAA,IAAI,CAACS,MAAM,CAACra,OAAO,CAAEqT,CAAC,IAAK;cACzB,IAAI,CAACiH,MAAM,CAACjH,CAAC,CAACmE,UAAU,EAAEnE,CAAC,CAACoE,MAAM,CAAC,CAAA;AACrC,aAAC,CAAC,CAAA;AACJ,WAAA;AAEA,UAAA,OAAO,IAAI,CAACuB,iBAAiB,CAAClI,EAAE,EAAE8I,IAAI,CAACW,KAAK,EAAO,OAAO,EAAEX,IAAI,CAAC9I,EAAE,CAAC,CAAA;AACtE,SAAA;AACA,QAAA,IAAI,CAAC8I,IAAI,CAACY,UAAU,EAAE;AAOpB,UAAA,SAAA;AACF,SAAA;;AAEA;AACA,QAAA,MAAMvD,GAAkB,GAAG;UACzBuD,UAAU,EAAEZ,IAAI,CAACY,UAA4B;AAC7C/a,UAAAA,GAAG,EAAEma,IAAI,CAACna,GAAG,IAAIqR,EAAAA;SAClB,CAAA;QACD,IAAI,UAAU,IAAI8I,IAAI,EAAE3C,GAAG,CAAC7G,QAAQ,GAAGwJ,IAAI,CAACxJ,QAAQ,CAAA;QACpD,IAAIwJ,IAAI,CAACvJ,OAAO,EAAE4G,GAAG,CAAC5G,OAAO,GAAGuJ,IAAI,CAACvJ,OAAO,CAAA;QAC5C,IAAIuJ,IAAI,CAACK,aAAa,EAAEhD,GAAG,CAACgD,aAAa,GAAGL,IAAI,CAACK,aAAa,CAAA;QAC9D,IAAIL,IAAI,CAACO,iBAAiB,EACxBlD,GAAG,CAACkD,iBAAiB,GAAGP,IAAI,CAACO,iBAAiB,CAAA;QAChD,IAAIP,IAAI,CAACM,sBAAsB,EAC7BjD,GAAG,CAACiD,sBAAsB,GAAGN,IAAI,CAACM,sBAAsB,CAAA;AAC1D,QAAA,IAAIN,IAAI,CAACa,aAAa,KAAK7e,SAAS,EAClCqb,GAAG,CAACwD,aAAa,GAAGb,IAAI,CAACa,aAAa,CAAA;AACxC,QAAA,IAAIb,IAAI,CAACc,gBAAgB,KAAK9e,SAAS,EACrCqb,GAAG,CAACyD,gBAAgB,GAAGd,IAAI,CAACc,gBAAgB,CAAA;QAC9C,IAAId,IAAI,CAAC5L,SAAS,EAAEiJ,GAAG,CAACjJ,SAAS,GAAG4L,IAAI,CAAC5L,SAAS,CAAA;QAClD,IAAI4L,IAAI,CAACe,IAAI,EAAE1D,GAAG,CAAC0D,IAAI,GAAGf,IAAI,CAACe,IAAI,CAAA;QACnC,IAAIf,IAAI,CAAC1L,MAAM,EAAE+I,GAAG,CAAC/I,MAAM,GAAG0L,IAAI,CAAC1L,MAAM,CAAA;QACzC,IAAI0L,IAAI,CAAClI,IAAI,EAAEuF,GAAG,CAACvF,IAAI,GAAGkI,IAAI,CAAClI,IAAI,CAAA;QACnC,IAAIkI,IAAI,CAACgB,KAAK,EAAE3D,GAAG,CAAC2D,KAAK,GAAGhB,IAAI,CAACgB,KAAK,CAAA;QACtC,IAAIhB,IAAI,CAACpM,IAAI,EAAEyJ,GAAG,CAACzJ,IAAI,GAAGoM,IAAI,CAACpM,IAAI,CAAA;QACnC,IAAIoM,IAAI,CAACQ,WAAW,EAAEnD,GAAG,CAACmD,WAAW,GAAGR,IAAI,CAACQ,WAAW,CAAA;QACxD,IAAIR,IAAI,CAACC,OAAO,EAAE5C,GAAG,CAAC4C,OAAO,GAAGD,IAAI,CAACC,OAAO,CAAA;QAC5C,IAAID,IAAI,CAACnH,SAAS,EAAEwE,GAAG,CAACxE,SAAS,GAAGmH,IAAI,CAACnH,SAAS,CAAA;;AAElD;QACA,MAAMrN,GAAG,GAAG,IAAI,CAACsS,IAAI,CAACT,GAAG,EAAEnG,EAAE,CAAC,CAAA;AAC9B,QAAA,IAAI,CAAC6G,kBAAkB,CAACV,GAAG,EAAE7R,GAAG,CAAC,CAAA;QACjC,IAAIA,GAAG,CAAC6S,YAAY,IAAI,CAAC7S,GAAG,CAACyV,WAAW,EAAE;AACxC,UAAA,OAAO,IAAI,CAAC7B,iBAAiB,CAC3BlI,EAAE,EACF1L,GAAG,CAACvC,KAAK,EACT,YAAY,EACZ+W,IAAI,CAAC9I,EAAE,EACPmG,GAAG,EACH7R,GAAG,CACJ,CAAA;AACH,SAAA;AACF,OAAA;AACF,KAAA;;AAQA;AACA,IAAA,OAAO,IAAI,CAAC4T,iBAAiB,CAC3BlI,EAAE,EACF4I,OAAO,CAACD,YAAY,KAAK7d,SAAS,GAAG,IAAI,GAAG8d,OAAO,CAACD,YAAY,EAChE,cAAc,CACf,CAAA;AACH,GAAA;AAEQO,EAAAA,oBAAoB,CAC1BxM,IAAY,EACZyM,aAAiC,EACjCE,iBAAqC,EACrCtM,KAAiC,EACjCuC,QAA4B,EAC5BgK,WAA+B,EACtB;IACT,IAAI,CAACvM,KAAK,IAAIuC,QAAQ,KAAKxU,SAAS,EAAE,OAAO,IAAI,CAAA;IAEjD,MAAM;AAAEmS,MAAAA,SAAAA;KAAW,GAAG,IAAI,CAAC+M,iBAAiB,CAC1Cb,aAAa,EACbE,iBAAiB,CAClB,CAAA;IACD,IAAI,CAACpM,SAAS,EAAE;AACd,MAAA,OAAO,KAAK,CAAA;AACd,KAAA;IAEA,MAAML,CAAC,GAAGH,IAAI,CAACC,IAAI,EAAEO,SAAS,EAAEqM,WAAW,IAAI,CAAC,CAAC,CAAA;AACjD,IAAA,IAAI1M,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,CAAA;AAE5B,IAAA,OAAOG,KAAK,GACRD,OAAO,CAACF,CAAC,EAAEG,KAAK,CAAC,GACjBuC,QAAQ,KAAKxU,SAAS,GACtB8R,CAAC,IAAI0C,QAAQ,GACb,IAAI,CAAA;AACV,GAAA;EAEQ2J,gBAAgB,CAACtH,SAA6B,EAAW;IAC/D,OAAOF,aAAa,CAAC,IAAI,CAAC3Q,aAAa,EAAE,EAAE6Q,SAAS,CAAC,CAAA;AACvD,GAAA;EAEQqH,cAAc,CAACD,OAAiB,EAAW;AACjD,IAAA,OAAOA,OAAO,CAAC/J,IAAI,CAAEhH,MAAM,IAAK;MAC9B,MAAM;AAAEiF,QAAAA,SAAAA;OAAW,GAAG,IAAI,CAAC+M,iBAAiB,CAAChS,MAAM,CAAC8C,SAAS,CAAC,CAAA;AAC9D,MAAA,IAAI,CAACmC,SAAS,EAAE,OAAO,IAAI,CAAA;AAC3B,MAAA,MAAML,CAAC,GAAGH,IAAI,CAACzE,MAAM,CAAC0E,IAAI,EAAEO,SAAS,EAAEjF,MAAM,CAACsR,WAAW,IAAI,CAAC,CAAC,CAAA;AAC/D,MAAA,IAAI1M,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,CAAA;AAC3B,MAAA,OAAO,CAAC5E,MAAM,CAACoF,MAAM,CAAC4B,IAAI,CAAEiL,CAAC,IAAKnN,OAAO,CAACF,CAAC,EAAEqN,CAAC,CAAC,CAAC,CAAA;AAClD,KAAC,CAAC,CAAA;AACJ,GAAA;AAEQrD,EAAAA,IAAI,CACVF,UAAyB,EACzBwD,SAAwB,EACb;AACX,IAAA,MAAMvb,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;AAC1B,IAAA,MAAM0Q,aAAa,GAAGqH,UAAU,CAACgD,UAAU,CAAChP,MAAM,CAAA;;AAElD;IACA,IAAI2E,aAAa,GAAG,CAAC,EAAE;AAGrB,MAAA,OAAO,IAAI,CAAC8K,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;AACA,IAAA,IAAI,IAAI,CAAC/G,IAAI,CAACiH,OAAO,KAAK,KAAK,EAAE;AAG/B,MAAA,OAAO,IAAI,CAACD,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;AACAxD,IAAAA,UAAU,GAAG,IAAI,CAAC2D,eAAe,CAAC3D,UAAU,CAAC,CAAA;;AAE7C;AACA,IAAA,IACEA,UAAU,CAAC4D,WAAW,IACtB,CAAC1M,aAAa,CAAC,IAAI,CAAC2M,cAAc,EAAE,EAAE7D,UAAU,CAAC4D,WAAW,CAAC,EAC7D;AAKA,MAAA,OAAO,IAAI,CAACH,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;AACA,IAAA,MAAMM,UAAU,GAAGzK,sBAAsB,CACvCpR,GAAG,EACH,IAAI,CAAC4b,cAAc,EAAE,EACrBlL,aAAa,CACd,CAAA;IACD,IAAImL,UAAU,KAAK,IAAI,EAAE;MAMvB,OAAO,IAAI,CAACL,UAAU,CAACzD,UAAU,EAAE8D,UAAU,EAAE,KAAK,EAAEN,SAAS,CAAC,CAAA;AAClE,KAAA;;AAEA;AACA,IAAA,IAAI,IAAI,CAAC/G,IAAI,CAAChP,gBAAgB,IAAIxF,GAAG,IAAI,IAAI,CAACwU,IAAI,CAAChP,gBAAgB,EAAE;MACnE,MAAMqS,SAAS,GAAG,IAAI,CAACrD,IAAI,CAAChP,gBAAgB,CAACxF,GAAG,CAAC,CAAA;MAMjD,OAAO,IAAI,CAACwb,UAAU,CAACzD,UAAU,EAAEF,SAAS,EAAE,KAAK,EAAE0D,SAAS,CAAC,CAAA;AACjE,KAAA;;AAEA;IACA,IAAIxD,UAAU,CAAC+D,MAAM,KAAK,OAAO,IAAI/D,UAAU,CAACgE,MAAM,KAAK,KAAK,EAAE;AAKhE,MAAA,OAAO,IAAI,CAACP,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;IACA,MAAM;MAAEf,aAAa;AAAElM,MAAAA,SAAAA;KAAW,GAAG,IAAI,CAAC+M,iBAAiB,CACzDtD,UAAU,CAACyC,aAAa,EACxB,IAAI,CAAChG,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,GAC/D1C,UAAU,CAAC2C,iBAAiB,GAC5Bve,SAAS,CACd,CAAA;IACD,IAAI,CAACmS,SAAS,EAAE;AAKd,MAAA,OAAO,IAAI,CAACkN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;IAEA,IAAIS,QAAQ,GAAG,CAAC,CAAC,CAAA;IAEjB,IAAIC,iBAAiB,GAAG,KAAK,CAAA;IAC7B,IAAIC,4BAA4B,GAAG,KAAK,CAAA;IACxC,IAAI,IAAI,CAAC1H,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,EAAE;MACvE,MAAM;QAAE5C,SAAS;AAAEsE,QAAAA,gBAAAA;OAAkB,GAAG,IAAI,CAACC,yBAAyB,CACpErE,UAAU,CAAC/X,GAAG,EACd+X,UAAU,CAACiD,aAAa,EACxBjD,UAAU,CAACkD,gBAAgB,EAC3BlD,UAAU,CAACmD,IAAI,CAChB,CAAA;MACDe,iBAAiB,GAAGpE,SAAS,IAAI,CAAC,CAAA;AAClCmE,MAAAA,QAAQ,GAAGnE,SAAS,CAAA;MACpBqE,4BAA4B,GAAG,CAAC,CAACC,gBAAgB,CAAA;AACnD,KAAA;;AAEA;IACA,IAAI,CAACF,iBAAiB,EAAE;AACtB;MACA,IAAIlE,UAAU,CAACqC,OAAO,EAAE;QACtB,IAAI,IAAI,CAACC,cAAc,CAACtC,UAAU,CAACqC,OAAO,CAAC,EAAE;AAK3C,UAAA,OAAO,IAAI,CAACoB,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,SAAA;AACF,OAAC,MAAM,IACLxD,UAAU,CAACxJ,SAAS,IACpB,CAACF,WAAW,CAACC,SAAS,EAAEyJ,UAAU,CAACxJ,SAAS,CAAC,EAC7C;AAKA,QAAA,OAAO,IAAI,CAACiN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,OAAA;;AAEA;MACA,IAAIxD,UAAU,CAACvI,OAAO,IAAI,CAACJ,UAAU,CAAC2I,UAAU,CAACvI,OAAO,CAAC,EAAE;AAKzD,QAAA,OAAO,IAAI,CAACgM,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,OAAA;;AAEA;AACA,MAAA,IACExD,UAAU,CAAC/E,SAAS,IACpB,CAAC,IAAI,CAACsH,gBAAgB,CAACvC,UAAU,CAAC/E,SAAS,CAAC,EAC5C;AAKA,QAAA,OAAO,IAAI,CAACwI,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,OAAA;;AAEA;AACA,MAAA,IACExD,UAAU,CAACsE,MAAM,IACjB,CAAC,IAAI,CAACC,gBAAgB,CAACvE,UAAU,CAACsE,MAAM,CAAa,EACrD;AAKA,QAAA,OAAO,IAAI,CAACb,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAIxD,UAAU,CAACrV,GAAG,IAAI,CAAC,IAAI,CAAC6Z,WAAW,CAACxE,UAAU,CAACrV,GAAG,CAAW,EAAE;AAKjE,MAAA,OAAO,IAAI,CAAC8Y,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;AACA,IAAA,MAAMtN,CAAC,GAAGH,IAAI,CACZiK,UAAU,CAAChK,IAAI,IAAI/N,GAAG,EACtBsO,SAAS,EACTyJ,UAAU,CAAC4C,WAAW,IAAI,CAAC,CAC5B,CAAA;IACD,IAAI1M,CAAC,KAAK,IAAI,EAAE;AAKd,MAAA,OAAO,IAAI,CAACuN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;IAEA,IAAI,CAACU,iBAAiB,EAAE;MACtB,MAAMxN,MAAM,GACVsJ,UAAU,CAACtJ,MAAM,IACjBgC,eAAe,CACbC,aAAa,EACbqH,UAAU,CAACpH,QAAQ,KAAKxU,SAAS,GAAG,CAAC,GAAG4b,UAAU,CAACpH,QAAQ,EAC3DoH,UAAU,CAACnH,OAAO,CACnB,CAAA;AACHoL,MAAAA,QAAQ,GAAGxN,eAAe,CAACP,CAAC,EAAEQ,MAAM,CAAC,CAAA;AACvC,KAAA;;AAEA;AACA,IAAA,IAAIyN,4BAA4B,EAAE;AAKhC,MAAA,OAAO,IAAI,CAACV,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,EAAEpf,SAAS,EAAE,IAAI,CAAC,CAAA;AAC3E,KAAA;;AAEA;IACA,IAAI6f,QAAQ,GAAG,CAAC,EAAE;AAKhB,MAAA,OAAO,IAAI,CAACR,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;IACA,IAAI,OAAO,IAAIxD,UAAU,EAAE;MAMzB,OAAO,IAAI,CAACyD,UAAU,CACpBzD,UAAU,EACVA,UAAU,CAAC+C,KAAK,KAAK3e,SAAS,GAAG,CAAC,CAAC,GAAG4b,UAAU,CAAC+C,KAAK,EACtD,KAAK,EACLS,SAAS,CACV,CAAA;AACH,KAAA;;AAEA;AACA,IAAA,IAAI,IAAI,CAAC/G,IAAI,CAACgI,MAAM,EAAE;AAKpB,MAAA,OAAO,IAAI,CAAChB,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;AACA,IAAA,IAAIxD,UAAU,CAAC+D,MAAM,KAAK,SAAS,EAAE;AAKnC,MAAA,OAAO,IAAI,CAACN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;AAC1D,KAAA;;AAEA;AACA,IAAA,MAAMvD,MAAM,GAAG,IAAI,CAACwD,UAAU,CAC5BzD,UAAU,EACViE,QAAQ,EACR,IAAI,EACJT,SAAS,EACTtN,CAAC,EACDgO,iBAAiB,CAClB,CAAA;;AAED;IACA,IAAI,IAAI,CAACzH,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,EAAE;MACvE,MAAM;QACJgC,OAAO;AACPzc,QAAAA,GAAG,EAAE0c,OAAO;AACZC,QAAAA,GAAAA;OACD,GAAG,IAAI,CAACC,kCAAkC,CACzCpC,aAAa,EACbjI,QAAQ,CAACjE,SAAS,CAAC,EACnB;AACE,QAAA,CAAC,IAAI,CAACuO,6BAA6B,CACjC9E,UAAU,CAAC/X,GAAG,EACd+X,UAAU,CAACiD,aAAa,CACzB,GAAGhD,MAAM,CAAChY,GAAAA;AACb,OAAC,CACF,CAAA;AACD,MAAA,IAAIyc,OAAO,EAAE;AACX;AACA,QAAA,IAAI,CAACjI,IAAI,CAAC6C,0BAA0B,GAClC,IAAI,CAAC7C,IAAI,CAAC6C,0BAA0B,IAAI,EAAE,CAAA;QAC5C,IAAI,CAAC7C,IAAI,CAAC6C,0BAA0B,CAACqF,OAAO,CAAC,GAAGC,GAAG,CAAA;AACnD;QACA,IAAI,CAACnI,IAAI,CAACqC,mBAAmB,CAACiG,eAAe,CAACH,GAAG,CAAC,CAAA;AACpD,OAAA;AACF,KAAA;;AAEA;AACA,IAAA,IAAI,CAAC9B,MAAM,CAAC9C,UAAU,EAAEC,MAAM,CAAC,CAAA;AAQ/B,IAAA,OAAOA,MAAM,CAAA;AACf,GAAA;AAEA+E,EAAAA,GAAG,CAACC,GAAW,EAAEC,GAA4B,EAAE;AAC7C,IAAA,IAAI,CAAC,IAAI,CAACrI,KAAK,EAAE,OAAA;IACjB,IAAI,IAAI,CAACJ,IAAI,CAACuI,GAAG,EAAE,IAAI,CAACvI,IAAI,CAACuI,GAAG,CAACC,GAAG,EAAEC,GAAG,CAAC,CAAC,KACtClO,OAAO,CAACgO,GAAG,CAACC,GAAG,EAAEC,GAAG,CAAC,CAAA;AAC5B,GAAA;AAEQpC,EAAAA,MAAM,CAAI9C,UAAyB,EAAEC,MAAiB,EAAE;AAC9D,IAAA,IAAI,CAAC,IAAI,CAACxD,IAAI,CAAC0I,gBAAgB,EAAE,OAAA;AAEjC,IAAA,MAAMld,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;;AAE1B;AACA,IAAA,MAAMoQ,CAAC,GACL4H,MAAM,CAACwC,aAAa,GAAGxC,MAAM,CAAC1J,SAAS,GAAGtO,GAAG,GAAGgY,MAAM,CAACY,WAAW,CAAA;IACpE,IAAI,IAAI,CAAClE,mBAAmB,CAACjQ,GAAG,CAAC2L,CAAC,CAAC,EAAE,OAAA;AACrC,IAAA,IAAI,CAACsE,mBAAmB,CAACtU,GAAG,CAACgQ,CAAC,CAAC,CAAA;IAE/B,IAAI;MACF,IAAI,CAACoE,IAAI,CAAC0I,gBAAgB,CAACnF,UAAU,EAAEC,MAAM,CAAC,CAAA;KAC/C,CAAC,OAAO5Z,CAAC,EAAE;AACV2Q,MAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;AAClB,KAAA;AACF,GAAA;EAEQsd,eAAe,CAAI3D,UAAyB,EAAiB;AACnE,IAAA,MAAM/X,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;AAC1B,IAAA,MAAMmd,CAAC,GAAG,IAAI,CAAC3I,IAAI,CAAC1V,SAAS,CAAA;AAC7B,IAAA,IAAIqe,CAAC,IAAIA,CAAC,CAACnd,GAAG,CAAC,EAAE;AACf+X,MAAAA,UAAU,GAAGhZ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE+Y,UAAU,EAAEoF,CAAC,CAACnd,GAAG,CAAC,CAAC,CAAA;AAClD,MAAA,IAAI,OAAO+X,UAAU,CAACrV,GAAG,KAAK,QAAQ,EAAE;QACtCqV,UAAU,CAACrV,GAAG,GAAGgM,YAAY;AAC3B;QACAqJ,UAAU,CAACrV,GAAG,CACf,CAAA;AACH,OAAA;AACF,KAAA;AAEA,IAAA,OAAOqV,UAAU,CAAA;AACnB,GAAA;AAEQsD,EAAAA,iBAAiB,CAACjU,IAAa,EAAEgW,QAAiB,EAAE;AAC1D,IAAA,IAAI5C,aAAa,GAAGpT,IAAI,IAAI,IAAI,CAAA;AAChC;IACA,IAAIkH,SAAc,GAAG,EAAE,CAAA;AAEvB,IAAA,IAAI,IAAI,CAAC6G,mBAAmB,CAACqF,aAAa,CAAC,EAAE;AAC3ClM,MAAAA,SAAS,GAAG,IAAI,CAAC6G,mBAAmB,CAACqF,aAAa,CAAC,CAAA;AACrD,KAAC,MAAM,IAAI,IAAI,CAAChG,IAAI,CAACtS,UAAU,EAAE;MAC/BoM,SAAS,GAAG,IAAI,CAACkG,IAAI,CAACtS,UAAU,CAACsY,aAAa,CAAC,IAAI,EAAE,CAAA;AACvD,KAAC,MAAM,IAAI,IAAI,CAAChG,IAAI,CAAC6I,IAAI,EAAE;MACzB/O,SAAS,GAAG,IAAI,CAACkG,IAAI,CAAC6I,IAAI,CAAC7C,aAAa,CAAC,IAAI,EAAE,CAAA;AACjD,KAAA;;AAEA;AACA,IAAA,IAAI,CAAClM,SAAS,IAAI8O,QAAQ,EAAE;AAC1B,MAAA,IAAI,IAAI,CAACjI,mBAAmB,CAACiI,QAAQ,CAAC,EAAE;AACtC9O,QAAAA,SAAS,GAAG,IAAI,CAAC6G,mBAAmB,CAACiI,QAAQ,CAAC,CAAA;AAChD,OAAC,MAAM,IAAI,IAAI,CAAC5I,IAAI,CAACtS,UAAU,EAAE;QAC/BoM,SAAS,GAAG,IAAI,CAACkG,IAAI,CAACtS,UAAU,CAACkb,QAAQ,CAAC,IAAI,EAAE,CAAA;AAClD,OAAC,MAAM,IAAI,IAAI,CAAC5I,IAAI,CAAC6I,IAAI,EAAE;QACzB/O,SAAS,GAAG,IAAI,CAACkG,IAAI,CAAC6I,IAAI,CAACD,QAAQ,CAAC,IAAI,EAAE,CAAA;AAC5C,OAAA;AACA,MAAA,IAAI9O,SAAS,EAAE;AACbkM,QAAAA,aAAa,GAAG4C,QAAQ,CAAA;AAC1B,OAAA;AACF,KAAA;IAEA,OAAO;MAAE5C,aAAa;AAAElM,MAAAA,SAAAA;KAAW,CAAA;AACrC,GAAA;AAEQkN,EAAAA,UAAU,CAChBzD,UAAyB,EACzBuF,cAAsB,EACtBC,QAAiB,EACjBhC,SAAwB,EACxBiC,MAAe,EACfC,gBAA0B,EACf;IACX,IAAIjF,YAAY,GAAG,IAAI,CAAA;AACvB;IACA,IAAI8E,cAAc,GAAG,CAAC,IAAIA,cAAc,IAAIvF,UAAU,CAACgD,UAAU,CAAChP,MAAM,EAAE;AACxEuR,MAAAA,cAAc,GAAG,CAAC,CAAA;AAClB9E,MAAAA,YAAY,GAAG,KAAK,CAAA;AACtB,KAAA;IAEA,MAAM;MAAEgC,aAAa;AAAElM,MAAAA,SAAAA;KAAW,GAAG,IAAI,CAAC+M,iBAAiB,CACzDtD,UAAU,CAACyC,aAAa,EACxB,IAAI,CAAChG,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,GAC/D1C,UAAU,CAAC2C,iBAAiB,GAC5Bve,SAAS,CACd,CAAA;AAED,IAAA,MAAM+e,IAA4B,GAAGnD,UAAU,CAACmD,IAAI,GAChDnD,UAAU,CAACmD,IAAI,CAACoC,cAAc,CAAC,GAC/B,EAAE,CAAA;AAEN,IAAA,MAAM3X,GAAc,GAAG;AACrB3F,MAAAA,GAAG,EAAEkb,IAAI,CAAClb,GAAG,IAAI,EAAE,GAAGsd,cAAc;MACpC/B,SAAS;MACT/C,YAAY;MACZ+E,QAAQ;AACR3E,MAAAA,WAAW,EAAE0E,cAAc;AAC3Bla,MAAAA,KAAK,EAAE2U,UAAU,CAACgD,UAAU,CAACuC,cAAc,CAAC;MAC5C9C,aAAa;MACblM,SAAS;MACTmP,gBAAgB,EAAE,CAAC,CAACA,gBAAAA;KACrB,CAAA;IAED,IAAIvC,IAAI,CAACjJ,IAAI,EAAEtM,GAAG,CAACsM,IAAI,GAAGiJ,IAAI,CAACjJ,IAAI,CAAA;IACnC,IAAIuL,MAAM,KAAKrhB,SAAS,EAAEwJ,GAAG,CAAC6X,MAAM,GAAGA,MAAM,CAAA;IAC7C,IAAItC,IAAI,CAACE,WAAW,EAAEzV,GAAG,CAACyV,WAAW,GAAGF,IAAI,CAACE,WAAW,CAAA;AAExD,IAAA,OAAOzV,GAAG,CAAA;AACZ,GAAA;AAEQiW,EAAAA,cAAc,GAAG;AACvB,IAAA,OAAO,IAAI,CAACpH,IAAI,CAAC9R,GAAG,KAAKnF,SAAS,GAAGC,MAAM,CAACkgB,QAAQ,CAACpN,IAAI,GAAG,EAAE,CAAC,CAAA;AACjE,GAAA;EAEQiM,WAAW,CAACoB,QAAgB,EAAW;AAC7C,IAAA,MAAMjb,GAAG,GAAG,IAAI,CAACkZ,cAAc,EAAE,CAAA;AACjC,IAAA,IAAI,CAAClZ,GAAG,EAAE,OAAO,KAAK,CAAA;AAEtB,IAAA,MAAMkb,QAAQ,GAAGlb,GAAG,CAACmM,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;IAEzE,IAAI8O,QAAQ,CAACxQ,IAAI,CAACzK,GAAG,CAAC,EAAE,OAAO,IAAI,CAAA;IACnC,IAAIib,QAAQ,CAACxQ,IAAI,CAACyQ,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAA;AACxC,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;EAEQtB,gBAAgB,CAACuB,SAAmB,EAAW;IACrD,MAAMxB,MAAM,GAAG,IAAI,CAAC7H,IAAI,CAAC6H,MAAM,IAAI,EAAE,CAAA;AACrC,IAAA,KAAK,IAAIhY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwZ,SAAS,CAAC9R,MAAM,EAAE1H,CAAC,EAAE,EAAE;MACzC,IAAIgY,MAAM,CAACwB,SAAS,CAACxZ,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAA;AACvC,KAAA;AACA,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;EAEQqU,gBAAgB,CAACoF,OAAgC,EAAE;IACzD,IAAI,CAACvgB,SAAS,EAAE,OAAA;IAChB,MAAMka,IAAoB,GAAG,EAAE,CAAA;IAC/B,IAAIqG,OAAO,CAACC,GAAG,EAAE;AACf,MAAA,MAAMvd,CAAC,GAAG/C,QAAQ,CAACoO,aAAa,CAAC,OAAO,CAAC,CAAA;AACzCrL,MAAAA,CAAC,CAACyJ,SAAS,GAAG6T,OAAO,CAACC,GAAG,CAAA;AACzBtgB,MAAAA,QAAQ,CAACugB,IAAI,CAACC,WAAW,CAACzd,CAAC,CAAC,CAAA;MAC5BiX,IAAI,CAACrL,IAAI,CAAC,MAAM5L,CAAC,CAAC0d,MAAM,EAAE,CAAC,CAAA;AAC7B,KAAA;IACA,IAAIJ,OAAO,CAACK,EAAE,EAAE;AACd,MAAA,MAAMC,MAAM,GAAG3gB,QAAQ,CAACoO,aAAa,CAAC,QAAQ,CAAC,CAAA;AAC/CuS,MAAAA,MAAM,CAACnU,SAAS,GAAG6T,OAAO,CAACK,EAAE,CAAA;AAC7B1gB,MAAAA,QAAQ,CAACugB,IAAI,CAACC,WAAW,CAACG,MAAM,CAAC,CAAA;MACjC3G,IAAI,CAACrL,IAAI,CAAC,MAAMgS,MAAM,CAACF,MAAM,EAAE,CAAC,CAAA;AAClC,KAAA;IACA,IAAIJ,OAAO,CAACO,YAAY,EAAE;AACxBP,MAAAA,OAAO,CAACO,YAAY,CAAC9d,OAAO,CAAE0L,QAAQ,IAAK;QACzCwL,IAAI,CAACrL,IAAI,CAACnD,KAAM,CAACsE,WAAW,CAACtB,QAAQ,CAAwB,CAACjF,MAAM,CAAC,CAAA;AACvE,OAAC,CAAC,CAAA;AACJ,KAAA;AACA,IAAA,OAAO,MAAM;AACXyQ,MAAAA,IAAI,CAAClX,OAAO,CAAE+d,EAAE,IAAKA,EAAE,EAAE,CAAC,CAAA;KAC3B,CAAA;AACH,GAAA;EAEQC,uCAAuC,CAAC3e,IAAyB,EAAE;AACzE,IAAA,MAAMsC,UAAU,GAAG,IAAItD,GAAG,EAAU,CAAA;AACpC,IAAA,MAAMqG,QAAQ,GAAGrF,IAAI,IAAIA,IAAI,CAACqF,QAAQ,GAAGrF,IAAI,CAACqF,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE,CAAA;AAC3E,IAAA,MAAMJ,WAAW,GACflF,IAAI,IAAIA,IAAI,CAACkF,WAAW,GAAGlF,IAAI,CAACkF,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE,CAAA;IACrEhG,MAAM,CAACuD,IAAI,CAAC2C,QAAQ,CAAC,CAAC1E,OAAO,CAAE8Q,EAAE,IAAK;AACpC,MAAA,MAAM4I,OAAO,GAAGhV,QAAQ,CAACoM,EAAE,CAAC,CAAA;MAC5B,IAAI4I,OAAO,CAACC,KAAK,EAAE;AACjB,QAAA,KAAK,MAAMC,IAAI,IAAIF,OAAO,CAACC,KAAK,EAAE;UAChC,IAAIC,IAAI,CAACY,UAAU,EAAE;YACnB7Y,UAAU,CAAC9B,GAAG,CAAC+Z,IAAI,CAACK,aAAa,IAAI,IAAI,CAAC,CAAA;YAC1C,IAAIL,IAAI,CAACO,iBAAiB,EAAE;AAC1BxY,cAAAA,UAAU,CAAC9B,GAAG,CAAC+Z,IAAI,CAACO,iBAAiB,CAAC,CAAA;AACxC,aAAA;AACF,WAAA;AACF,SAAA;AACF,OAAA;AACF,KAAC,CAAC,CAAA;AACF5V,IAAAA,WAAW,CAAClB,GAAG,CAAEmU,UAAU,IAAK;MAC9B7V,UAAU,CAAC9B,GAAG,CAAC2X,UAAU,CAACyC,aAAa,IAAI,IAAI,CAAC,CAAA;MAChD,IAAIzC,UAAU,CAAC2C,iBAAiB,EAAE;AAChCxY,QAAAA,UAAU,CAAC9B,GAAG,CAAC2X,UAAU,CAAC2C,iBAAiB,CAAC,CAAA;AAC9C,OAAA;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAO3Z,KAAK,CAACC,IAAI,CAACkB,UAAU,CAAC,CAAA;AAC/B,GAAA;EAEA,MAAa0C,oBAAoB,CAAChF,IAAyB,EAAE;AAC3D,IAAA,IAAI,IAAI,CAAC4U,IAAI,CAACqC,mBAAmB,EAAE;AACjC,MAAA,MAAM3U,UAAU,GAAG,IAAI,CAACsc,0BAA0B,CAAC5e,IAAI,CAAC,CAAA;AACxD,MAAA,IAAI,CAAC4U,IAAI,CAAC6C,0BAA0B,GAAG,MAAM,IAAI,CAAC7C,IAAI,CAACqC,mBAAmB,CAAC4H,iBAAiB,CAC1Fvc,UAAU,CACX,CAAA;AACH,KAAA;AACF,GAAA;AAEQwc,EAAAA,2BAA2B,GAAsB;IACvD,MAAMC,iBAAoC,GAAG,EAAE,CAAA;AAC/C5f,IAAAA,MAAM,CAAC6f,MAAM,CAAC,IAAI,CAACpK,IAAI,CAAC6C,0BAA0B,IAAI,EAAE,CAAC,CAAC9W,OAAO,CAAEoc,GAAG,IAAK;AACzE,MAAA,IAAIA,GAAG,CAACkC,WAAW,EAAE9f,MAAM,CAACC,MAAM,CAAC2f,iBAAiB,EAAEhC,GAAG,CAACkC,WAAW,CAAC,CAAA;AACxE,KAAC,CAAC,CAAA;AACF,IAAA,OAAOF,iBAAiB,CAAA;AAC1B,GAAA;EAEQvC,yBAAyB,CAC/B0C,aAAqB,EACrBC,uBAAgC,EAChCC,0BAAmC,EACnC9D,IAAsB,EAItB;IACA6D,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC,CAAA;IACtDC,0BAA0B,GAAGA,0BAA0B,IAAI,CAAC,CAAA;IAC5D9D,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;IACjB,MAAM7J,EAAE,GAAG,IAAI,CAACwL,6BAA6B,CAC3CiC,aAAa,EACbC,uBAAuB,CACxB,CAAA;AACD,IAAA,MAAMF,WAAW,GAAG,IAAI,CAACH,2BAA2B,EAAE,CAAA;;AAEtD;IACA,IAAIM,0BAA0B,GAAG,CAAC,EAAE;MAClC,KAAK,IAAI3a,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI2a,0BAA0B,EAAE3a,CAAC,EAAE,EAAE;QACpD,MAAM4a,UAAU,GAAG,IAAI,CAACpC,6BAA6B,CAACiC,aAAa,EAAEza,CAAC,CAAC,CAAA;AACvE,QAAA,IAAIwa,WAAW,CAACI,UAAU,CAAC,KAAK9iB,SAAS,EAAE;UACzC,OAAO;YACL0b,SAAS,EAAE,CAAC,CAAC;AACbsE,YAAAA,gBAAgB,EAAE,IAAA;WACnB,CAAA;AACH,SAAA;AACF,OAAA;AACF,KAAA;AACA,IAAA,MAAM+C,YAAY,GAAGL,WAAW,CAACxN,EAAE,CAAC,CAAA;IACpC,IAAI6N,YAAY,KAAK/iB,SAAS;AAC5B;MACA,OAAO;AAAE0b,QAAAA,SAAS,EAAE,CAAC,CAAA;OAAG,CAAA;AAC1B,IAAA,MAAMA,SAAS,GAAGqD,IAAI,CAACiE,SAAS,CAAEnW,CAAC,IAAKA,CAAC,CAAChJ,GAAG,KAAKkf,YAAY,CAAC,CAAA;IAC/D,IAAIrH,SAAS,GAAG,CAAC;AACf;MACA,OAAO;AAAEA,QAAAA,SAAS,EAAE,CAAC,CAAA;OAAG,CAAA;IAE1B,OAAO;AAAEA,MAAAA,SAAAA;KAAW,CAAA;AACtB,GAAA;AAEQgF,EAAAA,6BAA6B,CACnCiC,aAAqB,EACrBC,uBAAgC,EACX;IACrBA,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC,CAAA;IACtD,OAAUD,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,eAAKC,uBAAuB,CAAA,CAAA;AACrD,GAAA;EAEQP,0BAA0B,CAChC5e,IAAyB,EACD;IACxB,MAAMsC,UAAkC,GAAG,EAAE,CAAA;IAC7C,IAAI,CAACsS,IAAI,CAAC4K,gCAAgC,GAAG,CAAC,IAAI,CAAC5K,IAAI,CACpD4K,gCAAgC,GAC/B,IAAI,CAACb,uCAAuC,CAAC3e,IAAI,CAAC,GAClD,IAAI,CAAC4U,IAAI,CAAC4K,gCAAgC,CAAA;IAC9C,IAAI,CAAC5K,IAAI,CAAC4K,gCAAgC,CAAC7e,OAAO,CAAE6G,IAAI,IAAK;MAC3D,MAAM;AAAEkH,QAAAA,SAAAA;AAAU,OAAC,GAAG,IAAI,CAAC+M,iBAAiB,CAACjU,IAAI,CAAC,CAAA;AAClDlF,MAAAA,UAAU,CAACkF,IAAI,CAAC,GAAGmL,QAAQ,CAACjE,SAAS,CAAC,CAAA;AACxC,KAAC,CAAC,CAAA;AACF,IAAA,OAAOpM,UAAU,CAAA;AACnB,GAAA;AAEQ0a,EAAAA,kCAAkC,CACxCyC,aAAqB,EACrBC,cAAsB,EACtBT,WAA8B,EAK9B;AACA,IAAA,MAAM7e,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;AACjD,IAAA,MAAMC,mBAAmB,GACvB,IAAI,CAAC/K,IAAI,CAAC6C,0BAA0B,IACpC,IAAI,CAAC7C,IAAI,CAAC6C,0BAA0B,CAACrX,GAAG,CAAC,GACrC,IAAI,CAACwU,IAAI,CAAC6C,0BAA0B,CAACrX,GAAG,CAAC,CAAC6e,WAAW,IAAI,EAAE,GAC3D,EAAE,CAAA;AACR,IAAA,MAAMW,cAAc,GAAG;AAAE,MAAA,GAAGD,mBAAmB;MAAE,GAAGV,WAAAA;KAAa,CAAA;AACjE,IAAA,MAAMpC,OAAO,GACXvf,IAAI,CAACC,SAAS,CAACoiB,mBAAmB,CAAC,KAAKriB,IAAI,CAACC,SAAS,CAACqiB,cAAc,CAAC,CAAA;IAExE,OAAO;MACLxf,GAAG;AACH2c,MAAAA,GAAG,EAAE;QACH0C,aAAa;QACbC,cAAc;AACdT,QAAAA,WAAW,EAAEW,cAAAA;OACd;AACD/C,MAAAA,OAAAA;KACD,CAAA;AACH,GAAA;AACF;;ACv3CA;AACA;AACA;AACO,MAAegD,mBAAmB,CAAC;AAQxC;AACF;AACA;AACA;AACA;EACE,MAAMhB,iBAAiB,CACrBvc,UAAkC,EAC8B;IAChE,MAAMwd,IAA+C,GAAG,EAAE,CAAA;AAC1D,IAAA,CACE,MAAM7c,OAAO,CAAC8c,GAAG,CACf5gB,MAAM,CAACkC,OAAO,CAACiB,UAAU,CAAC,CAAC0B,GAAG,CAAC,IAAA,IAAA;AAAA,MAAA,IAAC,CAACyb,aAAa,EAAEC,cAAc,CAAC,GAAA,IAAA,CAAA;AAAA,MAAA,OAC7D,IAAI,CAACM,cAAc,CAACP,aAAa,EAAEC,cAAc,CAAC,CAAA;AAAA,KAAA,CACnD,CACF,EACD/e,OAAO,CAAEoc,GAAG,IAAK;AACjB,MAAA,IAAIA,GAAG,EAAE;QACP,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;AACzDI,QAAAA,IAAI,CAAC1f,GAAG,CAAC,GAAG2c,GAAG,CAAA;AACjB,OAAA;AACF,KAAC,CAAC,CAAA;AACF,IAAA,OAAO+C,IAAI,CAAA;AACb,GAAA;AACF,CAAA;AAEO,MAAMG,+BAA+B,SAASJ,mBAAmB,CAAC;EAGvEnL,WAAW,CAACwL,IAA6D,EAAE;AACzEA,IAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;AACjB,IAAA,KAAK,EAAE,CAAA;AACP,IAAA,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACC,MAAM,IAAI,mBAAmB,CAAA;IAChD,IAAI;MACF,IAAI,CAAC5hB,YAAY,GAAG2hB,IAAI,CAAC3hB,YAAY,IAAIlC,UAAU,CAACkC,YAAY,CAAA;KACjE,CAAC,OAAOC,CAAC,EAAE;AACV;AAAA,KAAA;AAEJ,GAAA;AACA,EAAA,MAAMwhB,cAAc,CAACP,aAAqB,EAAEC,cAAsB,EAAE;AAClE,IAAA,MAAMtf,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;IACjD,IAAI3C,GAAqC,GAAG,IAAI,CAAA;AAChD,IAAA,IAAI,CAAC,IAAI,CAACxe,YAAY,EAAE,OAAOwe,GAAG,CAAA;IAClC,IAAI;AACF,MAAA,MAAMqD,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC7hB,YAAY,CAACkF,OAAO,CAAC,IAAI,CAAC0c,MAAM,GAAG/f,GAAG,CAAC,KAAK,IAAI,CAAA;AACxE,MAAA,MAAMJ,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,CAAC,CAAA;MAC5B,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;AACjElC,QAAAA,GAAG,GAAG/c,IAAI,CAAA;AACZ,OAAA;KACD,CAAC,OAAOxB,CAAC,EAAE;AACV;AAAA,KAAA;AAEF,IAAA,OAAOue,GAAG,CAAA;AACZ,GAAA;EACA,MAAMG,eAAe,CAACH,GAA8B,EAAE;IACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;AACzD,IAAA,IAAI,CAAC,IAAI,CAACnhB,YAAY,EAAE,OAAA;IACxB,IAAI;AACF,MAAA,MAAM,IAAI,CAACA,YAAY,CAAC2C,OAAO,CAAC,IAAI,CAACif,MAAM,GAAG/f,GAAG,EAAE9C,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAC,CAAA;KACxE,CAAC,OAAOve,CAAC,EAAE;AACV;AAAA,KAAA;AAEJ,GAAA;AACF,CAAA;AAEO,MAAM6hB,gCAAgC,SAASR,mBAAmB,CAAC;AACxE;AACF;AACA;AACA;AACA;AACA;AACA;;AAKEnL,EAAAA,WAAW,CAUR,KAAA,EAAA;IAAA,IAVS;AACVyL,MAAAA,MAAM,GAAG,mBAAmB;MAC5BG,GAAG;MACHva,GAAG;AACHwa,MAAAA,gBAAgB,GAAG,EAAC;KAMrB,GAAA,KAAA,CAAA;AACC,IAAA,KAAK,EAAE,CAAA;IACP,IAAI,CAACJ,MAAM,GAAGA,MAAM,CAAA;IACpB,IAAI,CAACG,GAAG,GAAGA,GAAG,CAAA;IACd,IAAI,CAACva,GAAG,GAAGA,GAAG,CAAA;IACd,IAAI,CAACwa,gBAAgB,GAAGA,gBAAgB,CAAA;AAC1C,GAAA;AACA,EAAA,MAAMP,cAAc,CAACP,aAAqB,EAAEC,cAAsB,EAAE;AAClE,IAAA,MAAMtf,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;IACjD,IAAI3C,GAAqC,GAAG,IAAI,CAAA;AAChD,IAAA,IAAI,CAAC,IAAI,CAACuD,GAAG,EAAE,OAAOvD,GAAG,CAAA;IACzB,IAAI;AACF,MAAA,MAAMqD,GAAG,GAAG,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,IAAI,CAACL,MAAM,GAAG/f,GAAG,CAAC,IAAI,IAAI,CAAA;AACvD,MAAA,MAAMJ,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,CAAC,CAAA;MAC5B,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;AACjElC,QAAAA,GAAG,GAAG/c,IAAI,CAAA;AACZ,OAAA;KACD,CAAC,OAAOxB,CAAC,EAAE;AACV;AAAA,KAAA;AAEF,IAAA,OAAOue,GAAG,CAAA;AACZ,GAAA;EACA,MAAMG,eAAe,CAACH,GAA8B,EAAE;IACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;AACzD,IAAA,IAAI,CAAC,IAAI,CAAC3Z,GAAG,EAAE,OAAA;AACf,IAAA,MAAM+H,GAAG,GAAGxQ,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAA;IAC/B,IAAI,CAAChX,GAAG,CAAC0a,MAAM,CACbjH,kBAAkB,CAAC,IAAI,CAAC2G,MAAM,GAAG/f,GAAG,CAAC,EACrCoZ,kBAAkB,CAAC1L,GAAG,CAAC,EACvB,IAAI,CAACyS,gBAAgB,CACtB,CAAA;AACH,GAAA;AACF,CAAA;AAEO,MAAMG,gCAAgC,SAASb,mBAAmB,CAAC;AACxE;AACF;AACA;AACA;AACA;AACA;AACA;;AAIEnL,EAAAA,WAAW,CAQR,KAAA,EAAA;IAAA,IARS;AACVyL,MAAAA,MAAM,GAAG,mBAAmB;MAC5BQ,QAAQ;AACRJ,MAAAA,gBAAgB,GAAG,EAAC;KAKrB,GAAA,KAAA,CAAA;AACC,IAAA,KAAK,EAAE,CAAA;IACP,IAAI,CAACJ,MAAM,GAAGA,MAAM,CAAA;IACpB,IAAI,CAACQ,QAAQ,GAAGA,QAAQ,CAAA;IACxB,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB,CAAA;AAC1C,GAAA;AACA,EAAA,MAAMP,cAAc,CAACP,aAAqB,EAAEC,cAAsB,EAAE;AAClE,IAAA,MAAMtf,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;IACjD,IAAI3C,GAAqC,GAAG,IAAI,CAAA;AAChD,IAAA,IAAI,CAAC,IAAI,CAAC4D,QAAQ,EAAE,OAAO5D,GAAG,CAAA;IAC9B,IAAI;AACF,MAAA,MAAMqD,GAAG,GAAG,IAAI,CAACO,QAAQ,CAACpgB,GAAG,CAAC,IAAI,CAAC4f,MAAM,GAAG/f,GAAG,CAAC,CAAA;MAChD,MAAMJ,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,IAAI,IAAI,CAAC,CAAA;MACpC,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;AACjElC,QAAAA,GAAG,GAAG/c,IAAI,CAAA;AACZ,OAAA;KACD,CAAC,OAAOxB,CAAC,EAAE;AACV;AAAA,KAAA;AAEF,IAAA,OAAOue,GAAG,CAAA;AACZ,GAAA;EACA,MAAMG,eAAe,CAACH,GAA8B,EAAE;IACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;AACzD,IAAA,IAAI,CAAC,IAAI,CAACiB,QAAQ,EAAE,OAAA;AACpB,IAAA,MAAM7S,GAAG,GAAGxQ,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAA;AAC/B,IAAA,IAAI,CAAC4D,QAAQ,CAAClgB,GAAG,CAAC,IAAI,CAAC0f,MAAM,GAAG/f,GAAG,EAAE0N,GAAG,EAAE,IAAI,CAACyS,gBAAgB,CAAC,CAAA;AAClE,GAAA;AACF,CAAA;AAEO,MAAMK,wBAAwB,SAASf,mBAAmB,CAAC;AAChE;;AAEAnL,EAAAA,WAAW,CAAsC,KAAA,EAAA;IAAA,IAArC;AAAEmM,MAAAA,KAAAA;KAAiC,GAAA,KAAA,CAAA;AAC7C,IAAA,KAAK,EAAE,CAAA;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAA;AACpB,GAAA;EAEA,MAAMhC,iBAAiB,CACrBvc,UAAkC,EAC8B;IAChE,MAAMwd,IAA2D,GAAG,EAAE,CAAA;IACtE,MAAMpd,IAAI,GAAGvD,MAAM,CAACkC,OAAO,CAACiB,UAAU,CAAC,CAAC0B,GAAG,CACzC,KAAA,IAAA;AAAA,MAAA,IAAC,CAACyb,aAAa,EAAEC,cAAc,CAAC,GAAA,KAAA,CAAA;MAAA,OAAQD,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,eAAKC,cAAc,CAAA,CAAA;AAAA,KAAE,CAC3E,CAAA;AACD,IAAA,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,OAAOf,IAAI,CAAA;AAC5B,IAAA,IAAI,CAACe,KAAK,CAACC,IAAI,CAAC,GAAGpe,IAAI,CAAC,CAACY,IAAI,CAAE0b,MAAM,IAAK;AACxCA,MAAAA,MAAM,CAACre,OAAO,CAAEyf,GAAG,IAAK;QACtB,IAAI;UACF,MAAMpgB,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,IAAI,IAAI,CAAC,CAAA;UACpC,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;YACjE,MAAM7e,GAAG,aAAMJ,IAAI,CAACyf,aAAa,EAAKzf,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,IAAI,CAAC0f,cAAc,CAAE,CAAA;AAC3DI,YAAAA,IAAI,CAAC1f,GAAG,CAAC,GAAGJ,IAAI,CAAA;AAClB,WAAA;SACD,CAAC,OAAOxB,CAAC,EAAE;AACV;AAAA,SAAA;AAEJ,OAAC,CAAC,CAAA;AACJ,KAAC,CAAC,CAAA;AACF,IAAA,OAAOshB,IAAI,CAAA;AACb,GAAA;AAEA,EAAA,MAAME,cAAc,CAACe,cAAsB,EAAEC,eAAuB,EAAE;AACpE;AACA,IAAA,OAAO,IAAI,CAAA;AACb,GAAA;EAEA,MAAM9D,eAAe,CAACH,GAA8B,EAAE;IACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;AACzD,IAAA,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,OAAA;AACjB,IAAA,MAAM,IAAI,CAACA,KAAK,CAACpgB,GAAG,CAACL,GAAG,EAAE9C,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAC,CAAA;AAChD,GAAA;AACF;;;;"}