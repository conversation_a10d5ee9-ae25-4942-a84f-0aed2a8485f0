var _growthbook=function(){"use strict";const t={staleTTL:6e4,maxAge:864e5,cacheKey:"gbFeaturesCache",backgroundSync:!0,maxEntries:10,disableIdleStreams:!1,idleStreamInterval:2e4},e={fetch:globalThis.fetch?globalThis.fetch.bind(globalThis):void 0,SubtleCrypto:globalThis.crypto?globalThis.crypto.subtle:void 0,EventSource:globalThis.EventSource},n={fetchFeaturesCall:t=>{let{host:n,clientKey:r,headers:i}=t;return e.fetch("".concat(n,"/api/features/").concat(r),{headers:i})},fetchRemoteEvalCall:t=>{let{host:n,clientKey:r,payload:i,headers:s}=t;const o={method:"POST",headers:{"Content-Type":"application/json",...s},body:JSON.stringify(i)};return e.fetch("".concat(n,"/api/eval/").concat(r),o)},eventSourceCall:t=>{let{host:n,clientKey:r,headers:i}=t;return i?new e.EventSource("".concat(n,"/sub/").concat(r),{headers:i}):new e.EventSource("".concat(n,"/sub/").concat(r))},startIdleListener:()=>{let e;if("undefined"==typeof window||"undefined"==typeof document)return;const n=()=>{"visible"===document.visibilityState?(window.clearTimeout(e),u.forEach((t=>{t&&"idle"===t.state&&b(t)}))):"hidden"===document.visibilityState&&(e=window.setTimeout(c,t.idleStreamInterval))};return document.addEventListener("visibilitychange",n),()=>document.removeEventListener("visibilitychange",n)},stopIdleListener:()=>{}};try{globalThis.localStorage&&(e.localStorage=globalThis.localStorage)}catch(t){}const r=new Map;let i=!1;const s=new Map,o=new Map,u=new Map,a=new Set;function c(){u.forEach((t=>{t&&(t.state="idle",g(t))}))}async function h(){try{if(!e.localStorage)return;await e.localStorage.setItem(t.cacheKey,JSON.stringify(Array.from(s.entries())))}catch(t){}}function l(t){const[e,n]=t.getApiInfo();return"".concat(e,"||").concat(n)}function f(t){const e=l(t);if(!t.isRemoteEval())return e;const n=t.getAttributes(),r=t.getCacheKeyAttributes()||Object.keys(t.getAttributes()),i={};r.forEach((t=>{i[t]=n[t]}));const s=t.getForcedVariations(),o=t.getUrl();return"".concat(e,"||").concat(JSON.stringify({ca:i,fv:s,url:o}))}function d(){const e=Array.from(s.entries()).map((t=>{let[e,n]=t;return{key:e,staleAt:n.staleAt.getTime()}})).sort(((t,e)=>t.staleAt-e.staleAt)),n=Math.min(Math.max(0,s.size-t.maxEntries),s.size);for(let t=0;t<n;t++)s.delete(e[t].key)}function w(e,n,i){const o=i.dateUpdated||"",u=new Date(Date.now()+t.staleTTL),c=s.get(n);if(c&&o&&c.version===o)return c.staleAt=u,void h();s.set(n,{data:i,version:o,staleAt:u,sse:a.has(e)}),d(),h();const l=r.get(e);l&&l.forEach((t=>y(t,i)))}async function y(t,n){n=await t.decryptPayload(n,void 0,e.SubtleCrypto),await t.refreshStickyBuckets(n),t.setExperiments(n.experiments||t.getExperiments()),t.setFeatures(n.features||t.getFeatures())}async function p(t){const{apiHost:e,apiRequestHeaders:r}=t.getApiHosts(),i=t.getClientKey(),s=t.isRemoteEval(),u=l(t),c=f(t);let h=o.get(c);return h||(h=(s?n.fetchRemoteEvalCall({host:e,clientKey:i,payload:{attributes:t.getAttributes(),forcedVariations:t.getForcedVariations(),forcedFeatures:Array.from(t.getForcedFeatures().entries()),url:t.getUrl()},headers:r}):n.fetchFeaturesCall({host:e,clientKey:i,headers:r})).then((t=>("enabled"===t.headers.get("x-sse-support")&&a.add(u),t.json()))).then((e=>(w(u,c,e),v(t),o.delete(c),e))).catch((t=>(o.delete(c),Promise.resolve({})))),o.set(c,h)),await h}function v(n){const i=l(n),s=f(n),{streamingHost:o,streamingHostRequestHeaders:c}=n.getApiHosts(),h=n.getClientKey();if(t.backgroundSync&&a.has(i)&&e.EventSource){if(u.has(i))return;const t={src:null,host:o,clientKey:h,headers:c,cb:e=>{try{if("features-updated"===e.type){const t=r.get(i);t&&t.forEach((t=>{p(t)}))}else if("features"===e.type){const t=JSON.parse(e.data);w(i,s,t)}t.errors=0}catch(e){m(t)}},errors:0,state:"active"};u.set(i,t),b(t)}}function m(t){if("idle"!==t.state&&(t.errors++,t.errors>3||t.src&&2===t.src.readyState)){const e=Math.pow(3,t.errors-3)*(1e3+1e3*Math.random());g(t),setTimeout((()=>{["idle","active"].includes(t.state)||b(t)}),Math.min(e,3e5))}}function g(t){t.src&&(t.src.onopen=null,t.src.onerror=null,t.src.close(),t.src=null,"active"===t.state&&(t.state="disabled"))}function b(t){t.src=n.eventSourceCall({host:t.host,clientKey:t.clientKey,headers:t.headers}),t.state="active",t.src.addEventListener("features",t.cb),t.src.addEventListener("features-updated",t.cb),t.src.onerror=()=>m(t),t.src.onopen=()=>{t.errors=0}}var S=/^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/,_={revert:function(){}},A=new Map,k=new Set;function $(t){var e=A.get(t);return e||A.set(t,e={element:t,attributes:{}}),e}function O(t,e,n,r,i){var s=n(t),o={isDirty:!1,originalValue:s,virtualValue:s,mutations:[],el:t,t:null,observer:new MutationObserver((function(){if("position"!==e||!o.t){"position"===e&&(o.t=setTimeout((function(){o.t=null}),1e3));var r=n(t);"position"===e&&r.parentNode===o.virtualValue.parentNode&&r.insertBeforeNode===o.virtualValue.insertBeforeNode||r!==o.virtualValue&&(o.originalValue=r,i(o))}})),mutationRunner:i,setValue:r,getCurrentValue:n};return"position"===e&&t.parentNode?o.observer.observe(t.parentNode,{childList:!0,subtree:!0,attributes:!1,characterData:!1}):o.observer.observe(t,function(t){return"html"===t?{childList:!0,subtree:!0,attributes:!0,characterData:!0}:{childList:!1,subtree:!1,attributes:!0,attributeFilter:[t]}}(e)),o}function E(t,e){var n=e.getCurrentValue(e.el);e.virtualValue=t,t&&"string"!=typeof t?n&&t.parentNode===n.parentNode&&t.insertBeforeNode===n.insertBeforeNode||(e.isDirty=!0,P()):t!==n&&(e.isDirty=!0,P())}function x(t){var e=t.originalValue;t.mutations.forEach((function(t){return e=t.mutate(e)})),E(function(t){return I||(I=document.createElement("div")),I.innerHTML=t,I.innerHTML}(e),t)}function N(t){var e=new Set(t.originalValue.split(/\s+/).filter(Boolean));t.mutations.forEach((function(t){return t.mutate(e)})),E(Array.from(e).filter(Boolean).join(" "),t)}function F(t){var e=t.originalValue;t.mutations.forEach((function(t){return e=t.mutate(e)})),E(e,t)}function T(t){var e=t.originalValue;t.mutations.forEach((function(t){var n=function(t){var e=t.insertBeforeSelector,n=document.querySelector(t.parentSelector);if(!n)return null;var r=e?document.querySelector(e):null;return e&&!r?null:{parentNode:n,insertBeforeNode:r}}(t.mutate());e=n||e})),E(e,t)}var C=function(t){return t.innerHTML},M=function(t,e){return t.innerHTML=e};function R(t){var e=$(t);return e.html||(e.html=O(t,"html",C,M,x)),e.html}var V=function(t){return{parentNode:t.parentElement,insertBeforeNode:t.nextElementSibling}},B=function(t,e){e.insertBeforeNode&&!e.parentNode.contains(e.insertBeforeNode)||e.parentNode.insertBefore(t,e.insertBeforeNode)};function J(t){var e=$(t);return e.position||(e.position=O(t,"position",V,B,T)),e.position}var I,D,K=function(t,e){return e?t.className=e:t.removeAttribute("class")},U=function(t){return t.className};function H(t){var e=$(t);return e.classes||(e.classes=O(t,"class",U,K,N)),e.classes}function j(t,e){var n,r=$(t);return r.attributes[e]||(r.attributes[e]=O(t,e,(n=e,function(t){var e;return null!=(e=t.getAttribute(n))?e:null}),function(t){return function(e,n){return null!==n?e.setAttribute(t,n):e.removeAttribute(t)}}(e),F)),r.attributes[e]}function L(t,e,n){if(n.isDirty){n.isDirty=!1;var r=n.virtualValue;n.mutations.length||function(t,e){var n,r,i=A.get(t);if(i)if("html"===e)null==(n=i.html)||null==(r=n.observer)||r.disconnect(),delete i.html;else if("class"===e){var s,o;null==(s=i.classes)||null==(o=s.observer)||o.disconnect(),delete i.classes}else if("position"===e){var u,a;null==(u=i.position)||null==(a=u.observer)||a.disconnect(),delete i.position}else{var c,h,l;null==(c=i.attributes)||null==(h=c[e])||null==(l=h.observer)||l.disconnect(),delete i.attributes[e]}}(t,e),n.setValue(t,r)}}function q(t,e){t.html&&L(e,"html",t.html),t.classes&&L(e,"class",t.classes),t.position&&L(e,"position",t.position),Object.keys(t.attributes).forEach((function(n){L(e,n,t.attributes[n])}))}function P(){A.forEach(q)}function z(t){if("position"!==t.kind||1!==t.elements.size){var e=new Set(t.elements);document.querySelectorAll(t.selector).forEach((function(n){e.has(n)||(t.elements.add(n),function(t,e){var n=null;"html"===t.kind?n=R(e):"class"===t.kind?n=H(e):"attribute"===t.kind?n=j(e,t.attribute):"position"===t.kind&&(n=J(e)),n&&(n.mutations.push(t),n.mutationRunner(n))}(t,n))}))}}function G(){k.forEach(z)}function Z(t){return"undefined"==typeof document?_:(k.add(t),z(t),{revert:function(){var e;(e=t).elements.forEach((function(t){return function(t,e){var n=null;if("html"===t.kind?n=R(e):"class"===t.kind?n=H(e):"attribute"===t.kind?n=j(e,t.attribute):"position"===t.kind&&(n=J(e)),n){var r=n.mutations.indexOf(t);-1!==r&&n.mutations.splice(r,1),n.mutationRunner(n)}}(e,t)})),e.elements.clear(),k.delete(e)}})}function Q(t,e){return Z({kind:"html",elements:new Set,mutate:e,selector:t})}function W(t,e){return Z({kind:"class",elements:new Set,mutate:e,selector:t})}function X(t,e,n){return S.test(e)?"class"===e||"className"===e?W(t,(function(t){var e=n(Array.from(t).join(" "));t.clear(),e&&e.split(/\s+/g).filter(Boolean).forEach((function(e){return t.add(e)}))})):Z({kind:"attribute",attribute:e,elements:new Set,mutate:n,selector:t}):_}function Y(t){let e=2166136261;const n=t.length;for(let r=0;r<n;r++)e^=t.charCodeAt(r),e+=(e<<1)+(e<<4)+(e<<7)+(e<<8)+(e<<24);return e>>>0}function tt(t,e,n){return 2===n?Y(Y(t+e)+"")%1e4/1e4:1===n?Y(e+t)%1e3/1e3:null}function et(t,e){return t>=e[0]&&t<e[1]}function nt(t){try{const e=t.replace(/([^\\])\//g,"$1\\/");return new RegExp(e)}catch(t){return void console.error(t)}}function rt(t,e,n){try{const r=new URL(t,"https://_");if("regex"===e){const t=nt(n);return!!t&&(t.test(r.href)||t.test(r.href.substring(r.origin.length)))}return"simple"===e&&function(t,e){try{const n=new URL(e.replace(/^([^:/?]*)\./i,"https://$1.").replace(/\*/g,"_____"),"https://_____"),r=[[t.host,n.host,!1],[t.pathname,n.pathname,!0]];return n.hash&&r.push([t.hash,n.hash,!1]),n.searchParams.forEach(((e,n)=>{r.push([t.searchParams.get(n)||"",e,!1])})),!r.some((t=>!function(t,e,n){try{let r=e.replace(/[*.+?^${}()|[\]\\]/g,"\\$&").replace(/_____/g,".*");return n&&(r="\\/?"+r.replace(/(^\/|\/$)/g,"")+"\\/?"),new RegExp("^"+r+"$","i").test(t)}catch(t){return!1}}(t[0],t[1],t[2])))}catch(t){return!1}}(r,n)}catch(t){return!1}}"undefined"!=typeof document&&(D||(D=new MutationObserver((function(){G()}))),G(),D.observe(document.documentElement,{childList:!0,subtree:!0,attributes:!1,characterData:!1}));const it=t=>Uint8Array.from(atob(t),(t=>t.charCodeAt(0)));async function st(t,e,n){if(e=e||"",!(n=n||globalThis.crypto&&globalThis.crypto.subtle))throw new Error("No SubtleCrypto implementation found");try{const r=await n.importKey("raw",it(e),{name:"AES-CBC",length:128},!0,["encrypt","decrypt"]),[i,s]=t.split("."),o=await n.decrypt({name:"AES-CBC",iv:it(i)},r,it(s));return(new TextDecoder).decode(o)}catch(t){throw new Error("Failed to decrypt")}}function ot(t){return"string"==typeof t?t:JSON.stringify(t)}function ut(t){"number"==typeof t&&(t+=""),t&&"string"==typeof t||(t="0");const e=t.replace(/(^v|\+.*$)/g,"").split(/[-.]/);return 3===e.length&&e.push("~"),e.map((t=>t.match(/^[0-9]+$/)?t.padStart(5," "):t)).join("-")}const at={};function ct(t,e){if("$or"in e)return yt(t,e.$or);if("$nor"in e)return!yt(t,e.$nor);if("$and"in e)return function(t,e){for(let n=0;n<e.length;n++)if(!ct(t,e[n]))return!1;return!0}(t,e.$and);if("$not"in e)return!ct(t,e.$not);for(const[n,r]of Object.entries(e))if(!lt(r,ht(t,n)))return!1;return!0}function ht(t,e){const n=e.split(".");let r=t;for(let t=0;t<n.length;t++){if(!r||"object"!=typeof r||!(n[t]in r))return null;r=r[n[t]]}return r}function lt(t,e){if("string"==typeof t)return e+""===t;if("number"==typeof t)return 1*e===t;if("boolean"==typeof t)return!!e===t;if(null===t)return null===e;if(Array.isArray(t)||!ft(t))return JSON.stringify(e)===JSON.stringify(t);for(const n in t)if(!wt(n,e,t[n]))return!1;return!0}function ft(t){const e=Object.keys(t);return e.length>0&&e.filter((t=>"$"===t[0])).length===e.length}function dt(t,e){return Array.isArray(t)?t.some((t=>e.includes(t))):e.includes(t)}function wt(t,e,n){switch(t){case"$veq":return ut(e)===ut(n);case"$vne":return ut(e)!==ut(n);case"$vgt":return ut(e)>ut(n);case"$vgte":return ut(e)>=ut(n);case"$vlt":return ut(e)<ut(n);case"$vlte":return ut(e)<=ut(n);case"$eq":return e===n;case"$ne":return e!==n;case"$lt":return e<n;case"$lte":return e<=n;case"$gt":return e>n;case"$gte":return e>=n;case"$exists":return n?null!=e:null==e;case"$in":return!!Array.isArray(n)&&dt(e,n);case"$nin":return!!Array.isArray(n)&&!dt(e,n);case"$not":return!lt(n,e);case"$size":return!!Array.isArray(e)&&lt(n,e.length);case"$elemMatch":return function(t,e){if(!Array.isArray(t))return!1;const n=ft(e)?t=>lt(e,t):t=>ct(t,e);for(let e=0;e<t.length;e++)if(t[e]&&n(t[e]))return!0;return!1}(e,n);case"$all":if(!Array.isArray(e))return!1;for(let t=0;t<n.length;t++){let r=!1;for(let i=0;i<e.length;i++)if(lt(n[t],e[i])){r=!0;break}if(!r)return!1}return!0;case"$regex":try{return(r=n,at[r]||(at[r]=new RegExp(r.replace(/([^\\])\//g,"$1\\/"))),at[r]).test(e)}catch(t){return!1}case"$type":return function(t){if(null===t)return"null";if(Array.isArray(t))return"array";const e=typeof t;return["string","number","boolean","object","undefined"].includes(e)?e:"unknown"}(e)===n;default:return console.error("Unknown operator: "+t),!1}var r}function yt(t,e){if(!e.length)return!0;for(let n=0;n<e.length;n++)if(ct(t,e[n]))return!0;return!1}const pt="undefined"!=typeof window&&"undefined"!=typeof document,vt=function(){let t;try{t="0.33.0"}catch(e){t=""}return t}(),mt=()=>{const t="gbuuid",e=t=>{const e=("; "+document.cookie).split("; ".concat(t,"="));return 2===e.length?e[1].split(";")[0]:""};if(e(t))return e(t);const n=window.crypto&&crypto.randomUUID?crypto.randomUUID():"10000000-1000-4000-8000-100000000000".replace(/[018]/g,(t=>(t^crypto.getRandomValues(new Uint8Array(1))[0]&15>>t/4).toString(16)));return((t,e)=>{const n=new Date;n.setTime(n.getTime()+3456e7),document.cookie="gbuuid="+e+";path=/;expires="+n.toUTCString()})(0,n),n};function gt(){let t={};try{const e=sessionStorage.getItem("utm_params");if(e&&(t=JSON.parse(e)),location.search){const e=new URLSearchParams(location.search);let n=!1;["source","medium","campaign","term","content"].forEach((r=>{const i="utm_".concat(r),s="utm"+r[0].toUpperCase()+r.slice(1);e.has(i)&&(t[s]=e.get(i)||"",n=!0)})),n&&sessionStorage.setItem("utm_params",JSON.stringify(t))}}catch(t){}return t}window.dataLayer=window.dataLayer||[];const bt=document.currentScript,St=bt?bt.dataset:{},_t=window.growthbook_config||{};function At(){const t=St.noAutoAttributes?{}:function(){const t=navigator.userAgent,e=t.match(/Edg/)?"edge":t.match(/Chrome/)?"chrome":t.match(/Firefox/)?"firefox":t.match(/Safari/)?"safari":"unknown";return{id:mt(),url:location.href,path:location.pathname,host:location.host,query:location.search,deviceType:t.match(/Mobi/)?"mobile":"desktop",browser:e,...gt()}}();return _t.attributes&&Object.assign(t,_t.attributes),t}const kt=new class{constructor(t){if(t=t||{},this.version=vt,this.i=this.context=t,this.o=null,this.u=new Set,this.h={},this.debug=!1,this.l=new Set,this.p=[],this.v=0,this.ready=!1,this.m=new Map,this.g=new Map,this.S={},this._=new Map,this.A=new Set,this.k=!1,t.remoteEval){if(t.decryptionKey)throw new Error("Encryption is not available for remoteEval");if(!t.clientKey)throw new Error("Missing clientKey");let e=!1;try{e=!!new URL(t.apiHost||"").hostname.match(/growthbook\.io$/i)}catch(t){}if(e)throw new Error("Cannot use remoteEval on GrowthBook Cloud")}else if(t.cacheKeyAttributes)throw new Error("cacheKeyAttributes are only used for remoteEval");t.features&&(this.ready=!0),pt&&t.enableDevMode&&(window._growthbook=this,document.dispatchEvent(new Event("gbloaded"))),t.experiments&&(this.ready=!0,this.$()),t.clientKey&&!t.remoteEval&&this.O({},!0,!1)}async loadFeatures(t){t&&t.autoRefresh&&(this.i.subscribeToChanges=!0),this.k=!0,await this.O(t,!0,!0),this.N()&&function(t){const e=l(t),n=r.get(e)||new Set;n.add(t),r.set(e,n)}(this)}async refreshFeatures(t){await this.O(t,!1,!0)}getApiInfo(){return[this.getApiHosts().apiHost,this.getClientKey()]}getApiHosts(){const t=this.i.apiHost||"https://cdn.growthbook.io";return{apiHost:t.replace(/\/*$/,""),streamingHost:(this.i.streamingHost||t).replace(/\/*$/,""),apiRequestHeaders:this.i.apiHostRequestHeaders,streamingHostRequestHeaders:this.i.streamingHostRequestHeaders}}getClientKey(){return this.i.clientKey||""}isRemoteEval(){return this.i.remoteEval||!1}getCacheKeyAttributes(){return this.i.cacheKeyAttributes}async O(r,o,u){if(r=r||{},!this.i.clientKey)throw new Error("Missing clientKey");await async function(r,o,u,c,h,w){w||(t.backgroundSync=!1);const m=await async function(r,o,u,c){const h=l(r),w=f(r),y=new Date,m=new Date(y.getTime()-t.maxAge+t.staleTTL);await async function(){if(!i){i=!0;try{if(e.localStorage){const n=await e.localStorage.getItem(t.cacheKey);if(n){const t=JSON.parse(n);t&&Array.isArray(t)&&t.forEach((t=>{let[e,n]=t;s.set(e,{...n,staleAt:new Date(n.staleAt)})})),d()}}}catch(t){}{const t=n.startIdleListener();t&&(n.stopIdleListener=t)}}}();const g=s.get(w);return g&&!c&&(o||g.staleAt>y)&&g.staleAt>m?(g.sse&&a.add(h),g.staleAt<y?p(r):v(r),g.data):await function(t,e){return new Promise((n=>{let r,i=!1;const s=t=>{i||(i=!0,r&&clearTimeout(r),n(t||null))};e&&(r=setTimeout((()=>s()),e)),t.then((t=>s(t))).catch((()=>s()))}))}(p(r),u)}(r,c,o,u);h&&m&&await y(r,m)}(this,r.timeout,r.skipCache||this.i.enableDevMode,o,u,!1!==this.i.backgroundSync)}F(){this.o&&this.o()}setFeatures(t){this.i.features=t,this.ready=!0,this.F()}async setEncryptedFeatures(t,e,n){const r=await st(t,e||this.i.decryptionKey,n);this.setFeatures(JSON.parse(r))}setExperiments(t){this.i.experiments=t,this.ready=!0,this.$()}async setEncryptedExperiments(t,e,n){const r=await st(t,e||this.i.decryptionKey,n);this.setExperiments(JSON.parse(r))}async decryptPayload(t,e,n){return t.encryptedFeatures&&(t.features=JSON.parse(await st(t.encryptedFeatures,e||this.i.decryptionKey,n)),delete t.encryptedFeatures),t.encryptedExperiments&&(t.experiments=JSON.parse(await st(t.encryptedExperiments,e||this.i.decryptionKey,n)),delete t.encryptedExperiments),t}async setAttributes(t){this.i.attributes=t,this.i.stickyBucketService&&await this.refreshStickyBuckets(),this.i.remoteEval?await this.T():(this.F(),this.$())}async setAttributeOverrides(t){this.S=t,this.i.stickyBucketService&&await this.refreshStickyBuckets(),this.i.remoteEval?await this.T():(this.F(),this.$())}async setForcedVariations(t){this.i.forcedVariations=t||{},this.i.remoteEval?await this.T():(this.F(),this.$())}setForcedFeatures(t){this.g=t,this.F()}async setURL(t){if(this.i.url=t,this.i.remoteEval)return await this.T(),void this.$(!0);this.$(!0)}getAttributes(){return{...this.i.attributes,...this.S}}getForcedVariations(){return this.i.forcedVariations||{}}getForcedFeatures(){return this.g||new Map}getStickyBucketAssignmentDocs(){return this.i.stickyBucketAssignmentDocs||{}}getUrl(){return this.i.url||""}getFeatures(){return this.i.features||{}}getExperiments(){return this.i.experiments||[]}subscribe(t){return this.l.add(t),()=>{this.l.delete(t)}}N(){return!1!==this.i.backgroundSync&&this.i.subscribeToChanges}async T(){this.i.remoteEval&&this.k&&await this.O({},!1,!0).catch((()=>{}))}getAllResults(){return new Map(this.m)}destroy(){var t;this.l.clear(),this.m.clear(),this.u.clear(),this.h={},this.p=[],this.v&&clearTimeout(this.v),t=this,r.forEach((e=>e.delete(t))),pt&&window._growthbook===this&&delete window._growthbook,this._.forEach((t=>{t.undo()})),this._.clear(),this.A.clear()}setRenderer(t){this.o=t}forceVariation(t,e){this.i.forcedVariations=this.i.forcedVariations||{},this.i.forcedVariations[t]=e,this.i.remoteEval?this.T():(this.$(),this.F())}run(t){const e=this.C(t,null);return this.M(t,e),e}triggerExperiment(t){return this.A.add(t),this.i.experiments?this.i.experiments.filter((e=>e.key===t)).map((t=>t.manual?this.R(t):null)).filter((t=>null!==t)):null}R(t,e){const n=this._.get(t);if(t.manual&&!this.A.has(t.key)&&!n)return null;const r=this.run(t),i=JSON.stringify(r.value);if(!e&&r.inExperiment&&n&&n.valueHash===i)return r;if(n&&this.V(t),r.inExperiment){const e=this.B(r.value);e&&this._.set(t,{undo:e,valueHash:i})}return r}V(t){const e=this._.get(t);e&&(e.undo(),this._.delete(t))}$(t){const e=this.i.experiments||[],n=new Set(e);this._.forEach(((t,e)=>{n.has(e)||(t.undo(),this._.delete(e))})),e.forEach((e=>{this.R(e,t)}))}M(t,e){const n=t.key,r=this.m.get(n);r&&r.result.inExperiment===e.inExperiment&&r.result.variationId===e.variationId||(this.m.set(n,{experiment:t,result:e}),this.l.forEach((n=>{try{n(t,e)}catch(t){console.error(t)}})))}J(t,e){if("override"===e.source)return;const n=JSON.stringify(e.value);if(this.h[t]!==n){if(this.h[t]=n,this.i.onFeatureUsage)try{this.i.onFeatureUsage(t,e)}catch(t){}pt&&window.fetch&&(this.p.push({key:t,on:e.on}),this.v||(this.v=window.setTimeout((()=>{this.v=0;const t=[...this.p];this.p=[],this.i.realtimeKey&&window.fetch("https://rt.growthbook.io/?key=".concat(this.i.realtimeKey,"&events=").concat(encodeURIComponent(JSON.stringify(t))),{cache:"no-cache",mode:"no-cors"}).catch((()=>{}))}),this.i.realtimeInterval||2e3)))}}I(t,e,n,r,i,s){const o={value:e,on:!!e,off:!e,source:n,ruleId:r||""};return i&&(o.experiment=i),s&&(o.experimentResult=s),this.J(t,o),o}isOn(t){return this.evalFeature(t).on}isOff(t){return this.evalFeature(t).off}getFeatureValue(t,e){const n=this.evalFeature(t).value;return null===n?e:n}feature(t){return this.evalFeature(t)}evalFeature(t){if(this.g.has(t))return this.I(t,this.g.get(t),"override");if(!this.i.features||!this.i.features[t])return this.I(t,null,"unknownFeature");const e=this.i.features[t];if(e.rules)for(const n of e.rules){if(n.filters&&this.D(n.filters))continue;if("force"in n){if(n.condition&&!this.K(n.condition))continue;if(!this.U(n.seed||t,n.hashAttribute,this.i.stickyBucketService&&!n.disableStickyBucketing?n.fallbackAttribute:void 0,n.range,n.coverage,n.hashVersion))continue;return n.tracks&&n.tracks.forEach((t=>{this.H(t.experiment,t.result)})),this.I(t,n.force,"force",n.id)}if(!n.variations)continue;const e={variations:n.variations,key:n.key||t};"coverage"in n&&(e.coverage=n.coverage),n.weights&&(e.weights=n.weights),n.hashAttribute&&(e.hashAttribute=n.hashAttribute),n.fallbackAttribute&&(e.fallbackAttribute=n.fallbackAttribute),n.disableStickyBucketing&&(e.disableStickyBucketing=n.disableStickyBucketing),void 0!==n.bucketVersion&&(e.bucketVersion=n.bucketVersion),void 0!==n.minBucketVersion&&(e.minBucketVersion=n.minBucketVersion),n.namespace&&(e.namespace=n.namespace),n.meta&&(e.meta=n.meta),n.ranges&&(e.ranges=n.ranges),n.name&&(e.name=n.name),n.phase&&(e.phase=n.phase),n.seed&&(e.seed=n.seed),n.hashVersion&&(e.hashVersion=n.hashVersion),n.filters&&(e.filters=n.filters),n.condition&&(e.condition=n.condition);const r=this.C(e,t);if(this.M(e,r),r.inExperiment&&!r.passthrough)return this.I(t,r.value,"experiment",n.id,e,r)}return this.I(t,void 0===e.defaultValue?null:e.defaultValue,"defaultValue")}U(t,e,n,r,i,s){if(!r&&void 0===i)return!0;const{hashValue:o}=this.j(e,n);if(!o)return!1;const u=tt(t,o,s||1);return null!==u&&(r?et(u,r):void 0===i||u<=i)}K(t){return ct(this.getAttributes(),t)}D(t){return t.some((t=>{const{hashValue:e}=this.j(t.attribute);if(!e)return!0;const n=tt(t.seed,e,t.hashVersion||2);return null===n||!t.ranges.some((t=>et(n,t)))}))}C(t,e){const n=t.key,r=t.variations.length;if(r<2)return this.L(t,-1,!1,e);if(!1===this.i.enabled)return this.L(t,-1,!1,e);if((t=this.q(t)).urlPatterns&&!function(t,e){if(!e.length)return!1;let n=!1,r=!1;for(let i=0;i<e.length;i++){const s=rt(t,e[i].type,e[i].pattern);if(!1===e[i].include){if(s)return!1}else n=!0,s&&(r=!0)}return r||!n}(this.P(),t.urlPatterns))return this.L(t,-1,!1,e);const i=function(t,e,n){if(!e)return null;const r=e.split("?")[1];if(!r)return null;const i=r.replace(/#.*/,"").split("&").map((t=>t.split("=",2))).filter((e=>{let[n]=e;return n===t})).map((t=>{let[,e]=t;return parseInt(e)}));return i.length>0&&i[0]>=0&&i[0]<n?i[0]:null}(n,this.P(),r);if(null!==i)return this.L(t,i,!1,e);if(this.i.forcedVariations&&n in this.i.forcedVariations)return this.L(t,this.i.forcedVariations[n],!1,e);if("draft"===t.status||!1===t.active)return this.L(t,-1,!1,e);const{hashAttribute:s,hashValue:o}=this.j(t.hashAttribute,this.i.stickyBucketService&&!t.disableStickyBucketing?t.fallbackAttribute:void 0);if(!o)return this.L(t,-1,!1,e);let u=-1,a=!1,c=!1;if(this.i.stickyBucketService&&!t.disableStickyBucketing){const{variation:e,versionIsBlocked:n}=this.G(t.key,t.bucketVersion,t.minBucketVersion,t.meta);a=e>=0,u=e,c=!!n}if(!a){if(t.filters){if(this.D(t.filters))return this.L(t,-1,!1,e)}else if(t.namespace&&!function(t,e){const n=tt("__"+e[0],t,1);return null!==n&&n>=e[1]&&n<e[2]}(o,t.namespace))return this.L(t,-1,!1,e);if(t.include&&!function(t){try{return t()}catch(t){return console.error(t),!1}}(t.include))return this.L(t,-1,!1,e);if(t.condition&&!this.K(t.condition))return this.L(t,-1,!1,e);if(t.groups&&!this.Z(t.groups))return this.L(t,-1,!1,e)}if(t.url&&!this.W(t.url))return this.L(t,-1,!1,e);const h=tt(t.seed||n,o,t.hashVersion||1);if(null===h)return this.L(t,-1,!1,e);if(a||(u=function(t,e){for(let n=0;n<e.length;n++)if(et(t,e[n]))return n;return-1}(h,t.ranges||function(t,e,n){(e=void 0===e?1:e)<0?e=0:e>1&&(e=1);const r=(i=t)<=0?[]:new Array(i).fill(1/i);var i;(n=n||r).length!==t&&(n=r);const s=n.reduce(((t,e)=>e+t),0);(s<.99||s>1.01)&&(n=r);let o=0;return n.map((t=>{const n=o;return o+=t,[n,n+e*t]}))}(r,void 0===t.coverage?1:t.coverage,t.weights))),c)return this.L(t,-1,!1,e,void 0,!0);if(u<0)return this.L(t,-1,!1,e);if("force"in t)return this.L(t,void 0===t.force?-1:t.force,!1,e);if(this.i.qaMode)return this.L(t,-1,!1,e);if("stopped"===t.status)return this.L(t,-1,!1,e);const l=this.L(t,u,!0,e,h,a);if(this.i.stickyBucketService&&!t.disableStickyBucketing){const{changed:e,key:n,doc:r}=this.X(s,ot(o),{[this.Y(t.key,t.bucketVersion)]:l.key});e&&(this.i.stickyBucketAssignmentDocs=this.i.stickyBucketAssignmentDocs||{},this.i.stickyBucketAssignmentDocs[n]=r,this.i.stickyBucketService.saveAssignments(r))}return this.H(t,l),l}log(t,e){this.debug&&(this.i.log?this.i.log(t,e):console.log(t,e))}H(t,e){if(!this.i.trackingCallback)return;const n=e.hashAttribute+e.hashValue+t.key+e.variationId;if(!this.u.has(n)){this.u.add(n);try{this.i.trackingCallback(t,e)}catch(t){console.error(t)}}}q(t){const e=t.key,n=this.i.overrides;return n&&n[e]&&"string"==typeof(t=Object.assign({},t,n[e])).url&&(t.url=nt(t.url)),t}j(t,e){let n=t||"id",r="";return this.S[n]?r=this.S[n]:this.i.attributes?r=this.i.attributes[n]||"":this.i.user&&(r=this.i.user[n]||""),!r&&e&&(this.S[e]?r=this.S[e]:this.i.attributes?r=this.i.attributes[e]||"":this.i.user&&(r=this.i.user[e]||""),r&&(n=e)),{hashAttribute:n,hashValue:r}}L(t,e,n,r,i,s){let o=!0;(e<0||e>=t.variations.length)&&(e=0,o=!1);const{hashAttribute:u,hashValue:a}=this.j(t.hashAttribute,this.i.stickyBucketService&&!t.disableStickyBucketing?t.fallbackAttribute:void 0),c=t.meta?t.meta[e]:{},h={key:c.key||""+e,featureId:r,inExperiment:o,hashUsed:n,variationId:e,value:t.variations[e],hashAttribute:u,hashValue:a,stickyBucketUsed:!!s};return c.name&&(h.name=c.name),void 0!==i&&(h.bucket=i),c.passthrough&&(h.passthrough=c.passthrough),h}P(){return this.i.url||(pt?window.location.href:"")}W(t){const e=this.P();if(!e)return!1;const n=e.replace(/^https?:\/\//,"").replace(/^[^/]*\//,"/");return!!t.test(e)||!!t.test(n)}Z(t){const e=this.i.groups||{};for(let n=0;n<t.length;n++)if(e[t[n]])return!0;return!1}B(t){if(!pt)return;const e=[];if(t.css){const n=document.createElement("style");n.innerHTML=t.css,document.head.appendChild(n),e.push((()=>n.remove()))}if(t.js){const n=document.createElement("script");n.innerHTML=t.js,document.head.appendChild(n),e.push((()=>n.remove()))}return t.domMutations&&t.domMutations.forEach((t=>{e.push(function(t){var e=t.selector,n=t.action,r=t.value,i=t.attribute,s=t.parentSelector,o=t.insertBeforeSelector;if("html"===i){if("append"===n)return Q(e,(function(t){return t+(null!=r?r:"")}));if("set"===n)return Q(e,(function(){return null!=r?r:""}))}else if("class"===i){if("append"===n)return W(e,(function(t){r&&t.add(r)}));if("remove"===n)return W(e,(function(t){r&&t.delete(r)}));if("set"===n)return W(e,(function(t){t.clear(),r&&t.add(r)}))}else if("position"===i){if("set"===n&&s)return function(t,e){return Z({kind:"position",elements:new Set,mutate:function(){return{insertBeforeSelector:o,parentSelector:s}},selector:t})}(e)}else{if("append"===n)return X(e,i,(function(t){return null!==t?t+(null!=r?r:""):null!=r?r:""}));if("set"===n)return X(e,i,(function(){return null!=r?r:""}));if("remove"===n)return X(e,i,(function(){return null}))}return _}(t).revert)})),()=>{e.forEach((t=>t()))}}tt(t){const e=new Set,n=t&&t.features?t.features:this.getFeatures(),r=t&&t.experiments?t.experiments:this.getExperiments();return Object.keys(n).forEach((t=>{const r=n[t];if(r.rules)for(const t of r.rules)t.variations&&(e.add(t.hashAttribute||"id"),t.fallbackAttribute&&e.add(t.fallbackAttribute))})),r.map((t=>{e.add(t.hashAttribute||"id"),t.fallbackAttribute&&e.add(t.fallbackAttribute)})),Array.from(e)}async refreshStickyBuckets(t){if(this.i.stickyBucketService){const e=this.et(t);this.i.stickyBucketAssignmentDocs=await this.i.stickyBucketService.getAllAssignments(e)}}nt(){const t={};return Object.values(this.i.stickyBucketAssignmentDocs||{}).forEach((e=>{e.assignments&&Object.assign(t,e.assignments)})),t}G(t,e,n,r){n=n||0,r=r||[];const i=this.Y(t,e=e||0),s=this.nt();if(n>0)for(let e=0;e<=n;e++)if(void 0!==s[this.Y(t,e)])return{variation:-1,versionIsBlocked:!0};const o=s[i];if(void 0===o)return{variation:-1};const u=r.findIndex((t=>t.key===o));return u<0?{variation:-1}:{variation:u}}Y(t,e){return e=e||0,"".concat(t,"__").concat(e)}et(t){const e={};return this.i.stickyBucketIdentifierAttributes=this.i.stickyBucketIdentifierAttributes?this.i.stickyBucketIdentifierAttributes:this.tt(t),this.i.stickyBucketIdentifierAttributes.forEach((t=>{const{hashValue:n}=this.j(t);e[t]=ot(n)})),e}X(t,e,n){const r="".concat(t,"||").concat(e),i=this.i.stickyBucketAssignmentDocs&&this.i.stickyBucketAssignmentDocs[r]&&this.i.stickyBucketAssignmentDocs[r].assignments||{},s={...i,...n};return{key:r,doc:{attributeName:t,attributeValue:e,assignments:s},changed:JSON.stringify(i)!==JSON.stringify(s)}}}({...St,remoteEval:!!St.remoteEval,subscribeToChanges:!0,trackingCallback:(t,e)=>{const n={experiment_id:t.key,variation_id:e.key};window.dataLayer.push(["event","experiment_viewed",n]),window.analytics&&window.analytics.track&&window.analytics.track("Experiment Viewed",n)},..._t,attributes:At()});kt.loadFeatures();let $t=location.href;return setInterval((()=>{location.href!==$t&&($t=location.href,kt.setURL($t),kt.setAttributes({...kt.getAttributes(),...At()}))}),500),kt}();
//# sourceMappingURL=auto.min.js.map
