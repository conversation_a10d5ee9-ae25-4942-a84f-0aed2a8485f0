const t={staleTTL:6e4,maxAge:864e5,cacheKey:"gbFeaturesCache",backgroundSync:!0,maxEntries:10,disableIdleStreams:!1,idleStreamInterval:2e4},e={fetch:globalThis.fetch?globalThis.fetch.bind(globalThis):void 0,SubtleCrypto:globalThis.crypto?globalThis.crypto.subtle:void 0,EventSource:globalThis.EventSource},n={fetchFeaturesCall:t=>{let{host:n,clientKey:i,headers:r}=t;return e.fetch("".concat(n,"/api/features/").concat(i),{headers:r})},fetchRemoteEvalCall:t=>{let{host:n,clientKey:i,payload:r,headers:s}=t;const u={method:"POST",headers:{"Content-Type":"application/json",...s},body:JSON.stringify(r)};return e.fetch("".concat(n,"/api/eval/").concat(i),u)},eventSourceCall:t=>{let{host:n,clientKey:i,headers:r}=t;return r?new e.EventSource("".concat(n,"/sub/").concat(i),{headers:r}):new e.EventSource("".concat(n,"/sub/").concat(i))},startIdleListener:()=>{let e;if("undefined"==typeof window||"undefined"==typeof document)return;const n=()=>{"visible"===document.visibilityState?(window.clearTimeout(e),d()):"hidden"===document.visibilityState&&(e=window.setTimeout(f,t.idleStreamInterval))};return document.addEventListener("visibilitychange",n),()=>document.removeEventListener("visibilitychange",n)},stopIdleListener:()=>{}};try{globalThis.localStorage&&(e.localStorage=globalThis.localStorage)}catch(t){}const i=new Map;let r=!1;const s=new Map,u=new Map,o=new Map,c=new Set;function a(t){Object.assign(e,t)}function h(e){Object.assign(t,e),t.backgroundSync||$()}async function l(){s.clear(),u.clear(),$(),r=!1,await y()}function f(){o.forEach((t=>{t&&(t.state="idle",_(t))}))}function d(){o.forEach((t=>{t&&"idle"===t.state&&k(t)}))}async function y(){try{if(!e.localStorage)return;await e.localStorage.setItem(t.cacheKey,JSON.stringify(Array.from(s.entries())))}catch(t){}}function v(t){const[e,n]=t.getApiInfo();return"".concat(e,"||").concat(n)}function p(t){const e=v(t);if(!t.isRemoteEval())return e;const n=t.getAttributes(),i=t.getCacheKeyAttributes()||Object.keys(t.getAttributes()),r={};i.forEach((t=>{r[t]=n[t]}));const s=t.getForcedVariations(),u=t.getUrl();return"".concat(e,"||").concat(JSON.stringify({ca:r,fv:s,url:u}))}function w(){const e=Array.from(s.entries()).map((t=>{let[e,n]=t;return{key:e,staleAt:n.staleAt.getTime()}})).sort(((t,e)=>t.staleAt-e.staleAt)),n=Math.min(Math.max(0,s.size-t.maxEntries),s.size);for(let t=0;t<n;t++)s.delete(e[t].key)}function g(e,n,r){const u=r.dateUpdated||"",o=new Date(Date.now()+t.staleTTL),a=s.get(n);if(a&&u&&a.version===u)return a.staleAt=o,void y();s.set(n,{data:r,version:u,staleAt:o,sse:c.has(e)}),w(),y();const h=i.get(e);h&&h.forEach((t=>m(t,r)))}async function m(t,n){n=await t.decryptPayload(n,void 0,e.SubtleCrypto),await t.refreshStickyBuckets(n),t.setExperiments(n.experiments||t.getExperiments()),t.setFeatures(n.features||t.getFeatures())}async function b(t){const{apiHost:e,apiRequestHeaders:i}=t.getApiHosts(),r=t.getClientKey(),s=t.isRemoteEval(),o=v(t),a=p(t);let h=u.get(a);return h||(h=(s?n.fetchRemoteEvalCall({host:e,clientKey:r,payload:{attributes:t.getAttributes(),forcedVariations:t.getForcedVariations(),forcedFeatures:Array.from(t.getForcedFeatures().entries()),url:t.getUrl()},headers:i}):n.fetchFeaturesCall({host:e,clientKey:r,headers:i})).then((t=>("enabled"===t.headers.get("x-sse-support")&&c.add(o),t.json()))).then((e=>(g(o,a,e),S(t),u.delete(a),e))).catch((t=>(u.delete(a),Promise.resolve({})))),u.set(a,h)),await h}function S(n){const r=v(n),s=p(n),{streamingHost:u,streamingHostRequestHeaders:a}=n.getApiHosts(),h=n.getClientKey();if(t.backgroundSync&&c.has(r)&&e.EventSource){if(o.has(r))return;const t={src:null,host:u,clientKey:h,headers:a,cb:e=>{try{if("features-updated"===e.type){const t=i.get(r);t&&t.forEach((t=>{b(t)}))}else if("features"===e.type){const t=JSON.parse(e.data);g(r,s,t)}t.errors=0}catch(e){A(t)}},errors:0,state:"active"};o.set(r,t),k(t)}}function A(t){if("idle"!==t.state&&(t.errors++,t.errors>3||t.src&&2===t.src.readyState)){const e=Math.pow(3,t.errors-3)*(1e3+1e3*Math.random());_(t),setTimeout((()=>{["idle","active"].includes(t.state)||k(t)}),Math.min(e,3e5))}}function _(t){t.src&&(t.src.onopen=null,t.src.onerror=null,t.src.close(),t.src=null,"active"===t.state&&(t.state="disabled"))}function k(t){t.src=n.eventSourceCall({host:t.host,clientKey:t.clientKey,headers:t.headers}),t.state="active",t.src.addEventListener("features",t.cb),t.src.addEventListener("features-updated",t.cb),t.src.onerror=()=>A(t),t.src.onopen=()=>{t.errors=0}}function O(t,e){_(t),o.delete(e)}function $(){c.clear(),o.forEach(O),i.clear(),n.stopIdleListener()}var E=/^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/,N={revert:function(){}},x=new Map,J=new Set;function B(t){var e=x.get(t);return e||x.set(t,e={element:t,attributes:{}}),e}function F(t,e,n,i,r){var s=n(t),u={isDirty:!1,originalValue:s,virtualValue:s,mutations:[],el:t,t:null,observer:new MutationObserver((function(){if("position"!==e||!u.t){"position"===e&&(u.t=setTimeout((function(){u.t=null}),1e3));var i=n(t);"position"===e&&i.parentNode===u.virtualValue.parentNode&&i.insertBeforeNode===u.virtualValue.insertBeforeNode||i!==u.virtualValue&&(u.originalValue=i,r(u))}})),mutationRunner:r,setValue:i,getCurrentValue:n};return"position"===e&&t.parentNode?u.observer.observe(t.parentNode,{childList:!0,subtree:!0,attributes:!1,characterData:!1}):u.observer.observe(t,function(t){return"html"===t?{childList:!0,subtree:!0,attributes:!0,characterData:!0}:{childList:!1,subtree:!1,attributes:!0,attributeFilter:[t]}}(e)),u}function R(t,e){var n=e.getCurrentValue(e.el);e.virtualValue=t,t&&"string"!=typeof t?n&&t.parentNode===n.parentNode&&t.insertBeforeNode===n.insertBeforeNode||(e.isDirty=!0,X()):t!==n&&(e.isDirty=!0,X())}function C(t){var e=t.originalValue;t.mutations.forEach((function(t){return e=t.mutate(e)})),R(function(t){return L||(L=document.createElement("div")),L.innerHTML=t,L.innerHTML}(e),t)}function T(t){var e=new Set(t.originalValue.split(/\s+/).filter(Boolean));t.mutations.forEach((function(t){return t.mutate(e)})),R(Array.from(e).filter(Boolean).join(" "),t)}function M(t){var e=t.originalValue;t.mutations.forEach((function(t){return e=t.mutate(e)})),R(e,t)}function V(t){var e=t.originalValue;t.mutations.forEach((function(t){var n=function(t){var e=t.insertBeforeSelector,n=document.querySelector(t.parentSelector);if(!n)return null;var i=e?document.querySelector(e):null;return e&&!i?null:{parentNode:n,insertBeforeNode:i}}(t.mutate());e=n||e})),R(e,t)}var I=function(t){return t.innerHTML},j=function(t,e){return t.innerHTML=e};function K(t){var e=B(t);return e.html||(e.html=F(t,"html",I,j,C)),e.html}var U=function(t){return{parentNode:t.parentElement,insertBeforeNode:t.nextElementSibling}},D=function(t,e){e.insertBeforeNode&&!e.parentNode.contains(e.insertBeforeNode)||e.parentNode.insertBefore(t,e.insertBeforeNode)};function H(t){var e=B(t);return e.position||(e.position=F(t,"position",U,D,V)),e.position}var L,q,P=function(t,e){return e?t.className=e:t.removeAttribute("class")},z=function(t){return t.className};function G(t){var e=B(t);return e.classes||(e.classes=F(t,"class",z,P,T)),e.classes}function Z(t,e){var n,i=B(t);return i.attributes[e]||(i.attributes[e]=F(t,e,(n=e,function(t){var e;return null!=(e=t.getAttribute(n))?e:null}),function(t){return function(e,n){return null!==n?e.setAttribute(t,n):e.removeAttribute(t)}}(e),M)),i.attributes[e]}function Q(t,e,n){if(n.isDirty){n.isDirty=!1;var i=n.virtualValue;n.mutations.length||function(t,e){var n,i,r=x.get(t);if(r)if("html"===e)null==(n=r.html)||null==(i=n.observer)||i.disconnect(),delete r.html;else if("class"===e){var s,u;null==(s=r.classes)||null==(u=s.observer)||u.disconnect(),delete r.classes}else if("position"===e){var o,c;null==(o=r.position)||null==(c=o.observer)||c.disconnect(),delete r.position}else{var a,h,l;null==(a=r.attributes)||null==(h=a[e])||null==(l=h.observer)||l.disconnect(),delete r.attributes[e]}}(t,e),n.setValue(t,i)}}function W(t,e){t.html&&Q(e,"html",t.html),t.classes&&Q(e,"class",t.classes),t.position&&Q(e,"position",t.position),Object.keys(t.attributes).forEach((function(n){Q(e,n,t.attributes[n])}))}function X(){x.forEach(W)}function Y(t){if("position"!==t.kind||1!==t.elements.size){var e=new Set(t.elements);document.querySelectorAll(t.selector).forEach((function(n){e.has(n)||(t.elements.add(n),function(t,e){var n=null;"html"===t.kind?n=K(e):"class"===t.kind?n=G(e):"attribute"===t.kind?n=Z(e,t.attribute):"position"===t.kind&&(n=H(e)),n&&(n.mutations.push(t),n.mutationRunner(n))}(t,n))}))}}function tt(){J.forEach(Y)}function et(t){return"undefined"==typeof document?N:(J.add(t),Y(t),{revert:function(){var e;(e=t).elements.forEach((function(t){return function(t,e){var n=null;if("html"===t.kind?n=K(e):"class"===t.kind?n=G(e):"attribute"===t.kind?n=Z(e,t.attribute):"position"===t.kind&&(n=H(e)),n){var i=n.mutations.indexOf(t);-1!==i&&n.mutations.splice(i,1),n.mutationRunner(n)}}(e,t)})),e.elements.clear(),J.delete(e)}})}function nt(t,e){return et({kind:"html",elements:new Set,mutate:e,selector:t})}function it(t,e){return et({kind:"class",elements:new Set,mutate:e,selector:t})}function rt(t,e,n){return E.test(e)?"class"===e||"className"===e?it(t,(function(t){var e=n(Array.from(t).join(" "));t.clear(),e&&e.split(/\s+/g).filter(Boolean).forEach((function(e){return t.add(e)}))})):et({kind:"attribute",attribute:e,elements:new Set,mutate:n,selector:t}):N}function st(t){let e=2166136261;const n=t.length;for(let i=0;i<n;i++)e^=t.charCodeAt(i),e+=(e<<1)+(e<<4)+(e<<7)+(e<<8)+(e<<24);return e>>>0}function ut(t,e,n){return 2===n?st(st(t+e)+"")%1e4/1e4:1===n?st(e+t)%1e3/1e3:null}function ot(t,e){return t>=e[0]&&t<e[1]}function ct(t){try{const e=t.replace(/([^\\])\//g,"$1\\/");return new RegExp(e)}catch(t){return void console.error(t)}}function at(t,e){if(!e.length)return!1;let n=!1,i=!1;for(let r=0;r<e.length;r++){const s=ht(t,e[r].type,e[r].pattern);if(!1===e[r].include){if(s)return!1}else n=!0,s&&(i=!0)}return i||!n}function ht(t,e,n){try{const i=new URL(t,"https://_");if("regex"===e){const t=ct(n);return!!t&&(t.test(i.href)||t.test(i.href.substring(i.origin.length)))}return"simple"===e&&function(t,e){try{const n=new URL(e.replace(/^([^:/?]*)\./i,"https://$1.").replace(/\*/g,"_____"),"https://_____"),i=[[t.host,n.host,!1],[t.pathname,n.pathname,!0]];return n.hash&&i.push([t.hash,n.hash,!1]),n.searchParams.forEach(((e,n)=>{i.push([t.searchParams.get(n)||"",e,!1])})),!i.some((t=>!function(t,e,n){try{let i=e.replace(/[*.+?^${}()|[\]\\]/g,"\\$&").replace(/_____/g,".*");return n&&(i="\\/?"+i.replace(/(^\/|\/$)/g,"")+"\\/?"),new RegExp("^"+i+"$","i").test(t)}catch(t){return!1}}(t[0],t[1],t[2])))}catch(t){return!1}}(i,n)}catch(t){return!1}}"undefined"!=typeof document&&(q||(q=new MutationObserver((function(){tt()}))),tt(),q.observe(document.documentElement,{childList:!0,subtree:!0,attributes:!1,characterData:!1}));const lt=t=>Uint8Array.from(atob(t),(t=>t.charCodeAt(0)));async function ft(t,e,n){if(e=e||"",!(n=n||globalThis.crypto&&globalThis.crypto.subtle))throw new Error("No SubtleCrypto implementation found");try{const i=await n.importKey("raw",lt(e),{name:"AES-CBC",length:128},!0,["encrypt","decrypt"]),[r,s]=t.split("."),u=await n.decrypt({name:"AES-CBC",iv:lt(r)},i,lt(s));return(new TextDecoder).decode(u)}catch(t){throw new Error("Failed to decrypt")}}function dt(t){return"string"==typeof t?t:JSON.stringify(t)}function yt(t){"number"==typeof t&&(t+=""),t&&"string"==typeof t||(t="0");const e=t.replace(/(^v|\+.*$)/g,"").split(/[-.]/);return 3===e.length&&e.push("~"),e.map((t=>t.match(/^[0-9]+$/)?t.padStart(5," "):t)).join("-")}const vt={};function pt(t,e){if("$or"in e)return At(t,e.$or);if("$nor"in e)return!At(t,e.$nor);if("$and"in e)return function(t,e){for(let n=0;n<e.length;n++)if(!pt(t,e[n]))return!1;return!0}(t,e.$and);if("$not"in e)return!pt(t,e.$not);for(const[n,i]of Object.entries(e))if(!gt(i,wt(t,n)))return!1;return!0}function wt(t,e){const n=e.split(".");let i=t;for(let t=0;t<n.length;t++){if(!i||"object"!=typeof i||!(n[t]in i))return null;i=i[n[t]]}return i}function gt(t,e){if("string"==typeof t)return e+""===t;if("number"==typeof t)return 1*e===t;if("boolean"==typeof t)return!!e===t;if(null===t)return null===e;if(Array.isArray(t)||!mt(t))return JSON.stringify(e)===JSON.stringify(t);for(const n in t)if(!St(n,e,t[n]))return!1;return!0}function mt(t){const e=Object.keys(t);return e.length>0&&e.filter((t=>"$"===t[0])).length===e.length}function bt(t,e){return Array.isArray(t)?t.some((t=>e.includes(t))):e.includes(t)}function St(t,e,n){switch(t){case"$veq":return yt(e)===yt(n);case"$vne":return yt(e)!==yt(n);case"$vgt":return yt(e)>yt(n);case"$vgte":return yt(e)>=yt(n);case"$vlt":return yt(e)<yt(n);case"$vlte":return yt(e)<=yt(n);case"$eq":return e===n;case"$ne":return e!==n;case"$lt":return e<n;case"$lte":return e<=n;case"$gt":return e>n;case"$gte":return e>=n;case"$exists":return n?null!=e:null==e;case"$in":return!!Array.isArray(n)&&bt(e,n);case"$nin":return!!Array.isArray(n)&&!bt(e,n);case"$not":return!gt(n,e);case"$size":return!!Array.isArray(e)&&gt(n,e.length);case"$elemMatch":return function(t,e){if(!Array.isArray(t))return!1;const n=mt(e)?t=>gt(e,t):t=>pt(t,e);for(let e=0;e<t.length;e++)if(t[e]&&n(t[e]))return!0;return!1}(e,n);case"$all":if(!Array.isArray(e))return!1;for(let t=0;t<n.length;t++){let i=!1;for(let r=0;r<e.length;r++)if(gt(n[t],e[r])){i=!0;break}if(!i)return!1}return!0;case"$regex":try{return(i=n,vt[i]||(vt[i]=new RegExp(i.replace(/([^\\])\//g,"$1\\/"))),vt[i]).test(e)}catch(t){return!1}case"$type":return function(t){if(null===t)return"null";if(Array.isArray(t))return"array";const e=typeof t;return["string","number","boolean","object","undefined"].includes(e)?e:"unknown"}(e)===n;default:return console.error("Unknown operator: "+t),!1}var i}function At(t,e){if(!e.length)return!0;for(let n=0;n<e.length;n++)if(pt(t,e[n]))return!0;return!1}const _t="undefined"!=typeof window&&"undefined"!=typeof document,kt=function(){let t;try{t="0.33.0"}catch(e){t=""}return t}();class Ot{constructor(t){if(t=t||{},this.version=kt,this.i=this.context=t,this.u=null,this.o=new Set,this.h={},this.debug=!1,this.l=new Set,this.v=[],this.p=0,this.ready=!1,this.g=new Map,this.m=new Map,this.S={},this.A=new Map,this._=new Set,this.k=!1,t.remoteEval){if(t.decryptionKey)throw new Error("Encryption is not available for remoteEval");if(!t.clientKey)throw new Error("Missing clientKey");let e=!1;try{e=!!new URL(t.apiHost||"").hostname.match(/growthbook\.io$/i)}catch(t){}if(e)throw new Error("Cannot use remoteEval on GrowthBook Cloud")}else if(t.cacheKeyAttributes)throw new Error("cacheKeyAttributes are only used for remoteEval");t.features&&(this.ready=!0),_t&&t.enableDevMode&&(window._growthbook=this,document.dispatchEvent(new Event("gbloaded"))),t.experiments&&(this.ready=!0,this.O()),t.clientKey&&!t.remoteEval&&this.$({},!0,!1)}async loadFeatures(t){t&&t.autoRefresh&&(this.i.subscribeToChanges=!0),this.k=!0,await this.$(t,!0,!0),this.N()&&function(t){const e=v(t),n=i.get(e)||new Set;n.add(t),i.set(e,n)}(this)}async refreshFeatures(t){await this.$(t,!1,!0)}getApiInfo(){return[this.getApiHosts().apiHost,this.getClientKey()]}getApiHosts(){const t=this.i.apiHost||"https://cdn.growthbook.io";return{apiHost:t.replace(/\/*$/,""),streamingHost:(this.i.streamingHost||t).replace(/\/*$/,""),apiRequestHeaders:this.i.apiHostRequestHeaders,streamingHostRequestHeaders:this.i.streamingHostRequestHeaders}}getClientKey(){return this.i.clientKey||""}isRemoteEval(){return this.i.remoteEval||!1}getCacheKeyAttributes(){return this.i.cacheKeyAttributes}async $(i,u,o){if(i=i||{},!this.i.clientKey)throw new Error("Missing clientKey");await async function(i,u,o,a,h,l){l||(t.backgroundSync=!1);const f=await async function(i,u,o,a){const h=v(i),l=p(i),f=new Date,d=new Date(f.getTime()-t.maxAge+t.staleTTL);await async function(){if(!r){r=!0;try{if(e.localStorage){const n=await e.localStorage.getItem(t.cacheKey);if(n){const t=JSON.parse(n);t&&Array.isArray(t)&&t.forEach((t=>{let[e,n]=t;s.set(e,{...n,staleAt:new Date(n.staleAt)})})),w()}}}catch(t){}if(!t.disableIdleStreams){const t=n.startIdleListener();t&&(n.stopIdleListener=t)}}}();const y=s.get(l);return y&&!a&&(u||y.staleAt>f)&&y.staleAt>d?(y.sse&&c.add(h),y.staleAt<f?b(i):S(i),y.data):await function(t,e){return new Promise((n=>{let i,r=!1;const s=t=>{r||(r=!0,i&&clearTimeout(i),n(t||null))};e&&(i=setTimeout((()=>s()),e)),t.then((t=>s(t))).catch((()=>s()))}))}(b(i),o)}(i,a,u,o);h&&f&&await m(i,f)}(this,i.timeout,i.skipCache||this.i.enableDevMode,u,o,!1!==this.i.backgroundSync)}J(){this.u&&this.u()}setFeatures(t){this.i.features=t,this.ready=!0,this.J()}async setEncryptedFeatures(t,e,n){const i=await ft(t,e||this.i.decryptionKey,n);this.setFeatures(JSON.parse(i))}setExperiments(t){this.i.experiments=t,this.ready=!0,this.O()}async setEncryptedExperiments(t,e,n){const i=await ft(t,e||this.i.decryptionKey,n);this.setExperiments(JSON.parse(i))}async decryptPayload(t,e,n){return t.encryptedFeatures&&(t.features=JSON.parse(await ft(t.encryptedFeatures,e||this.i.decryptionKey,n)),delete t.encryptedFeatures),t.encryptedExperiments&&(t.experiments=JSON.parse(await ft(t.encryptedExperiments,e||this.i.decryptionKey,n)),delete t.encryptedExperiments),t}async setAttributes(t){this.i.attributes=t,this.i.stickyBucketService&&await this.refreshStickyBuckets(),this.i.remoteEval?await this.B():(this.J(),this.O())}async setAttributeOverrides(t){this.S=t,this.i.stickyBucketService&&await this.refreshStickyBuckets(),this.i.remoteEval?await this.B():(this.J(),this.O())}async setForcedVariations(t){this.i.forcedVariations=t||{},this.i.remoteEval?await this.B():(this.J(),this.O())}setForcedFeatures(t){this.m=t,this.J()}async setURL(t){if(this.i.url=t,this.i.remoteEval)return await this.B(),void this.O(!0);this.O(!0)}getAttributes(){return{...this.i.attributes,...this.S}}getForcedVariations(){return this.i.forcedVariations||{}}getForcedFeatures(){return this.m||new Map}getStickyBucketAssignmentDocs(){return this.i.stickyBucketAssignmentDocs||{}}getUrl(){return this.i.url||""}getFeatures(){return this.i.features||{}}getExperiments(){return this.i.experiments||[]}subscribe(t){return this.l.add(t),()=>{this.l.delete(t)}}N(){return!1!==this.i.backgroundSync&&this.i.subscribeToChanges}async B(){this.i.remoteEval&&this.k&&await this.$({},!1,!0).catch((()=>{}))}getAllResults(){return new Map(this.g)}destroy(){var t;this.l.clear(),this.g.clear(),this.o.clear(),this.h={},this.v=[],this.p&&clearTimeout(this.p),t=this,i.forEach((e=>e.delete(t))),_t&&window._growthbook===this&&delete window._growthbook,this.A.forEach((t=>{t.undo()})),this.A.clear(),this._.clear()}setRenderer(t){this.u=t}forceVariation(t,e){this.i.forcedVariations=this.i.forcedVariations||{},this.i.forcedVariations[t]=e,this.i.remoteEval?this.B():(this.O(),this.J())}run(t){const e=this.F(t,null);return this.R(t,e),e}triggerExperiment(t){return this._.add(t),this.i.experiments?this.i.experiments.filter((e=>e.key===t)).map((t=>t.manual?this.C(t):null)).filter((t=>null!==t)):null}C(t,e){const n=this.A.get(t);if(t.manual&&!this._.has(t.key)&&!n)return null;const i=this.run(t),r=JSON.stringify(i.value);if(!e&&i.inExperiment&&n&&n.valueHash===r)return i;if(n&&this.T(t),i.inExperiment){const e=this.M(i.value);e&&this.A.set(t,{undo:e,valueHash:r})}return i}T(t){const e=this.A.get(t);e&&(e.undo(),this.A.delete(t))}O(t){const e=this.i.experiments||[],n=new Set(e);this.A.forEach(((t,e)=>{n.has(e)||(t.undo(),this.A.delete(e))})),e.forEach((e=>{this.C(e,t)}))}R(t,e){const n=t.key,i=this.g.get(n);i&&i.result.inExperiment===e.inExperiment&&i.result.variationId===e.variationId||(this.g.set(n,{experiment:t,result:e}),this.l.forEach((n=>{try{n(t,e)}catch(t){console.error(t)}})))}V(t,e){if("override"===e.source)return;const n=JSON.stringify(e.value);if(this.h[t]!==n){if(this.h[t]=n,this.i.onFeatureUsage)try{this.i.onFeatureUsage(t,e)}catch(t){}_t&&window.fetch&&(this.v.push({key:t,on:e.on}),this.p||(this.p=window.setTimeout((()=>{this.p=0;const t=[...this.v];this.v=[],this.i.realtimeKey&&window.fetch("https://rt.growthbook.io/?key=".concat(this.i.realtimeKey,"&events=").concat(encodeURIComponent(JSON.stringify(t))),{cache:"no-cache",mode:"no-cors"}).catch((()=>{}))}),this.i.realtimeInterval||2e3)))}}I(t,e,n,i,r,s){const u={value:e,on:!!e,off:!e,source:n,ruleId:i||""};return r&&(u.experiment=r),s&&(u.experimentResult=s),this.V(t,u),u}isOn(t){return this.evalFeature(t).on}isOff(t){return this.evalFeature(t).off}getFeatureValue(t,e){const n=this.evalFeature(t).value;return null===n?e:n}feature(t){return this.evalFeature(t)}evalFeature(t){if(this.m.has(t))return this.I(t,this.m.get(t),"override");if(!this.i.features||!this.i.features[t])return this.I(t,null,"unknownFeature");const e=this.i.features[t];if(e.rules)for(const n of e.rules){if(n.filters&&this.j(n.filters))continue;if("force"in n){if(n.condition&&!this.K(n.condition))continue;if(!this.U(n.seed||t,n.hashAttribute,this.i.stickyBucketService&&!n.disableStickyBucketing?n.fallbackAttribute:void 0,n.range,n.coverage,n.hashVersion))continue;return n.tracks&&n.tracks.forEach((t=>{this.D(t.experiment,t.result)})),this.I(t,n.force,"force",n.id)}if(!n.variations)continue;const e={variations:n.variations,key:n.key||t};"coverage"in n&&(e.coverage=n.coverage),n.weights&&(e.weights=n.weights),n.hashAttribute&&(e.hashAttribute=n.hashAttribute),n.fallbackAttribute&&(e.fallbackAttribute=n.fallbackAttribute),n.disableStickyBucketing&&(e.disableStickyBucketing=n.disableStickyBucketing),void 0!==n.bucketVersion&&(e.bucketVersion=n.bucketVersion),void 0!==n.minBucketVersion&&(e.minBucketVersion=n.minBucketVersion),n.namespace&&(e.namespace=n.namespace),n.meta&&(e.meta=n.meta),n.ranges&&(e.ranges=n.ranges),n.name&&(e.name=n.name),n.phase&&(e.phase=n.phase),n.seed&&(e.seed=n.seed),n.hashVersion&&(e.hashVersion=n.hashVersion),n.filters&&(e.filters=n.filters),n.condition&&(e.condition=n.condition);const i=this.F(e,t);if(this.R(e,i),i.inExperiment&&!i.passthrough)return this.I(t,i.value,"experiment",n.id,e,i)}return this.I(t,void 0===e.defaultValue?null:e.defaultValue,"defaultValue")}U(t,e,n,i,r,s){if(!i&&void 0===r)return!0;const{hashValue:u}=this.H(e,n);if(!u)return!1;const o=ut(t,u,s||1);return null!==o&&(i?ot(o,i):void 0===r||o<=r)}K(t){return pt(this.getAttributes(),t)}j(t){return t.some((t=>{const{hashValue:e}=this.H(t.attribute);if(!e)return!0;const n=ut(t.seed,e,t.hashVersion||2);return null===n||!t.ranges.some((t=>ot(n,t)))}))}F(t,e){const n=t.key,i=t.variations.length;if(i<2)return this.L(t,-1,!1,e);if(!1===this.i.enabled)return this.L(t,-1,!1,e);if((t=this.q(t)).urlPatterns&&!at(this.P(),t.urlPatterns))return this.L(t,-1,!1,e);const r=function(t,e,n){if(!e)return null;const i=e.split("?")[1];if(!i)return null;const r=i.replace(/#.*/,"").split("&").map((t=>t.split("=",2))).filter((e=>{let[n]=e;return n===t})).map((t=>{let[,e]=t;return parseInt(e)}));return r.length>0&&r[0]>=0&&r[0]<n?r[0]:null}(n,this.P(),i);if(null!==r)return this.L(t,r,!1,e);if(this.i.forcedVariations&&n in this.i.forcedVariations)return this.L(t,this.i.forcedVariations[n],!1,e);if("draft"===t.status||!1===t.active)return this.L(t,-1,!1,e);const{hashAttribute:s,hashValue:u}=this.H(t.hashAttribute,this.i.stickyBucketService&&!t.disableStickyBucketing?t.fallbackAttribute:void 0);if(!u)return this.L(t,-1,!1,e);let o=-1,c=!1,a=!1;if(this.i.stickyBucketService&&!t.disableStickyBucketing){const{variation:e,versionIsBlocked:n}=this.G(t.key,t.bucketVersion,t.minBucketVersion,t.meta);c=e>=0,o=e,a=!!n}if(!c){if(t.filters){if(this.j(t.filters))return this.L(t,-1,!1,e)}else if(t.namespace&&!function(t,e){const n=ut("__"+e[0],t,1);return null!==n&&n>=e[1]&&n<e[2]}(u,t.namespace))return this.L(t,-1,!1,e);if(t.include&&!function(t){try{return t()}catch(t){return console.error(t),!1}}(t.include))return this.L(t,-1,!1,e);if(t.condition&&!this.K(t.condition))return this.L(t,-1,!1,e);if(t.groups&&!this.Z(t.groups))return this.L(t,-1,!1,e)}if(t.url&&!this.W(t.url))return this.L(t,-1,!1,e);const h=ut(t.seed||n,u,t.hashVersion||1);if(null===h)return this.L(t,-1,!1,e);if(c||(o=function(t,e){for(let n=0;n<e.length;n++)if(ot(t,e[n]))return n;return-1}(h,t.ranges||function(t,e,n){(e=void 0===e?1:e)<0?e=0:e>1&&(e=1);const i=(r=t)<=0?[]:new Array(r).fill(1/r);var r;(n=n||i).length!==t&&(n=i);const s=n.reduce(((t,e)=>e+t),0);(s<.99||s>1.01)&&(n=i);let u=0;return n.map((t=>{const n=u;return u+=t,[n,n+e*t]}))}(i,void 0===t.coverage?1:t.coverage,t.weights))),a)return this.L(t,-1,!1,e,void 0,!0);if(o<0)return this.L(t,-1,!1,e);if("force"in t)return this.L(t,void 0===t.force?-1:t.force,!1,e);if(this.i.qaMode)return this.L(t,-1,!1,e);if("stopped"===t.status)return this.L(t,-1,!1,e);const l=this.L(t,o,!0,e,h,c);if(this.i.stickyBucketService&&!t.disableStickyBucketing){const{changed:e,key:n,doc:i}=this.X(s,dt(u),{[this.Y(t.key,t.bucketVersion)]:l.key});e&&(this.i.stickyBucketAssignmentDocs=this.i.stickyBucketAssignmentDocs||{},this.i.stickyBucketAssignmentDocs[n]=i,this.i.stickyBucketService.saveAssignments(i))}return this.D(t,l),l}log(t,e){this.debug&&(this.i.log?this.i.log(t,e):console.log(t,e))}D(t,e){if(!this.i.trackingCallback)return;const n=e.hashAttribute+e.hashValue+t.key+e.variationId;if(!this.o.has(n)){this.o.add(n);try{this.i.trackingCallback(t,e)}catch(t){console.error(t)}}}q(t){const e=t.key,n=this.i.overrides;return n&&n[e]&&"string"==typeof(t=Object.assign({},t,n[e])).url&&(t.url=ct(t.url)),t}H(t,e){let n=t||"id",i="";return this.S[n]?i=this.S[n]:this.i.attributes?i=this.i.attributes[n]||"":this.i.user&&(i=this.i.user[n]||""),!i&&e&&(this.S[e]?i=this.S[e]:this.i.attributes?i=this.i.attributes[e]||"":this.i.user&&(i=this.i.user[e]||""),i&&(n=e)),{hashAttribute:n,hashValue:i}}L(t,e,n,i,r,s){let u=!0;(e<0||e>=t.variations.length)&&(e=0,u=!1);const{hashAttribute:o,hashValue:c}=this.H(t.hashAttribute,this.i.stickyBucketService&&!t.disableStickyBucketing?t.fallbackAttribute:void 0),a=t.meta?t.meta[e]:{},h={key:a.key||""+e,featureId:i,inExperiment:u,hashUsed:n,variationId:e,value:t.variations[e],hashAttribute:o,hashValue:c,stickyBucketUsed:!!s};return a.name&&(h.name=a.name),void 0!==r&&(h.bucket=r),a.passthrough&&(h.passthrough=a.passthrough),h}P(){return this.i.url||(_t?window.location.href:"")}W(t){const e=this.P();if(!e)return!1;const n=e.replace(/^https?:\/\//,"").replace(/^[^/]*\//,"/");return!!t.test(e)||!!t.test(n)}Z(t){const e=this.i.groups||{};for(let n=0;n<t.length;n++)if(e[t[n]])return!0;return!1}M(t){if(!_t)return;const e=[];if(t.css){const n=document.createElement("style");n.innerHTML=t.css,document.head.appendChild(n),e.push((()=>n.remove()))}if(t.js){const n=document.createElement("script");n.innerHTML=t.js,document.head.appendChild(n),e.push((()=>n.remove()))}return t.domMutations&&t.domMutations.forEach((t=>{e.push(function(t){var e=t.selector,n=t.action,i=t.value,r=t.attribute,s=t.parentSelector,u=t.insertBeforeSelector;if("html"===r){if("append"===n)return nt(e,(function(t){return t+(null!=i?i:"")}));if("set"===n)return nt(e,(function(){return null!=i?i:""}))}else if("class"===r){if("append"===n)return it(e,(function(t){i&&t.add(i)}));if("remove"===n)return it(e,(function(t){i&&t.delete(i)}));if("set"===n)return it(e,(function(t){t.clear(),i&&t.add(i)}))}else if("position"===r){if("set"===n&&s)return function(t,e){return et({kind:"position",elements:new Set,mutate:function(){return{insertBeforeSelector:u,parentSelector:s}},selector:t})}(e)}else{if("append"===n)return rt(e,r,(function(t){return null!==t?t+(null!=i?i:""):null!=i?i:""}));if("set"===n)return rt(e,r,(function(){return null!=i?i:""}));if("remove"===n)return rt(e,r,(function(){return null}))}return N}(t).revert)})),()=>{e.forEach((t=>t()))}}tt(t){const e=new Set,n=t&&t.features?t.features:this.getFeatures(),i=t&&t.experiments?t.experiments:this.getExperiments();return Object.keys(n).forEach((t=>{const i=n[t];if(i.rules)for(const t of i.rules)t.variations&&(e.add(t.hashAttribute||"id"),t.fallbackAttribute&&e.add(t.fallbackAttribute))})),i.map((t=>{e.add(t.hashAttribute||"id"),t.fallbackAttribute&&e.add(t.fallbackAttribute)})),Array.from(e)}async refreshStickyBuckets(t){if(this.i.stickyBucketService){const e=this.et(t);this.i.stickyBucketAssignmentDocs=await this.i.stickyBucketService.getAllAssignments(e)}}nt(){const t={};return Object.values(this.i.stickyBucketAssignmentDocs||{}).forEach((e=>{e.assignments&&Object.assign(t,e.assignments)})),t}G(t,e,n,i){n=n||0,i=i||[];const r=this.Y(t,e=e||0),s=this.nt();if(n>0)for(let e=0;e<=n;e++)if(void 0!==s[this.Y(t,e)])return{variation:-1,versionIsBlocked:!0};const u=s[r];if(void 0===u)return{variation:-1};const o=i.findIndex((t=>t.key===u));return o<0?{variation:-1}:{variation:o}}Y(t,e){return e=e||0,"".concat(t,"__").concat(e)}et(t){const e={};return this.i.stickyBucketIdentifierAttributes=this.i.stickyBucketIdentifierAttributes?this.i.stickyBucketIdentifierAttributes:this.tt(t),this.i.stickyBucketIdentifierAttributes.forEach((t=>{const{hashValue:n}=this.H(t);e[t]=dt(n)})),e}X(t,e,n){const i="".concat(t,"||").concat(e),r=this.i.stickyBucketAssignmentDocs&&this.i.stickyBucketAssignmentDocs[i]&&this.i.stickyBucketAssignmentDocs[i].assignments||{},s={...r,...n};return{key:i,doc:{attributeName:t,attributeValue:e,assignments:s},changed:JSON.stringify(r)!==JSON.stringify(s)}}}class $t{async getAllAssignments(t){const e={};return(await Promise.all(Object.entries(t).map((t=>{let[e,n]=t;return this.getAssignments(e,n)})))).forEach((t=>{if(t){const n="".concat(t.attributeName,"||").concat(t.attributeValue);e[n]=t}})),e}}class Et extends $t{constructor(t){t=t||{},super(),this.prefix=t.prefix||"gbStickyBuckets__";try{this.localStorage=t.localStorage||globalThis.localStorage}catch(t){}}async getAssignments(t,e){const n="".concat(t,"||").concat(e);let i=null;if(!this.localStorage)return i;try{const t=await this.localStorage.getItem(this.prefix+n)||"{}",e=JSON.parse(t);e.attributeName&&e.attributeValue&&e.assignments&&(i=e)}catch(t){}return i}async saveAssignments(t){const e="".concat(t.attributeName,"||").concat(t.attributeValue);if(this.localStorage)try{await this.localStorage.setItem(this.prefix+e,JSON.stringify(t))}catch(t){}}}class Nt extends $t{constructor(t){let{prefix:e="gbStickyBuckets__",req:n,res:i,cookieAttributes:r={}}=t;super(),this.prefix=e,this.req=n,this.res=i,this.cookieAttributes=r}async getAssignments(t,e){const n="".concat(t,"||").concat(e);let i=null;if(!this.req)return i;try{const t=JSON.parse(this.req.cookies[this.prefix+n]||"{}");t.attributeName&&t.attributeValue&&t.assignments&&(i=t)}catch(t){}return i}async saveAssignments(t){const e="".concat(t.attributeName,"||").concat(t.attributeValue);if(!this.res)return;const n=JSON.stringify(t);this.res.cookie(encodeURIComponent(this.prefix+e),encodeURIComponent(n),this.cookieAttributes)}}class xt extends $t{constructor(t){let{prefix:e="gbStickyBuckets__",jsCookie:n,cookieAttributes:i={}}=t;super(),this.prefix=e,this.jsCookie=n,this.cookieAttributes=i}async getAssignments(t,e){const n="".concat(t,"||").concat(e);let i=null;if(!this.jsCookie)return i;try{const t=this.jsCookie.get(this.prefix+n),e=JSON.parse(t||"{}");e.attributeName&&e.attributeValue&&e.assignments&&(i=e)}catch(t){}return i}async saveAssignments(t){const e="".concat(t.attributeName,"||").concat(t.attributeValue);if(!this.jsCookie)return;const n=JSON.stringify(t);this.jsCookie.set(this.prefix+e,n,this.cookieAttributes)}}class Jt extends $t{constructor(t){let{redis:e}=t;super(),this.redis=e}async getAllAssignments(t){const e={},n=Object.entries(t).map((t=>{let[e,n]=t;return"".concat(e,"||").concat(n)}));return this.redis?(this.redis.mget(...n).then((t=>{t.forEach((t=>{try{const n=JSON.parse(t||"{}");if(n.attributeName&&n.attributeValue&&n.assignments){const t="".concat(n.attributeName,"||").concat(n.attributeValue);e[t]=n}}catch(t){}}))})),e):e}async getAssignments(t,e){return null}async saveAssignments(t){const e="".concat(t.attributeName,"||").concat(t.attributeValue);this.redis&&await this.redis.set(e,JSON.stringify(t))}}export{xt as BrowserCookieStickyBucketService,Nt as ExpressCookieStickyBucketService,Ot as GrowthBook,Et as LocalStorageStickyBucketService,Jt as RedisStickyBucketService,$t as StickyBucketService,l as clearCache,h as configureCache,n as helpers,at as isURLTargeted,f as onHidden,d as onVisible,a as setPolyfills};
//# sourceMappingURL=esm.min.js.map
