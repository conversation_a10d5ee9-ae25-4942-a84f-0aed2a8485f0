{"version": 3, "file": "auto.js", "sources": ["../../src/feature-repository.ts", "../../../../node_modules/dom-mutator/dist/dom-mutator.esm.js", "../../src/util.ts", "../../src/mongrule.ts", "../../src/GrowthBook.ts", "../../src/auto-wrapper.ts"], "sourcesContent": ["import {\n  Attributes,\n  CacheSettings,\n  FeatureApiResponse,\n  Helpers,\n  Polyfills,\n} from \"./types/growthbook\";\nimport type { GrowthBook } from \".\";\n\ntype CacheEntry = {\n  data: FeatureApiResponse;\n  sse?: boolean;\n  version: string;\n  staleAt: Date;\n};\ntype ScopedChannel = {\n  src: EventSource | null;\n  cb: (event: MessageEvent<string>) => void;\n  host: string;\n  clientKey: string;\n  headers?: Record<string, string>;\n  errors: number;\n  state: \"active\" | \"idle\" | \"disabled\";\n};\n\n// Config settings\nconst cacheSettings: CacheSettings = {\n  // Consider a fetch stale after 1 minute\n  staleTTL: 1000 * 60,\n  // Max time to keep a fetch in cache (24 hours default)\n  maxAge: 1000 * 60 * 60 * 24,\n  cacheKey: \"gbFeaturesCache\",\n  backgroundSync: true,\n  maxEntries: 10,\n  disableIdleStreams: false,\n  idleStreamInterval: 20000,\n};\nconst polyfills: Polyfills = {\n  fetch: globalThis.fetch ? globalThis.fetch.bind(globalThis) : undefined,\n  SubtleCrypto: globalThis.crypto ? globalThis.crypto.subtle : undefined,\n  EventSource: globalThis.EventSource,\n};\nexport const helpers: Helpers = {\n  fetchFeaturesCall: ({ host, clientKey, headers }) => {\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/features/${clientKey}`,\n      { headers }\n    );\n  },\n  fetchRemoteEvalCall: ({ host, clientKey, payload, headers }) => {\n    const options = {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\", ...headers },\n      body: JSON.stringify(payload),\n    };\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/eval/${clientKey}`,\n      options\n    );\n  },\n  eventSourceCall: ({ host, clientKey, headers }) => {\n    if (headers) {\n      return new polyfills.EventSource(`${host}/sub/${clientKey}`, {\n        headers,\n      });\n    }\n    return new polyfills.EventSource(`${host}/sub/${clientKey}`);\n  },\n  startIdleListener: () => {\n    let idleTimeout: number | undefined;\n    const isBrowser =\n      typeof window !== \"undefined\" && typeof document !== \"undefined\";\n    if (!isBrowser) return;\n    const onVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        window.clearTimeout(idleTimeout);\n        onVisible();\n      } else if (document.visibilityState === \"hidden\") {\n        idleTimeout = window.setTimeout(\n          onHidden,\n          cacheSettings.idleStreamInterval\n        );\n      }\n    };\n    document.addEventListener(\"visibilitychange\", onVisibilityChange);\n    return () =>\n      document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n  },\n  stopIdleListener: () => {\n    // No-op, replaced by startIdleListener\n  },\n};\n\ntry {\n  if (globalThis.localStorage) {\n    polyfills.localStorage = globalThis.localStorage;\n  }\n} catch (e) {\n  // Ignore localStorage errors\n}\n\n// Global state\nconst subscribedInstances: Map<string, Set<GrowthBook>> = new Map();\nlet cacheInitialized = false;\nconst cache: Map<string, CacheEntry> = new Map();\nconst activeFetches: Map<string, Promise<FeatureApiResponse>> = new Map();\nconst streams: Map<string, ScopedChannel> = new Map();\nconst supportsSSE: Set<string> = new Set();\n\n// Public functions\nexport function setPolyfills(overrides: Partial<Polyfills>): void {\n  Object.assign(polyfills, overrides);\n}\nexport function configureCache(overrides: Partial<CacheSettings>): void {\n  Object.assign(cacheSettings, overrides);\n  if (!cacheSettings.backgroundSync) {\n    clearAutoRefresh();\n  }\n}\n\nexport async function clearCache(): Promise<void> {\n  cache.clear();\n  activeFetches.clear();\n  clearAutoRefresh();\n  cacheInitialized = false;\n  await updatePersistentCache();\n}\n\nexport async function refreshFeatures(\n  instance: GrowthBook,\n  timeout?: number,\n  skipCache?: boolean,\n  allowStale?: boolean,\n  updateInstance?: boolean,\n  backgroundSync?: boolean\n): Promise<void> {\n  if (!backgroundSync) {\n    cacheSettings.backgroundSync = false;\n  }\n\n  const data = await fetchFeaturesWithCache(\n    instance,\n    allowStale,\n    timeout,\n    skipCache\n  );\n  updateInstance && data && (await refreshInstance(instance, data));\n}\n\n// Subscribe a GrowthBook instance to feature changes\nexport function subscribe(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const subs = subscribedInstances.get(key) || new Set();\n  subs.add(instance);\n  subscribedInstances.set(key, subs);\n}\nexport function unsubscribe(instance: GrowthBook): void {\n  subscribedInstances.forEach((s) => s.delete(instance));\n}\n\nexport function onHidden() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    channel.state = \"idle\";\n    disableChannel(channel);\n  });\n}\n\nexport function onVisible() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    if (channel.state !== \"idle\") return;\n    enableChannel(channel);\n  });\n}\n\n// Private functions\n\nasync function updatePersistentCache() {\n  try {\n    if (!polyfills.localStorage) return;\n    await polyfills.localStorage.setItem(\n      cacheSettings.cacheKey,\n      JSON.stringify(Array.from(cache.entries()))\n    );\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n}\n\nasync function fetchFeaturesWithCache(\n  instance: GrowthBook,\n  allowStale?: boolean,\n  timeout?: number,\n  skipCache?: boolean\n): Promise<FeatureApiResponse | null> {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const now = new Date();\n\n  const minStaleAt = new Date(\n    now.getTime() - cacheSettings.maxAge + cacheSettings.staleTTL\n  );\n\n  await initializeCache();\n  const existing = cache.get(cacheKey);\n  if (\n    existing &&\n    !skipCache &&\n    (allowStale || existing.staleAt > now) &&\n    existing.staleAt > minStaleAt\n  ) {\n    // Restore from cache whether SSE is supported\n    if (existing.sse) supportsSSE.add(key);\n\n    // Reload features in the background if stale\n    if (existing.staleAt < now) {\n      fetchFeatures(instance);\n    }\n    // Otherwise, if we don't need to refresh now, start a background sync\n    else {\n      startAutoRefresh(instance);\n    }\n    return existing.data;\n  } else {\n    return await promiseTimeout(fetchFeatures(instance), timeout);\n  }\n}\n\nfunction getKey(instance: GrowthBook): string {\n  const [apiHost, clientKey] = instance.getApiInfo();\n  return `${apiHost}||${clientKey}`;\n}\n\nfunction getCacheKey(instance: GrowthBook): string {\n  const baseKey = getKey(instance);\n  if (!instance.isRemoteEval()) return baseKey;\n\n  const attributes = instance.getAttributes();\n  const cacheKeyAttributes =\n    instance.getCacheKeyAttributes() || Object.keys(instance.getAttributes());\n  const ca: Attributes = {};\n  cacheKeyAttributes.forEach((key) => {\n    ca[key] = attributes[key];\n  });\n\n  const fv = instance.getForcedVariations();\n  const url = instance.getUrl();\n\n  return `${baseKey}||${JSON.stringify({\n    ca,\n    fv,\n    url,\n  })}`;\n}\n\n// Guarantee the promise always resolves within {timeout} ms\n// Resolved value will be `null` when there's an error or it takes too long\n// Note: The promise will continue running in the background, even if the timeout is hit\nfunction promiseTimeout<T>(\n  promise: Promise<T>,\n  timeout?: number\n): Promise<T | null> {\n  return new Promise((resolve) => {\n    let resolved = false;\n    let timer: unknown;\n    const finish = (data?: T) => {\n      if (resolved) return;\n      resolved = true;\n      timer && clearTimeout(timer as NodeJS.Timer);\n      resolve(data || null);\n    };\n\n    if (timeout) {\n      timer = setTimeout(() => finish(), timeout);\n    }\n\n    promise.then((data) => finish(data)).catch(() => finish());\n  });\n}\n\n// Populate cache from localStorage (if available)\nasync function initializeCache(): Promise<void> {\n  if (cacheInitialized) return;\n  cacheInitialized = true;\n  try {\n    if (polyfills.localStorage) {\n      const value = await polyfills.localStorage.getItem(\n        cacheSettings.cacheKey\n      );\n      if (value) {\n        const parsed: [string, CacheEntry][] = JSON.parse(value);\n        if (parsed && Array.isArray(parsed)) {\n          parsed.forEach(([key, data]) => {\n            cache.set(key, {\n              ...data,\n              staleAt: new Date(data.staleAt),\n            });\n          });\n        }\n        cleanupCache();\n      }\n    }\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n  if (!cacheSettings.disableIdleStreams) {\n    const cleanupFn = helpers.startIdleListener();\n    if (cleanupFn) {\n      helpers.stopIdleListener = cleanupFn;\n    }\n  }\n}\n\n// Enforce the maxEntries limit\nfunction cleanupCache() {\n  const entriesWithTimestamps = Array.from(cache.entries())\n    .map(([key, value]) => ({\n      key,\n      staleAt: value.staleAt.getTime(),\n    }))\n    .sort((a, b) => a.staleAt - b.staleAt);\n\n  const entriesToRemoveCount = Math.min(\n    Math.max(0, cache.size - cacheSettings.maxEntries),\n    cache.size\n  );\n\n  for (let i = 0; i < entriesToRemoveCount; i++) {\n    cache.delete(entriesWithTimestamps[i].key);\n  }\n}\n\n// Called whenever new features are fetched from the API\nfunction onNewFeatureData(\n  key: string,\n  cacheKey: string,\n  data: FeatureApiResponse\n): void {\n  // If contents haven't changed, ignore the update, extend the stale TTL\n  const version = data.dateUpdated || \"\";\n  const staleAt = new Date(Date.now() + cacheSettings.staleTTL);\n  const existing = cache.get(cacheKey);\n  if (existing && version && existing.version === version) {\n    existing.staleAt = staleAt;\n    updatePersistentCache();\n    return;\n  }\n\n  // Update in-memory cache\n  cache.set(cacheKey, {\n    data,\n    version,\n    staleAt,\n    sse: supportsSSE.has(key),\n  });\n  cleanupCache();\n  // Update local storage (don't await this, just update asynchronously)\n  updatePersistentCache();\n\n  // Update features for all subscribed GrowthBook instances\n  const instances = subscribedInstances.get(key);\n  instances && instances.forEach((instance) => refreshInstance(instance, data));\n}\n\nasync function refreshInstance(\n  instance: GrowthBook,\n  data: FeatureApiResponse\n): Promise<void> {\n  data = await instance.decryptPayload(data, undefined, polyfills.SubtleCrypto);\n\n  await instance.refreshStickyBuckets(data);\n  instance.setExperiments(data.experiments || instance.getExperiments());\n  instance.setFeatures(data.features || instance.getFeatures());\n}\n\nasync function fetchFeatures(\n  instance: GrowthBook\n): Promise<FeatureApiResponse> {\n  const { apiHost, apiRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  const remoteEval = instance.isRemoteEval();\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n\n  let promise = activeFetches.get(cacheKey);\n  if (!promise) {\n    const fetcher: Promise<Response> = remoteEval\n      ? helpers.fetchRemoteEvalCall({\n          host: apiHost,\n          clientKey,\n          payload: {\n            attributes: instance.getAttributes(),\n            forcedVariations: instance.getForcedVariations(),\n            forcedFeatures: Array.from(instance.getForcedFeatures().entries()),\n            url: instance.getUrl(),\n          },\n          headers: apiRequestHeaders,\n        })\n      : helpers.fetchFeaturesCall({\n          host: apiHost,\n          clientKey,\n          headers: apiRequestHeaders,\n        });\n\n    // TODO: auto-retry if status code indicates a temporary error\n    promise = fetcher\n      .then((res) => {\n        if (res.headers.get(\"x-sse-support\") === \"enabled\") {\n          supportsSSE.add(key);\n        }\n        return res.json();\n      })\n      .then((data: FeatureApiResponse) => {\n        onNewFeatureData(key, cacheKey, data);\n        startAutoRefresh(instance);\n        activeFetches.delete(cacheKey);\n        return data;\n      })\n      .catch((e) => {\n        process.env.NODE_ENV !== \"production\" &&\n          instance.log(\"Error fetching features\", {\n            apiHost,\n            clientKey,\n            error: e ? e.message : null,\n          });\n        activeFetches.delete(cacheKey);\n        return Promise.resolve({});\n      });\n    activeFetches.set(cacheKey, promise);\n  }\n  return await promise;\n}\n\n// Watch a feature endpoint for changes\n// Will prefer SSE if enabled, otherwise fall back to cron\nfunction startAutoRefresh(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const { streamingHost, streamingHostRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  if (\n    cacheSettings.backgroundSync &&\n    supportsSSE.has(key) &&\n    polyfills.EventSource\n  ) {\n    if (streams.has(key)) return;\n    const channel: ScopedChannel = {\n      src: null,\n      host: streamingHost,\n      clientKey,\n      headers: streamingHostRequestHeaders,\n      cb: (event: MessageEvent<string>) => {\n        try {\n          if (event.type === \"features-updated\") {\n            const instances = subscribedInstances.get(key);\n            instances &&\n              instances.forEach((instance) => {\n                fetchFeatures(instance);\n              });\n          } else if (event.type === \"features\") {\n            const json: FeatureApiResponse = JSON.parse(event.data);\n            onNewFeatureData(key, cacheKey, json);\n          }\n          // Reset error count on success\n          channel.errors = 0;\n        } catch (e) {\n          process.env.NODE_ENV !== \"production\" &&\n            instance.log(\"SSE Error\", {\n              streamingHost,\n              clientKey,\n              error: e ? (e as Error).message : null,\n            });\n          onSSEError(channel);\n        }\n      },\n      errors: 0,\n      state: \"active\",\n    };\n    streams.set(key, channel);\n    enableChannel(channel);\n  }\n}\n\nfunction onSSEError(channel: ScopedChannel) {\n  if (channel.state === \"idle\") return;\n  channel.errors++;\n  if (channel.errors > 3 || (channel.src && channel.src.readyState === 2)) {\n    // exponential backoff after 4 errors, with jitter\n    const delay =\n      Math.pow(3, channel.errors - 3) * (1000 + Math.random() * 1000);\n    disableChannel(channel);\n    setTimeout(() => {\n      if ([\"idle\", \"active\"].includes(channel.state)) return;\n      enableChannel(channel);\n    }, Math.min(delay, 300000)); // 5 minutes max\n  }\n}\n\nfunction disableChannel(channel: ScopedChannel) {\n  if (!channel.src) return;\n  channel.src.onopen = null;\n  channel.src.onerror = null;\n  channel.src.close();\n  channel.src = null;\n  if (channel.state === \"active\") {\n    channel.state = \"disabled\";\n  }\n}\n\nfunction enableChannel(channel: ScopedChannel) {\n  channel.src = helpers.eventSourceCall({\n    host: channel.host,\n    clientKey: channel.clientKey,\n    headers: channel.headers,\n  }) as EventSource;\n  channel.state = \"active\";\n  channel.src.addEventListener(\"features\", channel.cb);\n  channel.src.addEventListener(\"features-updated\", channel.cb);\n  channel.src.onerror = () => onSSEError(channel);\n  channel.src.onopen = () => {\n    channel.errors = 0;\n  };\n}\n\nfunction destroyChannel(channel: ScopedChannel, key: string) {\n  disableChannel(channel);\n  streams.delete(key);\n}\n\nfunction clearAutoRefresh() {\n  // Clear list of which keys are auto-updated\n  supportsSSE.clear();\n\n  // Stop listening for any SSE events\n  streams.forEach(destroyChannel);\n\n  // Remove all references to GrowthBook instances\n  subscribedInstances.clear();\n\n  // Run the idle stream cleanup function\n  helpers.stopIdleListener();\n}\n", "var validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nvar nullController = {\n  revert: function revert() {}\n};\nvar elements = /*#__PURE__*/new Map();\nvar mutations = /*#__PURE__*/new Set();\n\nfunction getObserverInit(attr) {\n  return attr === 'html' ? {\n    childList: true,\n    subtree: true,\n    attributes: true,\n    characterData: true\n  } : {\n    childList: false,\n    subtree: false,\n    attributes: true,\n    attributeFilter: [attr]\n  };\n}\n\nfunction getElementRecord(element) {\n  var record = elements.get(element);\n\n  if (!record) {\n    record = {\n      element: element,\n      attributes: {}\n    };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(el, attr, getCurrentValue, setValue, mutationRunner) {\n  var currentValue = getCurrentValue(el);\n  var record = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el: el,\n    _positionTimeout: null,\n    observer: new MutationObserver(function () {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;else if (attr === 'position') record._positionTimeout = setTimeout(function () {\n        record._positionTimeout = null;\n      }, 1000);\n      var currentValue = getCurrentValue(el);\n      if (attr === 'position' && currentValue.parentNode === record.virtualValue.parentNode && currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode) return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner: mutationRunner,\n    setValue: setValue,\n    getCurrentValue: getCurrentValue\n  };\n\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n\n  return record;\n}\n\nfunction queueIfNeeded(val, record) {\n  var currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n\n  if (val && typeof val !== 'string') {\n    if (!currentVal || val.parentNode !== currentVal.parentNode || val.insertBeforeNode !== currentVal.insertBeforeNode) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(getTransformedHTML(val), record);\n}\n\nfunction classMutationRunner(record) {\n  var val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(function (m) {\n    return m.mutate(val);\n  });\n  queueIfNeeded(Array.from(val).filter(Boolean).join(' '), record);\n}\n\nfunction attrMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes(_ref) {\n  var parentSelector = _ref.parentSelector,\n      insertBeforeSelector = _ref.insertBeforeSelector;\n  var parentNode = document.querySelector(parentSelector);\n  if (!parentNode) return null;\n  var insertBeforeNode = insertBeforeSelector ? document.querySelector(insertBeforeSelector) : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode: parentNode,\n    insertBeforeNode: insertBeforeNode\n  };\n}\n\nfunction positionMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    var selectors = m.mutate();\n\n    var newNodes = _loadDOMNodes(selectors);\n\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nvar getHTMLValue = function getHTMLValue(el) {\n  return el.innerHTML;\n};\n\nvar setHTMLValue = function setHTMLValue(el, value) {\n  return el.innerHTML = value;\n};\n\nfunction getElementHTMLRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(element, 'html', getHTMLValue, setHTMLValue, htmlMutationRunner);\n  }\n\n  return elementRecord.html;\n}\n\nvar getElementPosition = function getElementPosition(el) {\n  return {\n    parentNode: el.parentElement,\n    insertBeforeNode: el.nextElementSibling\n  };\n};\n\nvar setElementPosition = function setElementPosition(el, value) {\n  if (value.insertBeforeNode && !value.parentNode.contains(value.insertBeforeNode)) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\n\nfunction getElementPositionRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(element, 'position', getElementPosition, setElementPosition, positionMutationRunner);\n  }\n\n  return elementRecord.position;\n}\n\nvar setClassValue = function setClassValue(el, val) {\n  return val ? el.className = val : el.removeAttribute('class');\n};\n\nvar getClassValue = function getClassValue(el) {\n  return el.className;\n};\n\nfunction getElementClassRecord(el) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(el, 'class', getClassValue, setClassValue, classMutationRunner);\n  }\n\n  return elementRecord.classes;\n}\n\nvar getAttrValue = function getAttrValue(attrName) {\n  return function (el) {\n    var _el$getAttribute;\n\n    return (_el$getAttribute = el.getAttribute(attrName)) != null ? _el$getAttribute : null;\n  };\n};\n\nvar setAttrValue = function setAttrValue(attrName) {\n  return function (el, val) {\n    return val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\n  };\n};\n\nfunction getElementAttributeRecord(el, attr) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(el, attr, getAttrValue(attr), setAttrValue(attr), attrMutationRunner);\n  }\n\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el, attr) {\n  var element = elements.get(el);\n  if (!element) return;\n\n  if (attr === 'html') {\n    var _element$html, _element$html$observe;\n\n    (_element$html = element.html) == null ? void 0 : (_element$html$observe = _element$html.observer) == null ? void 0 : _element$html$observe.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    var _element$classes, _element$classes$obse;\n\n    (_element$classes = element.classes) == null ? void 0 : (_element$classes$obse = _element$classes.observer) == null ? void 0 : _element$classes$obse.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    var _element$position, _element$position$obs;\n\n    (_element$position = element.position) == null ? void 0 : (_element$position$obs = _element$position.observer) == null ? void 0 : _element$position$obs.disconnect();\n    delete element.position;\n  } else {\n    var _element$attributes, _element$attributes$a, _element$attributes$a2;\n\n    (_element$attributes = element.attributes) == null ? void 0 : (_element$attributes$a = _element$attributes[attr]) == null ? void 0 : (_element$attributes$a2 = _element$attributes$a.observer) == null ? void 0 : _element$attributes$a2.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nvar transformContainer;\n\nfunction getTransformedHTML(html) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue(el, attr, m) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  var val = m.virtualValue;\n\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n\n  m.setValue(el, val);\n}\n\nfunction setValue(m, el) {\n  m.html && setPropertyValue(el, 'html', m.html);\n  m.classes && setPropertyValue(el, 'class', m.classes);\n  m.position && setPropertyValue(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(function (attr) {\n    setPropertyValue(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n} // find or create ElementPropertyRecord, add mutation to it, then run\n\n\nfunction startMutating(mutation, element) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n} // get (existing) ElementPropertyRecord, remove mutation from it, then run\n\n\nfunction stopMutating(mutation, el) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n\n  if (!record) return;\n  var index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n} // maintain list of elements associated with mutation\n\n\nfunction refreshElementsSet(mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n  var existingElements = new Set(mutation.elements);\n  var matchingElements = document.querySelectorAll(mutation.selector);\n  matchingElements.forEach(function (el) {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation) {\n  mutation.elements.forEach(function (el) {\n    return stopMutating(mutation, el);\n  });\n  mutation.elements.clear();\n  mutations[\"delete\"](mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n} // Observer for elements that don't exist in the DOM yet\n\n\nvar observer;\nfunction disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nfunction connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(function () {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false\n  });\n} // run on init\n\nconnectGlobalObserver();\n\nfunction newMutation(m) {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController; // add to global index of mutations\n\n  mutations.add(m); // run refresh on init to establish list of elements associated w/ mutation\n\n  refreshElementsSet(m);\n  return {\n    revert: function revert() {\n      revertMutation(m);\n    }\n  };\n}\n\nfunction html(selector, mutate) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction position(selector, mutate) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction classes(selector, mutate) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction attribute(selector, attribute, mutate) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, function (classnames) {\n      var mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames.split(/\\s+/g).filter(Boolean).forEach(function (c) {\n        return classnames.add(c);\n      });\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute: attribute,\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction declarative(_ref2) {\n  var selector = _ref2.selector,\n      action = _ref2.action,\n      value = _ref2.value,\n      attr = _ref2.attribute,\n      parentSelector = _ref2.parentSelector,\n      insertBeforeSelector = _ref2.insertBeforeSelector;\n\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, function (val) {\n        return val + (value != null ? value : '');\n      });\n    } else if (action === 'set') {\n      return html(selector, function () {\n        return value != null ? value : '';\n      });\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, function (val) {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, function (val) {\n        if (value) val[\"delete\"](value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, function (val) {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, function () {\n        return {\n          insertBeforeSelector: insertBeforeSelector,\n          parentSelector: parentSelector\n        };\n      });\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, function (val) {\n        return val !== null ? val + (value != null ? value : '') : value != null ? value : '';\n      });\n    } else if (action === 'set') {\n      return attribute(selector, attr, function () {\n        return value != null ? value : '';\n      });\n    } else if (action === 'remove') {\n      return attribute(selector, attr, function () {\n        return null;\n      });\n    }\n  }\n\n  return nullController;\n}\n\nvar index = {\n  html: html,\n  classes: classes,\n  attribute: attribute,\n  position: position,\n  declarative: declarative\n};\n\nexport default index;\nexport { connectGlobalObserver, disconnectGlobalObserver, validAttributeName };\n//# sourceMappingURL=dom-mutator.esm.js.map\n", "import { UrlTarget, UrlTargetType, VariationRange } from \"./types/growthbook\";\n\nfunction hashFnv32a(str: string): number {\n  let hval = 0x811c9dc5;\n  const l = str.length;\n\n  for (let i = 0; i < l; i++) {\n    hval ^= str.charCodeAt(i);\n    hval +=\n      (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);\n  }\n  return hval >>> 0;\n}\n\nexport function hash(\n  seed: string,\n  value: string,\n  version: number\n): number | null {\n  // New unbiased hashing algorithm\n  if (version === 2) {\n    return (hashFnv32a(hashFnv32a(seed + value) + \"\") % 10000) / 10000;\n  }\n  // Original biased hashing algorithm (keep for backwards compatibility)\n  if (version === 1) {\n    return (hashFnv32a(value + seed) % 1000) / 1000;\n  }\n\n  // Unknown hash version\n  return null;\n}\n\nexport function getEqualWeights(n: number): number[] {\n  if (n <= 0) return [];\n  return new Array(n).fill(1 / n);\n}\n\nexport function inRange(n: number, range: VariationRange): boolean {\n  return n >= range[0] && n < range[1];\n}\n\nexport function inNamespace(\n  hashValue: string,\n  namespace: [string, number, number]\n): boolean {\n  const n = hash(\"__\" + namespace[0], hashValue, 1);\n  if (n === null) return false;\n  return n >= namespace[1] && n < namespace[2];\n}\n\nexport function chooseVariation(n: number, ranges: VariationRange[]): number {\n  for (let i = 0; i < ranges.length; i++) {\n    if (inRange(n, ranges[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nexport function getUrlRegExp(regexString: string): RegExp | undefined {\n  try {\n    const escaped = regexString.replace(/([^\\\\])\\//g, \"$1\\\\/\");\n    return new RegExp(escaped);\n  } catch (e) {\n    console.error(e);\n    return undefined;\n  }\n}\n\nexport function isURLTargeted(url: string, targets: UrlTarget[]) {\n  if (!targets.length) return false;\n  let hasIncludeRules = false;\n  let isIncluded = false;\n\n  for (let i = 0; i < targets.length; i++) {\n    const match = _evalURLTarget(url, targets[i].type, targets[i].pattern);\n    if (targets[i].include === false) {\n      if (match) return false;\n    } else {\n      hasIncludeRules = true;\n      if (match) isIncluded = true;\n    }\n  }\n\n  return isIncluded || !hasIncludeRules;\n}\n\nfunction _evalSimpleUrlPart(\n  actual: string,\n  pattern: string,\n  isPath: boolean\n): boolean {\n  try {\n    // Escape special regex characters and change wildcard `_____` to `.*`\n    let escaped = pattern\n      .replace(/[*.+?^${}()|[\\]\\\\]/g, \"\\\\$&\")\n      .replace(/_____/g, \".*\");\n\n    if (isPath) {\n      // When matching pathname, make leading/trailing slashes optional\n      escaped = \"\\\\/?\" + escaped.replace(/(^\\/|\\/$)/g, \"\") + \"\\\\/?\";\n    }\n\n    const regex = new RegExp(\"^\" + escaped + \"$\", \"i\");\n    return regex.test(actual);\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalSimpleUrlTarget(actual: URL, pattern: string) {\n  try {\n    // If a protocol is missing, but a host is specified, add `https://` to the front\n    // Use \"_____\" as the wildcard since `*` is not a valid hostname in some browsers\n    const expected = new URL(\n      pattern.replace(/^([^:/?]*)\\./i, \"https://$1.\").replace(/\\*/g, \"_____\"),\n      \"https://_____\"\n    );\n\n    // Compare each part of the URL separately\n    const comps: Array<[string, string, boolean]> = [\n      [actual.host, expected.host, false],\n      [actual.pathname, expected.pathname, true],\n    ];\n    // We only want to compare hashes if it's explicitly being targeted\n    if (expected.hash) {\n      comps.push([actual.hash, expected.hash, false]);\n    }\n\n    expected.searchParams.forEach((v, k) => {\n      comps.push([actual.searchParams.get(k) || \"\", v, false]);\n    });\n\n    // If any comparisons fail, the whole thing fails\n    return !comps.some(\n      (data) => !_evalSimpleUrlPart(data[0], data[1], data[2])\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalURLTarget(\n  url: string,\n  type: UrlTargetType,\n  pattern: string\n): boolean {\n  try {\n    const parsed = new URL(url, \"https://_\");\n\n    if (type === \"regex\") {\n      const regex = getUrlRegExp(pattern);\n      if (!regex) return false;\n      return (\n        regex.test(parsed.href) ||\n        regex.test(parsed.href.substring(parsed.origin.length))\n      );\n    } else if (type === \"simple\") {\n      return _evalSimpleUrlTarget(parsed, pattern);\n    }\n\n    return false;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function getBucketRanges(\n  numVariations: number,\n  coverage: number | undefined,\n  weights?: number[]\n): VariationRange[] {\n  coverage = coverage === undefined ? 1 : coverage;\n\n  // Make sure coverage is within bounds\n  if (coverage < 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be greater than or equal to 0\");\n    }\n    coverage = 0;\n  } else if (coverage > 1) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be less than or equal to 1\");\n    }\n    coverage = 1;\n  }\n\n  // Default to equal weights if missing or invalid\n  const equal = getEqualWeights(numVariations);\n  weights = weights || equal;\n  if (weights.length !== numVariations) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\n        \"Experiment.weights array must be the same length as Experiment.variations\"\n      );\n    }\n    weights = equal;\n  }\n\n  // If weights don't add up to 1 (or close to it), default to equal weights\n  const totalWeight = weights.reduce((w, sum) => sum + w, 0);\n  if (totalWeight < 0.99 || totalWeight > 1.01) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.weights must add up to 1\");\n    }\n    weights = equal;\n  }\n\n  // Covert weights to ranges\n  let cumulative = 0;\n  return weights.map((w) => {\n    const start = cumulative;\n    cumulative += w;\n    return [start, start + (coverage as number) * w];\n  }) as VariationRange[];\n}\n\nexport function getQueryStringOverride(\n  id: string,\n  url: string,\n  numVariations: number\n) {\n  if (!url) {\n    return null;\n  }\n\n  const search = url.split(\"?\")[1];\n  if (!search) {\n    return null;\n  }\n\n  const match = search\n    .replace(/#.*/, \"\") // Get rid of anchor\n    .split(\"&\") // Split into key/value pairs\n    .map((kv) => kv.split(\"=\", 2))\n    .filter(([k]) => k === id) // Look for key that matches the experiment id\n    .map(([, v]) => parseInt(v)); // Parse the value into an integer\n\n  if (match.length > 0 && match[0] >= 0 && match[0] < numVariations)\n    return match[0];\n\n  return null;\n}\n\nexport function isIncluded(include: () => boolean) {\n  try {\n    return include();\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\n\nconst base64ToBuf = (b: string) =>\n  Uint8Array.from(atob(b), (c) => c.charCodeAt(0));\n\nexport async function decrypt(\n  encryptedString: string,\n  decryptionKey?: string,\n  subtle?: SubtleCrypto\n): Promise<string> {\n  decryptionKey = decryptionKey || \"\";\n  subtle = subtle || (globalThis.crypto && globalThis.crypto.subtle);\n  if (!subtle) {\n    throw new Error(\"No SubtleCrypto implementation found\");\n  }\n  try {\n    const key = await subtle.importKey(\n      \"raw\",\n      base64ToBuf(decryptionKey),\n      { name: \"AES-CBC\", length: 128 },\n      true,\n      [\"encrypt\", \"decrypt\"]\n    );\n    const [iv, cipherText] = encryptedString.split(\".\");\n    const plainTextBuffer = await subtle.decrypt(\n      { name: \"AES-CBC\", iv: base64ToBuf(iv) },\n      key,\n      base64ToBuf(cipherText)\n    );\n\n    return new TextDecoder().decode(plainTextBuffer);\n  } catch (e) {\n    throw new Error(\"Failed to decrypt\");\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function toString(input: any): string {\n  if (typeof input === \"string\") return input;\n  return JSON.stringify(input);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function paddedVersionString(input: any): string {\n  if (typeof input === \"number\") {\n    input = input + \"\";\n  }\n  if (!input || typeof input !== \"string\") {\n    input = \"0\";\n  }\n  // Remove build info and leading `v` if any\n  // Split version into parts (both core version numbers and pre-release tags)\n  // \"v1.2.3-rc.1+build123\" -> [\"1\",\"2\",\"3\",\"rc\",\"1\"]\n  const parts = (input as string).replace(/(^v|\\+.*$)/g, \"\").split(/[-.]/);\n\n  // If it's SemVer without a pre-release, add `~` to the end\n  // [\"1\",\"0\",\"0\"] -> [\"1\",\"0\",\"0\",\"~\"]\n  // \"~\" is the largest ASCII character, so this will make \"1.0.0\" greater than \"1.0.0-beta\" for example\n  if (parts.length === 3) {\n    parts.push(\"~\");\n  }\n\n  // Left pad each numeric part with spaces so string comparisons will work (\"9\">\"10\", but \" 9\"<\"10\")\n  // Then, join back together into a single string\n  return parts\n    .map((v) => (v.match(/^[0-9]+$/) ? v.padStart(5, \" \") : v))\n    .join(\"-\");\n}\n\nexport function loadSDKVersion(): string {\n  let version: string;\n  try {\n    // @ts-expect-error right-hand value to be replaced by build with string literal\n    version = __SDK_VERSION__;\n  } catch (e) {\n    version = \"\";\n  }\n  return version;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport {\n  ConditionInterface,\n  TestedObj,\n  ConditionValue,\n  Operator,\n  OperatorConditionValue,\n  VarType,\n} from \"./types/mongrule\";\nimport { paddedVersionString } from \"./util\";\n\nconst _regexCache: { [key: string]: RegExp } = {};\n\n// The top-level condition evaluation function\nexport function evalCondition(\n  obj: TestedObj,\n  condition: ConditionInterface\n): boolean {\n  // Recursive condition\n  if (\"$or\" in condition) {\n    return evalOr(obj, condition[\"$or\"] as ConditionInterface[]);\n  }\n  if (\"$nor\" in condition) {\n    return !evalOr(obj, condition[\"$nor\"] as ConditionInterface[]);\n  }\n  if (\"$and\" in condition) {\n    return evalAnd(obj, condition[\"$and\"] as ConditionInterface[]);\n  }\n  if (\"$not\" in condition) {\n    return !evalCondition(obj, condition[\"$not\"] as ConditionInterface);\n  }\n\n  // Condition is an object, keys are object paths, values are the condition for that path\n  for (const [k, v] of Object.entries(condition)) {\n    if (!evalConditionValue(v, getPath(obj, k))) return false;\n  }\n  return true;\n}\n\n// Return value at dot-separated path of an object\nfunction getPath(obj: TestedObj, path: string) {\n  const parts = path.split(\".\");\n  let current: any = obj;\n  for (let i = 0; i < parts.length; i++) {\n    if (current && typeof current === \"object\" && parts[i] in current) {\n      current = current[parts[i]];\n    } else {\n      return null;\n    }\n  }\n  return current;\n}\n\n// Transform a regex string into a real RegExp object\nfunction getRegex(regex: string): RegExp {\n  if (!_regexCache[regex]) {\n    _regexCache[regex] = new RegExp(regex.replace(/([^\\\\])\\//g, \"$1\\\\/\"));\n  }\n  return _regexCache[regex];\n}\n\n// Evaluate a single value against a condition\nfunction evalConditionValue(condition: ConditionValue, value: any) {\n  // Simple equality comparisons\n  if (typeof condition === \"string\") {\n    return value + \"\" === condition;\n  }\n  if (typeof condition === \"number\") {\n    return value * 1 === condition;\n  }\n  if (typeof condition === \"boolean\") {\n    return !!value === condition;\n  }\n\n  if (condition === null) {\n    return value === null;\n  }\n\n  if (Array.isArray(condition) || !isOperatorObject(condition)) {\n    return JSON.stringify(value) === JSON.stringify(condition);\n  }\n\n  // This is a special operator condition and we should evaluate each one separately\n  for (const op in condition) {\n    if (\n      !evalOperatorCondition(\n        op as Operator,\n        value,\n        condition[op as keyof OperatorConditionValue]\n      )\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// If the object has only keys that start with '$'\nfunction isOperatorObject(obj: any): boolean {\n  const keys = Object.keys(obj);\n  return (\n    keys.length > 0 && keys.filter((k) => k[0] === \"$\").length === keys.length\n  );\n}\n\n// Return the data type of a value\nfunction getType(v: any): VarType | \"unknown\" {\n  if (v === null) return \"null\";\n  if (Array.isArray(v)) return \"array\";\n  const t = typeof v;\n  if ([\"string\", \"number\", \"boolean\", \"object\", \"undefined\"].includes(t)) {\n    return t as VarType;\n  }\n  return \"unknown\";\n}\n\n// At least one element of actual must match the expected condition/value\nfunction elemMatch(actual: any, expected: any) {\n  if (!Array.isArray(actual)) return false;\n  const check = isOperatorObject(expected)\n    ? (v: any) => evalConditionValue(expected, v)\n    : (v: any) => evalCondition(v, expected);\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i] && check(actual[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isIn(actual: any, expected: Array<any>): boolean {\n  // Do an intersection is attribute is an array\n  if (Array.isArray(actual)) {\n    return actual.some((el) => expected.includes(el));\n  }\n  return expected.includes(actual);\n}\n\n// Evaluate a single operator condition\nfunction evalOperatorCondition(\n  operator: Operator,\n  actual: any,\n  expected: any\n): boolean {\n  switch (operator) {\n    case \"$veq\":\n      return paddedVersionString(actual) === paddedVersionString(expected);\n    case \"$vne\":\n      return paddedVersionString(actual) !== paddedVersionString(expected);\n    case \"$vgt\":\n      return paddedVersionString(actual) > paddedVersionString(expected);\n    case \"$vgte\":\n      return paddedVersionString(actual) >= paddedVersionString(expected);\n    case \"$vlt\":\n      return paddedVersionString(actual) < paddedVersionString(expected);\n    case \"$vlte\":\n      return paddedVersionString(actual) <= paddedVersionString(expected);\n    case \"$eq\":\n      return actual === expected;\n    case \"$ne\":\n      return actual !== expected;\n    case \"$lt\":\n      return actual < expected;\n    case \"$lte\":\n      return actual <= expected;\n    case \"$gt\":\n      return actual > expected;\n    case \"$gte\":\n      return actual >= expected;\n    case \"$exists\":\n      // Using `!=` and `==` instead of strict checks so it also matches for undefined\n      return expected ? actual != null : actual == null;\n    case \"$in\":\n      if (!Array.isArray(expected)) return false;\n      return isIn(actual, expected);\n    case \"$nin\":\n      if (!Array.isArray(expected)) return false;\n      return !isIn(actual, expected);\n    case \"$not\":\n      return !evalConditionValue(expected, actual);\n    case \"$size\":\n      if (!Array.isArray(actual)) return false;\n      return evalConditionValue(expected, actual.length);\n    case \"$elemMatch\":\n      return elemMatch(actual, expected);\n    case \"$all\":\n      if (!Array.isArray(actual)) return false;\n      for (let i = 0; i < expected.length; i++) {\n        let passed = false;\n        for (let j = 0; j < actual.length; j++) {\n          if (evalConditionValue(expected[i], actual[j])) {\n            passed = true;\n            break;\n          }\n        }\n        if (!passed) return false;\n      }\n      return true;\n    case \"$regex\":\n      try {\n        return getRegex(expected).test(actual);\n      } catch (e) {\n        return false;\n      }\n    case \"$type\":\n      return getType(actual) === expected;\n    default:\n      console.error(\"Unknown operator: \" + operator);\n      return false;\n  }\n}\n\n// Recursive $or rule\nfunction evalOr(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  if (!conditions.length) return true;\n  for (let i = 0; i < conditions.length; i++) {\n    if (evalCondition(obj, conditions[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// Recursive $and rule\nfunction evalAnd(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  for (let i = 0; i < conditions.length; i++) {\n    if (!evalCondition(obj, conditions[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n", "import mutate, { DeclarativeMutation } from \"dom-mutator\";\nimport type {\n  ApiHost,\n  Attributes,\n  AutoExperiment,\n  AutoExperimentVariation,\n  ClientKey,\n  Context,\n  Experiment,\n  FeatureApiResponse,\n  FeatureDefinition,\n  FeatureResult,\n  FeatureResultSource,\n  Filter,\n  LoadFeaturesOptions,\n  RealtimeUsageData,\n  RefreshFeaturesOptions,\n  Result,\n  StickyAssignments,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n  StickyExperimentKey,\n  SubscriptionFunction,\n  VariationMeta,\n  VariationRange,\n  WidenPrimitives,\n} from \"./types/growthbook\";\nimport type { ConditionInterface } from \"./types/mongrule\";\nimport {\n  chooseVariation,\n  decrypt,\n  getBucketRanges,\n  getQueryStringOverride,\n  getUrlRegExp,\n  hash,\n  inNamespace,\n  inRange,\n  isIncluded,\n  isURLTargeted,\n  loadSDKVersion,\n  toString,\n} from \"./util\";\nimport { evalCondition } from \"./mongrule\";\nimport { refreshFeatures, subscribe, unsubscribe } from \"./feature-repository\";\n\nconst isBrowser =\n  typeof window !== \"undefined\" && typeof document !== \"undefined\";\n\nconst SDK_VERSION = loadSDKVersion();\n\nexport class GrowthBook<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  AppFeatures extends Record<string, any> = Record<string, any>\n> {\n  // context is technically private, but some tools depend on it so we can't mangle the name\n  // _ctx below is a clone of this property that we use internally\n  private context: Context;\n  public debug: boolean;\n  public ready: boolean;\n  public version: string;\n\n  // Properties and methods that start with \"_\" are mangled by Terser (saves ~150 bytes)\n  private _ctx: Context;\n  private _renderer: null | (() => void);\n  private _trackedExperiments: Set<unknown>;\n  private _trackedFeatures: Record<string, string>;\n  private _subscriptions: Set<SubscriptionFunction>;\n  private _rtQueue: RealtimeUsageData[];\n  private _rtTimer: number;\n  private _assigned: Map<\n    string,\n    {\n      // eslint-disable-next-line\n      experiment: Experiment<any>;\n      // eslint-disable-next-line\n      result: Result<any>;\n    }\n  >;\n  // eslint-disable-next-line\n  private _forcedFeatureValues: Map<string, any>;\n  private _attributeOverrides: Attributes;\n  private _activeAutoExperiments: Map<\n    AutoExperiment,\n    { valueHash: string; undo: () => void }\n  >;\n  private _triggeredExpKeys: Set<string>;\n  private _loadFeaturesCalled: boolean;\n\n  constructor(context?: Context) {\n    context = context || {};\n    // These properties are all initialized in the constructor instead of above\n    // This saves ~80 bytes in the final output\n    this.version = SDK_VERSION;\n    this._ctx = this.context = context;\n    this._renderer = null;\n    this._trackedExperiments = new Set();\n    this._trackedFeatures = {};\n    this.debug = false;\n    this._subscriptions = new Set();\n    this._rtQueue = [];\n    this._rtTimer = 0;\n    this.ready = false;\n    this._assigned = new Map();\n    this._forcedFeatureValues = new Map();\n    this._attributeOverrides = {};\n    this._activeAutoExperiments = new Map();\n    this._triggeredExpKeys = new Set();\n    this._loadFeaturesCalled = false;\n\n    if (context.remoteEval) {\n      if (context.decryptionKey) {\n        throw new Error(\"Encryption is not available for remoteEval\");\n      }\n      if (!context.clientKey) {\n        throw new Error(\"Missing clientKey\");\n      }\n      let isGbHost = false;\n      try {\n        isGbHost = !!new URL(context.apiHost || \"\").hostname.match(\n          /growthbook\\.io$/i\n        );\n      } catch (e) {\n        // ignore invalid URLs\n      }\n      if (isGbHost) {\n        throw new Error(\"Cannot use remoteEval on GrowthBook Cloud\");\n      }\n    } else {\n      if (context.cacheKeyAttributes) {\n        throw new Error(\"cacheKeyAttributes are only used for remoteEval\");\n      }\n    }\n\n    if (context.features) {\n      this.ready = true;\n    }\n\n    if (isBrowser && context.enableDevMode) {\n      window._growthbook = this;\n      document.dispatchEvent(new Event(\"gbloaded\"));\n    }\n\n    if (context.experiments) {\n      this.ready = true;\n      this._updateAllAutoExperiments();\n    }\n\n    if (context.clientKey && !context.remoteEval) {\n      this._refresh({}, true, false);\n    }\n  }\n\n  public async loadFeatures(options?: LoadFeaturesOptions): Promise<void> {\n    if (options && options.autoRefresh) {\n      // interpret deprecated autoRefresh option as subscribeToChanges\n      this._ctx.subscribeToChanges = true;\n    }\n    this._loadFeaturesCalled = true;\n\n    await this._refresh(options, true, true);\n\n    if (this._canSubscribe()) {\n      subscribe(this);\n    }\n  }\n\n  public async refreshFeatures(\n    options?: RefreshFeaturesOptions\n  ): Promise<void> {\n    await this._refresh(options, false, true);\n  }\n\n  public getApiInfo(): [ApiHost, ClientKey] {\n    return [this.getApiHosts().apiHost, this.getClientKey()];\n  }\n  public getApiHosts(): {\n    apiHost: string;\n    streamingHost: string;\n    apiRequestHeaders?: Record<string, string>;\n    streamingHostRequestHeaders?: Record<string, string>;\n  } {\n    const defaultHost = this._ctx.apiHost || \"https://cdn.growthbook.io\";\n    return {\n      apiHost: defaultHost.replace(/\\/*$/, \"\"),\n      streamingHost: (this._ctx.streamingHost || defaultHost).replace(\n        /\\/*$/,\n        \"\"\n      ),\n      apiRequestHeaders: this._ctx.apiHostRequestHeaders,\n      streamingHostRequestHeaders: this._ctx.streamingHostRequestHeaders,\n    };\n  }\n  public getClientKey(): string {\n    return this._ctx.clientKey || \"\";\n  }\n\n  public isRemoteEval(): boolean {\n    return this._ctx.remoteEval || false;\n  }\n\n  public getCacheKeyAttributes(): (keyof Attributes)[] | undefined {\n    return this._ctx.cacheKeyAttributes;\n  }\n\n  private async _refresh(\n    options?: RefreshFeaturesOptions,\n    allowStale?: boolean,\n    updateInstance?: boolean\n  ) {\n    options = options || {};\n    if (!this._ctx.clientKey) {\n      throw new Error(\"Missing clientKey\");\n    }\n    await refreshFeatures(\n      this,\n      options.timeout,\n      options.skipCache || this._ctx.enableDevMode,\n      allowStale,\n      updateInstance,\n      this._ctx.backgroundSync !== false\n    );\n  }\n\n  private _render() {\n    if (this._renderer) {\n      this._renderer();\n    }\n  }\n\n  public setFeatures(features: Record<string, FeatureDefinition>) {\n    this._ctx.features = features;\n    this.ready = true;\n    this._render();\n  }\n\n  public async setEncryptedFeatures(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const featuresJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setFeatures(\n      JSON.parse(featuresJSON) as Record<string, FeatureDefinition>\n    );\n  }\n\n  public setExperiments(experiments: AutoExperiment[]): void {\n    this._ctx.experiments = experiments;\n    this.ready = true;\n    this._updateAllAutoExperiments();\n  }\n\n  public async setEncryptedExperiments(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const experimentsJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setExperiments(JSON.parse(experimentsJSON) as AutoExperiment[]);\n  }\n\n  public async decryptPayload(\n    data: FeatureApiResponse,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<FeatureApiResponse> {\n    if (data.encryptedFeatures) {\n      data.features = JSON.parse(\n        await decrypt(\n          data.encryptedFeatures,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedFeatures;\n    }\n    if (data.encryptedExperiments) {\n      data.experiments = JSON.parse(\n        await decrypt(\n          data.encryptedExperiments,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedExperiments;\n    }\n    return data;\n  }\n\n  public async setAttributes(attributes: Attributes) {\n    this._ctx.attributes = attributes;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setAttributeOverrides(overrides: Attributes) {\n    this._attributeOverrides = overrides;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setForcedVariations(vars: Record<string, number>) {\n    this._ctx.forcedVariations = vars || {};\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  // eslint-disable-next-line\n  public setForcedFeatures(map: Map<string, any>) {\n    this._forcedFeatureValues = map;\n    this._render();\n  }\n\n  public async setURL(url: string) {\n    this._ctx.url = url;\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      this._updateAllAutoExperiments(true);\n      return;\n    }\n    this._updateAllAutoExperiments(true);\n  }\n\n  public getAttributes() {\n    return { ...this._ctx.attributes, ...this._attributeOverrides };\n  }\n\n  public getForcedVariations() {\n    return this._ctx.forcedVariations || {};\n  }\n\n  public getForcedFeatures() {\n    // eslint-disable-next-line\n    return this._forcedFeatureValues || new Map<string, any>();\n  }\n\n  public getStickyBucketAssignmentDocs() {\n    return this._ctx.stickyBucketAssignmentDocs || {};\n  }\n\n  public getUrl() {\n    return this._ctx.url || \"\";\n  }\n\n  public getFeatures() {\n    return this._ctx.features || {};\n  }\n\n  public getExperiments() {\n    return this._ctx.experiments || [];\n  }\n\n  public subscribe(cb: SubscriptionFunction): () => void {\n    this._subscriptions.add(cb);\n    return () => {\n      this._subscriptions.delete(cb);\n    };\n  }\n\n  private _canSubscribe() {\n    return this._ctx.backgroundSync !== false && this._ctx.subscribeToChanges;\n  }\n\n  private async _refreshForRemoteEval() {\n    if (!this._ctx.remoteEval) return;\n    if (!this._loadFeaturesCalled) return;\n    await this._refresh({}, false, true).catch(() => {\n      // Ignore errors\n    });\n  }\n\n  public getAllResults() {\n    return new Map(this._assigned);\n  }\n\n  public destroy() {\n    // Release references to save memory\n    this._subscriptions.clear();\n    this._assigned.clear();\n    this._trackedExperiments.clear();\n    this._trackedFeatures = {};\n    this._rtQueue = [];\n    if (this._rtTimer) {\n      clearTimeout(this._rtTimer);\n    }\n    unsubscribe(this);\n\n    if (isBrowser && window._growthbook === this) {\n      delete window._growthbook;\n    }\n\n    // Undo any active auto experiments\n    this._activeAutoExperiments.forEach((exp) => {\n      exp.undo();\n    });\n    this._activeAutoExperiments.clear();\n    this._triggeredExpKeys.clear();\n  }\n\n  public setRenderer(renderer: () => void) {\n    this._renderer = renderer;\n  }\n\n  public forceVariation(key: string, variation: number) {\n    this._ctx.forcedVariations = this._ctx.forcedVariations || {};\n    this._ctx.forcedVariations[key] = variation;\n    if (this._ctx.remoteEval) {\n      this._refreshForRemoteEval();\n      return;\n    }\n    this._updateAllAutoExperiments();\n    this._render();\n  }\n\n  public run<T>(experiment: Experiment<T>): Result<T> {\n    const result = this._run(experiment, null);\n    this._fireSubscriptions(experiment, result);\n    return result;\n  }\n\n  public triggerExperiment(key: string) {\n    this._triggeredExpKeys.add(key);\n    if (!this._ctx.experiments) return null;\n    const experiments = this._ctx.experiments.filter((exp) => exp.key === key);\n    return experiments\n      .map((exp) => {\n        if (!exp.manual) return null;\n        return this._runAutoExperiment(exp);\n      })\n      .filter((res) => res !== null);\n  }\n\n  private _runAutoExperiment(experiment: AutoExperiment, forceRerun?: boolean) {\n    const existing = this._activeAutoExperiments.get(experiment);\n\n    // If this is a manual experiment and it's not already running, skip\n    if (\n      experiment.manual &&\n      !this._triggeredExpKeys.has(experiment.key) &&\n      !existing\n    )\n      return null;\n\n    // Run the experiment\n    const result = this.run(experiment);\n\n    // A hash to quickly tell if the assigned value changed\n    const valueHash = JSON.stringify(result.value);\n\n    // If the changes are already active, no need to re-apply them\n    if (\n      !forceRerun &&\n      result.inExperiment &&\n      existing &&\n      existing.valueHash === valueHash\n    ) {\n      return result;\n    }\n\n    // Undo any existing changes\n    if (existing) this._undoActiveAutoExperiment(experiment);\n\n    // Apply new changes\n    if (result.inExperiment) {\n      const undo = this._applyDOMChanges(result.value);\n      if (undo) {\n        this._activeAutoExperiments.set(experiment, {\n          undo,\n          valueHash,\n        });\n      }\n    }\n\n    return result;\n  }\n\n  private _undoActiveAutoExperiment(exp: AutoExperiment) {\n    const data = this._activeAutoExperiments.get(exp);\n    if (data) {\n      data.undo();\n      this._activeAutoExperiments.delete(exp);\n    }\n  }\n\n  private _updateAllAutoExperiments(forceRerun?: boolean) {\n    const experiments = this._ctx.experiments || [];\n\n    // Stop any experiments that are no longer defined\n    const keys = new Set(experiments);\n    this._activeAutoExperiments.forEach((v, k) => {\n      if (!keys.has(k)) {\n        v.undo();\n        this._activeAutoExperiments.delete(k);\n      }\n    });\n\n    // Re-run all new/updated experiments\n    experiments.forEach((exp) => {\n      this._runAutoExperiment(exp, forceRerun);\n    });\n  }\n\n  private _fireSubscriptions<T>(experiment: Experiment<T>, result: Result<T>) {\n    const key = experiment.key;\n\n    // If assigned variation has changed, fire subscriptions\n    const prev = this._assigned.get(key);\n    // TODO: what if the experiment definition has changed?\n    if (\n      !prev ||\n      prev.result.inExperiment !== result.inExperiment ||\n      prev.result.variationId !== result.variationId\n    ) {\n      this._assigned.set(key, { experiment, result });\n      this._subscriptions.forEach((cb) => {\n        try {\n          cb(experiment, result);\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }\n\n  private _trackFeatureUsage(key: string, res: FeatureResult): void {\n    // Don't track feature usage that was forced via an override\n    if (res.source === \"override\") return;\n\n    // Only track a feature once, unless the assigned value changed\n    const stringifiedValue = JSON.stringify(res.value);\n    if (this._trackedFeatures[key] === stringifiedValue) return;\n    this._trackedFeatures[key] = stringifiedValue;\n\n    // Fire user-supplied callback\n    if (this._ctx.onFeatureUsage) {\n      try {\n        this._ctx.onFeatureUsage(key, res);\n      } catch (e) {\n        // Ignore feature usage callback errors\n      }\n    }\n\n    // In browser environments, queue up feature usage to be tracked in batches\n    if (!isBrowser || !window.fetch) return;\n    this._rtQueue.push({\n      key,\n      on: res.on,\n    });\n    if (!this._rtTimer) {\n      this._rtTimer = window.setTimeout(() => {\n        // Reset the queue\n        this._rtTimer = 0;\n        const q = [...this._rtQueue];\n        this._rtQueue = [];\n\n        // Skip logging if a real-time usage key is not configured\n        if (!this._ctx.realtimeKey) return;\n\n        window\n          .fetch(\n            `https://rt.growthbook.io/?key=${\n              this._ctx.realtimeKey\n            }&events=${encodeURIComponent(JSON.stringify(q))}`,\n\n            {\n              cache: \"no-cache\",\n              mode: \"no-cors\",\n            }\n          )\n          .catch(() => {\n            // TODO: retry in case of network errors?\n          });\n      }, this._ctx.realtimeInterval || 2000);\n    }\n  }\n\n  private _getFeatureResult<T>(\n    key: string,\n    value: T,\n    source: FeatureResultSource,\n    ruleId?: string,\n    experiment?: Experiment<T>,\n    result?: Result<T>\n  ): FeatureResult<T> {\n    const ret: FeatureResult = {\n      value,\n      on: !!value,\n      off: !value,\n      source,\n      ruleId: ruleId || \"\",\n    };\n    if (experiment) ret.experiment = experiment;\n    if (result) ret.experimentResult = result;\n\n    // Track the usage of this feature in real-time\n    this._trackFeatureUsage(key, ret);\n\n    return ret;\n  }\n\n  public isOn<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).on;\n  }\n\n  public isOff<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).off;\n  }\n\n  public getFeatureValue<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(key: K, defaultValue: V): WidenPrimitives<V> {\n    const value = this.evalFeature<WidenPrimitives<V>, K>(key).value;\n    return value === null ? (defaultValue as WidenPrimitives<V>) : value;\n  }\n\n  /**\n   * @deprecated Use {@link evalFeature}\n   * @param id\n   */\n  // eslint-disable-next-line\n  public feature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    return this.evalFeature(id);\n  }\n\n  public evalFeature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    // Global override\n    if (this._forcedFeatureValues.has(id)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Global override\", {\n          id,\n          value: this._forcedFeatureValues.get(id),\n        });\n      return this._getFeatureResult(\n        id,\n        this._forcedFeatureValues.get(id),\n        \"override\"\n      );\n    }\n\n    // Unknown feature id\n    if (!this._ctx.features || !this._ctx.features[id]) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Unknown feature\", { id });\n      return this._getFeatureResult(id, null, \"unknownFeature\");\n    }\n\n    // Get the feature\n    const feature: FeatureDefinition<V> = this._ctx.features[id];\n\n    // Loop through the rules\n    if (feature.rules) {\n      for (const rule of feature.rules) {\n        // If there are filters for who is included (e.g. namespaces)\n        if (rule.filters && this._isFilteredOut(rule.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip rule because of filters\", {\n              id,\n              rule,\n            });\n          continue;\n        }\n\n        // Feature value is being forced\n        if (\"force\" in rule) {\n          // If it's a conditional rule, skip if the condition doesn't pass\n          if (rule.condition && !this._conditionPasses(rule.condition)) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because of condition ff\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          // If this is a percentage rollout, skip if not included\n          if (\n            !this._isIncludedInRollout(\n              rule.seed || id,\n              rule.hashAttribute,\n              this._ctx.stickyBucketService && !rule.disableStickyBucketing\n                ? rule.fallbackAttribute\n                : undefined,\n              rule.range,\n              rule.coverage,\n              rule.hashVersion\n            )\n          ) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because user not included in rollout\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Force value from rule\", {\n              id,\n              rule,\n            });\n\n          // If this was a remotely evaluated experiment, fire the tracking callbacks\n          if (rule.tracks) {\n            rule.tracks.forEach((t) => {\n              this._track(t.experiment, t.result);\n            });\n          }\n\n          return this._getFeatureResult(id, rule.force as V, \"force\", rule.id);\n        }\n        if (!rule.variations) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip invalid rule\", {\n              id,\n              rule,\n            });\n\n          continue;\n        }\n\n        // For experiment rules, run an experiment\n        const exp: Experiment<V> = {\n          variations: rule.variations as [V, V, ...V[]],\n          key: rule.key || id,\n        };\n        if (\"coverage\" in rule) exp.coverage = rule.coverage;\n        if (rule.weights) exp.weights = rule.weights;\n        if (rule.hashAttribute) exp.hashAttribute = rule.hashAttribute;\n        if (rule.fallbackAttribute)\n          exp.fallbackAttribute = rule.fallbackAttribute;\n        if (rule.disableStickyBucketing)\n          exp.disableStickyBucketing = rule.disableStickyBucketing;\n        if (rule.bucketVersion !== undefined)\n          exp.bucketVersion = rule.bucketVersion;\n        if (rule.minBucketVersion !== undefined)\n          exp.minBucketVersion = rule.minBucketVersion;\n        if (rule.namespace) exp.namespace = rule.namespace;\n        if (rule.meta) exp.meta = rule.meta;\n        if (rule.ranges) exp.ranges = rule.ranges;\n        if (rule.name) exp.name = rule.name;\n        if (rule.phase) exp.phase = rule.phase;\n        if (rule.seed) exp.seed = rule.seed;\n        if (rule.hashVersion) exp.hashVersion = rule.hashVersion;\n        if (rule.filters) exp.filters = rule.filters;\n        if (rule.condition) exp.condition = rule.condition;\n\n        // Only return a value if the user is part of the experiment\n        const res = this._run(exp, id);\n        this._fireSubscriptions(exp, res);\n        if (res.inExperiment && !res.passthrough) {\n          return this._getFeatureResult(\n            id,\n            res.value,\n            \"experiment\",\n            rule.id,\n            exp,\n            res\n          );\n        }\n      }\n    }\n\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"Use default value\", {\n        id,\n        value: feature.defaultValue,\n      });\n\n    // Fall back to using the default value\n    return this._getFeatureResult(\n      id,\n      feature.defaultValue === undefined ? null : feature.defaultValue,\n      \"defaultValue\"\n    );\n  }\n\n  private _isIncludedInRollout(\n    seed: string,\n    hashAttribute: string | undefined,\n    fallbackAttribute: string | undefined,\n    range: VariationRange | undefined,\n    coverage: number | undefined,\n    hashVersion: number | undefined\n  ): boolean {\n    if (!range && coverage === undefined) return true;\n\n    const { hashValue } = this._getHashAttribute(\n      hashAttribute,\n      fallbackAttribute\n    );\n    if (!hashValue) {\n      return false;\n    }\n\n    const n = hash(seed, hashValue, hashVersion || 1);\n    if (n === null) return false;\n\n    return range\n      ? inRange(n, range)\n      : coverage !== undefined\n      ? n <= coverage\n      : true;\n  }\n\n  private _conditionPasses(condition: ConditionInterface): boolean {\n    return evalCondition(this.getAttributes(), condition);\n  }\n\n  private _isFilteredOut(filters: Filter[]): boolean {\n    return filters.some((filter) => {\n      const { hashValue } = this._getHashAttribute(filter.attribute);\n      if (!hashValue) return true;\n      const n = hash(filter.seed, hashValue, filter.hashVersion || 2);\n      if (n === null) return true;\n      return !filter.ranges.some((r) => inRange(n, r));\n    });\n  }\n\n  private _run<T>(\n    experiment: Experiment<T>,\n    featureId: string | null\n  ): Result<T> {\n    const key = experiment.key;\n    const numVariations = experiment.variations.length;\n\n    // 1. If experiment has less than 2 variations, return immediately\n    if (numVariations < 2) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Invalid experiment\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2. If the context is disabled, return immediately\n    if (this._ctx.enabled === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Context disabled\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2.5. Merge in experiment overrides from the context\n    experiment = this._mergeOverrides(experiment);\n\n    // 2.6 New, more powerful URL targeting\n    if (\n      experiment.urlPatterns &&\n      !isURLTargeted(this._getContextUrl(), experiment.urlPatterns)\n    ) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url targeting\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 3. If a variation is forced from a querystring, return the forced variation\n    const qsOverride = getQueryStringOverride(\n      key,\n      this._getContextUrl(),\n      numVariations\n    );\n    if (qsOverride !== null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via querystring\", {\n          id: key,\n          variation: qsOverride,\n        });\n      return this._getResult(experiment, qsOverride, false, featureId);\n    }\n\n    // 4. If a variation is forced in the context, return the forced variation\n    if (this._ctx.forcedVariations && key in this._ctx.forcedVariations) {\n      const variation = this._ctx.forcedVariations[key];\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via dev tools\", {\n          id: key,\n          variation,\n        });\n      return this._getResult(experiment, variation, false, featureId);\n    }\n\n    // 5. Exclude if a draft experiment or not active\n    if (experiment.status === \"draft\" || experiment.active === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because inactive\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 6. Get the hash attribute and return if empty\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n    if (!hashValue) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because missing hashAttribute\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    let assigned = -1;\n\n    let foundStickyBucket = false;\n    let stickyBucketVersionIsBlocked = false;\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const { variation, versionIsBlocked } = this._getStickyBucketVariation(\n        experiment.key,\n        experiment.bucketVersion,\n        experiment.minBucketVersion,\n        experiment.meta\n      );\n      foundStickyBucket = variation >= 0;\n      assigned = variation;\n      stickyBucketVersionIsBlocked = !!versionIsBlocked;\n    }\n\n    // Some checks are not needed if we already have a sticky bucket\n    if (!foundStickyBucket) {\n      // 7. Exclude if user is filtered out (used to be called \"namespace\")\n      if (experiment.filters) {\n        if (this._isFilteredOut(experiment.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip because of filters\", {\n              id: key,\n            });\n          return this._getResult(experiment, -1, false, featureId);\n        }\n      } else if (\n        experiment.namespace &&\n        !inNamespace(hashValue, experiment.namespace)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of namespace\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 7.5. Exclude if experiment.include returns false or throws\n      if (experiment.include && !isIncluded(experiment.include)) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of include function\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8. Exclude if condition is false\n      if (\n        experiment.condition &&\n        !this._conditionPasses(experiment.condition)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of condition exp\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8.1. Exclude if user is not in a required group\n      if (\n        experiment.groups &&\n        !this._hasGroupOverlap(experiment.groups as string[])\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of groups\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n    }\n\n    // 8.2. Old style URL targeting\n    if (experiment.url && !this._urlIsValid(experiment.url as RegExp)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 9. Get the variation from the sticky bucket or get bucket ranges and choose variation\n    const n = hash(\n      experiment.seed || key,\n      hashValue,\n      experiment.hashVersion || 1\n    );\n    if (n === null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of invalid hash version\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    if (!foundStickyBucket) {\n      const ranges =\n        experiment.ranges ||\n        getBucketRanges(\n          numVariations,\n          experiment.coverage === undefined ? 1 : experiment.coverage,\n          experiment.weights\n        );\n      assigned = chooseVariation(n, ranges);\n    }\n\n    // 9.5 Unenroll if any prior sticky buckets are blocked by version\n    if (stickyBucketVersionIsBlocked) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because sticky bucket version is blocked\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId, undefined, true);\n    }\n\n    // 10. Return if not in experiment\n    if (assigned < 0) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of coverage\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 11. Experiment has a forced variation\n    if (\"force\" in experiment) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force variation\", {\n          id: key,\n          variation: experiment.force,\n        });\n      return this._getResult(\n        experiment,\n        experiment.force === undefined ? -1 : experiment.force,\n        false,\n        featureId\n      );\n    }\n\n    // 12. Exclude if in QA mode\n    if (this._ctx.qaMode) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because QA mode\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 12.5. Exclude if experiment is stopped\n    if (experiment.status === \"stopped\") {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because stopped\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 13. Build the result object\n    const result = this._getResult(\n      experiment,\n      assigned,\n      true,\n      featureId,\n      n,\n      foundStickyBucket\n    );\n\n    // 13.5. Persist sticky bucket\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const {\n        changed,\n        key: attrKey,\n        doc,\n      } = this._generateStickyBucketAssignmentDoc(\n        hashAttribute,\n        toString(hashValue),\n        {\n          [this._getStickyBucketExperimentKey(\n            experiment.key,\n            experiment.bucketVersion\n          )]: result.key,\n        }\n      );\n      if (changed) {\n        // update local docs\n        this._ctx.stickyBucketAssignmentDocs =\n          this._ctx.stickyBucketAssignmentDocs || {};\n        this._ctx.stickyBucketAssignmentDocs[attrKey] = doc;\n        // save doc\n        this._ctx.stickyBucketService.saveAssignments(doc);\n      }\n    }\n\n    // 14. Fire the tracking callback\n    this._track(experiment, result);\n\n    // 15. Return the result\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"In experiment\", {\n        id: key,\n        variation: result.variationId,\n      });\n    return result;\n  }\n\n  log(msg: string, ctx: Record<string, unknown>) {\n    if (!this.debug) return;\n    if (this._ctx.log) this._ctx.log(msg, ctx);\n    else console.log(msg, ctx);\n  }\n\n  private _track<T>(experiment: Experiment<T>, result: Result<T>) {\n    if (!this._ctx.trackingCallback) return;\n\n    const key = experiment.key;\n\n    // Make sure a tracking callback is only fired once per unique experiment\n    const k =\n      result.hashAttribute + result.hashValue + key + result.variationId;\n    if (this._trackedExperiments.has(k)) return;\n    this._trackedExperiments.add(k);\n\n    try {\n      this._ctx.trackingCallback(experiment, result);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  private _mergeOverrides<T>(experiment: Experiment<T>): Experiment<T> {\n    const key = experiment.key;\n    const o = this._ctx.overrides;\n    if (o && o[key]) {\n      experiment = Object.assign({}, experiment, o[key]);\n      if (typeof experiment.url === \"string\") {\n        experiment.url = getUrlRegExp(\n          // eslint-disable-next-line\n          experiment.url as any\n        );\n      }\n    }\n\n    return experiment;\n  }\n\n  private _getHashAttribute(attr?: string, fallback?: string) {\n    let hashAttribute = attr || \"id\";\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let hashValue: any = \"\";\n\n    if (this._attributeOverrides[hashAttribute]) {\n      hashValue = this._attributeOverrides[hashAttribute];\n    } else if (this._ctx.attributes) {\n      hashValue = this._ctx.attributes[hashAttribute] || \"\";\n    } else if (this._ctx.user) {\n      hashValue = this._ctx.user[hashAttribute] || \"\";\n    }\n\n    // if no match, try fallback\n    if (!hashValue && fallback) {\n      if (this._attributeOverrides[fallback]) {\n        hashValue = this._attributeOverrides[fallback];\n      } else if (this._ctx.attributes) {\n        hashValue = this._ctx.attributes[fallback] || \"\";\n      } else if (this._ctx.user) {\n        hashValue = this._ctx.user[fallback] || \"\";\n      }\n      if (hashValue) {\n        hashAttribute = fallback;\n      }\n    }\n\n    return { hashAttribute, hashValue };\n  }\n\n  private _getResult<T>(\n    experiment: Experiment<T>,\n    variationIndex: number,\n    hashUsed: boolean,\n    featureId: string | null,\n    bucket?: number,\n    stickyBucketUsed?: boolean\n  ): Result<T> {\n    let inExperiment = true;\n    // If assigned variation is not valid, use the baseline and mark the user as not in the experiment\n    if (variationIndex < 0 || variationIndex >= experiment.variations.length) {\n      variationIndex = 0;\n      inExperiment = false;\n    }\n\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n\n    const meta: Partial<VariationMeta> = experiment.meta\n      ? experiment.meta[variationIndex]\n      : {};\n\n    const res: Result<T> = {\n      key: meta.key || \"\" + variationIndex,\n      featureId,\n      inExperiment,\n      hashUsed,\n      variationId: variationIndex,\n      value: experiment.variations[variationIndex],\n      hashAttribute,\n      hashValue,\n      stickyBucketUsed: !!stickyBucketUsed,\n    };\n\n    if (meta.name) res.name = meta.name;\n    if (bucket !== undefined) res.bucket = bucket;\n    if (meta.passthrough) res.passthrough = meta.passthrough;\n\n    return res;\n  }\n\n  private _getContextUrl() {\n    return this._ctx.url || (isBrowser ? window.location.href : \"\");\n  }\n\n  private _urlIsValid(urlRegex: RegExp): boolean {\n    const url = this._getContextUrl();\n    if (!url) return false;\n\n    const pathOnly = url.replace(/^https?:\\/\\//, \"\").replace(/^[^/]*\\//, \"/\");\n\n    if (urlRegex.test(url)) return true;\n    if (urlRegex.test(pathOnly)) return true;\n    return false;\n  }\n\n  private _hasGroupOverlap(expGroups: string[]): boolean {\n    const groups = this._ctx.groups || {};\n    for (let i = 0; i < expGroups.length; i++) {\n      if (groups[expGroups[i]]) return true;\n    }\n    return false;\n  }\n\n  private _applyDOMChanges(changes: AutoExperimentVariation) {\n    if (!isBrowser) return;\n    const undo: (() => void)[] = [];\n    if (changes.css) {\n      const s = document.createElement(\"style\");\n      s.innerHTML = changes.css;\n      document.head.appendChild(s);\n      undo.push(() => s.remove());\n    }\n    if (changes.js) {\n      const script = document.createElement(\"script\");\n      script.innerHTML = changes.js;\n      document.head.appendChild(script);\n      undo.push(() => script.remove());\n    }\n    if (changes.domMutations) {\n      changes.domMutations.forEach((mutation) => {\n        undo.push(mutate.declarative(mutation as DeclarativeMutation).revert);\n      });\n    }\n    return () => {\n      undo.forEach((fn) => fn());\n    };\n  }\n\n  private _deriveStickyBucketIdentifierAttributes(data?: FeatureApiResponse) {\n    const attributes = new Set<string>();\n    const features = data && data.features ? data.features : this.getFeatures();\n    const experiments =\n      data && data.experiments ? data.experiments : this.getExperiments();\n    Object.keys(features).forEach((id) => {\n      const feature = features[id];\n      if (feature.rules) {\n        for (const rule of feature.rules) {\n          if (rule.variations) {\n            attributes.add(rule.hashAttribute || \"id\");\n            if (rule.fallbackAttribute) {\n              attributes.add(rule.fallbackAttribute);\n            }\n          }\n        }\n      }\n    });\n    experiments.map((experiment) => {\n      attributes.add(experiment.hashAttribute || \"id\");\n      if (experiment.fallbackAttribute) {\n        attributes.add(experiment.fallbackAttribute);\n      }\n    });\n    return Array.from(attributes);\n  }\n\n  public async refreshStickyBuckets(data?: FeatureApiResponse) {\n    if (this._ctx.stickyBucketService) {\n      const attributes = this._getStickyBucketAttributes(data);\n      this._ctx.stickyBucketAssignmentDocs = await this._ctx.stickyBucketService.getAllAssignments(\n        attributes\n      );\n    }\n  }\n\n  private _getStickyBucketAssignments(): StickyAssignments {\n    const mergedAssignments: StickyAssignments = {};\n    Object.values(this._ctx.stickyBucketAssignmentDocs || {}).forEach((doc) => {\n      if (doc.assignments) Object.assign(mergedAssignments, doc.assignments);\n    });\n    return mergedAssignments;\n  }\n\n  private _getStickyBucketVariation(\n    experimentKey: string,\n    experimentBucketVersion?: number,\n    minExperimentBucketVersion?: number,\n    meta?: VariationMeta[]\n  ): {\n    variation: number;\n    versionIsBlocked?: boolean;\n  } {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    minExperimentBucketVersion = minExperimentBucketVersion || 0;\n    meta = meta || [];\n    const id = this._getStickyBucketExperimentKey(\n      experimentKey,\n      experimentBucketVersion\n    );\n    const assignments = this._getStickyBucketAssignments();\n\n    // users with any blocked bucket version (0 to minExperimentBucketVersion) are excluded from the test\n    if (minExperimentBucketVersion > 0) {\n      for (let i = 0; i <= minExperimentBucketVersion; i++) {\n        const blockedKey = this._getStickyBucketExperimentKey(experimentKey, i);\n        if (assignments[blockedKey] !== undefined) {\n          return {\n            variation: -1,\n            versionIsBlocked: true,\n          };\n        }\n      }\n    }\n    const variationKey = assignments[id];\n    if (variationKey === undefined)\n      // no assignment found\n      return { variation: -1 };\n    const variation = meta.findIndex((m) => m.key === variationKey);\n    if (variation < 0)\n      // invalid assignment, treat as \"no assignment found\"\n      return { variation: -1 };\n\n    return { variation };\n  }\n\n  private _getStickyBucketExperimentKey(\n    experimentKey: string,\n    experimentBucketVersion?: number\n  ): StickyExperimentKey {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    return `${experimentKey}__${experimentBucketVersion}`;\n  }\n\n  private _getStickyBucketAttributes(\n    data?: FeatureApiResponse\n  ): Record<string, string> {\n    const attributes: Record<string, string> = {};\n    this._ctx.stickyBucketIdentifierAttributes = !this._ctx\n      .stickyBucketIdentifierAttributes\n      ? this._deriveStickyBucketIdentifierAttributes(data)\n      : this._ctx.stickyBucketIdentifierAttributes;\n    this._ctx.stickyBucketIdentifierAttributes.forEach((attr) => {\n      const { hashValue } = this._getHashAttribute(attr);\n      attributes[attr] = toString(hashValue);\n    });\n    return attributes;\n  }\n\n  private _generateStickyBucketAssignmentDoc(\n    attributeName: string,\n    attributeValue: string,\n    assignments: StickyAssignments\n  ): {\n    key: StickyAttributeKey;\n    doc: StickyAssignmentsDocument;\n    changed: boolean;\n  } {\n    const key = `${attributeName}||${attributeValue}`;\n    const existingAssignments =\n      this._ctx.stickyBucketAssignmentDocs &&\n      this._ctx.stickyBucketAssignmentDocs[key]\n        ? this._ctx.stickyBucketAssignmentDocs[key].assignments || {}\n        : {};\n    const newAssignments = { ...existingAssignments, ...assignments };\n    const changed =\n      JSON.stringify(existingAssignments) !== JSON.stringify(newAssignments);\n\n    return {\n      key,\n      doc: {\n        attributeName,\n        attributeValue,\n        assignments: newAssignments,\n      },\n      changed,\n    };\n  }\n}\n", "import { Context, GrowthBook } from \"./index\";\n\ndeclare global {\n  interface Window {\n    _growthbook?: GrowthBook;\n    growthbook_config?: Context;\n    // eslint-disable-next-line\n    dataLayer: any[];\n    analytics?: {\n      track?: (name: string, props?: Record<string, unknown>) => void;\n    };\n  }\n}\n\nconst getUUID = () => {\n  const COOKIE_NAME = \"gbuuid\";\n  const COOKIE_DAYS = 400; // 400 days is the max cookie duration for chrome\n\n  // use the browsers crypto.randomUUID if set\n  const genUUID = () => {\n    if (window.crypto && crypto.randomUUID) return crypto.randomUUID();\n    return (\"\" + 1e7 + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>\n      (\n        ((c as unknown) as number) ^\n        (crypto.getRandomValues(new Uint8Array(1))[0] &\n          (15 >> (((c as unknown) as number) / 4)))\n      ).toString(16)\n    );\n  };\n  const getCookie = (name: string): string => {\n    const value = \"; \" + document.cookie;\n    const parts = value.split(`; ${name}=`);\n    return parts.length === 2 ? parts[1].split(\";\")[0] : \"\";\n  };\n  const setCookie = (name: string, value: string) => {\n    const d = new Date();\n    d.setTime(d.getTime() + 24 * 60 * 60 * 1000 * COOKIE_DAYS);\n    document.cookie = name + \"=\" + value + \";path=/;expires=\" + d.toUTCString();\n  };\n\n  // get the existing UUID from cookie if set, otherwise create one and store it in the cookie\n  if (getCookie(COOKIE_NAME)) return getCookie(COOKIE_NAME);\n\n  const uuid = genUUID();\n  setCookie(COOKIE_NAME, uuid);\n  return uuid;\n};\n\nfunction getUtmAttributes() {\n  // Store utm- params in sessionStorage for future page loads\n  let utms: Record<string, string> = {};\n  try {\n    const existing = sessionStorage.getItem(\"utm_params\");\n    if (existing) {\n      utms = JSON.parse(existing);\n    }\n\n    // Add utm params from querystring\n    if (location.search) {\n      const params = new URLSearchParams(location.search);\n      let hasChanges = false;\n      [\"source\", \"medium\", \"campaign\", \"term\", \"content\"].forEach((k) => {\n        // Querystring is in snake_case\n        const param = `utm_${k}`;\n        // Attribute keys are camelCase\n        const attr = `utm` + k[0].toUpperCase() + k.slice(1);\n\n        if (params.has(param)) {\n          utms[attr] = params.get(param) || \"\";\n          hasChanges = true;\n        }\n      });\n\n      // Write back to sessionStorage\n      if (hasChanges) {\n        sessionStorage.setItem(\"utm_params\", JSON.stringify(utms));\n      }\n    }\n  } catch (e) {\n    // Do nothing if sessionStorage is disabled (e.g. incognito window)\n  }\n\n  return utms;\n}\n\nfunction getAutoAttributes() {\n  const ua = navigator.userAgent;\n\n  const browser = ua.match(/Edg/)\n    ? \"edge\"\n    : ua.match(/Chrome/)\n    ? \"chrome\"\n    : ua.match(/Firefox/)\n    ? \"firefox\"\n    : ua.match(/Safari/)\n    ? \"safari\"\n    : \"unknown\";\n\n  return {\n    id: getUUID(),\n    url: location.href,\n    path: location.pathname,\n    host: location.host,\n    query: location.search,\n    deviceType: ua.match(/Mobi/) ? \"mobile\" : \"desktop\",\n    browser,\n    ...getUtmAttributes(),\n  };\n}\n\n// Initialize the data layer if it doesn't exist yet (GA4, GTM)\nwindow.dataLayer = window.dataLayer || [];\n\nconst currentScript = document.currentScript;\nconst dataContext = currentScript ? currentScript.dataset : {};\nconst windowContext = window.growthbook_config || {};\n\nfunction getAttributes() {\n  // Merge auto attributes and user-supplied attributes\n  const attributes = dataContext[\"noAutoAttributes\"] ? {} : getAutoAttributes();\n  if (windowContext.attributes) {\n    Object.assign(attributes, windowContext.attributes);\n  }\n  return attributes;\n}\n\n// Create GrowthBook instance\nconst gb = new GrowthBook({\n  ...dataContext,\n  remoteEval: !!dataContext.remoteEval,\n  subscribeToChanges: true,\n  trackingCallback: (e, r) => {\n    const p = { experiment_id: e.key, variation_id: r.key };\n    window.dataLayer.push([\"event\", \"experiment_viewed\", p]);\n    window.analytics &&\n      window.analytics.track &&\n      window.analytics.track(\"Experiment Viewed\", p);\n  },\n  ...windowContext,\n  attributes: getAttributes(),\n});\n\n// Load features/experiments\ngb.loadFeatures();\n\n// Poll for URL changes and update GrowthBook\nlet currentUrl = location.href;\nsetInterval(() => {\n  if (location.href !== currentUrl) {\n    currentUrl = location.href;\n    gb.setURL(currentUrl);\n    gb.setAttributes({\n      ...gb.getAttributes(),\n      ...getAttributes(),\n    });\n  }\n}, 500);\n\n// Store a reference in window to enable more advanced use cases\nexport default gb;\n"], "names": ["cacheSettings", "staleTTL", "maxAge", "cache<PERSON>ey", "backgroundSync", "maxEntries", "disableIdleStreams", "idleStreamInterval", "polyfills", "fetch", "globalThis", "bind", "undefined", "SubtleCrypto", "crypto", "subtle", "EventSource", "helpers", "fetchFeaturesCall", "host", "client<PERSON>ey", "headers", "fetchRemoteEvalCall", "payload", "options", "method", "body", "JSON", "stringify", "eventSourceCall", "startIdleListener", "idleTimeout", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "onVisibilityChange", "visibilityState", "clearTimeout", "onVisible", "setTimeout", "onHidden", "addEventListener", "removeEventListener", "stopIdleListener", "localStorage", "e", "subscribedInstances", "Map", "cacheInitialized", "cache", "activeFetches", "streams", "supportsSSE", "Set", "refreshFeatures", "instance", "timeout", "<PERSON><PERSON><PERSON>", "allowStale", "updateInstance", "data", "fetchFeaturesWithCache", "refreshInstance", "subscribe", "key", "<PERSON><PERSON><PERSON>", "subs", "get", "add", "set", "unsubscribe", "for<PERSON>ach", "s", "delete", "channel", "state", "disableChannel", "enableChannel", "updatePersistentCache", "setItem", "Array", "from", "entries", "get<PERSON><PERSON><PERSON><PERSON>", "now", "Date", "minStaleAt", "getTime", "initializeCache", "existing", "staleAt", "sse", "fetchFeatures", "startAutoRefresh", "promiseTimeout", "apiHost", "getApiInfo", "baseKey", "isRemoteEval", "attributes", "getAttributes", "cacheKeyAttributes", "getCacheKeyAttributes", "Object", "keys", "ca", "fv", "getForcedVariations", "url", "getUrl", "promise", "Promise", "resolve", "resolved", "timer", "finish", "then", "catch", "value", "getItem", "parsed", "parse", "isArray", "cleanupCache", "cleanupFn", "entriesWithTimestamps", "map", "sort", "a", "b", "entriesToRemoveCount", "Math", "min", "max", "size", "i", "onNewFeatureData", "version", "dateUpdated", "has", "instances", "decryptPayload", "refreshStickyBuckets", "setExperiments", "experiments", "getExperiments", "setFeatures", "features", "getFeatures", "apiRequestHeaders", "getApiHosts", "getClientKey", "remoteEval", "fetcher", "forcedVariations", "forcedFeatures", "getForcedFeatures", "res", "json", "streamingHost", "streamingHostRequestHeaders", "src", "cb", "event", "type", "errors", "onSSEError", "readyState", "delay", "pow", "random", "includes", "onopen", "onerror", "close", "validAttributeName", "nullController", "revert", "elements", "mutations", "getObserverInit", "attr", "childList", "subtree", "characterData", "attributeFilter", "getElementRecord", "element", "record", "createElementPropertyRecord", "el", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "parentNode", "insertBeforeNode", "observe", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "m", "mutate", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "join", "attrMutationRunner", "_loadDOMNodes", "parentSelector", "insertBeforeSelector", "querySelector", "positionMutationRunner", "selectors", "newNodes", "getHTMLValue", "innerHTML", "setHTMLValue", "getElementHTMLRecord", "elementRecord", "html", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getAttrValue", "attrName", "getAttribute", "setAttrValue", "setAttribute", "getElementAttributeRecord", "deleteElementPropertyRecord", "disconnect", "transformContainer", "createElement", "setPropertyV<PERSON>ue", "length", "startMutating", "mutation", "kind", "attribute", "push", "stopMutating", "index", "indexOf", "splice", "refreshElementsSet", "existingElements", "matchingElements", "querySelectorAll", "selector", "revertMutation", "clear", "refreshAllElementSets", "connectGlobalObserver", "documentElement", "newMutation", "test", "mutatedClassnames", "classnames", "c", "declarative", "action", "hashFnv32a", "str", "hval", "l", "charCodeAt", "hash", "seed", "getEqualWeights", "n", "fill", "inRange", "range", "inNamespace", "hashValue", "namespace", "chooseVariation", "ranges", "getUrlRegExp", "regexString", "escaped", "replace", "RegExp", "console", "error", "isURLTargeted", "targets", "hasIncludeRules", "isIncluded", "match", "_evalURLTarget", "pattern", "include", "_evalSimpleUrlPart", "actual", "isPath", "regex", "_evalSimpleUrlTarget", "expected", "URL", "comps", "pathname", "searchParams", "v", "k", "some", "href", "substring", "origin", "getBucketRanges", "numVariations", "coverage", "weights", "equal", "totalWeight", "reduce", "w", "sum", "cumulative", "start", "getQueryStringOverride", "id", "search", "kv", "parseInt", "base64ToBuf", "Uint8Array", "atob", "decrypt", "encryptedString", "decryptionKey", "Error", "importKey", "name", "iv", "cipherText", "plainTextBuffer", "TextDecoder", "decode", "toString", "input", "paddedVersionString", "parts", "padStart", "loadSDKVersion", "_regexCache", "evalCondition", "obj", "condition", "evalOr", "evalAnd", "evalConditionValue", "<PERSON><PERSON><PERSON>", "path", "current", "getRegex", "isOperatorObject", "op", "evalOperatorCondition", "getType", "t", "elemMatch", "check", "isIn", "operator", "passed", "j", "conditions", "SDK_VERSION", "GrowthBook", "constructor", "context", "_ctx", "_renderer", "_trackedExperiments", "_trackedFeatures", "debug", "_subscriptions", "_rtQueue", "_rtTimer", "ready", "_assigned", "_forcedFeatureV<PERSON>ues", "_attributeOverrides", "_activeAutoExperiments", "_triggeredExpKeys", "_loadFeaturesCalled", "isGbHost", "hostname", "enableDevMode", "_growthbook", "dispatchEvent", "Event", "_updateAllAutoExperiments", "_refresh", "loadFeatures", "autoRefresh", "subscribeToChanges", "_canSubscribe", "defaultHost", "apiHostRequestHeaders", "_render", "setEncryptedFeatures", "featuresJSON", "setEncryptedExperiments", "experimentsJSON", "encryptedFeatures", "encryptedExperiments", "setAttributes", "stickyBucketService", "_refreshForRemoteEval", "setAttributeOverrides", "overrides", "setForcedVariations", "vars", "setForcedFeatures", "setURL", "getStickyBucketAssignmentDocs", "stickyBucketAssignmentDocs", "getAllResults", "destroy", "exp", "undo", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "forceVariation", "variation", "run", "experiment", "result", "_run", "_fireSubscriptions", "triggerExperiment", "manual", "_runAutoExperiment", "forceRerun", "valueHash", "inExperiment", "_undoActiveAutoExperiment", "_applyDOMChanges", "prev", "variationId", "_trackFeatureUsage", "source", "stringifiedValue", "onFeatureUsage", "on", "q", "realtimeKey", "encodeURIComponent", "mode", "realtimeInterval", "_getFeatureResult", "ruleId", "ret", "off", "experimentResult", "isOn", "evalFeature", "isOff", "getFeatureValue", "defaultValue", "feature", "rules", "rule", "filters", "_isFilteredOut", "_conditionPasses", "_isIncludedInRollout", "hashAttribute", "disableStickyBucketing", "fallbackAttribute", "hashVersion", "tracks", "_track", "force", "variations", "bucketVersion", "minBucketVersion", "meta", "phase", "passthrough", "_getHashAttribute", "r", "featureId", "_getResult", "enabled", "_mergeOverrides", "urlPatterns", "_getContextUrl", "qsOverride", "status", "active", "assigned", "foundStickyBucket", "stickyBucketVersionIsBlocked", "versionIsBlocked", "_getStickyBucketVariation", "groups", "_hasGroupOverlap", "_urlIsValid", "qaMode", "changed", "attrKey", "doc", "_generateStickyBucketAssignmentDoc", "_getStickyBucketExperimentKey", "saveAssignments", "log", "msg", "ctx", "trackingCallback", "o", "assign", "fallback", "user", "variationIndex", "hashUsed", "bucket", "stickyBucketUsed", "location", "urlRegex", "pathOnly", "expGroups", "changes", "css", "head", "append<PERSON><PERSON><PERSON>", "remove", "js", "script", "domMutations", "fn", "_deriveStickyBucketIdentifierAttributes", "_getStickyBucketAttributes", "getAllAssignments", "_getStickyBucketAssignments", "mergedAssignments", "values", "assignments", "<PERSON><PERSON><PERSON>", "experimentBucketVersion", "minExperimentBucketVersion", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "findIndex", "stickyBucketIdentifierAttributes", "attributeName", "attributeValue", "existingAssignments", "newAssignments", "getUUID", "COOKIE_NAME", "COOKIE_DAYS", "genUUID", "randomUUID", "getRandomValues", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "<PERSON><PERSON><PERSON><PERSON>", "d", "setTime", "toUTCString", "uuid", "getUtmAttributes", "utms", "sessionStorage", "params", "URLSearchParams", "has<PERSON><PERSON><PERSON>", "param", "toUpperCase", "slice", "getAutoAttributes", "ua", "navigator", "userAgent", "browser", "query", "deviceType", "dataLayer", "currentScript", "dataContext", "dataset", "windowContext", "growthbook_config", "gb", "p", "experiment_id", "variation_id", "analytics", "track", "currentUrl", "setInterval"], "mappings": ";;;EAyBA;EACA,MAAMA,aAA4B,GAAG;EACnC;IACAC,QAAQ,EAAE,IAAI,GAAG,EAAE;EACnB;EACAC,EAAAA,MAAM,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC3BC,EAAAA,QAAQ,EAAE,iBAAiB;EAC3BC,EAAAA,cAAc,EAAE,IAAI;EACpBC,EAAAA,UAAU,EAAE,EAAE;EACdC,EAAAA,kBAAkB,EAAE,KAAK;EACzBC,EAAAA,kBAAkB,EAAE,KAAA;EACtB,CAAC,CAAA;EACD,MAAMC,SAAoB,GAAG;EAC3BC,EAAAA,KAAK,EAAEC,UAAU,CAACD,KAAK,GAAGC,UAAU,CAACD,KAAK,CAACE,IAAI,CAACD,UAAU,CAAC,GAAGE,SAAS;IACvEC,YAAY,EAAEH,UAAU,CAACI,MAAM,GAAGJ,UAAU,CAACI,MAAM,CAACC,MAAM,GAAGH,SAAS;IACtEI,WAAW,EAAEN,UAAU,CAACM,WAAAA;EAC1B,CAAC,CAAA;EACM,MAAMC,OAAgB,GAAG;EAC9BC,EAAAA,iBAAiB,EAAE,IAAkC,IAAA;MAAA,IAAjC;QAAEC,IAAI;QAAEC,SAAS;EAAEC,MAAAA,OAAAA;OAAS,GAAA,IAAA,CAAA;EAC9C,IAAA,OAAQb,SAAS,CAACC,KAAK,WAClBU,IAAI,EAAA,gBAAA,CAAA,CAAA,MAAA,CAAiBC,SAAS,CACjC,EAAA;EAAEC,MAAAA,OAAAA;EAAQ,KAAC,CACZ,CAAA;KACF;EACDC,EAAAA,mBAAmB,EAAE,KAA2C,IAAA;MAAA,IAA1C;QAAEH,IAAI;QAAEC,SAAS;QAAEG,OAAO;EAAEF,MAAAA,OAAAA;OAAS,GAAA,KAAA,CAAA;EACzD,IAAA,MAAMG,OAAO,GAAG;EACdC,MAAAA,MAAM,EAAE,MAAM;EACdJ,MAAAA,OAAO,EAAE;EAAE,QAAA,cAAc,EAAE,kBAAkB;UAAE,GAAGA,OAAAA;SAAS;EAC3DK,MAAAA,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAA;OAC7B,CAAA;MACD,OAAQf,SAAS,CAACC,KAAK,CAAA,EAAA,CAAA,MAAA,CAClBU,IAAI,EAAaC,YAAAA,CAAAA,CAAAA,MAAAA,CAAAA,SAAS,CAC7BI,EAAAA,OAAO,CACR,CAAA;KACF;EACDK,EAAAA,eAAe,EAAE,KAAkC,IAAA;MAAA,IAAjC;QAAEV,IAAI;QAAEC,SAAS;EAAEC,MAAAA,OAAAA;OAAS,GAAA,KAAA,CAAA;EAC5C,IAAA,IAAIA,OAAO,EAAE;QACX,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,EAAA,OAAA,CAAA,CAAA,MAAA,CAAQC,SAAS,CAAI,EAAA;EAC3DC,QAAAA,OAAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;MACA,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,EAAA,OAAA,CAAA,CAAA,MAAA,CAAQC,SAAS,CAAG,CAAA,CAAA;KAC7D;EACDU,EAAAA,iBAAiB,EAAE,MAAM;EACvB,IAAA,IAAIC,WAA+B,CAAA;MACnC,MAAMC,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,CAAA;MAClE,IAAI,CAACF,SAAS,EAAE,OAAA;MAChB,MAAMG,kBAAkB,GAAG,MAAM;EAC/B,MAAA,IAAID,QAAQ,CAACE,eAAe,KAAK,SAAS,EAAE;EAC1CH,QAAAA,MAAM,CAACI,YAAY,CAACN,WAAW,CAAC,CAAA;EAChCO,QAAAA,SAAS,EAAE,CAAA;EACb,OAAC,MAAM,IAAIJ,QAAQ,CAACE,eAAe,KAAK,QAAQ,EAAE;UAChDL,WAAW,GAAGE,MAAM,CAACM,UAAU,CAC7BC,QAAQ,EACRxC,aAAa,CAACO,kBAAkB,CACjC,CAAA;EACH,OAAA;OACD,CAAA;EACD2B,IAAAA,QAAQ,CAACO,gBAAgB,CAAC,kBAAkB,EAAEN,kBAAkB,CAAC,CAAA;MACjE,OAAO,MACLD,QAAQ,CAACQ,mBAAmB,CAAC,kBAAkB,EAAEP,kBAAkB,CAAC,CAAA;KACvE;EACDQ,EAAAA,gBAAgB,EAAE,MAAM;EACtB;EAAA,GAAA;EAEJ,CAAC,CAAA;EAED,IAAI;IACF,IAAIjC,UAAU,CAACkC,YAAY,EAAE;EAC3BpC,IAAAA,SAAS,CAACoC,YAAY,GAAGlC,UAAU,CAACkC,YAAY,CAAA;EAClD,GAAA;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;EAAA,CAAA;;EAGF;EACA,MAAMC,mBAAiD,GAAG,IAAIC,GAAG,EAAE,CAAA;EACnE,IAAIC,gBAAgB,GAAG,KAAK,CAAA;EAC5B,MAAMC,KAA8B,GAAG,IAAIF,GAAG,EAAE,CAAA;EAChD,MAAMG,aAAuD,GAAG,IAAIH,GAAG,EAAE,CAAA;EACzE,MAAMI,OAAmC,GAAG,IAAIJ,GAAG,EAAE,CAAA;EACrD,MAAMK,WAAwB,GAAG,IAAIC,GAAG,EAAE,CAAA;EAqBnC,eAAeC,eAAe,CACnCC,QAAoB,EACpBC,OAAgB,EAChBC,SAAmB,EACnBC,UAAoB,EACpBC,cAAwB,EACxBvD,cAAwB,EACT;IACf,IAAI,CAACA,cAAc,EAAE;MACnBJ,aAAa,CAACI,cAAc,GAAG,KAAK,CAAA;EACtC,GAAA;EAEA,EAAA,MAAMwD,IAAI,GAAG,MAAMC,sBAAsB,CACvCN,QAAQ,EACRG,UAAU,EACVF,OAAO,EACPC,SAAS,CACV,CAAA;IACDE,cAAc,IAAIC,IAAI,KAAK,MAAME,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC,CAAA;EACnE,CAAA;;EAEA;EACO,SAASG,SAAS,CAACR,QAAoB,EAAQ;EACpD,EAAA,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;IAC5B,MAAMW,IAAI,GAAGpB,mBAAmB,CAACqB,GAAG,CAACH,GAAG,CAAC,IAAI,IAAIX,GAAG,EAAE,CAAA;EACtDa,EAAAA,IAAI,CAACE,GAAG,CAACb,QAAQ,CAAC,CAAA;EAClBT,EAAAA,mBAAmB,CAACuB,GAAG,CAACL,GAAG,EAAEE,IAAI,CAAC,CAAA;EACpC,CAAA;EACO,SAASI,WAAW,CAACf,QAAoB,EAAQ;IACtDT,mBAAmB,CAACyB,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClB,QAAQ,CAAC,CAAC,CAAA;EACxD,CAAA;EAEO,SAASf,QAAQ,GAAG;EACzBW,EAAAA,OAAO,CAACoB,OAAO,CAAEG,OAAO,IAAK;MAC3B,IAAI,CAACA,OAAO,EAAE,OAAA;MACdA,OAAO,CAACC,KAAK,GAAG,MAAM,CAAA;MACtBC,cAAc,CAACF,OAAO,CAAC,CAAA;EACzB,GAAC,CAAC,CAAA;EACJ,CAAA;EAEO,SAASpC,SAAS,GAAG;EAC1Ba,EAAAA,OAAO,CAACoB,OAAO,CAAEG,OAAO,IAAK;MAC3B,IAAI,CAACA,OAAO,EAAE,OAAA;EACd,IAAA,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE,OAAA;MAC9BE,aAAa,CAACH,OAAO,CAAC,CAAA;EACxB,GAAC,CAAC,CAAA;EACJ,CAAA;;EAEA;;EAEA,eAAeI,qBAAqB,GAAG;IACrC,IAAI;EACF,IAAA,IAAI,CAACtE,SAAS,CAACoC,YAAY,EAAE,OAAA;MAC7B,MAAMpC,SAAS,CAACoC,YAAY,CAACmC,OAAO,CAClC/E,aAAa,CAACG,QAAQ,EACtBwB,IAAI,CAACC,SAAS,CAACoD,KAAK,CAACC,IAAI,CAAChC,KAAK,CAACiC,OAAO,EAAE,CAAC,CAAC,CAC5C,CAAA;KACF,CAAC,OAAOrC,CAAC,EAAE;EACV;EAAA,GAAA;EAEJ,CAAA;EAEA,eAAegB,sBAAsB,CACnCN,QAAoB,EACpBG,UAAoB,EACpBF,OAAgB,EAChBC,SAAmB,EACiB;EACpC,EAAA,MAAMO,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAC5B,EAAA,MAAMpD,QAAQ,GAAGgF,WAAW,CAAC5B,QAAQ,CAAC,CAAA;EACtC,EAAA,MAAM6B,GAAG,GAAG,IAAIC,IAAI,EAAE,CAAA;EAEtB,EAAA,MAAMC,UAAU,GAAG,IAAID,IAAI,CACzBD,GAAG,CAACG,OAAO,EAAE,GAAGvF,aAAa,CAACE,MAAM,GAAGF,aAAa,CAACC,QAAQ,CAC9D,CAAA;EAED,EAAA,MAAMuF,eAAe,EAAE,CAAA;EACvB,EAAA,MAAMC,QAAQ,GAAGxC,KAAK,CAACkB,GAAG,CAAChE,QAAQ,CAAC,CAAA;EACpC,EAAA,IACEsF,QAAQ,IACR,CAAChC,SAAS,KACTC,UAAU,IAAI+B,QAAQ,CAACC,OAAO,GAAGN,GAAG,CAAC,IACtCK,QAAQ,CAACC,OAAO,GAAGJ,UAAU,EAC7B;EACA;MACA,IAAIG,QAAQ,CAACE,GAAG,EAAEvC,WAAW,CAACgB,GAAG,CAACJ,GAAG,CAAC,CAAA;;EAEtC;EACA,IAAA,IAAIyB,QAAQ,CAACC,OAAO,GAAGN,GAAG,EAAE;QAC1BQ,aAAa,CAACrC,QAAQ,CAAC,CAAA;EACzB,KAAA;EACA;WACK;QACHsC,gBAAgB,CAACtC,QAAQ,CAAC,CAAA;EAC5B,KAAA;MACA,OAAOkC,QAAQ,CAAC7B,IAAI,CAAA;EACtB,GAAC,MAAM;MACL,OAAO,MAAMkC,cAAc,CAACF,aAAa,CAACrC,QAAQ,CAAC,EAAEC,OAAO,CAAC,CAAA;EAC/D,GAAA;EACF,CAAA;EAEA,SAASS,MAAM,CAACV,QAAoB,EAAU;IAC5C,MAAM,CAACwC,OAAO,EAAE3E,SAAS,CAAC,GAAGmC,QAAQ,CAACyC,UAAU,EAAE,CAAA;IAClD,OAAUD,EAAAA,CAAAA,MAAAA,CAAAA,OAAO,eAAK3E,SAAS,CAAA,CAAA;EACjC,CAAA;EAEA,SAAS+D,WAAW,CAAC5B,QAAoB,EAAU;EACjD,EAAA,MAAM0C,OAAO,GAAGhC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAChC,EAAA,IAAI,CAACA,QAAQ,CAAC2C,YAAY,EAAE,EAAE,OAAOD,OAAO,CAAA;EAE5C,EAAA,MAAME,UAAU,GAAG5C,QAAQ,CAAC6C,aAAa,EAAE,CAAA;EAC3C,EAAA,MAAMC,kBAAkB,GACtB9C,QAAQ,CAAC+C,qBAAqB,EAAE,IAAIC,MAAM,CAACC,IAAI,CAACjD,QAAQ,CAAC6C,aAAa,EAAE,CAAC,CAAA;IAC3E,MAAMK,EAAc,GAAG,EAAE,CAAA;EACzBJ,EAAAA,kBAAkB,CAAC9B,OAAO,CAAEP,GAAG,IAAK;EAClCyC,IAAAA,EAAE,CAACzC,GAAG,CAAC,GAAGmC,UAAU,CAACnC,GAAG,CAAC,CAAA;EAC3B,GAAC,CAAC,CAAA;EAEF,EAAA,MAAM0C,EAAE,GAAGnD,QAAQ,CAACoD,mBAAmB,EAAE,CAAA;EACzC,EAAA,MAAMC,GAAG,GAAGrD,QAAQ,CAACsD,MAAM,EAAE,CAAA;EAE7B,EAAA,OAAA,EAAA,CAAA,MAAA,CAAUZ,OAAO,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKtE,IAAI,CAACC,SAAS,CAAC;MACnC6E,EAAE;MACFC,EAAE;EACFE,IAAAA,GAAAA;EACF,GAAC,CAAC,CAAA,CAAA;EACJ,CAAA;;EAEA;EACA;EACA;EACA,SAASd,cAAc,CACrBgB,OAAmB,EACnBtD,OAAgB,EACG;EACnB,EAAA,OAAO,IAAIuD,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAIC,QAAQ,GAAG,KAAK,CAAA;EACpB,IAAA,IAAIC,KAAc,CAAA;MAClB,MAAMC,MAAM,GAAIvD,IAAQ,IAAK;EAC3B,MAAA,IAAIqD,QAAQ,EAAE,OAAA;EACdA,MAAAA,QAAQ,GAAG,IAAI,CAAA;EACfC,MAAAA,KAAK,IAAI7E,YAAY,CAAC6E,KAAK,CAAiB,CAAA;EAC5CF,MAAAA,OAAO,CAACpD,IAAI,IAAI,IAAI,CAAC,CAAA;OACtB,CAAA;EAED,IAAA,IAAIJ,OAAO,EAAE;QACX0D,KAAK,GAAG3E,UAAU,CAAC,MAAM4E,MAAM,EAAE,EAAE3D,OAAO,CAAC,CAAA;EAC7C,KAAA;EAEAsD,IAAAA,OAAO,CAACM,IAAI,CAAExD,IAAI,IAAKuD,MAAM,CAACvD,IAAI,CAAC,CAAC,CAACyD,KAAK,CAAC,MAAMF,MAAM,EAAE,CAAC,CAAA;EAC5D,GAAC,CAAC,CAAA;EACJ,CAAA;;EAEA;EACA,eAAe3B,eAAe,GAAkB;EAC9C,EAAA,IAAIxC,gBAAgB,EAAE,OAAA;EACtBA,EAAAA,gBAAgB,GAAG,IAAI,CAAA;IACvB,IAAI;MACF,IAAIxC,SAAS,CAACoC,YAAY,EAAE;EAC1B,MAAA,MAAM0E,KAAK,GAAG,MAAM9G,SAAS,CAACoC,YAAY,CAAC2E,OAAO,CAChDvH,aAAa,CAACG,QAAQ,CACvB,CAAA;EACD,MAAA,IAAImH,KAAK,EAAE;EACT,QAAA,MAAME,MAA8B,GAAG7F,IAAI,CAAC8F,KAAK,CAACH,KAAK,CAAC,CAAA;UACxD,IAAIE,MAAM,IAAIxC,KAAK,CAAC0C,OAAO,CAACF,MAAM,CAAC,EAAE;YACnCA,MAAM,CAACjD,OAAO,CAAC,KAAiB,IAAA;EAAA,YAAA,IAAhB,CAACP,GAAG,EAAEJ,IAAI,CAAC,GAAA,KAAA,CAAA;EACzBX,YAAAA,KAAK,CAACoB,GAAG,CAACL,GAAG,EAAE;EACb,cAAA,GAAGJ,IAAI;EACP8B,cAAAA,OAAO,EAAE,IAAIL,IAAI,CAACzB,IAAI,CAAC8B,OAAO,CAAA;EAChC,aAAC,CAAC,CAAA;EACJ,WAAC,CAAC,CAAA;EACJ,SAAA;EACAiC,QAAAA,YAAY,EAAE,CAAA;EAChB,OAAA;EACF,KAAA;KACD,CAAC,OAAO9E,CAAC,EAAE;EACV;EAAA,GAAA;EAEF,EAAuC;EACrC,IAAA,MAAM+E,SAAS,GAAG3G,OAAO,CAACa,iBAAiB,EAAE,CAAA;EAC7C,IAAA,IAAI8F,SAAS,EAAE;QACb3G,OAAO,CAAC0B,gBAAgB,GAAGiF,SAAS,CAAA;EACtC,KAAA;EACF,GAAA;EACF,CAAA;;EAEA;EACA,SAASD,YAAY,GAAG;EACtB,EAAA,MAAME,qBAAqB,GAAG7C,KAAK,CAACC,IAAI,CAAChC,KAAK,CAACiC,OAAO,EAAE,CAAC,CACtD4C,GAAG,CAAC,KAAA,IAAA;EAAA,IAAA,IAAC,CAAC9D,GAAG,EAAEsD,KAAK,CAAC,GAAA,KAAA,CAAA;MAAA,OAAM;QACtBtD,GAAG;EACH0B,MAAAA,OAAO,EAAE4B,KAAK,CAAC5B,OAAO,CAACH,OAAO,EAAA;OAC/B,CAAA;EAAA,GAAC,CAAC,CACFwC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACtC,OAAO,GAAGuC,CAAC,CAACvC,OAAO,CAAC,CAAA;IAExC,MAAMwC,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CACnCD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAEpF,KAAK,CAACqF,IAAI,GAAGtI,aAAa,CAACK,UAAU,CAAC,EAClD4C,KAAK,CAACqF,IAAI,CACX,CAAA;IAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,oBAAoB,EAAEK,CAAC,EAAE,EAAE;MAC7CtF,KAAK,CAACwB,MAAM,CAACoD,qBAAqB,CAACU,CAAC,CAAC,CAACvE,GAAG,CAAC,CAAA;EAC5C,GAAA;EACF,CAAA;;EAEA;EACA,SAASwE,gBAAgB,CACvBxE,GAAW,EACX7D,QAAgB,EAChByD,IAAwB,EAClB;EACN;EACA,EAAA,MAAM6E,OAAO,GAAG7E,IAAI,CAAC8E,WAAW,IAAI,EAAE,CAAA;EACtC,EAAA,MAAMhD,OAAO,GAAG,IAAIL,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAGpF,aAAa,CAACC,QAAQ,CAAC,CAAA;EAC7D,EAAA,MAAMwF,QAAQ,GAAGxC,KAAK,CAACkB,GAAG,CAAChE,QAAQ,CAAC,CAAA;IACpC,IAAIsF,QAAQ,IAAIgD,OAAO,IAAIhD,QAAQ,CAACgD,OAAO,KAAKA,OAAO,EAAE;MACvDhD,QAAQ,CAACC,OAAO,GAAGA,OAAO,CAAA;EAC1BZ,IAAAA,qBAAqB,EAAE,CAAA;EACvB,IAAA,OAAA;EACF,GAAA;;EAEA;EACA7B,EAAAA,KAAK,CAACoB,GAAG,CAAClE,QAAQ,EAAE;MAClByD,IAAI;MACJ6E,OAAO;MACP/C,OAAO;EACPC,IAAAA,GAAG,EAAEvC,WAAW,CAACuF,GAAG,CAAC3E,GAAG,CAAA;EAC1B,GAAC,CAAC,CAAA;EACF2D,EAAAA,YAAY,EAAE,CAAA;EACd;EACA7C,EAAAA,qBAAqB,EAAE,CAAA;;EAEvB;EACA,EAAA,MAAM8D,SAAS,GAAG9F,mBAAmB,CAACqB,GAAG,CAACH,GAAG,CAAC,CAAA;EAC9C4E,EAAAA,SAAS,IAAIA,SAAS,CAACrE,OAAO,CAAEhB,QAAQ,IAAKO,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC,CAAA;EAC/E,CAAA;EAEA,eAAeE,eAAe,CAC5BP,QAAoB,EACpBK,IAAwB,EACT;EACfA,EAAAA,IAAI,GAAG,MAAML,QAAQ,CAACsF,cAAc,CAACjF,IAAI,EAAEhD,SAAS,EAAEJ,SAAS,CAACK,YAAY,CAAC,CAAA;EAE7E,EAAA,MAAM0C,QAAQ,CAACuF,oBAAoB,CAAClF,IAAI,CAAC,CAAA;IACzCL,QAAQ,CAACwF,cAAc,CAACnF,IAAI,CAACoF,WAAW,IAAIzF,QAAQ,CAAC0F,cAAc,EAAE,CAAC,CAAA;IACtE1F,QAAQ,CAAC2F,WAAW,CAACtF,IAAI,CAACuF,QAAQ,IAAI5F,QAAQ,CAAC6F,WAAW,EAAE,CAAC,CAAA;EAC/D,CAAA;EAEA,eAAexD,aAAa,CAC1BrC,QAAoB,EACS;IAC7B,MAAM;MAAEwC,OAAO;EAAEsD,IAAAA,iBAAAA;EAAkB,GAAC,GAAG9F,QAAQ,CAAC+F,WAAW,EAAE,CAAA;EAC7D,EAAA,MAAMlI,SAAS,GAAGmC,QAAQ,CAACgG,YAAY,EAAE,CAAA;EACzC,EAAA,MAAMC,UAAU,GAAGjG,QAAQ,CAAC2C,YAAY,EAAE,CAAA;EAC1C,EAAA,MAAMlC,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAC5B,EAAA,MAAMpD,QAAQ,GAAGgF,WAAW,CAAC5B,QAAQ,CAAC,CAAA;EAEtC,EAAA,IAAIuD,OAAO,GAAG5D,aAAa,CAACiB,GAAG,CAAChE,QAAQ,CAAC,CAAA;IACzC,IAAI,CAAC2G,OAAO,EAAE;EACZ,IAAA,MAAM2C,OAA0B,GAAGD,UAAU,GACzCvI,OAAO,CAACK,mBAAmB,CAAC;EAC1BH,MAAAA,IAAI,EAAE4E,OAAO;QACb3E,SAAS;EACTG,MAAAA,OAAO,EAAE;EACP4E,QAAAA,UAAU,EAAE5C,QAAQ,CAAC6C,aAAa,EAAE;EACpCsD,QAAAA,gBAAgB,EAAEnG,QAAQ,CAACoD,mBAAmB,EAAE;EAChDgD,QAAAA,cAAc,EAAE3E,KAAK,CAACC,IAAI,CAAC1B,QAAQ,CAACqG,iBAAiB,EAAE,CAAC1E,OAAO,EAAE,CAAC;UAClE0B,GAAG,EAAErD,QAAQ,CAACsD,MAAM,EAAA;SACrB;EACDxF,MAAAA,OAAO,EAAEgI,iBAAAA;EACX,KAAC,CAAC,GACFpI,OAAO,CAACC,iBAAiB,CAAC;EACxBC,MAAAA,IAAI,EAAE4E,OAAO;QACb3E,SAAS;EACTC,MAAAA,OAAO,EAAEgI,iBAAAA;EACX,KAAC,CAAC,CAAA;;EAEN;EACAvC,IAAAA,OAAO,GAAG2C,OAAO,CACdrC,IAAI,CAAEyC,GAAG,IAAK;QACb,IAAIA,GAAG,CAACxI,OAAO,CAAC8C,GAAG,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE;EAClDf,QAAAA,WAAW,CAACgB,GAAG,CAACJ,GAAG,CAAC,CAAA;EACtB,OAAA;QACA,OAAO6F,GAAG,CAACC,IAAI,EAAE,CAAA;EACnB,KAAC,CAAC,CACD1C,IAAI,CAAExD,IAAwB,IAAK;EAClC4E,MAAAA,gBAAgB,CAACxE,GAAG,EAAE7D,QAAQ,EAAEyD,IAAI,CAAC,CAAA;QACrCiC,gBAAgB,CAACtC,QAAQ,CAAC,CAAA;EAC1BL,MAAAA,aAAa,CAACuB,MAAM,CAACtE,QAAQ,CAAC,CAAA;EAC9B,MAAA,OAAOyD,IAAI,CAAA;EACb,KAAC,CAAC,CACDyD,KAAK,CAAExE,CAAC,IAAK;EAOZK,MAAAA,aAAa,CAACuB,MAAM,CAACtE,QAAQ,CAAC,CAAA;EAC9B,MAAA,OAAO4G,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,CAAA;EAC5B,KAAC,CAAC,CAAA;EACJ9D,IAAAA,aAAa,CAACmB,GAAG,CAAClE,QAAQ,EAAE2G,OAAO,CAAC,CAAA;EACtC,GAAA;EACA,EAAA,OAAO,MAAMA,OAAO,CAAA;EACtB,CAAA;;EAEA;EACA;EACA,SAASjB,gBAAgB,CAACtC,QAAoB,EAAQ;EACpD,EAAA,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAC5B,EAAA,MAAMpD,QAAQ,GAAGgF,WAAW,CAAC5B,QAAQ,CAAC,CAAA;IACtC,MAAM;MAAEwG,aAAa;EAAEC,IAAAA,2BAAAA;EAA4B,GAAC,GAAGzG,QAAQ,CAAC+F,WAAW,EAAE,CAAA;EAC7E,EAAA,MAAMlI,SAAS,GAAGmC,QAAQ,CAACgG,YAAY,EAAE,CAAA;EACzC,EAAA,IACEvJ,aAAa,CAACI,cAAc,IAC5BgD,WAAW,CAACuF,GAAG,CAAC3E,GAAG,CAAC,IACpBxD,SAAS,CAACQ,WAAW,EACrB;EACA,IAAA,IAAImC,OAAO,CAACwF,GAAG,CAAC3E,GAAG,CAAC,EAAE,OAAA;EACtB,IAAA,MAAMU,OAAsB,GAAG;EAC7BuF,MAAAA,GAAG,EAAE,IAAI;EACT9I,MAAAA,IAAI,EAAE4I,aAAa;QACnB3I,SAAS;EACTC,MAAAA,OAAO,EAAE2I,2BAA2B;QACpCE,EAAE,EAAGC,KAA2B,IAAK;UACnC,IAAI;EACF,UAAA,IAAIA,KAAK,CAACC,IAAI,KAAK,kBAAkB,EAAE;EACrC,YAAA,MAAMxB,SAAS,GAAG9F,mBAAmB,CAACqB,GAAG,CAACH,GAAG,CAAC,CAAA;EAC9C4E,YAAAA,SAAS,IACPA,SAAS,CAACrE,OAAO,CAAEhB,QAAQ,IAAK;gBAC9BqC,aAAa,CAACrC,QAAQ,CAAC,CAAA;EACzB,aAAC,CAAC,CAAA;EACN,WAAC,MAAM,IAAI4G,KAAK,CAACC,IAAI,KAAK,UAAU,EAAE;cACpC,MAAMN,IAAwB,GAAGnI,IAAI,CAAC8F,KAAK,CAAC0C,KAAK,CAACvG,IAAI,CAAC,CAAA;EACvD4E,YAAAA,gBAAgB,CAACxE,GAAG,EAAE7D,QAAQ,EAAE2J,IAAI,CAAC,CAAA;EACvC,WAAA;EACA;YACApF,OAAO,CAAC2F,MAAM,GAAG,CAAC,CAAA;WACnB,CAAC,OAAOxH,CAAC,EAAE;YAOVyH,UAAU,CAAC5F,OAAO,CAAC,CAAA;EACrB,SAAA;SACD;EACD2F,MAAAA,MAAM,EAAE,CAAC;EACT1F,MAAAA,KAAK,EAAE,QAAA;OACR,CAAA;EACDxB,IAAAA,OAAO,CAACkB,GAAG,CAACL,GAAG,EAAEU,OAAO,CAAC,CAAA;MACzBG,aAAa,CAACH,OAAO,CAAC,CAAA;EACxB,GAAA;EACF,CAAA;EAEA,SAAS4F,UAAU,CAAC5F,OAAsB,EAAE;EAC1C,EAAA,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE,OAAA;IAC9BD,OAAO,CAAC2F,MAAM,EAAE,CAAA;EAChB,EAAA,IAAI3F,OAAO,CAAC2F,MAAM,GAAG,CAAC,IAAK3F,OAAO,CAACuF,GAAG,IAAIvF,OAAO,CAACuF,GAAG,CAACM,UAAU,KAAK,CAAE,EAAE;EACvE;MACA,MAAMC,KAAK,GACTrC,IAAI,CAACsC,GAAG,CAAC,CAAC,EAAE/F,OAAO,CAAC2F,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,GAAGlC,IAAI,CAACuC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAA;MACjE9F,cAAc,CAACF,OAAO,CAAC,CAAA;EACvBnC,IAAAA,UAAU,CAAC,MAAM;EACf,MAAA,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAACoI,QAAQ,CAACjG,OAAO,CAACC,KAAK,CAAC,EAAE,OAAA;QAChDE,aAAa,CAACH,OAAO,CAAC,CAAA;OACvB,EAAEyD,IAAI,CAACC,GAAG,CAACoC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;EAC9B,GAAA;EACF,CAAA;;EAEA,SAAS5F,cAAc,CAACF,OAAsB,EAAE;EAC9C,EAAA,IAAI,CAACA,OAAO,CAACuF,GAAG,EAAE,OAAA;EAClBvF,EAAAA,OAAO,CAACuF,GAAG,CAACW,MAAM,GAAG,IAAI,CAAA;EACzBlG,EAAAA,OAAO,CAACuF,GAAG,CAACY,OAAO,GAAG,IAAI,CAAA;EAC1BnG,EAAAA,OAAO,CAACuF,GAAG,CAACa,KAAK,EAAE,CAAA;IACnBpG,OAAO,CAACuF,GAAG,GAAG,IAAI,CAAA;EAClB,EAAA,IAAIvF,OAAO,CAACC,KAAK,KAAK,QAAQ,EAAE;MAC9BD,OAAO,CAACC,KAAK,GAAG,UAAU,CAAA;EAC5B,GAAA;EACF,CAAA;EAEA,SAASE,aAAa,CAACH,OAAsB,EAAE;EAC7CA,EAAAA,OAAO,CAACuF,GAAG,GAAGhJ,OAAO,CAACY,eAAe,CAAC;MACpCV,IAAI,EAAEuD,OAAO,CAACvD,IAAI;MAClBC,SAAS,EAAEsD,OAAO,CAACtD,SAAS;MAC5BC,OAAO,EAAEqD,OAAO,CAACrD,OAAAA;EACnB,GAAC,CAAgB,CAAA;IACjBqD,OAAO,CAACC,KAAK,GAAG,QAAQ,CAAA;IACxBD,OAAO,CAACuF,GAAG,CAACxH,gBAAgB,CAAC,UAAU,EAAEiC,OAAO,CAACwF,EAAE,CAAC,CAAA;IACpDxF,OAAO,CAACuF,GAAG,CAACxH,gBAAgB,CAAC,kBAAkB,EAAEiC,OAAO,CAACwF,EAAE,CAAC,CAAA;IAC5DxF,OAAO,CAACuF,GAAG,CAACY,OAAO,GAAG,MAAMP,UAAU,CAAC5F,OAAO,CAAC,CAAA;EAC/CA,EAAAA,OAAO,CAACuF,GAAG,CAACW,MAAM,GAAG,MAAM;MACzBlG,OAAO,CAAC2F,MAAM,GAAG,CAAC,CAAA;KACnB,CAAA;EACH;;EC3gBaU,IAAAA,kBAAkB,GAAG,8BAAA,CAAA;EAClC,IAAMC,cAAc,GAAuB;EACzCC,EAAAA,MAAM,EAAE,SAAA,MAAA,GAAA,EAAA;EADiC,CAA3C,CAAA;EAIA,IAAMC,QAAQ,gBAAgC,IAAInI,GAAJ,EAA9C,CAAA;EACA,IAAMoI,SAAS,gBAAkB,IAAI9H,GAAJ,EAAjC,CAAA;EAEA,SAAS+H,eAAT,CAAyBC,IAAzB,EAAA;IACE,OAAOA,IAAI,KAAK,MAAT,GACH;EACEC,IAAAA,SAAS,EAAE,IADb;EAEEC,IAAAA,OAAO,EAAE,IAFX;EAGEpF,IAAAA,UAAU,EAAE,IAHd;EAIEqF,IAAAA,aAAa,EAAE,IAAA;EAJjB,GADG,GAOH;EACEF,IAAAA,SAAS,EAAE,KADb;EAEEC,IAAAA,OAAO,EAAE,KAFX;EAGEpF,IAAAA,UAAU,EAAE,IAHd;MAIEsF,eAAe,EAAE,CAACJ,IAAD,CAAA;KAXvB,CAAA;EAaD,CAAA;EAED,SAASK,gBAAT,CAA0BC,OAA1B,EAAA;EACE,EAAA,IAAIC,MAAM,GAAGV,QAAQ,CAAC/G,GAAT,CAAawH,OAAb,CAAb,CAAA;IAEA,IAAI,CAACC,MAAL,EAAa;EACXA,IAAAA,MAAM,GAAG;EAAED,MAAAA,OAAO,EAAPA,OAAF;EAAWxF,MAAAA,UAAU,EAAE,EAAA;OAAhC,CAAA;EACA+E,IAAAA,QAAQ,CAAC7G,GAAT,CAAasH,OAAb,EAAsBC,MAAtB,CAAA,CAAA;EACD,GAAA;EAED,EAAA,OAAOA,MAAP,CAAA;EACD,CAAA;EAED,SAASC,2BAAT,CACEC,EADF,EAEET,IAFF,EAGEU,eAHF,EAIEC,QAJF,EAKEC,cALF,EAAA;EAOE,EAAA,IAAMC,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC,CAAA;EACA,EAAA,IAAMF,MAAM,GAAoC;EAC9CO,IAAAA,OAAO,EAAE,KADqC;EAE9CC,IAAAA,aAAa,EAAEF,YAF+B;EAG9CG,IAAAA,YAAY,EAAEH,YAHgC;EAI9Cf,IAAAA,SAAS,EAAE,EAJmC;EAK9CW,IAAAA,EAAE,EAAFA,EAL8C;EAM9CQ,IAAAA,gBAAgB,EAAE,IAN4B;MAO9CC,QAAQ,EAAE,IAAIC,gBAAJ,CAAqB,YAAA;EAC7B;EACA;EACA;EACA;QACA,IAAInB,IAAI,KAAK,UAAT,IAAuBO,MAAM,CAACU,gBAAlC,EAAoD,OAApD,KACK,IAAIjB,IAAI,KAAK,UAAb,EACHO,MAAM,CAACU,gBAAP,GAA0B/J,UAAU,CAAC,YAAA;UACnCqJ,MAAM,CAACU,gBAAP,GAA0B,IAA1B,CAAA;SADkC,EAEjC,IAFiC,CAApC,CAAA;EAIF,MAAA,IAAMJ,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC,CAAA;QACA,IACET,IAAI,KAAK,UAAT,IACAa,YAAY,CAACO,UAAb,KAA4Bb,MAAM,CAACS,YAAP,CAAoBI,UADhD,IAEAP,YAAY,CAACQ,gBAAb,KAAkCd,MAAM,CAACS,YAAP,CAAoBK,gBAHxD,EAKE,OAAA;EACF,MAAA,IAAIR,YAAY,KAAKN,MAAM,CAACS,YAA5B,EAA0C,OAAA;QAC1CT,MAAM,CAACQ,aAAP,GAAuBF,YAAvB,CAAA;QACAD,cAAc,CAACL,MAAD,CAAd,CAAA;EACD,KArBS,CAPoC;EA6B9CK,IAAAA,cAAc,EAAdA,cA7B8C;EA8B9CD,IAAAA,QAAQ,EAARA,QA9B8C;EA+B9CD,IAAAA,eAAe,EAAfA,eAAAA;KA/BF,CAAA;EAiCA,EAAA,IAAIV,IAAI,KAAK,UAAT,IAAuBS,EAAE,CAACW,UAA9B,EAA0C;MACxCb,MAAM,CAACW,QAAP,CAAgBI,OAAhB,CAAwBb,EAAE,CAACW,UAA3B,EAAuC;EACrCnB,MAAAA,SAAS,EAAE,IAD0B;EAErCC,MAAAA,OAAO,EAAE,IAF4B;EAGrCpF,MAAAA,UAAU,EAAE,KAHyB;EAIrCqF,MAAAA,aAAa,EAAE,KAAA;OAJjB,CAAA,CAAA;EAMD,GAPD,MAOO;MACLI,MAAM,CAACW,QAAP,CAAgBI,OAAhB,CAAwBb,EAAxB,EAA4BV,eAAe,CAACC,IAAD,CAA3C,CAAA,CAAA;EACD,GAAA;EACD,EAAA,OAAOO,MAAP,CAAA;EACD,CAAA;EAED,SAASgB,aAAT,CACEC,GADF,EAEEjB,MAFF,EAAA;IAIE,IAAMkB,UAAU,GAAGlB,MAAM,CAACG,eAAP,CAAuBH,MAAM,CAACE,EAA9B,CAAnB,CAAA;IACAF,MAAM,CAACS,YAAP,GAAsBQ,GAAtB,CAAA;EACA,EAAA,IAAIA,GAAG,IAAI,OAAOA,GAAP,KAAe,QAA1B,EAAoC;EAClC,IAAA,IACE,CAACC,UAAD,IACAD,GAAG,CAACJ,UAAJ,KAAmBK,UAAU,CAACL,UAD9B,IAEAI,GAAG,CAACH,gBAAJ,KAAyBI,UAAU,CAACJ,gBAHtC,EAIE;QACAd,MAAM,CAACO,OAAP,GAAiB,IAAjB,CAAA;QACAY,aAAa,EAAA,CAAA;EACd,KAAA;EACF,GATD,MASO,IAAIF,GAAG,KAAKC,UAAZ,EAAwB;MAC7BlB,MAAM,CAACO,OAAP,GAAiB,IAAjB,CAAA;MACAY,aAAa,EAAA,CAAA;EACd,GAAA;EACF,CAAA;EAED,SAASC,kBAAT,CAA4BpB,MAA5B,EAAA;EACE,EAAA,IAAIiB,GAAG,GAAGjB,MAAM,CAACQ,aAAjB,CAAA;EACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB5G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EAAA,IAAA,OAAKsI,GAAG,GAAGI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAX,CAAA;KAA1B,CAAA,CAAA;EACAD,EAAAA,aAAa,CAACO,kBAAkB,CAACN,GAAD,CAAnB,EAA0BjB,MAA1B,CAAb,CAAA;EACD,CAAA;EACD,SAASwB,mBAAT,CAA6BxB,MAA7B,EAAA;EACE,EAAA,IAAMiB,GAAG,GAAG,IAAIxJ,GAAJ,CAAQuI,MAAM,CAACQ,aAAP,CAAqBiB,KAArB,CAA2B,KAA3B,CAAA,CAAkCC,MAAlC,CAAyCC,OAAzC,CAAR,CAAZ,CAAA;EACA3B,EAAAA,MAAM,CAACT,SAAP,CAAiB5G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EAAA,IAAA,OAAI0I,CAAC,CAACC,MAAF,CAASL,GAAT,CAAJ,CAAA;KAA1B,CAAA,CAAA;EACAD,EAAAA,aAAa,CACX5H,KAAK,CAACC,IAAN,CAAW4H,GAAX,CACGS,CAAAA,MADH,CACUC,OADV,EAEGC,IAFH,CAEQ,GAFR,CADW,EAIX5B,MAJW,CAAb,CAAA;EAMD,CAAA;EAED,SAAS6B,kBAAT,CAA4B7B,MAA5B,EAAA;EACE,EAAA,IAAIiB,GAAG,GAAkBjB,MAAM,CAACQ,aAAhC,CAAA;EACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB5G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EAAA,IAAA,OAAKsI,GAAG,GAAGI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAX,CAAA;KAA1B,CAAA,CAAA;EACAD,EAAAA,aAAa,CAACC,GAAD,EAAMjB,MAAN,CAAb,CAAA;EACD,CAAA;EAED,SAAS8B,aAAT,CAAA,IAAA,EAAA;EACEC,EAAAA,IAAAA,cAAAA,GAAAA,IAAAA,CAAAA,cAAAA;EACAC,IAAAA,oBAAAA,GAAAA,IAAAA,CAAAA,oBAAAA,CAAAA;EAEA,EAAA,IAAMnB,UAAU,GAAGvK,QAAQ,CAAC2L,aAAT,CAAoCF,cAApC,CAAnB,CAAA;EACA,EAAA,IAAI,CAAClB,UAAL,EAAiB,OAAO,IAAP,CAAA;IACjB,IAAMC,gBAAgB,GAAGkB,oBAAoB,GACzC1L,QAAQ,CAAC2L,aAAT,CAAoCD,oBAApC,CADyC,GAEzC,IAFJ,CAAA;EAGA,EAAA,IAAIA,oBAAoB,IAAI,CAAClB,gBAA7B,EAA+C,OAAO,IAAP,CAAA;IAC/C,OAAO;EACLD,IAAAA,UAAU,EAAVA,UADK;EAELC,IAAAA,gBAAgB,EAAhBA,gBAAAA;KAFF,CAAA;EAID,CAAA;EAED,SAASoB,sBAAT,CAAgClC,MAAhC,EAAA;EACE,EAAA,IAAIiB,GAAG,GAAGjB,MAAM,CAACQ,aAAjB,CAAA;EACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB5G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EACxB,IAAA,IAAMwJ,SAAS,GAAGd,CAAC,CAACC,MAAF,EAAlB,CAAA;EACA,IAAA,IAAMc,QAAQ,GAAGN,aAAa,CAACK,SAAD,CAA9B,CAAA;MACAlB,GAAG,GAAGmB,QAAQ,IAAInB,GAAlB,CAAA;KAHF,CAAA,CAAA;EAKAD,EAAAA,aAAa,CAACC,GAAD,EAAMjB,MAAN,CAAb,CAAA;EACD,CAAA;EAED,IAAMqC,YAAY,GAAG,SAAfA,YAAe,CAACnC,EAAD,EAAA;IAAA,OAAiBA,EAAE,CAACoC,SAApB,CAAA;EAAA,CAArB,CAAA;EACA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACrC,EAAD,EAAcxE,KAAd,EAAA;EAAA,EAAA,OAAiCwE,EAAE,CAACoC,SAAH,GAAe5G,KAAhD,CAAA;EAAA,CAArB,CAAA;EACA,SAAS8G,oBAAT,CAA8BzC,OAA9B,EAAA;EACE,EAAA,IAAM0C,aAAa,GAAG3C,gBAAgB,CAACC,OAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAAC0C,aAAa,CAACC,IAAnB,EAAyB;EACvBD,IAAAA,aAAa,CAACC,IAAd,GAAqBzC,2BAA2B,CAC9CF,OAD8C,EAE9C,MAF8C,EAG9CsC,YAH8C,EAI9CE,YAJ8C,EAK9CnB,kBAL8C,CAAhD,CAAA;EAOD,GAAA;IACD,OAAOqB,aAAa,CAACC,IAArB,CAAA;EACD,CAAA;EAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAACzC,EAAD,EAAA;IACzB,OAAO;MACLW,UAAU,EAAEX,EAAE,CAAC0C,aADV;MAEL9B,gBAAgB,EAAEZ,EAAE,CAAC2C,kBAAAA;KAFvB,CAAA;EAID,CALD,CAAA;EAMA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC5C,EAAD,EAAcxE,KAAd,EAAA;EACzB,EAAA,IACEA,KAAK,CAACoF,gBAAN,IACA,CAACpF,KAAK,CAACmF,UAAN,CAAiBkC,QAAjB,CAA0BrH,KAAK,CAACoF,gBAAhC,CAFH,EAGE;EACA;EACA;EACA,IAAA,OAAA;EACD,GAAA;IACDpF,KAAK,CAACmF,UAAN,CAAiBmC,YAAjB,CAA8B9C,EAA9B,EAAkCxE,KAAK,CAACoF,gBAAxC,CAAA,CAAA;EACD,CAVD,CAAA;EAWA,SAASmC,wBAAT,CAAkClD,OAAlC,EAAA;EACE,EAAA,IAAM0C,aAAa,GAAG3C,gBAAgB,CAACC,OAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAAC0C,aAAa,CAACS,QAAnB,EAA6B;EAC3BT,IAAAA,aAAa,CAACS,QAAd,GAAyBjD,2BAA2B,CAClDF,OADkD,EAElD,UAFkD,EAGlD4C,kBAHkD,EAIlDG,kBAJkD,EAKlDZ,sBALkD,CAApD,CAAA;EAOD,GAAA;IACD,OAAOO,aAAa,CAACS,QAArB,CAAA;EACD,CAAA;EAED,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACjD,EAAD,EAAce,GAAd,EAAA;EAAA,EAAA,OACpBA,GAAG,GAAIf,EAAE,CAACkD,SAAH,GAAenC,GAAnB,GAA0Bf,EAAE,CAACmD,eAAH,CAAmB,OAAnB,CADT,CAAA;EAAA,CAAtB,CAAA;EAEA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACpD,EAAD,EAAA;IAAA,OAAiBA,EAAE,CAACkD,SAApB,CAAA;EAAA,CAAtB,CAAA;EACA,SAASG,qBAAT,CAA+BrD,EAA/B,EAAA;EACE,EAAA,IAAMuC,aAAa,GAAG3C,gBAAgB,CAACI,EAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAACuC,aAAa,CAACe,OAAnB,EAA4B;EAC1Bf,IAAAA,aAAa,CAACe,OAAd,GAAwBvD,2BAA2B,CACjDC,EADiD,EAEjD,OAFiD,EAGjDoD,aAHiD,EAIjDH,aAJiD,EAKjD3B,mBALiD,CAAnD,CAAA;EAOD,GAAA;IACD,OAAOiB,aAAa,CAACe,OAArB,CAAA;EACD,CAAA;EAED,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACC,QAAD,EAAA;EAAA,EAAA,OAAsB,UAACxD,EAAD,EAAA;EAAA,IAAA,IAAA,gBAAA,CAAA;EAAA,IAAA,OAAA,CAAA,gBAAA,GACzCA,EAAE,CAACyD,YAAH,CAAgBD,QAAhB,CADyC,+BACZ,IADY,CAAA;KAAtB,CAAA;EAAA,CAArB,CAAA;EAEA,IAAME,YAAY,GAAG,SAAfA,YAAe,CAACF,QAAD,EAAA;IAAA,OAAsB,UAACxD,EAAD,EAAce,GAAd,EAAA;EAAA,IAAA,OACzCA,GAAG,KAAK,IAAR,GAAef,EAAE,CAAC2D,YAAH,CAAgBH,QAAhB,EAA0BzC,GAA1B,CAAf,GAAgDf,EAAE,CAACmD,eAAH,CAAmBK,QAAnB,CADP,CAAA;KAAtB,CAAA;EAAA,CAArB,CAAA;EAEA,SAASI,yBAAT,CAAmC5D,EAAnC,EAAgDT,IAAhD,EAAA;EACE,EAAA,IAAMgD,aAAa,GAAG3C,gBAAgB,CAACI,EAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAACuC,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,CAAL,EAAqC;MACnCgD,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,IAAiCQ,2BAA2B,CAC1DC,EAD0D,EAE1DT,IAF0D,EAG1DgE,YAAY,CAAChE,IAAD,CAH8C,EAI1DmE,YAAY,CAACnE,IAAD,CAJ8C,EAK1DoC,kBAL0D,CAA5D,CAAA;EAOD,GAAA;EACD,EAAA,OAAOY,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,CAAP,CAAA;EACD,CAAA;EAED,SAASsE,2BAAT,CAAqC7D,EAArC,EAAkDT,IAAlD,EAAA;EACE,EAAA,IAAMM,OAAO,GAAGT,QAAQ,CAAC/G,GAAT,CAAa2H,EAAb,CAAhB,CAAA;IACA,IAAI,CAACH,OAAL,EAAc,OAAA;IACd,IAAIN,IAAI,KAAK,MAAb,EAAqB;EAAA,IAAA,IAAA,aAAA,EAAA,qBAAA,CAAA;EACnB,IAAA,CAAA,aAAA,GAAA,OAAO,CAACiD,IAAR,KAAc/B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,aAAAA,CAAAA,QAAd,2CAAwBqD,UAAxB,EAAA,CAAA;MACA,OAAOjE,OAAO,CAAC2C,IAAf,CAAA;EACD,GAHD,MAGO,IAAIjD,IAAI,KAAK,OAAb,EAAsB;EAAA,IAAA,IAAA,gBAAA,EAAA,qBAAA,CAAA;EAC3B,IAAA,CAAA,gBAAA,GAAA,OAAO,CAAC+D,OAAR,KAAiB7C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,gBAAAA,CAAAA,QAAjB,2CAA2BqD,UAA3B,EAAA,CAAA;MACA,OAAOjE,OAAO,CAACyD,OAAf,CAAA;EACD,GAHM,MAGA,IAAI/D,IAAI,KAAK,UAAb,EAAyB;EAAA,IAAA,IAAA,iBAAA,EAAA,qBAAA,CAAA;EAC9B,IAAA,CAAA,iBAAA,GAAA,OAAO,CAACyD,QAAR,KAAkBvC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,iBAAAA,CAAAA,QAAlB,2CAA4BqD,UAA5B,EAAA,CAAA;MACA,OAAOjE,OAAO,CAACmD,QAAf,CAAA;EACD,GAHM,MAGA;EAAA,IAAA,IAAA,mBAAA,EAAA,qBAAA,EAAA,sBAAA,CAAA;EACL,IAAA,CAAA,mBAAA,GAAA,OAAO,CAAC3I,UAAR,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,GAAA,mBAAA,CAAqBkF,IAArB,CAA4BkB,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,sBAAAA,GAAAA,qBAAAA,CAAAA,QAA5B,4CAAsCqD,UAAtC,EAAA,CAAA;EACA,IAAA,OAAOjE,OAAO,CAACxF,UAAR,CAAmBkF,IAAnB,CAAP,CAAA;EACD,GAAA;EACF,CAAA;EAED,IAAIwE,kBAAJ,CAAA;EACA,SAAS1C,kBAAT,CAA4BmB,IAA5B,EAAA;IACE,IAAI,CAACuB,kBAAL,EAAyB;EACvBA,IAAAA,kBAAkB,GAAG3N,QAAQ,CAAC4N,aAAT,CAAuB,KAAvB,CAArB,CAAA;EACD,GAAA;IACDD,kBAAkB,CAAC3B,SAAnB,GAA+BI,IAA/B,CAAA;IACA,OAAOuB,kBAAkB,CAAC3B,SAA1B,CAAA;EACD,CAAA;EAED,SAAS6B,gBAAT,CACEjE,EADF,EAEET,IAFF,EAGE4B,CAHF,EAAA;EAKE,EAAA,IAAI,CAACA,CAAC,CAACd,OAAP,EAAgB,OAAA;IAChBc,CAAC,CAACd,OAAF,GAAY,KAAZ,CAAA;EACA,EAAA,IAAMU,GAAG,GAAGI,CAAC,CAACZ,YAAd,CAAA;EACA,EAAA,IAAI,CAACY,CAAC,CAAC9B,SAAF,CAAY6E,MAAjB,EAAyB;EACvBL,IAAAA,2BAA2B,CAAC7D,EAAD,EAAKT,IAAL,CAA3B,CAAA;EACD,GAAA;EACD4B,EAAAA,CAAC,CAACjB,QAAF,CAAWF,EAAX,EAAee,GAAf,CAAA,CAAA;EACD,CAAA;EAED,SAASb,QAAT,CAAkBiB,CAAlB,EAAoCnB,EAApC,EAAA;EACEmB,EAAAA,CAAC,CAACqB,IAAF,IAAUyB,gBAAgB,CAAajE,EAAb,EAAiB,MAAjB,EAAyBmB,CAAC,CAACqB,IAA3B,CAA1B,CAAA;EACArB,EAAAA,CAAC,CAACmC,OAAF,IAAaW,gBAAgB,CAAkBjE,EAAlB,EAAsB,OAAtB,EAA+BmB,CAAC,CAACmC,OAAjC,CAA7B,CAAA;EACAnC,EAAAA,CAAC,CAAC6B,QAAF,IAAciB,gBAAgB,CAAiBjE,EAAjB,EAAqB,UAArB,EAAiCmB,CAAC,CAAC6B,QAAnC,CAA9B,CAAA;IACAvI,MAAM,CAACC,IAAP,CAAYyG,CAAC,CAAC9G,UAAd,CAAA,CAA0B5B,OAA1B,CAAkC,UAAI,IAAA,EAAA;MACpCwL,gBAAgB,CAAkBjE,EAAlB,EAAsBT,IAAtB,EAA4B4B,CAAC,CAAC9G,UAAF,CAAakF,IAAb,CAA5B,CAAhB,CAAA;KADF,CAAA,CAAA;EAGD,CAAA;EAED,SAAS0B,aAAT,GAAA;IACE7B,QAAQ,CAAC3G,OAAT,CAAiByH,QAAjB,CAAA,CAAA;EACD,CAAA;;EAGD,SAASiE,aAAT,CAAuBC,QAAvB,EAA2CvE,OAA3C,EAAA;IACE,IAAIC,MAAM,GAA2C,IAArD,CAAA;EACA,EAAA,IAAIsE,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;EAC5BvE,IAAAA,MAAM,GAAGwC,oBAAoB,CAACzC,OAAD,CAA7B,CAAA;EACD,GAFD,MAEO,IAAIuE,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;EACpCvE,IAAAA,MAAM,GAAGuD,qBAAqB,CAACxD,OAAD,CAA9B,CAAA;EACD,GAFM,MAEA,IAAIuE,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;MACxCvE,MAAM,GAAG8D,yBAAyB,CAAC/D,OAAD,EAAUuE,QAAQ,CAACE,SAAnB,CAAlC,CAAA;EACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;EACvCvE,IAAAA,MAAM,GAAGiD,wBAAwB,CAAClD,OAAD,CAAjC,CAAA;EACD,GAAA;IACD,IAAI,CAACC,MAAL,EAAa,OAAA;EACbA,EAAAA,MAAM,CAACT,SAAP,CAAiBkF,IAAjB,CAAsBH,QAAtB,CAAA,CAAA;IACAtE,MAAM,CAACK,cAAP,CAAsBL,MAAtB,CAAA,CAAA;EACD,CAAA;;EAGD,SAAS0E,YAAT,CAAsBJ,QAAtB,EAA0CpE,EAA1C,EAAA;IACE,IAAIF,MAAM,GAA2C,IAArD,CAAA;EACA,EAAA,IAAIsE,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;EAC5BvE,IAAAA,MAAM,GAAGwC,oBAAoB,CAACtC,EAAD,CAA7B,CAAA;EACD,GAFD,MAEO,IAAIoE,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;EACpCvE,IAAAA,MAAM,GAAGuD,qBAAqB,CAACrD,EAAD,CAA9B,CAAA;EACD,GAFM,MAEA,IAAIoE,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;MACxCvE,MAAM,GAAG8D,yBAAyB,CAAC5D,EAAD,EAAKoE,QAAQ,CAACE,SAAd,CAAlC,CAAA;EACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;EACvCvE,IAAAA,MAAM,GAAGiD,wBAAwB,CAAC/C,EAAD,CAAjC,CAAA;EACD,GAAA;IACD,IAAI,CAACF,MAAL,EAAa,OAAA;IACb,IAAM2E,KAAK,GAAG3E,MAAM,CAACT,SAAP,CAAiBqF,OAAjB,CAAyBN,QAAzB,CAAd,CAAA;EACA,EAAA,IAAIK,KAAK,KAAK,CAAC,CAAf,EAAkB3E,MAAM,CAACT,SAAP,CAAiBsF,MAAjB,CAAwBF,KAAxB,EAA+B,CAA/B,CAAA,CAAA;IAClB3E,MAAM,CAACK,cAAP,CAAsBL,MAAtB,CAAA,CAAA;EACD,CAAA;;EAGD,SAAS8E,kBAAT,CAA4BR,QAA5B,EAAA;EACE;EACA;EACA,EAAA,IAAIA,QAAQ,CAACC,IAAT,KAAkB,UAAlB,IAAgCD,QAAQ,CAAChF,QAAT,CAAkB5C,IAAlB,KAA2B,CAA/D,EAAkE,OAAA;IAElE,IAAMqI,gBAAgB,GAAG,IAAItN,GAAJ,CAAQ6M,QAAQ,CAAChF,QAAjB,CAAzB,CAAA;IACA,IAAM0F,gBAAgB,GAAG1O,QAAQ,CAAC2O,gBAAT,CAA0BX,QAAQ,CAACY,QAAnC,CAAzB,CAAA;IAEAF,gBAAgB,CAACrM,OAAjB,CAAyB,UAAE,EAAA,EAAA;EACzB,IAAA,IAAI,CAACoM,gBAAgB,CAAChI,GAAjB,CAAqBmD,EAArB,CAAL,EAA+B;EAC7BoE,MAAAA,QAAQ,CAAChF,QAAT,CAAkB9G,GAAlB,CAAsB0H,EAAtB,CAAA,CAAA;EACAmE,MAAAA,aAAa,CAACC,QAAD,EAAWpE,EAAX,CAAb,CAAA;EACD,KAAA;KAJH,CAAA,CAAA;EAMD,CAAA;EAED,SAASiF,cAAT,CAAwBb,QAAxB,EAAA;EACEA,EAAAA,QAAQ,CAAChF,QAAT,CAAkB3G,OAAlB,CAA0B,UAAE,EAAA,EAAA;EAAA,IAAA,OAAI+L,YAAY,CAACJ,QAAD,EAAWpE,EAAX,CAAhB,CAAA;KAA5B,CAAA,CAAA;IACAoE,QAAQ,CAAChF,QAAT,CAAkB8F,KAAlB,EAAA,CAAA;IACA7F,SAAS,CAAA,QAAA,CAAT,CAAiB+E,QAAjB,CAAA,CAAA;EACD,CAAA;EAED,SAASe,qBAAT,GAAA;IACE9F,SAAS,CAAC5G,OAAV,CAAkBmM,kBAAlB,CAAA,CAAA;EACD,CAAA;;EAGD,IAAInE,QAAJ,CAAA;EAIgB2E,SAAAA,qBAAAA,GAAAA;EACd,EAAA,IAAI,OAAOhP,QAAP,KAAoB,WAAxB,EAAqC,OAAA;IAErC,IAAI,CAACqK,QAAL,EAAe;MACbA,QAAQ,GAAG,IAAIC,gBAAJ,CAAqB,YAAA;QAC9ByE,qBAAqB,EAAA,CAAA;EACtB,KAFU,CAAX,CAAA;EAGD,GAAA;IAEDA,qBAAqB,EAAA,CAAA;EACrB1E,EAAAA,QAAQ,CAACI,OAAT,CAAiBzK,QAAQ,CAACiP,eAA1B,EAA2C;EACzC7F,IAAAA,SAAS,EAAE,IAD8B;EAEzCC,IAAAA,OAAO,EAAE,IAFgC;EAGzCpF,IAAAA,UAAU,EAAE,KAH6B;EAIzCqF,IAAAA,aAAa,EAAE,KAAA;KAJjB,CAAA,CAAA;EAMD,CAAA;;EAGD0F,qBAAqB,EAAA,CAAA;EAErB,SAASE,WAAT,CAAqBnE,CAArB,EAAA;EACE;EACA,EAAA,IAAI,OAAO/K,QAAP,KAAoB,WAAxB,EAAqC,OAAO8I,cAAP,CAAA;;IAErCG,SAAS,CAAC/G,GAAV,CAAc6I,CAAd,CAAA,CAAA;;IAEAyD,kBAAkB,CAACzD,CAAD,CAAlB,CAAA;IACA,OAAO;EACLhC,IAAAA,MAAM,EAAE,SAAA,MAAA,GAAA;QACN8F,cAAc,CAAC9D,CAAD,CAAd,CAAA;EACD,KAAA;KAHH,CAAA;EAKD,CAAA;EAED,SAASqB,IAAT,CACEwC,QADF,EAEE5D,MAFF,EAAA;EAIE,EAAA,OAAOkE,WAAW,CAAC;EACjBjB,IAAAA,IAAI,EAAE,MADW;MAEjBjF,QAAQ,EAAE,IAAI7H,GAAJ,EAFO;EAGjB6J,IAAAA,MAAM,EAANA,MAHiB;EAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;EAJiB,GAAD,CAAlB,CAAA;EAMD,CAAA;EAED,SAAShC,QAAT,CACEgC,QADF,EAEE5D,MAFF,EAAA;EAIE,EAAA,OAAOkE,WAAW,CAAC;EACjBjB,IAAAA,IAAI,EAAE,UADW;MAEjBjF,QAAQ,EAAE,IAAI7H,GAAJ,EAFO;EAGjB6J,IAAAA,MAAM,EAANA,MAHiB;EAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;EAJiB,GAAD,CAAlB,CAAA;EAMD,CAAA;EAED,SAAS1B,OAAT,CACE0B,QADF,EAEE5D,MAFF,EAAA;EAIE,EAAA,OAAOkE,WAAW,CAAC;EACjBjB,IAAAA,IAAI,EAAE,OADW;MAEjBjF,QAAQ,EAAE,IAAI7H,GAAJ,EAFO;EAGjB6J,IAAAA,MAAM,EAANA,MAHiB;EAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;EAJiB,GAAD,CAAlB,CAAA;EAMD,CAAA;EAED,SAASV,SAAT,CACEU,QADF,EAEEV,SAFF,EAGElD,MAHF,EAAA;IAKE,IAAI,CAACnC,kBAAkB,CAACsG,IAAnB,CAAwBjB,SAAxB,CAAL,EAAyC,OAAOpF,cAAP,CAAA;EAEzC,EAAA,IAAIoF,SAAS,KAAK,OAAd,IAAyBA,SAAS,KAAK,WAA3C,EAAwD;EACtD,IAAA,OAAOhB,OAAO,CAAC0B,QAAD,EAAW,UAAU,UAAA,EAAA;EACjC,MAAA,IAAMQ,iBAAiB,GAAGpE,MAAM,CAAClI,KAAK,CAACC,IAAN,CAAWsM,UAAX,CAAuB/D,CAAAA,IAAvB,CAA4B,GAA5B,CAAD,CAAhC,CAAA;EACA+D,MAAAA,UAAU,CAACP,KAAX,EAAA,CAAA;QACA,IAAI,CAACM,iBAAL,EAAwB,OAAA;QACxBA,iBAAiB,CACdjE,KADH,CACS,MADT,CAAA,CAEGC,MAFH,CAEUC,OAFV,CAAA,CAGGhJ,OAHH,CAGW,UAAC,CAAA,EAAA;EAAA,QAAA,OAAIgN,UAAU,CAACnN,GAAX,CAAeoN,CAAf,CAAJ,CAAA;SAHZ,CAAA,CAAA;EAID,KARa,CAAd,CAAA;EASD,GAAA;EAED,EAAA,OAAOJ,WAAW,CAAC;EACjBjB,IAAAA,IAAI,EAAE,WADW;EAEjBC,IAAAA,SAAS,EAATA,SAFiB;MAGjBlF,QAAQ,EAAE,IAAI7H,GAAJ,EAHO;EAIjB6J,IAAAA,MAAM,EAANA,MAJiB;EAKjB4D,IAAAA,QAAQ,EAARA,QAAAA;EALiB,GAAD,CAAlB,CAAA;EAOD,CAAA;EAED,SAASW,WAAT,CAAA,KAAA,EAAA;EACEX,EAAAA,IAAAA,QAAAA,GAAAA,KAAAA,CAAAA,QAAAA;EACAY,IAAAA,MAAAA,GAAAA,KAAAA,CAAAA,MAAAA;EACApK,IAAAA,KAAAA,GAAAA,KAAAA,CAAAA,KAAAA;MACW+D,IAAX+E,GAAAA,KAAAA,CAAAA,SAAAA;EACAzC,IAAAA,cAAAA,GAAAA,KAAAA,CAAAA,cAAAA;EACAC,IAAAA,oBAAAA,GAAAA,KAAAA,CAAAA,oBAAAA,CAAAA;IAEA,IAAIvC,IAAI,KAAK,MAAb,EAAqB;MACnB,IAAIqG,MAAM,KAAK,QAAf,EAAyB;EACvB,MAAA,OAAOpD,IAAI,CAACwC,QAAD,EAAW,UAAG,GAAA,EAAA;EAAA,QAAA,OAAIjE,GAAG,IAAIvF,KAAJ,WAAIA,KAAJ,GAAa,EAAb,CAAP,CAAA;EAAA,OAAd,CAAX,CAAA;EACD,KAFD,MAEO,IAAIoK,MAAM,KAAK,KAAf,EAAsB;QAC3B,OAAOpD,IAAI,CAACwC,QAAD,EAAW,YAAA;EAAA,QAAA,OAAMxJ,KAAN,IAAA,IAAA,GAAMA,KAAN,GAAe,EAAf,CAAA;EAAA,OAAX,CAAX,CAAA;EACD,KAAA;EACF,GAND,MAMO,IAAI+D,IAAI,KAAK,OAAb,EAAsB;MAC3B,IAAIqG,MAAM,KAAK,QAAf,EAAyB;EACvB,MAAA,OAAOtC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;EAC1B,QAAA,IAAIxJ,KAAJ,EAAWuF,GAAG,CAACzI,GAAJ,CAAQkD,KAAR,CAAA,CAAA;EACZ,OAFa,CAAd,CAAA;EAGD,KAJD,MAIO,IAAIoK,MAAM,KAAK,QAAf,EAAyB;EAC9B,MAAA,OAAOtC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;EAC1B,QAAA,IAAIxJ,KAAJ,EAAWuF,GAAG,CAAA,QAAA,CAAH,CAAWvF,KAAX,CAAA,CAAA;EACZ,OAFa,CAAd,CAAA;EAGD,KAJM,MAIA,IAAIoK,MAAM,KAAK,KAAf,EAAsB;EAC3B,MAAA,OAAOtC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;EAC1BjE,QAAAA,GAAG,CAACmE,KAAJ,EAAA,CAAA;EACA,QAAA,IAAI1J,KAAJ,EAAWuF,GAAG,CAACzI,GAAJ,CAAQkD,KAAR,CAAA,CAAA;EACZ,OAHa,CAAd,CAAA;EAID,KAAA;EACF,GAfM,MAeA,IAAI+D,IAAI,KAAK,UAAb,EAAyB;EAC9B,IAAA,IAAIqG,MAAM,KAAK,KAAX,IAAoB/D,cAAxB,EAAwC;QACtC,OAAOmB,QAAQ,CAACgC,QAAD,EAAW,YAAA;UAAA,OAAO;EAC/BlD,UAAAA,oBAAoB,EAApBA,oBAD+B;EAE/BD,UAAAA,cAAc,EAAdA,cAAAA;WAFwB,CAAA;EAAA,OAAX,CAAf,CAAA;EAID,KAAA;EACF,GAPM,MAOA;MACL,IAAI+D,MAAM,KAAK,QAAf,EAAyB;EACvB,MAAA,OAAOtB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,UAAG,GAAA,EAAA;EAAA,QAAA,OAClCwB,GAAG,KAAK,IAAR,GAAeA,GAAG,IAAIvF,KAAJ,IAAA,IAAA,GAAIA,KAAJ,GAAa,EAAb,CAAlB,GAAqCA,KAArC,IAAqCA,IAAAA,GAAAA,KAArC,GAA8C,EADZ,CAAA;EAAA,OAApB,CAAhB,CAAA;EAGD,KAJD,MAIO,IAAIoK,MAAM,KAAK,KAAf,EAAsB;EAC3B,MAAA,OAAOtB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,YAAA;EAAA,QAAA,OAAM/D,KAAN,IAAA,IAAA,GAAMA,KAAN,GAAe,EAAf,CAAA;EAAA,OAAjB,CAAhB,CAAA;EACD,KAFM,MAEA,IAAIoK,MAAM,KAAK,QAAf,EAAyB;EAC9B,MAAA,OAAOtB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,YAAA;EAAA,QAAA,OAAM,IAAN,CAAA;EAAA,OAAjB,CAAhB,CAAA;EACD,KAAA;EACF,GAAA;EACD,EAAA,OAAOL,cAAP,CAAA;EACD,CAAA;EAeD,IAAe,KAAA,GAAA;EACbsD,EAAAA,IAAI,EAAJA,IADa;EAEbc,EAAAA,OAAO,EAAPA,OAFa;EAGbgB,EAAAA,SAAS,EAATA,SAHa;EAIbtB,EAAAA,QAAQ,EAARA,QAJa;EAKb2C,EAAAA,WAAW,EAAXA,WAAAA;EALa,CAAf;;ECzgBA,SAASE,UAAU,CAACC,GAAW,EAAU;IACvC,IAAIC,IAAI,GAAG,UAAU,CAAA;EACrB,EAAA,MAAMC,CAAC,GAAGF,GAAG,CAAC5B,MAAM,CAAA;IAEpB,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuJ,CAAC,EAAEvJ,CAAC,EAAE,EAAE;EAC1BsJ,IAAAA,IAAI,IAAID,GAAG,CAACG,UAAU,CAACxJ,CAAC,CAAC,CAAA;MACzBsJ,IAAI,IACF,CAACA,IAAI,IAAI,CAAC,KAAKA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,EAAE,CAAC,CAAA;EACxE,GAAA;IACA,OAAOA,IAAI,KAAK,CAAC,CAAA;EACnB,CAAA;EAEO,SAASG,IAAI,CAClBC,IAAY,EACZ3K,KAAa,EACbmB,OAAe,EACA;EACf;IACA,IAAIA,OAAO,KAAK,CAAC,EAAE;EACjB,IAAA,OAAQkJ,UAAU,CAACA,UAAU,CAACM,IAAI,GAAG3K,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,GAAI,KAAK,CAAA;EACpE,GAAA;EACA;IACA,IAAImB,OAAO,KAAK,CAAC,EAAE;MACjB,OAAQkJ,UAAU,CAACrK,KAAK,GAAG2K,IAAI,CAAC,GAAG,IAAI,GAAI,IAAI,CAAA;EACjD,GAAA;;EAEA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;EAEO,SAASC,eAAe,CAACC,CAAS,EAAY;EACnD,EAAA,IAAIA,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAA;IACrB,OAAO,IAAInN,KAAK,CAACmN,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAA;EACjC,CAAA;EAEO,SAASE,OAAO,CAACF,CAAS,EAAEG,KAAqB,EAAW;EACjE,EAAA,OAAOH,CAAC,IAAIG,KAAK,CAAC,CAAC,CAAC,IAAIH,CAAC,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAA;EACtC,CAAA;EAEO,SAASC,WAAW,CACzBC,SAAiB,EACjBC,SAAmC,EAC1B;EACT,EAAA,MAAMN,CAAC,GAAGH,IAAI,CAAC,IAAI,GAAGS,SAAS,CAAC,CAAC,CAAC,EAAED,SAAS,EAAE,CAAC,CAAC,CAAA;EACjD,EAAA,IAAIL,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,CAAA;EAC5B,EAAA,OAAOA,CAAC,IAAIM,SAAS,CAAC,CAAC,CAAC,IAAIN,CAAC,GAAGM,SAAS,CAAC,CAAC,CAAC,CAAA;EAC9C,CAAA;EAEO,SAASC,eAAe,CAACP,CAAS,EAAEQ,MAAwB,EAAU;EAC3E,EAAA,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoK,MAAM,CAAC3C,MAAM,EAAEzH,CAAC,EAAE,EAAE;MACtC,IAAI8J,OAAO,CAACF,CAAC,EAAEQ,MAAM,CAACpK,CAAC,CAAC,CAAC,EAAE;EACzB,MAAA,OAAOA,CAAC,CAAA;EACV,KAAA;EACF,GAAA;EACA,EAAA,OAAO,CAAC,CAAC,CAAA;EACX,CAAA;EAEO,SAASqK,YAAY,CAACC,WAAmB,EAAsB;IACpE,IAAI;MACF,MAAMC,OAAO,GAAGD,WAAW,CAACE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;EAC1D,IAAA,OAAO,IAAIC,MAAM,CAACF,OAAO,CAAC,CAAA;KAC3B,CAAC,OAAOjQ,CAAC,EAAE;EACVoQ,IAAAA,OAAO,CAACC,KAAK,CAACrQ,CAAC,CAAC,CAAA;EAChB,IAAA,OAAOjC,SAAS,CAAA;EAClB,GAAA;EACF,CAAA;EAEO,SAASuS,aAAa,CAACvM,GAAW,EAAEwM,OAAoB,EAAE;EAC/D,EAAA,IAAI,CAACA,OAAO,CAACpD,MAAM,EAAE,OAAO,KAAK,CAAA;IACjC,IAAIqD,eAAe,GAAG,KAAK,CAAA;IAC3B,IAAIC,UAAU,GAAG,KAAK,CAAA;EAEtB,EAAA,KAAK,IAAI/K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6K,OAAO,CAACpD,MAAM,EAAEzH,CAAC,EAAE,EAAE;EACvC,IAAA,MAAMgL,KAAK,GAAGC,cAAc,CAAC5M,GAAG,EAAEwM,OAAO,CAAC7K,CAAC,CAAC,CAAC6B,IAAI,EAAEgJ,OAAO,CAAC7K,CAAC,CAAC,CAACkL,OAAO,CAAC,CAAA;MACtE,IAAIL,OAAO,CAAC7K,CAAC,CAAC,CAACmL,OAAO,KAAK,KAAK,EAAE;QAChC,IAAIH,KAAK,EAAE,OAAO,KAAK,CAAA;EACzB,KAAC,MAAM;EACLF,MAAAA,eAAe,GAAG,IAAI,CAAA;EACtB,MAAA,IAAIE,KAAK,EAAED,UAAU,GAAG,IAAI,CAAA;EAC9B,KAAA;EACF,GAAA;IAEA,OAAOA,UAAU,IAAI,CAACD,eAAe,CAAA;EACvC,CAAA;EAEA,SAASM,kBAAkB,CACzBC,MAAc,EACdH,OAAe,EACfI,MAAe,EACN;IACT,IAAI;EACF;EACA,IAAA,IAAIf,OAAO,GAAGW,OAAO,CAClBV,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CACtCA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;EAE1B,IAAA,IAAIc,MAAM,EAAE;EACV;EACAf,MAAAA,OAAO,GAAG,MAAM,GAAGA,OAAO,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,GAAG,MAAM,CAAA;EAC/D,KAAA;EAEA,IAAA,MAAMe,KAAK,GAAG,IAAId,MAAM,CAAC,GAAG,GAAGF,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;EAClD,IAAA,OAAOgB,KAAK,CAACzC,IAAI,CAACuC,MAAM,CAAC,CAAA;KAC1B,CAAC,OAAO/Q,CAAC,EAAE;EACV,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEA,SAASkR,oBAAoB,CAACH,MAAW,EAAEH,OAAe,EAAE;IAC1D,IAAI;EACF;EACA;MACA,MAAMO,QAAQ,GAAG,IAAIC,GAAG,CACtBR,OAAO,CAACV,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,EACvE,eAAe,CAChB,CAAA;;EAED;MACA,MAAMmB,KAAuC,GAAG,CAC9C,CAACN,MAAM,CAACzS,IAAI,EAAE6S,QAAQ,CAAC7S,IAAI,EAAE,KAAK,CAAC,EACnC,CAACyS,MAAM,CAACO,QAAQ,EAAEH,QAAQ,CAACG,QAAQ,EAAE,IAAI,CAAC,CAC3C,CAAA;EACD;MACA,IAAIH,QAAQ,CAAChC,IAAI,EAAE;EACjBkC,MAAAA,KAAK,CAAC7D,IAAI,CAAC,CAACuD,MAAM,CAAC5B,IAAI,EAAEgC,QAAQ,CAAChC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;EACjD,KAAA;MAEAgC,QAAQ,CAACI,YAAY,CAAC7P,OAAO,CAAC,CAAC8P,CAAC,EAAEC,CAAC,KAAK;EACtCJ,MAAAA,KAAK,CAAC7D,IAAI,CAAC,CAACuD,MAAM,CAACQ,YAAY,CAACjQ,GAAG,CAACmQ,CAAC,CAAC,IAAI,EAAE,EAAED,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;EAC1D,KAAC,CAAC,CAAA;;EAEF;MACA,OAAO,CAACH,KAAK,CAACK,IAAI,CACf3Q,IAAI,IAAK,CAAC+P,kBAAkB,CAAC/P,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD,CAAA;KACF,CAAC,OAAOf,CAAC,EAAE;EACV,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEA,SAAS2Q,cAAc,CACrB5M,GAAW,EACXwD,IAAmB,EACnBqJ,OAAe,EACN;IACT,IAAI;MACF,MAAMjM,MAAM,GAAG,IAAIyM,GAAG,CAACrN,GAAG,EAAE,WAAW,CAAC,CAAA;MAExC,IAAIwD,IAAI,KAAK,OAAO,EAAE;EACpB,MAAA,MAAM0J,KAAK,GAAGlB,YAAY,CAACa,OAAO,CAAC,CAAA;EACnC,MAAA,IAAI,CAACK,KAAK,EAAE,OAAO,KAAK,CAAA;QACxB,OACEA,KAAK,CAACzC,IAAI,CAAC7J,MAAM,CAACgN,IAAI,CAAC,IACvBV,KAAK,CAACzC,IAAI,CAAC7J,MAAM,CAACgN,IAAI,CAACC,SAAS,CAACjN,MAAM,CAACkN,MAAM,CAAC1E,MAAM,CAAC,CAAC,CAAA;EAE3D,KAAC,MAAM,IAAI5F,IAAI,KAAK,QAAQ,EAAE;EAC5B,MAAA,OAAO2J,oBAAoB,CAACvM,MAAM,EAAEiM,OAAO,CAAC,CAAA;EAC9C,KAAA;EAEA,IAAA,OAAO,KAAK,CAAA;KACb,CAAC,OAAO5Q,CAAC,EAAE;EACV,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEO,SAAS8R,eAAe,CAC7BC,aAAqB,EACrBC,QAA4B,EAC5BC,OAAkB,EACA;EAClBD,EAAAA,QAAQ,GAAGA,QAAQ,KAAKjU,SAAS,GAAG,CAAC,GAAGiU,QAAQ,CAAA;;EAEhD;IACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;EAIhBA,IAAAA,QAAQ,GAAG,CAAC,CAAA;EACd,GAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;EAIvBA,IAAAA,QAAQ,GAAG,CAAC,CAAA;EACd,GAAA;;EAEA;EACA,EAAA,MAAME,KAAK,GAAG7C,eAAe,CAAC0C,aAAa,CAAC,CAAA;IAC5CE,OAAO,GAAGA,OAAO,IAAIC,KAAK,CAAA;EAC1B,EAAA,IAAID,OAAO,CAAC9E,MAAM,KAAK4E,aAAa,EAAE;EAMpCE,IAAAA,OAAO,GAAGC,KAAK,CAAA;EACjB,GAAA;;EAEA;EACA,EAAA,MAAMC,WAAW,GAAGF,OAAO,CAACG,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKA,GAAG,GAAGD,CAAC,EAAE,CAAC,CAAC,CAAA;EAC1D,EAAA,IAAIF,WAAW,GAAG,IAAI,IAAIA,WAAW,GAAG,IAAI,EAAE;EAI5CF,IAAAA,OAAO,GAAGC,KAAK,CAAA;EACjB,GAAA;;EAEA;IACA,IAAIK,UAAU,GAAG,CAAC,CAAA;EAClB,EAAA,OAAON,OAAO,CAAChN,GAAG,CAAEoN,CAAC,IAAK;MACxB,MAAMG,KAAK,GAAGD,UAAU,CAAA;EACxBA,IAAAA,UAAU,IAAIF,CAAC,CAAA;MACf,OAAO,CAACG,KAAK,EAAEA,KAAK,GAAIR,QAAQ,GAAcK,CAAC,CAAC,CAAA;EAClD,GAAC,CAAC,CAAA;EACJ,CAAA;EAEO,SAASI,sBAAsB,CACpCC,EAAU,EACV3O,GAAW,EACXgO,aAAqB,EACrB;IACA,IAAI,CAAChO,GAAG,EAAE;EACR,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,MAAM4O,MAAM,GAAG5O,GAAG,CAACyG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAChC,IAAI,CAACmI,MAAM,EAAE;EACX,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,MAAMjC,KAAK,GAAGiC,MAAM,CACjBzC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAAC,GACnB1F,KAAK,CAAC,GAAG,CAAC;EAAC,GACXvF,GAAG,CAAE2N,EAAE,IAAKA,EAAE,CAACpI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAC7BC,MAAM,CAAC,IAAA,IAAA;MAAA,IAAC,CAACgH,CAAC,CAAC,GAAA,IAAA,CAAA;MAAA,OAAKA,CAAC,KAAKiB,EAAE,CAAA;EAAA,GAAA,CAAC;EAAC,GAC1BzN,GAAG,CAAC,KAAA,IAAA;MAAA,IAAC,GAAGuM,CAAC,CAAC,GAAA,KAAA,CAAA;MAAA,OAAKqB,QAAQ,CAACrB,CAAC,CAAC,CAAA;EAAA,GAAA,CAAC,CAAC;;IAE/B,IAAId,KAAK,CAACvD,MAAM,GAAG,CAAC,IAAIuD,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGqB,aAAa,EAC/D,OAAOrB,KAAK,CAAC,CAAC,CAAC,CAAA;EAEjB,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;EAEO,SAASD,UAAU,CAACI,OAAsB,EAAE;IACjD,IAAI;EACF,IAAA,OAAOA,OAAO,EAAE,CAAA;KACjB,CAAC,OAAO7Q,CAAC,EAAE;EACVoQ,IAAAA,OAAO,CAACC,KAAK,CAACrQ,CAAC,CAAC,CAAA;EAChB,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEA,MAAM8S,WAAW,GAAI1N,CAAS,IAC5B2N,UAAU,CAAC3Q,IAAI,CAAC4Q,IAAI,CAAC5N,CAAC,CAAC,EAAGuJ,CAAC,IAAKA,CAAC,CAACO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;EAE3C,eAAe+D,OAAO,CAC3BC,eAAuB,EACvBC,aAAsB,EACtBjV,MAAqB,EACJ;IACjBiV,aAAa,GAAGA,aAAa,IAAI,EAAE,CAAA;IACnCjV,MAAM,GAAGA,MAAM,IAAKL,UAAU,CAACI,MAAM,IAAIJ,UAAU,CAACI,MAAM,CAACC,MAAO,CAAA;IAClE,IAAI,CAACA,MAAM,EAAE;EACX,IAAA,MAAM,IAAIkV,KAAK,CAAC,sCAAsC,CAAC,CAAA;EACzD,GAAA;IACA,IAAI;EACF,IAAA,MAAMjS,GAAG,GAAG,MAAMjD,MAAM,CAACmV,SAAS,CAChC,KAAK,EACLP,WAAW,CAACK,aAAa,CAAC,EAC1B;EAAEG,MAAAA,IAAI,EAAE,SAAS;EAAEnG,MAAAA,MAAM,EAAE,GAAA;OAAK,EAChC,IAAI,EACJ,CAAC,SAAS,EAAE,SAAS,CAAC,CACvB,CAAA;MACD,MAAM,CAACoG,EAAE,EAAEC,UAAU,CAAC,GAAGN,eAAe,CAAC1I,KAAK,CAAC,GAAG,CAAC,CAAA;EACnD,IAAA,MAAMiJ,eAAe,GAAG,MAAMvV,MAAM,CAAC+U,OAAO,CAC1C;EAAEK,MAAAA,IAAI,EAAE,SAAS;QAAEC,EAAE,EAAET,WAAW,CAACS,EAAE,CAAA;EAAE,KAAC,EACxCpS,GAAG,EACH2R,WAAW,CAACU,UAAU,CAAC,CACxB,CAAA;EAED,IAAA,OAAO,IAAIE,WAAW,EAAE,CAACC,MAAM,CAACF,eAAe,CAAC,CAAA;KACjD,CAAC,OAAOzT,CAAC,EAAE;EACV,IAAA,MAAM,IAAIoT,KAAK,CAAC,mBAAmB,CAAC,CAAA;EACtC,GAAA;EACF,CAAA;;EAEA;EACO,SAASQ,QAAQ,CAACC,KAAU,EAAU;EAC3C,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK,CAAA;EAC3C,EAAA,OAAO/U,IAAI,CAACC,SAAS,CAAC8U,KAAK,CAAC,CAAA;EAC9B,CAAA;;EAEA;EACO,SAASC,mBAAmB,CAACD,KAAU,EAAU;EACtD,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGA,KAAK,GAAG,EAAE,CAAA;EACpB,GAAA;EACA,EAAA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;EACvCA,IAAAA,KAAK,GAAG,GAAG,CAAA;EACb,GAAA;EACA;EACA;EACA;EACA,EAAA,MAAME,KAAK,GAAIF,KAAK,CAAY3D,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC1F,KAAK,CAAC,MAAM,CAAC,CAAA;;EAExE;EACA;EACA;EACA,EAAA,IAAIuJ,KAAK,CAAC5G,MAAM,KAAK,CAAC,EAAE;EACtB4G,IAAAA,KAAK,CAACvG,IAAI,CAAC,GAAG,CAAC,CAAA;EACjB,GAAA;;EAEA;EACA;EACA,EAAA,OAAOuG,KAAK,CACT9O,GAAG,CAAEuM,CAAC,IAAMA,CAAC,CAACd,KAAK,CAAC,UAAU,CAAC,GAAGc,CAAC,CAACwC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGxC,CAAE,CAAC,CAC1D7G,IAAI,CAAC,GAAG,CAAC,CAAA;EACd,CAAA;EAEO,SAASsJ,cAAc,GAAW;EACvC,EAAA,IAAIrO,OAAe,CAAA;IACnB,IAAI;EACF;EACAA,IAAAA,OAAO,GAAG,QAAe,CAAA;KAC1B,CAAC,OAAO5F,CAAC,EAAE;EACV4F,IAAAA,OAAO,GAAG,EAAE,CAAA;EACd,GAAA;EACA,EAAA,OAAOA,OAAO,CAAA;EAChB;;ECzUA;EAYA,MAAMsO,WAAsC,GAAG,EAAE,CAAA;;EAEjD;EACO,SAASC,aAAa,CAC3BC,GAAc,EACdC,SAA6B,EACpB;EACT;IACA,IAAI,KAAK,IAAIA,SAAS,EAAE;MACtB,OAAOC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,KAAK,CAAC,CAAyB,CAAA;EAC9D,GAAA;IACA,IAAI,MAAM,IAAIA,SAAS,EAAE;MACvB,OAAO,CAACC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB,CAAA;EAChE,GAAA;IACA,IAAI,MAAM,IAAIA,SAAS,EAAE;MACvB,OAAOE,OAAO,CAACH,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB,CAAA;EAChE,GAAA;IACA,IAAI,MAAM,IAAIA,SAAS,EAAE;MACvB,OAAO,CAACF,aAAa,CAACC,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAuB,CAAA;EACrE,GAAA;;EAEA;EACA,EAAA,KAAK,MAAM,CAAC5C,CAAC,EAAED,CAAC,CAAC,IAAI9N,MAAM,CAACrB,OAAO,CAACgS,SAAS,CAAC,EAAE;EAC9C,IAAA,IAAI,CAACG,kBAAkB,CAAChD,CAAC,EAAEiD,OAAO,CAACL,GAAG,EAAE3C,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAA;EAC3D,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;;EAEA;EACA,SAASgD,OAAO,CAACL,GAAc,EAAEM,IAAY,EAAE;EAC7C,EAAA,MAAMX,KAAK,GAAGW,IAAI,CAAClK,KAAK,CAAC,GAAG,CAAC,CAAA;IAC7B,IAAImK,OAAY,GAAGP,GAAG,CAAA;EACtB,EAAA,KAAK,IAAI1O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,KAAK,CAAC5G,MAAM,EAAEzH,CAAC,EAAE,EAAE;EACrC,IAAA,IAAIiP,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIZ,KAAK,CAACrO,CAAC,CAAC,IAAIiP,OAAO,EAAE;EACjEA,MAAAA,OAAO,GAAGA,OAAO,CAACZ,KAAK,CAACrO,CAAC,CAAC,CAAC,CAAA;EAC7B,KAAC,MAAM;EACL,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACF,GAAA;EACA,EAAA,OAAOiP,OAAO,CAAA;EAChB,CAAA;;EAEA;EACA,SAASC,QAAQ,CAAC3D,KAAa,EAAU;EACvC,EAAA,IAAI,CAACiD,WAAW,CAACjD,KAAK,CAAC,EAAE;EACvBiD,IAAAA,WAAW,CAACjD,KAAK,CAAC,GAAG,IAAId,MAAM,CAACc,KAAK,CAACf,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAA;EACvE,GAAA;IACA,OAAOgE,WAAW,CAACjD,KAAK,CAAC,CAAA;EAC3B,CAAA;;EAEA;EACA,SAASuD,kBAAkB,CAACH,SAAyB,EAAE5P,KAAU,EAAE;EACjE;EACA,EAAA,IAAI,OAAO4P,SAAS,KAAK,QAAQ,EAAE;EACjC,IAAA,OAAO5P,KAAK,GAAG,EAAE,KAAK4P,SAAS,CAAA;EACjC,GAAA;EACA,EAAA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;EACjC,IAAA,OAAO5P,KAAK,GAAG,CAAC,KAAK4P,SAAS,CAAA;EAChC,GAAA;EACA,EAAA,IAAI,OAAOA,SAAS,KAAK,SAAS,EAAE;EAClC,IAAA,OAAO,CAAC,CAAC5P,KAAK,KAAK4P,SAAS,CAAA;EAC9B,GAAA;IAEA,IAAIA,SAAS,KAAK,IAAI,EAAE;MACtB,OAAO5P,KAAK,KAAK,IAAI,CAAA;EACvB,GAAA;EAEA,EAAA,IAAItC,KAAK,CAAC0C,OAAO,CAACwP,SAAS,CAAC,IAAI,CAACQ,gBAAgB,CAACR,SAAS,CAAC,EAAE;EAC5D,IAAA,OAAOvV,IAAI,CAACC,SAAS,CAAC0F,KAAK,CAAC,KAAK3F,IAAI,CAACC,SAAS,CAACsV,SAAS,CAAC,CAAA;EAC5D,GAAA;;EAEA;EACA,EAAA,KAAK,MAAMS,EAAE,IAAIT,SAAS,EAAE;EAC1B,IAAA,IACE,CAACU,qBAAqB,CACpBD,EAAE,EACFrQ,KAAK,EACL4P,SAAS,CAACS,EAAE,CAAiC,CAC9C,EACD;EACA,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EACF,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;;EAEA;EACA,SAASD,gBAAgB,CAACT,GAAQ,EAAW;EAC3C,EAAA,MAAMzQ,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACyQ,GAAG,CAAC,CAAA;IAC7B,OACEzQ,IAAI,CAACwJ,MAAM,GAAG,CAAC,IAAIxJ,IAAI,CAAC8G,MAAM,CAAEgH,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAACtE,MAAM,KAAKxJ,IAAI,CAACwJ,MAAM,CAAA;EAE9E,CAAA;;EAEA;EACA,SAAS6H,OAAO,CAACxD,CAAM,EAAuB;EAC5C,EAAA,IAAIA,CAAC,KAAK,IAAI,EAAE,OAAO,MAAM,CAAA;IAC7B,IAAIrP,KAAK,CAAC0C,OAAO,CAAC2M,CAAC,CAAC,EAAE,OAAO,OAAO,CAAA;IACpC,MAAMyD,CAAC,GAAG,OAAOzD,CAAC,CAAA;EAClB,EAAA,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC1J,QAAQ,CAACmN,CAAC,CAAC,EAAE;EACtE,IAAA,OAAOA,CAAC,CAAA;EACV,GAAA;EACA,EAAA,OAAO,SAAS,CAAA;EAClB,CAAA;;EAEA;EACA,SAASC,SAAS,CAACnE,MAAW,EAAEI,QAAa,EAAE;IAC7C,IAAI,CAAChP,KAAK,CAAC0C,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;IACxC,MAAMoE,KAAK,GAAGN,gBAAgB,CAAC1D,QAAQ,CAAC,GACnCK,CAAM,IAAKgD,kBAAkB,CAACrD,QAAQ,EAAEK,CAAC,CAAC,GAC1CA,CAAM,IAAK2C,aAAa,CAAC3C,CAAC,EAAEL,QAAQ,CAAC,CAAA;EAC1C,EAAA,KAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqL,MAAM,CAAC5D,MAAM,EAAEzH,CAAC,EAAE,EAAE;EACtC,IAAA,IAAIqL,MAAM,CAACrL,CAAC,CAAC,IAAIyP,KAAK,CAACpE,MAAM,CAACrL,CAAC,CAAC,CAAC,EAAE;EACjC,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACF,GAAA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAA;EAEA,SAAS0P,IAAI,CAACrE,MAAW,EAAEI,QAAoB,EAAW;EACxD;EACA,EAAA,IAAIhP,KAAK,CAAC0C,OAAO,CAACkM,MAAM,CAAC,EAAE;EACzB,IAAA,OAAOA,MAAM,CAACW,IAAI,CAAEzI,EAAE,IAAKkI,QAAQ,CAACrJ,QAAQ,CAACmB,EAAE,CAAC,CAAC,CAAA;EACnD,GAAA;EACA,EAAA,OAAOkI,QAAQ,CAACrJ,QAAQ,CAACiJ,MAAM,CAAC,CAAA;EAClC,CAAA;;EAEA;EACA,SAASgE,qBAAqB,CAC5BM,QAAkB,EAClBtE,MAAW,EACXI,QAAa,EACJ;EACT,EAAA,QAAQkE,QAAQ;EACd,IAAA,KAAK,MAAM;QACT,OAAOvB,mBAAmB,CAAC/C,MAAM,CAAC,KAAK+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACtE,IAAA,KAAK,MAAM;QACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,KAAK+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACtE,IAAA,KAAK,MAAM;QACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,GAAG+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACpE,IAAA,KAAK,OAAO;QACV,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,IAAI+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACrE,IAAA,KAAK,MAAM;QACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,GAAG+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACpE,IAAA,KAAK,OAAO;QACV,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,IAAI+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACrE,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,KAAKI,QAAQ,CAAA;EAC5B,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,KAAKI,QAAQ,CAAA;EAC5B,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,GAAGI,QAAQ,CAAA;EAC1B,IAAA,KAAK,MAAM;QACT,OAAOJ,MAAM,IAAII,QAAQ,CAAA;EAC3B,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,GAAGI,QAAQ,CAAA;EAC1B,IAAA,KAAK,MAAM;QACT,OAAOJ,MAAM,IAAII,QAAQ,CAAA;EAC3B,IAAA,KAAK,SAAS;EACZ;QACA,OAAOA,QAAQ,GAAGJ,MAAM,IAAI,IAAI,GAAGA,MAAM,IAAI,IAAI,CAAA;EACnD,IAAA,KAAK,KAAK;QACR,IAAI,CAAC5O,KAAK,CAAC0C,OAAO,CAACsM,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAA;EAC1C,MAAA,OAAOiE,IAAI,CAACrE,MAAM,EAAEI,QAAQ,CAAC,CAAA;EAC/B,IAAA,KAAK,MAAM;QACT,IAAI,CAAChP,KAAK,CAAC0C,OAAO,CAACsM,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAA;EAC1C,MAAA,OAAO,CAACiE,IAAI,CAACrE,MAAM,EAAEI,QAAQ,CAAC,CAAA;EAChC,IAAA,KAAK,MAAM;EACT,MAAA,OAAO,CAACqD,kBAAkB,CAACrD,QAAQ,EAAEJ,MAAM,CAAC,CAAA;EAC9C,IAAA,KAAK,OAAO;QACV,IAAI,CAAC5O,KAAK,CAAC0C,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;EACxC,MAAA,OAAOyD,kBAAkB,CAACrD,QAAQ,EAAEJ,MAAM,CAAC5D,MAAM,CAAC,CAAA;EACpD,IAAA,KAAK,YAAY;EACf,MAAA,OAAO+H,SAAS,CAACnE,MAAM,EAAEI,QAAQ,CAAC,CAAA;EACpC,IAAA,KAAK,MAAM;QACT,IAAI,CAAChP,KAAK,CAAC0C,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;EACxC,MAAA,KAAK,IAAIrL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyL,QAAQ,CAAChE,MAAM,EAAEzH,CAAC,EAAE,EAAE;UACxC,IAAI4P,MAAM,GAAG,KAAK,CAAA;EAClB,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxE,MAAM,CAAC5D,MAAM,EAAEoI,CAAC,EAAE,EAAE;EACtC,UAAA,IAAIf,kBAAkB,CAACrD,QAAQ,CAACzL,CAAC,CAAC,EAAEqL,MAAM,CAACwE,CAAC,CAAC,CAAC,EAAE;EAC9CD,YAAAA,MAAM,GAAG,IAAI,CAAA;EACb,YAAA,MAAA;EACF,WAAA;EACF,SAAA;EACA,QAAA,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK,CAAA;EAC3B,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACb,IAAA,KAAK,QAAQ;QACX,IAAI;UACF,OAAOV,QAAQ,CAACzD,QAAQ,CAAC,CAAC3C,IAAI,CAACuC,MAAM,CAAC,CAAA;SACvC,CAAC,OAAO/Q,CAAC,EAAE;EACV,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;EACF,IAAA,KAAK,OAAO;EACV,MAAA,OAAOgV,OAAO,CAACjE,MAAM,CAAC,KAAKI,QAAQ,CAAA;EACrC,IAAA;EACEf,MAAAA,OAAO,CAACC,KAAK,CAAC,oBAAoB,GAAGgF,QAAQ,CAAC,CAAA;EAC9C,MAAA,OAAO,KAAK,CAAA;EAAC,GAAA;EAEnB,CAAA;;EAEA;EACA,SAASf,MAAM,CAACF,GAAc,EAAEoB,UAAgC,EAAW;EACzE,EAAA,IAAI,CAACA,UAAU,CAACrI,MAAM,EAAE,OAAO,IAAI,CAAA;EACnC,EAAA,KAAK,IAAIzH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,UAAU,CAACrI,MAAM,EAAEzH,CAAC,EAAE,EAAE;MAC1C,IAAIyO,aAAa,CAACC,GAAG,EAAEoB,UAAU,CAAC9P,CAAC,CAAC,CAAC,EAAE;EACrC,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACF,GAAA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAA;;EAEA;EACA,SAAS6O,OAAO,CAACH,GAAc,EAAEoB,UAAgC,EAAW;EAC1E,EAAA,KAAK,IAAI9P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,UAAU,CAACrI,MAAM,EAAEzH,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACyO,aAAa,CAACC,GAAG,EAAEoB,UAAU,CAAC9P,CAAC,CAAC,CAAC,EAAE;EACtC,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EACF,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb;;EC3LA,MAAMvG,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,CAAA;EAElE,MAAMoW,WAAW,GAAGxB,cAAc,EAAE,CAAA;EAE7B,MAAMyB,UAAU,CAGrB;EACA;EACA;;EAMA;;EAiBA;;IAUAC,WAAW,CAACC,OAAiB,EAAE;EAC7BA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvB;EACA;MACA,IAAI,CAAChQ,OAAO,GAAG6P,WAAW,CAAA;EAC1B,IAAA,IAAI,CAACI,IAAI,GAAG,IAAI,CAACD,OAAO,GAAGA,OAAO,CAAA;MAClC,IAAI,CAACE,SAAS,GAAG,IAAI,CAAA;EACrB,IAAA,IAAI,CAACC,mBAAmB,GAAG,IAAIvV,GAAG,EAAE,CAAA;EACpC,IAAA,IAAI,CAACwV,gBAAgB,GAAG,EAAE,CAAA;MAC1B,IAAI,CAACC,KAAK,GAAG,KAAK,CAAA;EAClB,IAAA,IAAI,CAACC,cAAc,GAAG,IAAI1V,GAAG,EAAE,CAAA;MAC/B,IAAI,CAAC2V,QAAQ,GAAG,EAAE,CAAA;MAClB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAA;MACjB,IAAI,CAACC,KAAK,GAAG,KAAK,CAAA;EAClB,IAAA,IAAI,CAACC,SAAS,GAAG,IAAIpW,GAAG,EAAE,CAAA;EAC1B,IAAA,IAAI,CAACqW,oBAAoB,GAAG,IAAIrW,GAAG,EAAE,CAAA;EACrC,IAAA,IAAI,CAACsW,mBAAmB,GAAG,EAAE,CAAA;EAC7B,IAAA,IAAI,CAACC,sBAAsB,GAAG,IAAIvW,GAAG,EAAE,CAAA;EACvC,IAAA,IAAI,CAACwW,iBAAiB,GAAG,IAAIlW,GAAG,EAAE,CAAA;MAClC,IAAI,CAACmW,mBAAmB,GAAG,KAAK,CAAA;MAEhC,IAAIf,OAAO,CAACjP,UAAU,EAAE;QACtB,IAAIiP,OAAO,CAACzC,aAAa,EAAE;EACzB,QAAA,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC,CAAA;EAC/D,OAAA;EACA,MAAA,IAAI,CAACwC,OAAO,CAACrX,SAAS,EAAE;EACtB,QAAA,MAAM,IAAI6U,KAAK,CAAC,mBAAmB,CAAC,CAAA;EACtC,OAAA;QACA,IAAIwD,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI;EACFA,QAAAA,QAAQ,GAAG,CAAC,CAAC,IAAIxF,GAAG,CAACwE,OAAO,CAAC1S,OAAO,IAAI,EAAE,CAAC,CAAC2T,QAAQ,CAACnG,KAAK,CACxD,kBAAkB,CACnB,CAAA;SACF,CAAC,OAAO1Q,CAAC,EAAE;EACV;EAAA,OAAA;EAEF,MAAA,IAAI4W,QAAQ,EAAE;EACZ,QAAA,MAAM,IAAIxD,KAAK,CAAC,2CAA2C,CAAC,CAAA;EAC9D,OAAA;EACF,KAAC,MAAM;QACL,IAAIwC,OAAO,CAACpS,kBAAkB,EAAE;EAC9B,QAAA,MAAM,IAAI4P,KAAK,CAAC,iDAAiD,CAAC,CAAA;EACpE,OAAA;EACF,KAAA;MAEA,IAAIwC,OAAO,CAACtP,QAAQ,EAAE;QACpB,IAAI,CAAC+P,KAAK,GAAG,IAAI,CAAA;EACnB,KAAA;EAEA,IAAA,IAAIlX,SAAS,IAAIyW,OAAO,CAACkB,aAAa,EAAE;QACtC1X,MAAM,CAAC2X,WAAW,GAAG,IAAI,CAAA;QACzB1X,QAAQ,CAAC2X,aAAa,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;EAC/C,KAAA;MAEA,IAAIrB,OAAO,CAACzP,WAAW,EAAE;QACvB,IAAI,CAACkQ,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAACa,yBAAyB,EAAE,CAAA;EAClC,KAAA;MAEA,IAAItB,OAAO,CAACrX,SAAS,IAAI,CAACqX,OAAO,CAACjP,UAAU,EAAE;QAC5C,IAAI,CAACwQ,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;EAChC,KAAA;EACF,GAAA;IAEA,MAAaC,YAAY,CAACzY,OAA6B,EAAiB;EACtE,IAAA,IAAIA,OAAO,IAAIA,OAAO,CAAC0Y,WAAW,EAAE;EAClC;EACA,MAAA,IAAI,CAACxB,IAAI,CAACyB,kBAAkB,GAAG,IAAI,CAAA;EACrC,KAAA;MACA,IAAI,CAACX,mBAAmB,GAAG,IAAI,CAAA;MAE/B,MAAM,IAAI,CAACQ,QAAQ,CAACxY,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;EAExC,IAAA,IAAI,IAAI,CAAC4Y,aAAa,EAAE,EAAE;QACxBrW,SAAS,CAAC,IAAI,CAAC,CAAA;EACjB,KAAA;EACF,GAAA;IAEA,MAAaT,eAAe,CAC1B9B,OAAgC,EACjB;MACf,MAAM,IAAI,CAACwY,QAAQ,CAACxY,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;EAC3C,GAAA;EAEOwE,EAAAA,UAAU,GAAyB;EACxC,IAAA,OAAO,CAAC,IAAI,CAACsD,WAAW,EAAE,CAACvD,OAAO,EAAE,IAAI,CAACwD,YAAY,EAAE,CAAC,CAAA;EAC1D,GAAA;EACOD,EAAAA,WAAW,GAKhB;MACA,MAAM+Q,WAAW,GAAG,IAAI,CAAC3B,IAAI,CAAC3S,OAAO,IAAI,2BAA2B,CAAA;MACpE,OAAO;QACLA,OAAO,EAAEsU,WAAW,CAACtH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EACxChJ,MAAAA,aAAa,EAAE,CAAC,IAAI,CAAC2O,IAAI,CAAC3O,aAAa,IAAIsQ,WAAW,EAAEtH,OAAO,CAC7D,MAAM,EACN,EAAE,CACH;EACD1J,MAAAA,iBAAiB,EAAE,IAAI,CAACqP,IAAI,CAAC4B,qBAAqB;EAClDtQ,MAAAA,2BAA2B,EAAE,IAAI,CAAC0O,IAAI,CAAC1O,2BAAAA;OACxC,CAAA;EACH,GAAA;EACOT,EAAAA,YAAY,GAAW;EAC5B,IAAA,OAAO,IAAI,CAACmP,IAAI,CAACtX,SAAS,IAAI,EAAE,CAAA;EAClC,GAAA;EAEO8E,EAAAA,YAAY,GAAY;EAC7B,IAAA,OAAO,IAAI,CAACwS,IAAI,CAAClP,UAAU,IAAI,KAAK,CAAA;EACtC,GAAA;EAEOlD,EAAAA,qBAAqB,GAAqC;EAC/D,IAAA,OAAO,IAAI,CAACoS,IAAI,CAACrS,kBAAkB,CAAA;EACrC,GAAA;EAEA,EAAA,MAAc2T,QAAQ,CACpBxY,OAAgC,EAChCkC,UAAoB,EACpBC,cAAwB,EACxB;EACAnC,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvB,IAAA,IAAI,CAAC,IAAI,CAACkX,IAAI,CAACtX,SAAS,EAAE;EACxB,MAAA,MAAM,IAAI6U,KAAK,CAAC,mBAAmB,CAAC,CAAA;EACtC,KAAA;EACA,IAAA,MAAM3S,eAAe,CACnB,IAAI,EACJ9B,OAAO,CAACgC,OAAO,EACfhC,OAAO,CAACiC,SAAS,IAAI,IAAI,CAACiV,IAAI,CAACiB,aAAa,EAC5CjW,UAAU,EACVC,cAAc,EACd,IAAI,CAAC+U,IAAI,CAACtY,cAAc,KAAK,KAAK,CACnC,CAAA;EACH,GAAA;EAEQma,EAAAA,OAAO,GAAG;MAChB,IAAI,IAAI,CAAC5B,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,EAAE,CAAA;EAClB,KAAA;EACF,GAAA;IAEOzP,WAAW,CAACC,QAA2C,EAAE;EAC9D,IAAA,IAAI,CAACuP,IAAI,CAACvP,QAAQ,GAAGA,QAAQ,CAAA;MAC7B,IAAI,CAAC+P,KAAK,GAAG,IAAI,CAAA;MACjB,IAAI,CAACqB,OAAO,EAAE,CAAA;EAChB,GAAA;EAEA,EAAA,MAAaC,oBAAoB,CAC/BzE,eAAuB,EACvBC,aAAsB,EACtBjV,MAAqB,EACN;EACf,IAAA,MAAM0Z,YAAY,GAAG,MAAM3E,OAAO,CAChCC,eAAe,EACfC,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCjV,MAAM,CACP,CAAA;MACD,IAAI,CAACmI,WAAW,CACdvH,IAAI,CAAC8F,KAAK,CAACgT,YAAY,CAAC,CACzB,CAAA;EACH,GAAA;IAEO1R,cAAc,CAACC,WAA6B,EAAQ;EACzD,IAAA,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,GAAGA,WAAW,CAAA;MACnC,IAAI,CAACkQ,KAAK,GAAG,IAAI,CAAA;MACjB,IAAI,CAACa,yBAAyB,EAAE,CAAA;EAClC,GAAA;EAEA,EAAA,MAAaW,uBAAuB,CAClC3E,eAAuB,EACvBC,aAAsB,EACtBjV,MAAqB,EACN;EACf,IAAA,MAAM4Z,eAAe,GAAG,MAAM7E,OAAO,CACnCC,eAAe,EACfC,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCjV,MAAM,CACP,CAAA;MACD,IAAI,CAACgI,cAAc,CAACpH,IAAI,CAAC8F,KAAK,CAACkT,eAAe,CAAC,CAAqB,CAAA;EACtE,GAAA;EAEA,EAAA,MAAa9R,cAAc,CACzBjF,IAAwB,EACxBoS,aAAsB,EACtBjV,MAAqB,EACQ;MAC7B,IAAI6C,IAAI,CAACgX,iBAAiB,EAAE;QAC1BhX,IAAI,CAACuF,QAAQ,GAAGxH,IAAI,CAAC8F,KAAK,CACxB,MAAMqO,OAAO,CACXlS,IAAI,CAACgX,iBAAiB,EACtB5E,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCjV,MAAM,CACP,CACF,CAAA;QACD,OAAO6C,IAAI,CAACgX,iBAAiB,CAAA;EAC/B,KAAA;MACA,IAAIhX,IAAI,CAACiX,oBAAoB,EAAE;QAC7BjX,IAAI,CAACoF,WAAW,GAAGrH,IAAI,CAAC8F,KAAK,CAC3B,MAAMqO,OAAO,CACXlS,IAAI,CAACiX,oBAAoB,EACzB7E,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCjV,MAAM,CACP,CACF,CAAA;QACD,OAAO6C,IAAI,CAACiX,oBAAoB,CAAA;EAClC,KAAA;EACA,IAAA,OAAOjX,IAAI,CAAA;EACb,GAAA;IAEA,MAAakX,aAAa,CAAC3U,UAAsB,EAAE;EACjD,IAAA,IAAI,CAACuS,IAAI,CAACvS,UAAU,GAAGA,UAAU,CAAA;EACjC,IAAA,IAAI,IAAI,CAACuS,IAAI,CAACqC,mBAAmB,EAAE;QACjC,MAAM,IAAI,CAACjS,oBAAoB,EAAE,CAAA;EACnC,KAAA;EACA,IAAA,IAAI,IAAI,CAAC4P,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACT,OAAO,EAAE,CAAA;MACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;EAClC,GAAA;IAEA,MAAakB,qBAAqB,CAACC,SAAqB,EAAE;MACxD,IAAI,CAAC7B,mBAAmB,GAAG6B,SAAS,CAAA;EACpC,IAAA,IAAI,IAAI,CAACxC,IAAI,CAACqC,mBAAmB,EAAE;QACjC,MAAM,IAAI,CAACjS,oBAAoB,EAAE,CAAA;EACnC,KAAA;EACA,IAAA,IAAI,IAAI,CAAC4P,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACT,OAAO,EAAE,CAAA;MACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;EAClC,GAAA;IAEA,MAAaoB,mBAAmB,CAACC,IAA4B,EAAE;MAC7D,IAAI,CAAC1C,IAAI,CAAChP,gBAAgB,GAAG0R,IAAI,IAAI,EAAE,CAAA;EACvC,IAAA,IAAI,IAAI,CAAC1C,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACT,OAAO,EAAE,CAAA;MACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;EAClC,GAAA;;EAEA;IACOsB,iBAAiB,CAACvT,GAAqB,EAAE;MAC9C,IAAI,CAACsR,oBAAoB,GAAGtR,GAAG,CAAA;MAC/B,IAAI,CAACyS,OAAO,EAAE,CAAA;EAChB,GAAA;IAEA,MAAae,MAAM,CAAC1U,GAAW,EAAE;EAC/B,IAAA,IAAI,CAAC8R,IAAI,CAAC9R,GAAG,GAAGA,GAAG,CAAA;EACnB,IAAA,IAAI,IAAI,CAAC8R,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,IAAI,CAACjB,yBAAyB,CAAC,IAAI,CAAC,CAAA;EACpC,MAAA,OAAA;EACF,KAAA;EACA,IAAA,IAAI,CAACA,yBAAyB,CAAC,IAAI,CAAC,CAAA;EACtC,GAAA;EAEO3T,EAAAA,aAAa,GAAG;MACrB,OAAO;EAAE,MAAA,GAAG,IAAI,CAACsS,IAAI,CAACvS,UAAU;EAAE,MAAA,GAAG,IAAI,CAACkT,mBAAAA;OAAqB,CAAA;EACjE,GAAA;EAEO1S,EAAAA,mBAAmB,GAAG;EAC3B,IAAA,OAAO,IAAI,CAAC+R,IAAI,CAAChP,gBAAgB,IAAI,EAAE,CAAA;EACzC,GAAA;EAEOE,EAAAA,iBAAiB,GAAG;EACzB;EACA,IAAA,OAAO,IAAI,CAACwP,oBAAoB,IAAI,IAAIrW,GAAG,EAAe,CAAA;EAC5D,GAAA;EAEOwY,EAAAA,6BAA6B,GAAG;EACrC,IAAA,OAAO,IAAI,CAAC7C,IAAI,CAAC8C,0BAA0B,IAAI,EAAE,CAAA;EACnD,GAAA;EAEO3U,EAAAA,MAAM,GAAG;EACd,IAAA,OAAO,IAAI,CAAC6R,IAAI,CAAC9R,GAAG,IAAI,EAAE,CAAA;EAC5B,GAAA;EAEOwC,EAAAA,WAAW,GAAG;EACnB,IAAA,OAAO,IAAI,CAACsP,IAAI,CAACvP,QAAQ,IAAI,EAAE,CAAA;EACjC,GAAA;EAEOF,EAAAA,cAAc,GAAG;EACtB,IAAA,OAAO,IAAI,CAACyP,IAAI,CAAC1P,WAAW,IAAI,EAAE,CAAA;EACpC,GAAA;IAEOjF,SAAS,CAACmG,EAAwB,EAAc;EACrD,IAAA,IAAI,CAAC6O,cAAc,CAAC3U,GAAG,CAAC8F,EAAE,CAAC,CAAA;EAC3B,IAAA,OAAO,MAAM;EACX,MAAA,IAAI,CAAC6O,cAAc,CAACtU,MAAM,CAACyF,EAAE,CAAC,CAAA;OAC/B,CAAA;EACH,GAAA;EAEQkQ,EAAAA,aAAa,GAAG;EACtB,IAAA,OAAO,IAAI,CAAC1B,IAAI,CAACtY,cAAc,KAAK,KAAK,IAAI,IAAI,CAACsY,IAAI,CAACyB,kBAAkB,CAAA;EAC3E,GAAA;EAEA,EAAA,MAAca,qBAAqB,GAAG;EACpC,IAAA,IAAI,CAAC,IAAI,CAACtC,IAAI,CAAClP,UAAU,EAAE,OAAA;EAC3B,IAAA,IAAI,CAAC,IAAI,CAACgQ,mBAAmB,EAAE,OAAA;EAC/B,IAAA,MAAM,IAAI,CAACQ,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC3S,KAAK,CAAC,MAAM;EAC/C;EAAA,KACD,CAAC,CAAA;EACJ,GAAA;EAEOoU,EAAAA,aAAa,GAAG;EACrB,IAAA,OAAO,IAAI1Y,GAAG,CAAC,IAAI,CAACoW,SAAS,CAAC,CAAA;EAChC,GAAA;EAEOuC,EAAAA,OAAO,GAAG;EACf;EACA,IAAA,IAAI,CAAC3C,cAAc,CAAC/H,KAAK,EAAE,CAAA;EAC3B,IAAA,IAAI,CAACmI,SAAS,CAACnI,KAAK,EAAE,CAAA;EACtB,IAAA,IAAI,CAAC4H,mBAAmB,CAAC5H,KAAK,EAAE,CAAA;EAChC,IAAA,IAAI,CAAC6H,gBAAgB,GAAG,EAAE,CAAA;MAC1B,IAAI,CAACG,QAAQ,GAAG,EAAE,CAAA;MAClB,IAAI,IAAI,CAACC,QAAQ,EAAE;EACjB5W,MAAAA,YAAY,CAAC,IAAI,CAAC4W,QAAQ,CAAC,CAAA;EAC7B,KAAA;MACA3U,WAAW,CAAC,IAAI,CAAC,CAAA;EAEjB,IAAA,IAAItC,SAAS,IAAIC,MAAM,CAAC2X,WAAW,KAAK,IAAI,EAAE;QAC5C,OAAO3X,MAAM,CAAC2X,WAAW,CAAA;EAC3B,KAAA;;EAEA;EACA,IAAA,IAAI,CAACN,sBAAsB,CAAC/U,OAAO,CAAEoX,GAAG,IAAK;QAC3CA,GAAG,CAACC,IAAI,EAAE,CAAA;EACZ,KAAC,CAAC,CAAA;EACF,IAAA,IAAI,CAACtC,sBAAsB,CAACtI,KAAK,EAAE,CAAA;EACnC,IAAA,IAAI,CAACuI,iBAAiB,CAACvI,KAAK,EAAE,CAAA;EAChC,GAAA;IAEO6K,WAAW,CAACC,QAAoB,EAAE;MACvC,IAAI,CAACnD,SAAS,GAAGmD,QAAQ,CAAA;EAC3B,GAAA;EAEOC,EAAAA,cAAc,CAAC/X,GAAW,EAAEgY,SAAiB,EAAE;EACpD,IAAA,IAAI,CAACtD,IAAI,CAAChP,gBAAgB,GAAG,IAAI,CAACgP,IAAI,CAAChP,gBAAgB,IAAI,EAAE,CAAA;MAC7D,IAAI,CAACgP,IAAI,CAAChP,gBAAgB,CAAC1F,GAAG,CAAC,GAAGgY,SAAS,CAAA;EAC3C,IAAA,IAAI,IAAI,CAACtD,IAAI,CAAClP,UAAU,EAAE;QACxB,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAC5B,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACjB,yBAAyB,EAAE,CAAA;MAChC,IAAI,CAACQ,OAAO,EAAE,CAAA;EAChB,GAAA;IAEO0B,GAAG,CAAIC,UAAyB,EAAa;MAClD,MAAMC,MAAM,GAAG,IAAI,CAACC,IAAI,CAACF,UAAU,EAAE,IAAI,CAAC,CAAA;EAC1C,IAAA,IAAI,CAACG,kBAAkB,CAACH,UAAU,EAAEC,MAAM,CAAC,CAAA;EAC3C,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;IAEOG,iBAAiB,CAACtY,GAAW,EAAE;EACpC,IAAA,IAAI,CAACuV,iBAAiB,CAACnV,GAAG,CAACJ,GAAG,CAAC,CAAA;MAC/B,IAAI,CAAC,IAAI,CAAC0U,IAAI,CAAC1P,WAAW,EAAE,OAAO,IAAI,CAAA;EACvC,IAAA,MAAMA,WAAW,GAAG,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,CAACsE,MAAM,CAAEqO,GAAG,IAAKA,GAAG,CAAC3X,GAAG,KAAKA,GAAG,CAAC,CAAA;EAC1E,IAAA,OAAOgF,WAAW,CACflB,GAAG,CAAE6T,GAAG,IAAK;EACZ,MAAA,IAAI,CAACA,GAAG,CAACY,MAAM,EAAE,OAAO,IAAI,CAAA;EAC5B,MAAA,OAAO,IAAI,CAACC,kBAAkB,CAACb,GAAG,CAAC,CAAA;OACpC,CAAC,CACDrO,MAAM,CAAEzD,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAAA;EAClC,GAAA;EAEQ2S,EAAAA,kBAAkB,CAACN,UAA0B,EAAEO,UAAoB,EAAE;MAC3E,MAAMhX,QAAQ,GAAG,IAAI,CAAC6T,sBAAsB,CAACnV,GAAG,CAAC+X,UAAU,CAAC,CAAA;;EAE5D;MACA,IACEA,UAAU,CAACK,MAAM,IACjB,CAAC,IAAI,CAAChD,iBAAiB,CAAC5Q,GAAG,CAACuT,UAAU,CAAClY,GAAG,CAAC,IAC3C,CAACyB,QAAQ,EAET,OAAO,IAAI,CAAA;;EAEb;EACA,IAAA,MAAM0W,MAAM,GAAG,IAAI,CAACF,GAAG,CAACC,UAAU,CAAC,CAAA;;EAEnC;MACA,MAAMQ,SAAS,GAAG/a,IAAI,CAACC,SAAS,CAACua,MAAM,CAAC7U,KAAK,CAAC,CAAA;;EAE9C;EACA,IAAA,IACE,CAACmV,UAAU,IACXN,MAAM,CAACQ,YAAY,IACnBlX,QAAQ,IACRA,QAAQ,CAACiX,SAAS,KAAKA,SAAS,EAChC;EACA,MAAA,OAAOP,MAAM,CAAA;EACf,KAAA;;EAEA;EACA,IAAA,IAAI1W,QAAQ,EAAE,IAAI,CAACmX,yBAAyB,CAACV,UAAU,CAAC,CAAA;;EAExD;MACA,IAAIC,MAAM,CAACQ,YAAY,EAAE;QACvB,MAAMf,IAAI,GAAG,IAAI,CAACiB,gBAAgB,CAACV,MAAM,CAAC7U,KAAK,CAAC,CAAA;EAChD,MAAA,IAAIsU,IAAI,EAAE;EACR,QAAA,IAAI,CAACtC,sBAAsB,CAACjV,GAAG,CAAC6X,UAAU,EAAE;YAC1CN,IAAI;EACJc,UAAAA,SAAAA;EACF,SAAC,CAAC,CAAA;EACJ,OAAA;EACF,KAAA;EAEA,IAAA,OAAOP,MAAM,CAAA;EACf,GAAA;IAEQS,yBAAyB,CAACjB,GAAmB,EAAE;MACrD,MAAM/X,IAAI,GAAG,IAAI,CAAC0V,sBAAsB,CAACnV,GAAG,CAACwX,GAAG,CAAC,CAAA;EACjD,IAAA,IAAI/X,IAAI,EAAE;QACRA,IAAI,CAACgY,IAAI,EAAE,CAAA;EACX,MAAA,IAAI,CAACtC,sBAAsB,CAAC7U,MAAM,CAACkX,GAAG,CAAC,CAAA;EACzC,KAAA;EACF,GAAA;IAEQ5B,yBAAyB,CAAC0C,UAAoB,EAAE;MACtD,MAAMzT,WAAW,GAAG,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,IAAI,EAAE,CAAA;;EAE/C;EACA,IAAA,MAAMxC,IAAI,GAAG,IAAInD,GAAG,CAAC2F,WAAW,CAAC,CAAA;MACjC,IAAI,CAACsQ,sBAAsB,CAAC/U,OAAO,CAAC,CAAC8P,CAAC,EAAEC,CAAC,KAAK;EAC5C,MAAA,IAAI,CAAC9N,IAAI,CAACmC,GAAG,CAAC2L,CAAC,CAAC,EAAE;UAChBD,CAAC,CAACuH,IAAI,EAAE,CAAA;EACR,QAAA,IAAI,CAACtC,sBAAsB,CAAC7U,MAAM,CAAC6P,CAAC,CAAC,CAAA;EACvC,OAAA;EACF,KAAC,CAAC,CAAA;;EAEF;EACAtL,IAAAA,WAAW,CAACzE,OAAO,CAAEoX,GAAG,IAAK;EAC3B,MAAA,IAAI,CAACa,kBAAkB,CAACb,GAAG,EAAEc,UAAU,CAAC,CAAA;EAC1C,KAAC,CAAC,CAAA;EACJ,GAAA;EAEQJ,EAAAA,kBAAkB,CAAIH,UAAyB,EAAEC,MAAiB,EAAE;EAC1E,IAAA,MAAMnY,GAAG,GAAGkY,UAAU,CAAClY,GAAG,CAAA;;EAE1B;MACA,MAAM8Y,IAAI,GAAG,IAAI,CAAC3D,SAAS,CAAChV,GAAG,CAACH,GAAG,CAAC,CAAA;EACpC;MACA,IACE,CAAC8Y,IAAI,IACLA,IAAI,CAACX,MAAM,CAACQ,YAAY,KAAKR,MAAM,CAACQ,YAAY,IAChDG,IAAI,CAACX,MAAM,CAACY,WAAW,KAAKZ,MAAM,CAACY,WAAW,EAC9C;EACA,MAAA,IAAI,CAAC5D,SAAS,CAAC9U,GAAG,CAACL,GAAG,EAAE;UAAEkY,UAAU;EAAEC,QAAAA,MAAAA;EAAO,OAAC,CAAC,CAAA;EAC/C,MAAA,IAAI,CAACpD,cAAc,CAACxU,OAAO,CAAE2F,EAAE,IAAK;UAClC,IAAI;EACFA,UAAAA,EAAE,CAACgS,UAAU,EAAEC,MAAM,CAAC,CAAA;WACvB,CAAC,OAAOtZ,CAAC,EAAE;EACVoQ,UAAAA,OAAO,CAACC,KAAK,CAACrQ,CAAC,CAAC,CAAA;EAClB,SAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;EACF,GAAA;EAEQma,EAAAA,kBAAkB,CAAChZ,GAAW,EAAE6F,GAAkB,EAAQ;EAChE;EACA,IAAA,IAAIA,GAAG,CAACoT,MAAM,KAAK,UAAU,EAAE,OAAA;;EAE/B;MACA,MAAMC,gBAAgB,GAAGvb,IAAI,CAACC,SAAS,CAACiI,GAAG,CAACvC,KAAK,CAAC,CAAA;MAClD,IAAI,IAAI,CAACuR,gBAAgB,CAAC7U,GAAG,CAAC,KAAKkZ,gBAAgB,EAAE,OAAA;EACrD,IAAA,IAAI,CAACrE,gBAAgB,CAAC7U,GAAG,CAAC,GAAGkZ,gBAAgB,CAAA;;EAE7C;EACA,IAAA,IAAI,IAAI,CAACxE,IAAI,CAACyE,cAAc,EAAE;QAC5B,IAAI;UACF,IAAI,CAACzE,IAAI,CAACyE,cAAc,CAACnZ,GAAG,EAAE6F,GAAG,CAAC,CAAA;SACnC,CAAC,OAAOhH,CAAC,EAAE;EACV;EAAA,OAAA;EAEJ,KAAA;;EAEA;EACA,IAAA,IAAI,CAACb,SAAS,IAAI,CAACC,MAAM,CAACxB,KAAK,EAAE,OAAA;EACjC,IAAA,IAAI,CAACuY,QAAQ,CAAC3I,IAAI,CAAC;QACjBrM,GAAG;QACHoZ,EAAE,EAAEvT,GAAG,CAACuT,EAAAA;EACV,KAAC,CAAC,CAAA;EACF,IAAA,IAAI,CAAC,IAAI,CAACnE,QAAQ,EAAE;EAClB,MAAA,IAAI,CAACA,QAAQ,GAAGhX,MAAM,CAACM,UAAU,CAAC,MAAM;EACtC;UACA,IAAI,CAAC0W,QAAQ,GAAG,CAAC,CAAA;EACjB,QAAA,MAAMoE,CAAC,GAAG,CAAC,GAAG,IAAI,CAACrE,QAAQ,CAAC,CAAA;UAC5B,IAAI,CAACA,QAAQ,GAAG,EAAE,CAAA;;EAElB;EACA,QAAA,IAAI,CAAC,IAAI,CAACN,IAAI,CAAC4E,WAAW,EAAE,OAAA;EAE5Brb,QAAAA,MAAM,CACHxB,KAAK,CAAA,gCAAA,CAAA,MAAA,CAEF,IAAI,CAACiY,IAAI,CAAC4E,WAAW,EAAA,UAAA,CAAA,CAAA,MAAA,CACZC,kBAAkB,CAAC5b,IAAI,CAACC,SAAS,CAACyb,CAAC,CAAC,CAAC,CAEhD,EAAA;EACEpa,UAAAA,KAAK,EAAE,UAAU;EACjBua,UAAAA,IAAI,EAAE,SAAA;EACR,SAAC,CACF,CACAnW,KAAK,CAAC,MAAM;EACX;EAAA,SACD,CAAC,CAAA;SACL,EAAE,IAAI,CAACqR,IAAI,CAAC+E,gBAAgB,IAAI,IAAI,CAAC,CAAA;EACxC,KAAA;EACF,GAAA;EAEQC,EAAAA,iBAAiB,CACvB1Z,GAAW,EACXsD,KAAQ,EACR2V,MAA2B,EAC3BU,MAAe,EACfzB,UAA0B,EAC1BC,MAAkB,EACA;EAClB,IAAA,MAAMyB,GAAkB,GAAG;QACzBtW,KAAK;QACL8V,EAAE,EAAE,CAAC,CAAC9V,KAAK;QACXuW,GAAG,EAAE,CAACvW,KAAK;QACX2V,MAAM;QACNU,MAAM,EAAEA,MAAM,IAAI,EAAA;OACnB,CAAA;EACD,IAAA,IAAIzB,UAAU,EAAE0B,GAAG,CAAC1B,UAAU,GAAGA,UAAU,CAAA;EAC3C,IAAA,IAAIC,MAAM,EAAEyB,GAAG,CAACE,gBAAgB,GAAG3B,MAAM,CAAA;;EAEzC;EACA,IAAA,IAAI,CAACa,kBAAkB,CAAChZ,GAAG,EAAE4Z,GAAG,CAAC,CAAA;EAEjC,IAAA,OAAOA,GAAG,CAAA;EACZ,GAAA;IAEOG,IAAI,CAAgD/Z,GAAM,EAAW;EAC1E,IAAA,OAAO,IAAI,CAACga,WAAW,CAACha,GAAG,CAAC,CAACoZ,EAAE,CAAA;EACjC,GAAA;IAEOa,KAAK,CAAgDja,GAAM,EAAW;EAC3E,IAAA,OAAO,IAAI,CAACga,WAAW,CAACha,GAAG,CAAC,CAAC6Z,GAAG,CAAA;EAClC,GAAA;EAEOK,EAAAA,eAAe,CAGpBla,GAAM,EAAEma,YAAe,EAAsB;MAC7C,MAAM7W,KAAK,GAAG,IAAI,CAAC0W,WAAW,CAAwBha,GAAG,CAAC,CAACsD,KAAK,CAAA;EAChE,IAAA,OAAOA,KAAK,KAAK,IAAI,GAAI6W,YAAY,GAA0B7W,KAAK,CAAA;EACtE,GAAA;;EAEA;EACF;EACA;EACA;EACE;IACO8W,OAAO,CAGZ7I,EAAK,EAA2B;EAChC,IAAA,OAAO,IAAI,CAACyI,WAAW,CAACzI,EAAE,CAAC,CAAA;EAC7B,GAAA;IAEOyI,WAAW,CAGhBzI,EAAK,EAA2B;EAChC;MACA,IAAI,IAAI,CAAC6D,oBAAoB,CAACzQ,GAAG,CAAC4M,EAAE,CAAC,EAAE;EAMrC,MAAA,OAAO,IAAI,CAACmI,iBAAiB,CAC3BnI,EAAE,EACF,IAAI,CAAC6D,oBAAoB,CAACjV,GAAG,CAACoR,EAAE,CAAC,EACjC,UAAU,CACX,CAAA;EACH,KAAA;;EAEA;EACA,IAAA,IAAI,CAAC,IAAI,CAACmD,IAAI,CAACvP,QAAQ,IAAI,CAAC,IAAI,CAACuP,IAAI,CAACvP,QAAQ,CAACoM,EAAE,CAAC,EAAE;QAGlD,OAAO,IAAI,CAACmI,iBAAiB,CAACnI,EAAE,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAA;EAC3D,KAAA;;EAEA;MACA,MAAM6I,OAA6B,GAAG,IAAI,CAAC1F,IAAI,CAACvP,QAAQ,CAACoM,EAAE,CAAC,CAAA;;EAE5D;MACA,IAAI6I,OAAO,CAACC,KAAK,EAAE;EACjB,MAAA,KAAK,MAAMC,IAAI,IAAIF,OAAO,CAACC,KAAK,EAAE;EAChC;EACA,QAAA,IAAIC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,cAAc,CAACF,IAAI,CAACC,OAAO,CAAC,EAAE;EAMrD,UAAA,SAAA;EACF,SAAA;;EAEA;UACA,IAAI,OAAO,IAAID,IAAI,EAAE;EACnB;EACA,UAAA,IAAIA,IAAI,CAACpH,SAAS,IAAI,CAAC,IAAI,CAACuH,gBAAgB,CAACH,IAAI,CAACpH,SAAS,CAAC,EAAE;EAM5D,YAAA,SAAA;EACF,WAAA;;EAEA;YACA,IACE,CAAC,IAAI,CAACwH,oBAAoB,CACxBJ,IAAI,CAACrM,IAAI,IAAIsD,EAAE,EACf+I,IAAI,CAACK,aAAa,EAClB,IAAI,CAACjG,IAAI,CAACqC,mBAAmB,IAAI,CAACuD,IAAI,CAACM,sBAAsB,GACzDN,IAAI,CAACO,iBAAiB,GACtBje,SAAS,EACb0d,IAAI,CAAChM,KAAK,EACVgM,IAAI,CAACzJ,QAAQ,EACbyJ,IAAI,CAACQ,WAAW,CACjB,EACD;EAMA,YAAA,SAAA;EACF,WAAA;;EAQA;YACA,IAAIR,IAAI,CAACS,MAAM,EAAE;EACfT,YAAAA,IAAI,CAACS,MAAM,CAACxa,OAAO,CAAEuT,CAAC,IAAK;gBACzB,IAAI,CAACkH,MAAM,CAAClH,CAAC,CAACoE,UAAU,EAAEpE,CAAC,CAACqE,MAAM,CAAC,CAAA;EACrC,aAAC,CAAC,CAAA;EACJ,WAAA;EAEA,UAAA,OAAO,IAAI,CAACuB,iBAAiB,CAACnI,EAAE,EAAE+I,IAAI,CAACW,KAAK,EAAO,OAAO,EAAEX,IAAI,CAAC/I,EAAE,CAAC,CAAA;EACtE,SAAA;EACA,QAAA,IAAI,CAAC+I,IAAI,CAACY,UAAU,EAAE;EAOpB,UAAA,SAAA;EACF,SAAA;;EAEA;EACA,QAAA,MAAMvD,GAAkB,GAAG;YACzBuD,UAAU,EAAEZ,IAAI,CAACY,UAA4B;EAC7Clb,UAAAA,GAAG,EAAEsa,IAAI,CAACta,GAAG,IAAIuR,EAAAA;WAClB,CAAA;UACD,IAAI,UAAU,IAAI+I,IAAI,EAAE3C,GAAG,CAAC9G,QAAQ,GAAGyJ,IAAI,CAACzJ,QAAQ,CAAA;UACpD,IAAIyJ,IAAI,CAACxJ,OAAO,EAAE6G,GAAG,CAAC7G,OAAO,GAAGwJ,IAAI,CAACxJ,OAAO,CAAA;UAC5C,IAAIwJ,IAAI,CAACK,aAAa,EAAEhD,GAAG,CAACgD,aAAa,GAAGL,IAAI,CAACK,aAAa,CAAA;UAC9D,IAAIL,IAAI,CAACO,iBAAiB,EACxBlD,GAAG,CAACkD,iBAAiB,GAAGP,IAAI,CAACO,iBAAiB,CAAA;UAChD,IAAIP,IAAI,CAACM,sBAAsB,EAC7BjD,GAAG,CAACiD,sBAAsB,GAAGN,IAAI,CAACM,sBAAsB,CAAA;EAC1D,QAAA,IAAIN,IAAI,CAACa,aAAa,KAAKve,SAAS,EAClC+a,GAAG,CAACwD,aAAa,GAAGb,IAAI,CAACa,aAAa,CAAA;EACxC,QAAA,IAAIb,IAAI,CAACc,gBAAgB,KAAKxe,SAAS,EACrC+a,GAAG,CAACyD,gBAAgB,GAAGd,IAAI,CAACc,gBAAgB,CAAA;UAC9C,IAAId,IAAI,CAAC7L,SAAS,EAAEkJ,GAAG,CAAClJ,SAAS,GAAG6L,IAAI,CAAC7L,SAAS,CAAA;UAClD,IAAI6L,IAAI,CAACe,IAAI,EAAE1D,GAAG,CAAC0D,IAAI,GAAGf,IAAI,CAACe,IAAI,CAAA;UACnC,IAAIf,IAAI,CAAC3L,MAAM,EAAEgJ,GAAG,CAAChJ,MAAM,GAAG2L,IAAI,CAAC3L,MAAM,CAAA;UACzC,IAAI2L,IAAI,CAACnI,IAAI,EAAEwF,GAAG,CAACxF,IAAI,GAAGmI,IAAI,CAACnI,IAAI,CAAA;UACnC,IAAImI,IAAI,CAACgB,KAAK,EAAE3D,GAAG,CAAC2D,KAAK,GAAGhB,IAAI,CAACgB,KAAK,CAAA;UACtC,IAAIhB,IAAI,CAACrM,IAAI,EAAE0J,GAAG,CAAC1J,IAAI,GAAGqM,IAAI,CAACrM,IAAI,CAAA;UACnC,IAAIqM,IAAI,CAACQ,WAAW,EAAEnD,GAAG,CAACmD,WAAW,GAAGR,IAAI,CAACQ,WAAW,CAAA;UACxD,IAAIR,IAAI,CAACC,OAAO,EAAE5C,GAAG,CAAC4C,OAAO,GAAGD,IAAI,CAACC,OAAO,CAAA;UAC5C,IAAID,IAAI,CAACpH,SAAS,EAAEyE,GAAG,CAACzE,SAAS,GAAGoH,IAAI,CAACpH,SAAS,CAAA;;EAElD;UACA,MAAMrN,GAAG,GAAG,IAAI,CAACuS,IAAI,CAACT,GAAG,EAAEpG,EAAE,CAAC,CAAA;EAC9B,QAAA,IAAI,CAAC8G,kBAAkB,CAACV,GAAG,EAAE9R,GAAG,CAAC,CAAA;UACjC,IAAIA,GAAG,CAAC8S,YAAY,IAAI,CAAC9S,GAAG,CAAC0V,WAAW,EAAE;EACxC,UAAA,OAAO,IAAI,CAAC7B,iBAAiB,CAC3BnI,EAAE,EACF1L,GAAG,CAACvC,KAAK,EACT,YAAY,EACZgX,IAAI,CAAC/I,EAAE,EACPoG,GAAG,EACH9R,GAAG,CACJ,CAAA;EACH,SAAA;EACF,OAAA;EACF,KAAA;;EAQA;EACA,IAAA,OAAO,IAAI,CAAC6T,iBAAiB,CAC3BnI,EAAE,EACF6I,OAAO,CAACD,YAAY,KAAKvd,SAAS,GAAG,IAAI,GAAGwd,OAAO,CAACD,YAAY,EAChE,cAAc,CACf,CAAA;EACH,GAAA;EAEQO,EAAAA,oBAAoB,CAC1BzM,IAAY,EACZ0M,aAAiC,EACjCE,iBAAqC,EACrCvM,KAAiC,EACjCuC,QAA4B,EAC5BiK,WAA+B,EACtB;MACT,IAAI,CAACxM,KAAK,IAAIuC,QAAQ,KAAKjU,SAAS,EAAE,OAAO,IAAI,CAAA;MAEjD,MAAM;EAAE4R,MAAAA,SAAAA;OAAW,GAAG,IAAI,CAACgN,iBAAiB,CAC1Cb,aAAa,EACbE,iBAAiB,CAClB,CAAA;MACD,IAAI,CAACrM,SAAS,EAAE;EACd,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;MAEA,MAAML,CAAC,GAAGH,IAAI,CAACC,IAAI,EAAEO,SAAS,EAAEsM,WAAW,IAAI,CAAC,CAAC,CAAA;EACjD,IAAA,IAAI3M,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,CAAA;EAE5B,IAAA,OAAOG,KAAK,GACRD,OAAO,CAACF,CAAC,EAAEG,KAAK,CAAC,GACjBuC,QAAQ,KAAKjU,SAAS,GACtBuR,CAAC,IAAI0C,QAAQ,GACb,IAAI,CAAA;EACV,GAAA;IAEQ4J,gBAAgB,CAACvH,SAA6B,EAAW;MAC/D,OAAOF,aAAa,CAAC,IAAI,CAAC5Q,aAAa,EAAE,EAAE8Q,SAAS,CAAC,CAAA;EACvD,GAAA;IAEQsH,cAAc,CAACD,OAAiB,EAAW;EACjD,IAAA,OAAOA,OAAO,CAAChK,IAAI,CAAEjH,MAAM,IAAK;QAC9B,MAAM;EAAEkF,QAAAA,SAAAA;SAAW,GAAG,IAAI,CAACgN,iBAAiB,CAAClS,MAAM,CAAC8C,SAAS,CAAC,CAAA;EAC9D,MAAA,IAAI,CAACoC,SAAS,EAAE,OAAO,IAAI,CAAA;EAC3B,MAAA,MAAML,CAAC,GAAGH,IAAI,CAAC1E,MAAM,CAAC2E,IAAI,EAAEO,SAAS,EAAElF,MAAM,CAACwR,WAAW,IAAI,CAAC,CAAC,CAAA;EAC/D,MAAA,IAAI3M,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,CAAA;EAC3B,MAAA,OAAO,CAAC7E,MAAM,CAACqF,MAAM,CAAC4B,IAAI,CAAEkL,CAAC,IAAKpN,OAAO,CAACF,CAAC,EAAEsN,CAAC,CAAC,CAAC,CAAA;EAClD,KAAC,CAAC,CAAA;EACJ,GAAA;EAEQrD,EAAAA,IAAI,CACVF,UAAyB,EACzBwD,SAAwB,EACb;EACX,IAAA,MAAM1b,GAAG,GAAGkY,UAAU,CAAClY,GAAG,CAAA;EAC1B,IAAA,MAAM4Q,aAAa,GAAGsH,UAAU,CAACgD,UAAU,CAAClP,MAAM,CAAA;;EAElD;MACA,IAAI4E,aAAa,GAAG,CAAC,EAAE;EAGrB,MAAA,OAAO,IAAI,CAAC+K,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,IAAI,IAAI,CAAChH,IAAI,CAACkH,OAAO,KAAK,KAAK,EAAE;EAG/B,MAAA,OAAO,IAAI,CAACD,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACAxD,IAAAA,UAAU,GAAG,IAAI,CAAC2D,eAAe,CAAC3D,UAAU,CAAC,CAAA;;EAE7C;EACA,IAAA,IACEA,UAAU,CAAC4D,WAAW,IACtB,CAAC3M,aAAa,CAAC,IAAI,CAAC4M,cAAc,EAAE,EAAE7D,UAAU,CAAC4D,WAAW,CAAC,EAC7D;EAKA,MAAA,OAAO,IAAI,CAACH,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,MAAMM,UAAU,GAAG1K,sBAAsB,CACvCtR,GAAG,EACH,IAAI,CAAC+b,cAAc,EAAE,EACrBnL,aAAa,CACd,CAAA;MACD,IAAIoL,UAAU,KAAK,IAAI,EAAE;QAMvB,OAAO,IAAI,CAACL,UAAU,CAACzD,UAAU,EAAE8D,UAAU,EAAE,KAAK,EAAEN,SAAS,CAAC,CAAA;EAClE,KAAA;;EAEA;EACA,IAAA,IAAI,IAAI,CAAChH,IAAI,CAAChP,gBAAgB,IAAI1F,GAAG,IAAI,IAAI,CAAC0U,IAAI,CAAChP,gBAAgB,EAAE;QACnE,MAAMsS,SAAS,GAAG,IAAI,CAACtD,IAAI,CAAChP,gBAAgB,CAAC1F,GAAG,CAAC,CAAA;QAMjD,OAAO,IAAI,CAAC2b,UAAU,CAACzD,UAAU,EAAEF,SAAS,EAAE,KAAK,EAAE0D,SAAS,CAAC,CAAA;EACjE,KAAA;;EAEA;MACA,IAAIxD,UAAU,CAAC+D,MAAM,KAAK,OAAO,IAAI/D,UAAU,CAACgE,MAAM,KAAK,KAAK,EAAE;EAKhE,MAAA,OAAO,IAAI,CAACP,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;MACA,MAAM;QAAEf,aAAa;EAAEnM,MAAAA,SAAAA;OAAW,GAAG,IAAI,CAACgN,iBAAiB,CACzDtD,UAAU,CAACyC,aAAa,EACxB,IAAI,CAACjG,IAAI,CAACqC,mBAAmB,IAAI,CAACmB,UAAU,CAAC0C,sBAAsB,GAC/D1C,UAAU,CAAC2C,iBAAiB,GAC5Bje,SAAS,CACd,CAAA;MACD,IAAI,CAAC4R,SAAS,EAAE;EAKd,MAAA,OAAO,IAAI,CAACmN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;MAEA,IAAIS,QAAQ,GAAG,CAAC,CAAC,CAAA;MAEjB,IAAIC,iBAAiB,GAAG,KAAK,CAAA;MAC7B,IAAIC,4BAA4B,GAAG,KAAK,CAAA;MACxC,IAAI,IAAI,CAAC3H,IAAI,CAACqC,mBAAmB,IAAI,CAACmB,UAAU,CAAC0C,sBAAsB,EAAE;QACvE,MAAM;UAAE5C,SAAS;EAAEsE,QAAAA,gBAAAA;SAAkB,GAAG,IAAI,CAACC,yBAAyB,CACpErE,UAAU,CAAClY,GAAG,EACdkY,UAAU,CAACiD,aAAa,EACxBjD,UAAU,CAACkD,gBAAgB,EAC3BlD,UAAU,CAACmD,IAAI,CAChB,CAAA;QACDe,iBAAiB,GAAGpE,SAAS,IAAI,CAAC,CAAA;EAClCmE,MAAAA,QAAQ,GAAGnE,SAAS,CAAA;QACpBqE,4BAA4B,GAAG,CAAC,CAACC,gBAAgB,CAAA;EACnD,KAAA;;EAEA;MACA,IAAI,CAACF,iBAAiB,EAAE;EACtB;QACA,IAAIlE,UAAU,CAACqC,OAAO,EAAE;UACtB,IAAI,IAAI,CAACC,cAAc,CAACtC,UAAU,CAACqC,OAAO,CAAC,EAAE;EAK3C,UAAA,OAAO,IAAI,CAACoB,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,SAAA;EACF,OAAC,MAAM,IACLxD,UAAU,CAACzJ,SAAS,IACpB,CAACF,WAAW,CAACC,SAAS,EAAE0J,UAAU,CAACzJ,SAAS,CAAC,EAC7C;EAKA,QAAA,OAAO,IAAI,CAACkN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;;EAEA;QACA,IAAIxD,UAAU,CAACxI,OAAO,IAAI,CAACJ,UAAU,CAAC4I,UAAU,CAACxI,OAAO,CAAC,EAAE;EAKzD,QAAA,OAAO,IAAI,CAACiM,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;;EAEA;EACA,MAAA,IACExD,UAAU,CAAChF,SAAS,IACpB,CAAC,IAAI,CAACuH,gBAAgB,CAACvC,UAAU,CAAChF,SAAS,CAAC,EAC5C;EAKA,QAAA,OAAO,IAAI,CAACyI,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;;EAEA;EACA,MAAA,IACExD,UAAU,CAACsE,MAAM,IACjB,CAAC,IAAI,CAACC,gBAAgB,CAACvE,UAAU,CAACsE,MAAM,CAAa,EACrD;EAKA,QAAA,OAAO,IAAI,CAACb,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAIxD,UAAU,CAACtV,GAAG,IAAI,CAAC,IAAI,CAAC8Z,WAAW,CAACxE,UAAU,CAACtV,GAAG,CAAW,EAAE;EAKjE,MAAA,OAAO,IAAI,CAAC+Y,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,MAAMvN,CAAC,GAAGH,IAAI,CACZkK,UAAU,CAACjK,IAAI,IAAIjO,GAAG,EACtBwO,SAAS,EACT0J,UAAU,CAAC4C,WAAW,IAAI,CAAC,CAC5B,CAAA;MACD,IAAI3M,CAAC,KAAK,IAAI,EAAE;EAKd,MAAA,OAAO,IAAI,CAACwN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;MAEA,IAAI,CAACU,iBAAiB,EAAE;QACtB,MAAMzN,MAAM,GACVuJ,UAAU,CAACvJ,MAAM,IACjBgC,eAAe,CACbC,aAAa,EACbsH,UAAU,CAACrH,QAAQ,KAAKjU,SAAS,GAAG,CAAC,GAAGsb,UAAU,CAACrH,QAAQ,EAC3DqH,UAAU,CAACpH,OAAO,CACnB,CAAA;EACHqL,MAAAA,QAAQ,GAAGzN,eAAe,CAACP,CAAC,EAAEQ,MAAM,CAAC,CAAA;EACvC,KAAA;;EAEA;EACA,IAAA,IAAI0N,4BAA4B,EAAE;EAKhC,MAAA,OAAO,IAAI,CAACV,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,EAAE9e,SAAS,EAAE,IAAI,CAAC,CAAA;EAC3E,KAAA;;EAEA;MACA,IAAIuf,QAAQ,GAAG,CAAC,EAAE;EAKhB,MAAA,OAAO,IAAI,CAACR,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;MACA,IAAI,OAAO,IAAIxD,UAAU,EAAE;QAMzB,OAAO,IAAI,CAACyD,UAAU,CACpBzD,UAAU,EACVA,UAAU,CAAC+C,KAAK,KAAKre,SAAS,GAAG,CAAC,CAAC,GAAGsb,UAAU,CAAC+C,KAAK,EACtD,KAAK,EACLS,SAAS,CACV,CAAA;EACH,KAAA;;EAEA;EACA,IAAA,IAAI,IAAI,CAAChH,IAAI,CAACiI,MAAM,EAAE;EAKpB,MAAA,OAAO,IAAI,CAAChB,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,IAAIxD,UAAU,CAAC+D,MAAM,KAAK,SAAS,EAAE;EAKnC,MAAA,OAAO,IAAI,CAACN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,MAAMvD,MAAM,GAAG,IAAI,CAACwD,UAAU,CAC5BzD,UAAU,EACViE,QAAQ,EACR,IAAI,EACJT,SAAS,EACTvN,CAAC,EACDiO,iBAAiB,CAClB,CAAA;;EAED;MACA,IAAI,IAAI,CAAC1H,IAAI,CAACqC,mBAAmB,IAAI,CAACmB,UAAU,CAAC0C,sBAAsB,EAAE;QACvE,MAAM;UACJgC,OAAO;EACP5c,QAAAA,GAAG,EAAE6c,OAAO;EACZC,QAAAA,GAAAA;SACD,GAAG,IAAI,CAACC,kCAAkC,CACzCpC,aAAa,EACblI,QAAQ,CAACjE,SAAS,CAAC,EACnB;EACE,QAAA,CAAC,IAAI,CAACwO,6BAA6B,CACjC9E,UAAU,CAAClY,GAAG,EACdkY,UAAU,CAACiD,aAAa,CACzB,GAAGhD,MAAM,CAACnY,GAAAA;EACb,OAAC,CACF,CAAA;EACD,MAAA,IAAI4c,OAAO,EAAE;EACX;EACA,QAAA,IAAI,CAAClI,IAAI,CAAC8C,0BAA0B,GAClC,IAAI,CAAC9C,IAAI,CAAC8C,0BAA0B,IAAI,EAAE,CAAA;UAC5C,IAAI,CAAC9C,IAAI,CAAC8C,0BAA0B,CAACqF,OAAO,CAAC,GAAGC,GAAG,CAAA;EACnD;UACA,IAAI,CAACpI,IAAI,CAACqC,mBAAmB,CAACkG,eAAe,CAACH,GAAG,CAAC,CAAA;EACpD,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAI,CAAC9B,MAAM,CAAC9C,UAAU,EAAEC,MAAM,CAAC,CAAA;EAQ/B,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;EAEA+E,EAAAA,GAAG,CAACC,GAAW,EAAEC,GAA4B,EAAE;EAC7C,IAAA,IAAI,CAAC,IAAI,CAACtI,KAAK,EAAE,OAAA;MACjB,IAAI,IAAI,CAACJ,IAAI,CAACwI,GAAG,EAAE,IAAI,CAACxI,IAAI,CAACwI,GAAG,CAACC,GAAG,EAAEC,GAAG,CAAC,CAAC,KACtCnO,OAAO,CAACiO,GAAG,CAACC,GAAG,EAAEC,GAAG,CAAC,CAAA;EAC5B,GAAA;EAEQpC,EAAAA,MAAM,CAAI9C,UAAyB,EAAEC,MAAiB,EAAE;EAC9D,IAAA,IAAI,CAAC,IAAI,CAACzD,IAAI,CAAC2I,gBAAgB,EAAE,OAAA;EAEjC,IAAA,MAAMrd,GAAG,GAAGkY,UAAU,CAAClY,GAAG,CAAA;;EAE1B;EACA,IAAA,MAAMsQ,CAAC,GACL6H,MAAM,CAACwC,aAAa,GAAGxC,MAAM,CAAC3J,SAAS,GAAGxO,GAAG,GAAGmY,MAAM,CAACY,WAAW,CAAA;MACpE,IAAI,IAAI,CAACnE,mBAAmB,CAACjQ,GAAG,CAAC2L,CAAC,CAAC,EAAE,OAAA;EACrC,IAAA,IAAI,CAACsE,mBAAmB,CAACxU,GAAG,CAACkQ,CAAC,CAAC,CAAA;MAE/B,IAAI;QACF,IAAI,CAACoE,IAAI,CAAC2I,gBAAgB,CAACnF,UAAU,EAAEC,MAAM,CAAC,CAAA;OAC/C,CAAC,OAAOtZ,CAAC,EAAE;EACVoQ,MAAAA,OAAO,CAACC,KAAK,CAACrQ,CAAC,CAAC,CAAA;EAClB,KAAA;EACF,GAAA;IAEQgd,eAAe,CAAI3D,UAAyB,EAAiB;EACnE,IAAA,MAAMlY,GAAG,GAAGkY,UAAU,CAAClY,GAAG,CAAA;EAC1B,IAAA,MAAMsd,CAAC,GAAG,IAAI,CAAC5I,IAAI,CAACwC,SAAS,CAAA;EAC7B,IAAA,IAAIoG,CAAC,IAAIA,CAAC,CAACtd,GAAG,CAAC,EAAE;EACfkY,MAAAA,UAAU,GAAG3V,MAAM,CAACgb,MAAM,CAAC,EAAE,EAAErF,UAAU,EAAEoF,CAAC,CAACtd,GAAG,CAAC,CAAC,CAAA;EAClD,MAAA,IAAI,OAAOkY,UAAU,CAACtV,GAAG,KAAK,QAAQ,EAAE;UACtCsV,UAAU,CAACtV,GAAG,GAAGgM,YAAY;EAC3B;UACAsJ,UAAU,CAACtV,GAAG,CACf,CAAA;EACH,OAAA;EACF,KAAA;EAEA,IAAA,OAAOsV,UAAU,CAAA;EACnB,GAAA;EAEQsD,EAAAA,iBAAiB,CAACnU,IAAa,EAAEmW,QAAiB,EAAE;EAC1D,IAAA,IAAI7C,aAAa,GAAGtT,IAAI,IAAI,IAAI,CAAA;EAChC;MACA,IAAImH,SAAc,GAAG,EAAE,CAAA;EAEvB,IAAA,IAAI,IAAI,CAAC6G,mBAAmB,CAACsF,aAAa,CAAC,EAAE;EAC3CnM,MAAAA,SAAS,GAAG,IAAI,CAAC6G,mBAAmB,CAACsF,aAAa,CAAC,CAAA;EACrD,KAAC,MAAM,IAAI,IAAI,CAACjG,IAAI,CAACvS,UAAU,EAAE;QAC/BqM,SAAS,GAAG,IAAI,CAACkG,IAAI,CAACvS,UAAU,CAACwY,aAAa,CAAC,IAAI,EAAE,CAAA;EACvD,KAAC,MAAM,IAAI,IAAI,CAACjG,IAAI,CAAC+I,IAAI,EAAE;QACzBjP,SAAS,GAAG,IAAI,CAACkG,IAAI,CAAC+I,IAAI,CAAC9C,aAAa,CAAC,IAAI,EAAE,CAAA;EACjD,KAAA;;EAEA;EACA,IAAA,IAAI,CAACnM,SAAS,IAAIgP,QAAQ,EAAE;EAC1B,MAAA,IAAI,IAAI,CAACnI,mBAAmB,CAACmI,QAAQ,CAAC,EAAE;EACtChP,QAAAA,SAAS,GAAG,IAAI,CAAC6G,mBAAmB,CAACmI,QAAQ,CAAC,CAAA;EAChD,OAAC,MAAM,IAAI,IAAI,CAAC9I,IAAI,CAACvS,UAAU,EAAE;UAC/BqM,SAAS,GAAG,IAAI,CAACkG,IAAI,CAACvS,UAAU,CAACqb,QAAQ,CAAC,IAAI,EAAE,CAAA;EAClD,OAAC,MAAM,IAAI,IAAI,CAAC9I,IAAI,CAAC+I,IAAI,EAAE;UACzBjP,SAAS,GAAG,IAAI,CAACkG,IAAI,CAAC+I,IAAI,CAACD,QAAQ,CAAC,IAAI,EAAE,CAAA;EAC5C,OAAA;EACA,MAAA,IAAIhP,SAAS,EAAE;EACbmM,QAAAA,aAAa,GAAG6C,QAAQ,CAAA;EAC1B,OAAA;EACF,KAAA;MAEA,OAAO;QAAE7C,aAAa;EAAEnM,MAAAA,SAAAA;OAAW,CAAA;EACrC,GAAA;EAEQmN,EAAAA,UAAU,CAChBzD,UAAyB,EACzBwF,cAAsB,EACtBC,QAAiB,EACjBjC,SAAwB,EACxBkC,MAAe,EACfC,gBAA0B,EACf;MACX,IAAIlF,YAAY,GAAG,IAAI,CAAA;EACvB;MACA,IAAI+E,cAAc,GAAG,CAAC,IAAIA,cAAc,IAAIxF,UAAU,CAACgD,UAAU,CAAClP,MAAM,EAAE;EACxE0R,MAAAA,cAAc,GAAG,CAAC,CAAA;EAClB/E,MAAAA,YAAY,GAAG,KAAK,CAAA;EACtB,KAAA;MAEA,MAAM;QAAEgC,aAAa;EAAEnM,MAAAA,SAAAA;OAAW,GAAG,IAAI,CAACgN,iBAAiB,CACzDtD,UAAU,CAACyC,aAAa,EACxB,IAAI,CAACjG,IAAI,CAACqC,mBAAmB,IAAI,CAACmB,UAAU,CAAC0C,sBAAsB,GAC/D1C,UAAU,CAAC2C,iBAAiB,GAC5Bje,SAAS,CACd,CAAA;EAED,IAAA,MAAMye,IAA4B,GAAGnD,UAAU,CAACmD,IAAI,GAChDnD,UAAU,CAACmD,IAAI,CAACqC,cAAc,CAAC,GAC/B,EAAE,CAAA;EAEN,IAAA,MAAM7X,GAAc,GAAG;EACrB7F,MAAAA,GAAG,EAAEqb,IAAI,CAACrb,GAAG,IAAI,EAAE,GAAG0d,cAAc;QACpChC,SAAS;QACT/C,YAAY;QACZgF,QAAQ;EACR5E,MAAAA,WAAW,EAAE2E,cAAc;EAC3Bpa,MAAAA,KAAK,EAAE4U,UAAU,CAACgD,UAAU,CAACwC,cAAc,CAAC;QAC5C/C,aAAa;QACbnM,SAAS;QACTqP,gBAAgB,EAAE,CAAC,CAACA,gBAAAA;OACrB,CAAA;MAED,IAAIxC,IAAI,CAAClJ,IAAI,EAAEtM,GAAG,CAACsM,IAAI,GAAGkJ,IAAI,CAAClJ,IAAI,CAAA;MACnC,IAAIyL,MAAM,KAAKhhB,SAAS,EAAEiJ,GAAG,CAAC+X,MAAM,GAAGA,MAAM,CAAA;MAC7C,IAAIvC,IAAI,CAACE,WAAW,EAAE1V,GAAG,CAAC0V,WAAW,GAAGF,IAAI,CAACE,WAAW,CAAA;EAExD,IAAA,OAAO1V,GAAG,CAAA;EACZ,GAAA;EAEQkW,EAAAA,cAAc,GAAG;EACvB,IAAA,OAAO,IAAI,CAACrH,IAAI,CAAC9R,GAAG,KAAK5E,SAAS,GAAGC,MAAM,CAAC6f,QAAQ,CAACtN,IAAI,GAAG,EAAE,CAAC,CAAA;EACjE,GAAA;IAEQkM,WAAW,CAACqB,QAAgB,EAAW;EAC7C,IAAA,MAAMnb,GAAG,GAAG,IAAI,CAACmZ,cAAc,EAAE,CAAA;EACjC,IAAA,IAAI,CAACnZ,GAAG,EAAE,OAAO,KAAK,CAAA;EAEtB,IAAA,MAAMob,QAAQ,GAAGpb,GAAG,CAACmM,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;MAEzE,IAAIgP,QAAQ,CAAC1Q,IAAI,CAACzK,GAAG,CAAC,EAAE,OAAO,IAAI,CAAA;MACnC,IAAImb,QAAQ,CAAC1Q,IAAI,CAAC2Q,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAA;EACxC,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;IAEQvB,gBAAgB,CAACwB,SAAmB,EAAW;MACrD,MAAMzB,MAAM,GAAG,IAAI,CAAC9H,IAAI,CAAC8H,MAAM,IAAI,EAAE,CAAA;EACrC,IAAA,KAAK,IAAIjY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0Z,SAAS,CAACjS,MAAM,EAAEzH,CAAC,EAAE,EAAE;QACzC,IAAIiY,MAAM,CAACyB,SAAS,CAAC1Z,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAA;EACvC,KAAA;EACA,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;IAEQsU,gBAAgB,CAACqF,OAAgC,EAAE;MACzD,IAAI,CAAClgB,SAAS,EAAE,OAAA;MAChB,MAAM4Z,IAAoB,GAAG,EAAE,CAAA;MAC/B,IAAIsG,OAAO,CAACC,GAAG,EAAE;EACf,MAAA,MAAM3d,CAAC,GAAGtC,QAAQ,CAAC4N,aAAa,CAAC,OAAO,CAAC,CAAA;EACzCtL,MAAAA,CAAC,CAAC0J,SAAS,GAAGgU,OAAO,CAACC,GAAG,CAAA;EACzBjgB,MAAAA,QAAQ,CAACkgB,IAAI,CAACC,WAAW,CAAC7d,CAAC,CAAC,CAAA;QAC5BoX,IAAI,CAACvL,IAAI,CAAC,MAAM7L,CAAC,CAAC8d,MAAM,EAAE,CAAC,CAAA;EAC7B,KAAA;MACA,IAAIJ,OAAO,CAACK,EAAE,EAAE;EACd,MAAA,MAAMC,MAAM,GAAGtgB,QAAQ,CAAC4N,aAAa,CAAC,QAAQ,CAAC,CAAA;EAC/C0S,MAAAA,MAAM,CAACtU,SAAS,GAAGgU,OAAO,CAACK,EAAE,CAAA;EAC7BrgB,MAAAA,QAAQ,CAACkgB,IAAI,CAACC,WAAW,CAACG,MAAM,CAAC,CAAA;QACjC5G,IAAI,CAACvL,IAAI,CAAC,MAAMmS,MAAM,CAACF,MAAM,EAAE,CAAC,CAAA;EAClC,KAAA;MACA,IAAIJ,OAAO,CAACO,YAAY,EAAE;EACxBP,MAAAA,OAAO,CAACO,YAAY,CAACle,OAAO,CAAE2L,QAAQ,IAAK;UACzC0L,IAAI,CAACvL,IAAI,CAACnD,KAAM,CAACuE,WAAW,CAACvB,QAAQ,CAAwB,CAACjF,MAAM,CAAC,CAAA;EACvE,OAAC,CAAC,CAAA;EACJ,KAAA;EACA,IAAA,OAAO,MAAM;EACX2Q,MAAAA,IAAI,CAACrX,OAAO,CAAEme,EAAE,IAAKA,EAAE,EAAE,CAAC,CAAA;OAC3B,CAAA;EACH,GAAA;IAEQC,uCAAuC,CAAC/e,IAAyB,EAAE;EACzE,IAAA,MAAMuC,UAAU,GAAG,IAAI9C,GAAG,EAAU,CAAA;EACpC,IAAA,MAAM8F,QAAQ,GAAGvF,IAAI,IAAIA,IAAI,CAACuF,QAAQ,GAAGvF,IAAI,CAACuF,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE,CAAA;EAC3E,IAAA,MAAMJ,WAAW,GACfpF,IAAI,IAAIA,IAAI,CAACoF,WAAW,GAAGpF,IAAI,CAACoF,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE,CAAA;MACrE1C,MAAM,CAACC,IAAI,CAAC2C,QAAQ,CAAC,CAAC5E,OAAO,CAAEgR,EAAE,IAAK;EACpC,MAAA,MAAM6I,OAAO,GAAGjV,QAAQ,CAACoM,EAAE,CAAC,CAAA;QAC5B,IAAI6I,OAAO,CAACC,KAAK,EAAE;EACjB,QAAA,KAAK,MAAMC,IAAI,IAAIF,OAAO,CAACC,KAAK,EAAE;YAChC,IAAIC,IAAI,CAACY,UAAU,EAAE;cACnB/Y,UAAU,CAAC/B,GAAG,CAACka,IAAI,CAACK,aAAa,IAAI,IAAI,CAAC,CAAA;cAC1C,IAAIL,IAAI,CAACO,iBAAiB,EAAE;EAC1B1Y,cAAAA,UAAU,CAAC/B,GAAG,CAACka,IAAI,CAACO,iBAAiB,CAAC,CAAA;EACxC,aAAA;EACF,WAAA;EACF,SAAA;EACF,OAAA;EACF,KAAC,CAAC,CAAA;EACF7V,IAAAA,WAAW,CAAClB,GAAG,CAAEoU,UAAU,IAAK;QAC9B/V,UAAU,CAAC/B,GAAG,CAAC8X,UAAU,CAACyC,aAAa,IAAI,IAAI,CAAC,CAAA;QAChD,IAAIzC,UAAU,CAAC2C,iBAAiB,EAAE;EAChC1Y,QAAAA,UAAU,CAAC/B,GAAG,CAAC8X,UAAU,CAAC2C,iBAAiB,CAAC,CAAA;EAC9C,OAAA;EACF,KAAC,CAAC,CAAA;EACF,IAAA,OAAO7Z,KAAK,CAACC,IAAI,CAACkB,UAAU,CAAC,CAAA;EAC/B,GAAA;IAEA,MAAa2C,oBAAoB,CAAClF,IAAyB,EAAE;EAC3D,IAAA,IAAI,IAAI,CAAC8U,IAAI,CAACqC,mBAAmB,EAAE;EACjC,MAAA,MAAM5U,UAAU,GAAG,IAAI,CAACyc,0BAA0B,CAAChf,IAAI,CAAC,CAAA;EACxD,MAAA,IAAI,CAAC8U,IAAI,CAAC8C,0BAA0B,GAAG,MAAM,IAAI,CAAC9C,IAAI,CAACqC,mBAAmB,CAAC8H,iBAAiB,CAC1F1c,UAAU,CACX,CAAA;EACH,KAAA;EACF,GAAA;EAEQ2c,EAAAA,2BAA2B,GAAsB;MACvD,MAAMC,iBAAoC,GAAG,EAAE,CAAA;EAC/Cxc,IAAAA,MAAM,CAACyc,MAAM,CAAC,IAAI,CAACtK,IAAI,CAAC8C,0BAA0B,IAAI,EAAE,CAAC,CAACjX,OAAO,CAAEuc,GAAG,IAAK;EACzE,MAAA,IAAIA,GAAG,CAACmC,WAAW,EAAE1c,MAAM,CAACgb,MAAM,CAACwB,iBAAiB,EAAEjC,GAAG,CAACmC,WAAW,CAAC,CAAA;EACxE,KAAC,CAAC,CAAA;EACF,IAAA,OAAOF,iBAAiB,CAAA;EAC1B,GAAA;IAEQxC,yBAAyB,CAC/B2C,aAAqB,EACrBC,uBAAgC,EAChCC,0BAAmC,EACnC/D,IAAsB,EAItB;MACA8D,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC,CAAA;MACtDC,0BAA0B,GAAGA,0BAA0B,IAAI,CAAC,CAAA;MAC5D/D,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;MACjB,MAAM9J,EAAE,GAAG,IAAI,CAACyL,6BAA6B,CAC3CkC,aAAa,EACbC,uBAAuB,CACxB,CAAA;EACD,IAAA,MAAMF,WAAW,GAAG,IAAI,CAACH,2BAA2B,EAAE,CAAA;;EAEtD;MACA,IAAIM,0BAA0B,GAAG,CAAC,EAAE;QAClC,KAAK,IAAI7a,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI6a,0BAA0B,EAAE7a,CAAC,EAAE,EAAE;UACpD,MAAM8a,UAAU,GAAG,IAAI,CAACrC,6BAA6B,CAACkC,aAAa,EAAE3a,CAAC,CAAC,CAAA;EACvE,QAAA,IAAI0a,WAAW,CAACI,UAAU,CAAC,KAAKziB,SAAS,EAAE;YACzC,OAAO;cACLob,SAAS,EAAE,CAAC,CAAC;EACbsE,YAAAA,gBAAgB,EAAE,IAAA;aACnB,CAAA;EACH,SAAA;EACF,OAAA;EACF,KAAA;EACA,IAAA,MAAMgD,YAAY,GAAGL,WAAW,CAAC1N,EAAE,CAAC,CAAA;MACpC,IAAI+N,YAAY,KAAK1iB,SAAS;EAC5B;QACA,OAAO;EAAEob,QAAAA,SAAS,EAAE,CAAC,CAAA;SAAG,CAAA;EAC1B,IAAA,MAAMA,SAAS,GAAGqD,IAAI,CAACkE,SAAS,CAAEtW,CAAC,IAAKA,CAAC,CAACjJ,GAAG,KAAKsf,YAAY,CAAC,CAAA;MAC/D,IAAItH,SAAS,GAAG,CAAC;EACf;QACA,OAAO;EAAEA,QAAAA,SAAS,EAAE,CAAC,CAAA;SAAG,CAAA;MAE1B,OAAO;EAAEA,MAAAA,SAAAA;OAAW,CAAA;EACtB,GAAA;EAEQgF,EAAAA,6BAA6B,CACnCkC,aAAqB,EACrBC,uBAAgC,EACX;MACrBA,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC,CAAA;MACtD,OAAUD,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,eAAKC,uBAAuB,CAAA,CAAA;EACrD,GAAA;IAEQP,0BAA0B,CAChChf,IAAyB,EACD;MACxB,MAAMuC,UAAkC,GAAG,EAAE,CAAA;MAC7C,IAAI,CAACuS,IAAI,CAAC8K,gCAAgC,GAAG,CAAC,IAAI,CAAC9K,IAAI,CACpD8K,gCAAgC,GAC/B,IAAI,CAACb,uCAAuC,CAAC/e,IAAI,CAAC,GAClD,IAAI,CAAC8U,IAAI,CAAC8K,gCAAgC,CAAA;MAC9C,IAAI,CAAC9K,IAAI,CAAC8K,gCAAgC,CAACjf,OAAO,CAAE8G,IAAI,IAAK;QAC3D,MAAM;EAAEmH,QAAAA,SAAAA;EAAU,OAAC,GAAG,IAAI,CAACgN,iBAAiB,CAACnU,IAAI,CAAC,CAAA;EAClDlF,MAAAA,UAAU,CAACkF,IAAI,CAAC,GAAGoL,QAAQ,CAACjE,SAAS,CAAC,CAAA;EACxC,KAAC,CAAC,CAAA;EACF,IAAA,OAAOrM,UAAU,CAAA;EACnB,GAAA;EAEQ4a,EAAAA,kCAAkC,CACxC0C,aAAqB,EACrBC,cAAsB,EACtBT,WAA8B,EAK9B;EACA,IAAA,MAAMjf,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMyf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;EACjD,IAAA,MAAMC,mBAAmB,GACvB,IAAI,CAACjL,IAAI,CAAC8C,0BAA0B,IACpC,IAAI,CAAC9C,IAAI,CAAC8C,0BAA0B,CAACxX,GAAG,CAAC,GACrC,IAAI,CAAC0U,IAAI,CAAC8C,0BAA0B,CAACxX,GAAG,CAAC,CAACif,WAAW,IAAI,EAAE,GAC3D,EAAE,CAAA;EACR,IAAA,MAAMW,cAAc,GAAG;EAAE,MAAA,GAAGD,mBAAmB;QAAE,GAAGV,WAAAA;OAAa,CAAA;EACjE,IAAA,MAAMrC,OAAO,GACXjf,IAAI,CAACC,SAAS,CAAC+hB,mBAAmB,CAAC,KAAKhiB,IAAI,CAACC,SAAS,CAACgiB,cAAc,CAAC,CAAA;MAExE,OAAO;QACL5f,GAAG;EACH8c,MAAAA,GAAG,EAAE;UACH2C,aAAa;UACbC,cAAc;EACdT,QAAAA,WAAW,EAAEW,cAAAA;SACd;EACDhD,MAAAA,OAAAA;OACD,CAAA;EACH,GAAA;EACF;;ECr5CA,MAAMiD,OAAO,GAAG,MAAM;IACpB,MAAMC,WAAW,GAAG,QAAQ,CAAA;EAC5B,EAAA,MAAMC,WAAW,GAAG,GAAG,CAAC;;EAExB;IACA,MAAMC,OAAO,GAAG,MAAM;EACpB,IAAA,IAAI/hB,MAAM,CAACnB,MAAM,IAAIA,MAAM,CAACmjB,UAAU,EAAE,OAAOnjB,MAAM,CAACmjB,UAAU,EAAE,CAAA;MAClE,OAAO,CAAC,EAAE,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,EAAElR,OAAO,CAAC,QAAQ,EAAGvB,CAAC,IACjE,CACIA,CAAC,GACF1Q,MAAM,CAACojB,eAAe,CAAC,IAAItO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAC1C,EAAE,IAAOpE,CAAC,GAA0B,CAAI,EAC3CiF,QAAQ,CAAC,EAAE,CAAC,CACf,CAAA;KACF,CAAA;IACD,MAAM0N,SAAS,GAAIhO,IAAY,IAAa;EAC1C,IAAA,MAAM7O,KAAK,GAAG,IAAI,GAAGpF,QAAQ,CAACkiB,MAAM,CAAA;EACpC,IAAA,MAAMxN,KAAK,GAAGtP,KAAK,CAAC+F,KAAK,CAAA,IAAA,CAAA,MAAA,CAAM8I,IAAI,EAAI,GAAA,CAAA,CAAA,CAAA;EACvC,IAAA,OAAOS,KAAK,CAAC5G,MAAM,KAAK,CAAC,GAAG4G,KAAK,CAAC,CAAC,CAAC,CAACvJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAA;KACxD,CAAA;EACD,EAAA,MAAMgX,SAAS,GAAG,CAAClO,IAAY,EAAE7O,KAAa,KAAK;EACjD,IAAA,MAAMgd,CAAC,GAAG,IAAIjf,IAAI,EAAE,CAAA;EACpBif,IAAAA,CAAC,CAACC,OAAO,CAACD,CAAC,CAAC/e,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,GAAGwe,WAAW,CAAC,CAAA;EAC1D7hB,IAAAA,QAAQ,CAACkiB,MAAM,GAAGjO,IAAI,GAAG,GAAG,GAAG7O,KAAK,GAAG,kBAAkB,GAAGgd,CAAC,CAACE,WAAW,EAAE,CAAA;KAC5E,CAAA;;EAED;IACA,IAAIL,SAAS,CAACL,WAAW,CAAC,EAAE,OAAOK,SAAS,CAACL,WAAW,CAAC,CAAA;IAEzD,MAAMW,IAAI,GAAGT,OAAO,EAAE,CAAA;EACtBK,EAAAA,SAAS,CAACP,WAAW,EAAEW,IAAI,CAAC,CAAA;EAC5B,EAAA,OAAOA,IAAI,CAAA;EACb,CAAC,CAAA;EAED,SAASC,gBAAgB,GAAG;EAC1B;IACA,IAAIC,IAA4B,GAAG,EAAE,CAAA;IACrC,IAAI;EACF,IAAA,MAAMlf,QAAQ,GAAGmf,cAAc,CAACrd,OAAO,CAAC,YAAY,CAAC,CAAA;EACrD,IAAA,IAAI9B,QAAQ,EAAE;EACZkf,MAAAA,IAAI,GAAGhjB,IAAI,CAAC8F,KAAK,CAAChC,QAAQ,CAAC,CAAA;EAC7B,KAAA;;EAEA;MACA,IAAIqc,QAAQ,CAACtM,MAAM,EAAE;QACnB,MAAMqP,MAAM,GAAG,IAAIC,eAAe,CAAChD,QAAQ,CAACtM,MAAM,CAAC,CAAA;QACnD,IAAIuP,UAAU,GAAG,KAAK,CAAA;EACtB,MAAA,CAAC,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC,CAACxgB,OAAO,CAAE+P,CAAC,IAAK;EACjE;UACA,MAAM0Q,KAAK,GAAU1Q,MAAAA,CAAAA,MAAAA,CAAAA,CAAC,CAAE,CAAA;EACxB;EACA,QAAA,MAAMjJ,IAAI,GAAG,KAAA,GAAQiJ,CAAC,CAAC,CAAC,CAAC,CAAC2Q,WAAW,EAAE,GAAG3Q,CAAC,CAAC4Q,KAAK,CAAC,CAAC,CAAC,CAAA;EAEpD,QAAA,IAAIL,MAAM,CAAClc,GAAG,CAACqc,KAAK,CAAC,EAAE;YACrBL,IAAI,CAACtZ,IAAI,CAAC,GAAGwZ,MAAM,CAAC1gB,GAAG,CAAC6gB,KAAK,CAAC,IAAI,EAAE,CAAA;EACpCD,UAAAA,UAAU,GAAG,IAAI,CAAA;EACnB,SAAA;EACF,OAAC,CAAC,CAAA;;EAEF;EACA,MAAA,IAAIA,UAAU,EAAE;UACdH,cAAc,CAAC7f,OAAO,CAAC,YAAY,EAAEpD,IAAI,CAACC,SAAS,CAAC+iB,IAAI,CAAC,CAAC,CAAA;EAC5D,OAAA;EACF,KAAA;KACD,CAAC,OAAO9hB,CAAC,EAAE;EACV;EAAA,GAAA;EAGF,EAAA,OAAO8hB,IAAI,CAAA;EACb,CAAA;EAEA,SAASQ,iBAAiB,GAAG;EAC3B,EAAA,MAAMC,EAAE,GAAGC,SAAS,CAACC,SAAS,CAAA;EAE9B,EAAA,MAAMC,OAAO,GAAGH,EAAE,CAAC7R,KAAK,CAAC,KAAK,CAAC,GAC3B,MAAM,GACN6R,EAAE,CAAC7R,KAAK,CAAC,QAAQ,CAAC,GAClB,QAAQ,GACR6R,EAAE,CAAC7R,KAAK,CAAC,SAAS,CAAC,GACnB,SAAS,GACT6R,EAAE,CAAC7R,KAAK,CAAC,QAAQ,CAAC,GAClB,QAAQ,GACR,SAAS,CAAA;IAEb,OAAO;MACLgC,EAAE,EAAEsO,OAAO,EAAE;MACbjd,GAAG,EAAEkb,QAAQ,CAACtN,IAAI;MAClB+C,IAAI,EAAEuK,QAAQ,CAAC3N,QAAQ;MACvBhT,IAAI,EAAE2gB,QAAQ,CAAC3gB,IAAI;MACnBqkB,KAAK,EAAE1D,QAAQ,CAACtM,MAAM;MACtBiQ,UAAU,EAAEL,EAAE,CAAC7R,KAAK,CAAC,MAAM,CAAC,GAAG,QAAQ,GAAG,SAAS;MACnDgS,OAAO;EACP,IAAA,GAAGb,gBAAgB,EAAA;KACpB,CAAA;EACH,CAAA;;EAEA;EACAziB,MAAM,CAACyjB,SAAS,GAAGzjB,MAAM,CAACyjB,SAAS,IAAI,EAAE,CAAA;EAEzC,MAAMC,aAAa,GAAGzjB,QAAQ,CAACyjB,aAAa,CAAA;EAC5C,MAAMC,WAAW,GAAGD,aAAa,GAAGA,aAAa,CAACE,OAAO,GAAG,EAAE,CAAA;EAC9D,MAAMC,aAAa,GAAG7jB,MAAM,CAAC8jB,iBAAiB,IAAI,EAAE,CAAA;EAEpD,SAAS3f,aAAa,GAAG;EACvB;IACA,MAAMD,UAAU,GAAGyf,WAAW,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAGT,iBAAiB,EAAE,CAAA;IAC7E,IAAIW,aAAa,CAAC3f,UAAU,EAAE;MAC5BI,MAAM,CAACgb,MAAM,CAACpb,UAAU,EAAE2f,aAAa,CAAC3f,UAAU,CAAC,CAAA;EACrD,GAAA;EACA,EAAA,OAAOA,UAAU,CAAA;EACnB,CAAA;;EAEA;AACA,QAAM6f,EAAE,GAAG,IAAIzN,UAAU,CAAC;EACxB,EAAA,GAAGqN,WAAW;EACdpc,EAAAA,UAAU,EAAE,CAAC,CAACoc,WAAW,CAACpc,UAAU;EACpC2Q,EAAAA,kBAAkB,EAAE,IAAI;EACxBkH,EAAAA,gBAAgB,EAAE,CAACxe,CAAC,EAAE4c,CAAC,KAAK;EAC1B,IAAA,MAAMwG,CAAC,GAAG;QAAEC,aAAa,EAAErjB,CAAC,CAACmB,GAAG;QAAEmiB,YAAY,EAAE1G,CAAC,CAACzb,GAAAA;OAAK,CAAA;EACvD/B,IAAAA,MAAM,CAACyjB,SAAS,CAACrV,IAAI,CAAC,CAAC,OAAO,EAAE,mBAAmB,EAAE4V,CAAC,CAAC,CAAC,CAAA;EACxDhkB,IAAAA,MAAM,CAACmkB,SAAS,IACdnkB,MAAM,CAACmkB,SAAS,CAACC,KAAK,IACtBpkB,MAAM,CAACmkB,SAAS,CAACC,KAAK,CAAC,mBAAmB,EAAEJ,CAAC,CAAC,CAAA;KACjD;EACD,EAAA,GAAGH,aAAa;EAChB3f,EAAAA,UAAU,EAAEC,aAAa,EAAA;EAC3B,CAAC,EAAC;;EAEF;EACA4f,EAAE,CAAC/L,YAAY,EAAE,CAAA;;EAEjB;EACA,IAAIqM,UAAU,GAAGxE,QAAQ,CAACtN,IAAI,CAAA;EAC9B+R,WAAW,CAAC,MAAM;EAChB,EAAA,IAAIzE,QAAQ,CAACtN,IAAI,KAAK8R,UAAU,EAAE;MAChCA,UAAU,GAAGxE,QAAQ,CAACtN,IAAI,CAAA;EAC1BwR,IAAAA,EAAE,CAAC1K,MAAM,CAACgL,UAAU,CAAC,CAAA;MACrBN,EAAE,CAAClL,aAAa,CAAC;QACf,GAAGkL,EAAE,CAAC5f,aAAa,EAAE;EACrB,MAAA,GAAGA,aAAa,EAAA;EAClB,KAAC,CAAC,CAAA;EACJ,GAAA;EACF,CAAC,EAAE,GAAG,CAAC;;;;;;;;"}