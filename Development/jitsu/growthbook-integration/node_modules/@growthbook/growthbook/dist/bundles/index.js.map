{"version": 3, "file": "index.js", "sources": ["../../src/feature-repository.ts", "../../../../node_modules/dom-mutator/dist/dom-mutator.esm.js", "../../src/util.ts", "../../src/mongrule.ts", "../../src/GrowthBook.ts", "../../src/sticky-bucket-service.ts"], "sourcesContent": ["import {\n  Attributes,\n  CacheSettings,\n  FeatureApiResponse,\n  Helpers,\n  Polyfills,\n} from \"./types/growthbook\";\nimport type { GrowthBook } from \".\";\n\ntype CacheEntry = {\n  data: FeatureApiResponse;\n  sse?: boolean;\n  version: string;\n  staleAt: Date;\n};\ntype ScopedChannel = {\n  src: EventSource | null;\n  cb: (event: MessageEvent<string>) => void;\n  host: string;\n  clientKey: string;\n  headers?: Record<string, string>;\n  errors: number;\n  state: \"active\" | \"idle\" | \"disabled\";\n};\n\n// Config settings\nconst cacheSettings: CacheSettings = {\n  // Consider a fetch stale after 1 minute\n  staleTTL: 1000 * 60,\n  // Max time to keep a fetch in cache (24 hours default)\n  maxAge: 1000 * 60 * 60 * 24,\n  cacheKey: \"gbFeaturesCache\",\n  backgroundSync: true,\n  maxEntries: 10,\n  disableIdleStreams: false,\n  idleStreamInterval: 20000,\n};\nconst polyfills: Polyfills = {\n  fetch: globalThis.fetch ? globalThis.fetch.bind(globalThis) : undefined,\n  SubtleCrypto: globalThis.crypto ? globalThis.crypto.subtle : undefined,\n  EventSource: globalThis.EventSource,\n};\nexport const helpers: Helpers = {\n  fetchFeaturesCall: ({ host, clientKey, headers }) => {\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/features/${clientKey}`,\n      { headers }\n    );\n  },\n  fetchRemoteEvalCall: ({ host, clientKey, payload, headers }) => {\n    const options = {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\", ...headers },\n      body: JSON.stringify(payload),\n    };\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/eval/${clientKey}`,\n      options\n    );\n  },\n  eventSourceCall: ({ host, clientKey, headers }) => {\n    if (headers) {\n      return new polyfills.EventSource(`${host}/sub/${clientKey}`, {\n        headers,\n      });\n    }\n    return new polyfills.EventSource(`${host}/sub/${clientKey}`);\n  },\n  startIdleListener: () => {\n    let idleTimeout: number | undefined;\n    const isBrowser =\n      typeof window !== \"undefined\" && typeof document !== \"undefined\";\n    if (!isBrowser) return;\n    const onVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        window.clearTimeout(idleTimeout);\n        onVisible();\n      } else if (document.visibilityState === \"hidden\") {\n        idleTimeout = window.setTimeout(\n          onHidden,\n          cacheSettings.idleStreamInterval\n        );\n      }\n    };\n    document.addEventListener(\"visibilitychange\", onVisibilityChange);\n    return () =>\n      document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n  },\n  stopIdleListener: () => {\n    // No-op, replaced by startIdleListener\n  },\n};\n\ntry {\n  if (globalThis.localStorage) {\n    polyfills.localStorage = globalThis.localStorage;\n  }\n} catch (e) {\n  // Ignore localStorage errors\n}\n\n// Global state\nconst subscribedInstances: Map<string, Set<GrowthBook>> = new Map();\nlet cacheInitialized = false;\nconst cache: Map<string, CacheEntry> = new Map();\nconst activeFetches: Map<string, Promise<FeatureApiResponse>> = new Map();\nconst streams: Map<string, ScopedChannel> = new Map();\nconst supportsSSE: Set<string> = new Set();\n\n// Public functions\nexport function setPolyfills(overrides: Partial<Polyfills>): void {\n  Object.assign(polyfills, overrides);\n}\nexport function configureCache(overrides: Partial<CacheSettings>): void {\n  Object.assign(cacheSettings, overrides);\n  if (!cacheSettings.backgroundSync) {\n    clearAutoRefresh();\n  }\n}\n\nexport async function clearCache(): Promise<void> {\n  cache.clear();\n  activeFetches.clear();\n  clearAutoRefresh();\n  cacheInitialized = false;\n  await updatePersistentCache();\n}\n\nexport async function refreshFeatures(\n  instance: GrowthBook,\n  timeout?: number,\n  skipCache?: boolean,\n  allowStale?: boolean,\n  updateInstance?: boolean,\n  backgroundSync?: boolean\n): Promise<void> {\n  if (!backgroundSync) {\n    cacheSettings.backgroundSync = false;\n  }\n\n  const data = await fetchFeaturesWithCache(\n    instance,\n    allowStale,\n    timeout,\n    skipCache\n  );\n  updateInstance && data && (await refreshInstance(instance, data));\n}\n\n// Subscribe a GrowthBook instance to feature changes\nexport function subscribe(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const subs = subscribedInstances.get(key) || new Set();\n  subs.add(instance);\n  subscribedInstances.set(key, subs);\n}\nexport function unsubscribe(instance: GrowthBook): void {\n  subscribedInstances.forEach((s) => s.delete(instance));\n}\n\nexport function onHidden() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    channel.state = \"idle\";\n    disableChannel(channel);\n  });\n}\n\nexport function onVisible() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    if (channel.state !== \"idle\") return;\n    enableChannel(channel);\n  });\n}\n\n// Private functions\n\nasync function updatePersistentCache() {\n  try {\n    if (!polyfills.localStorage) return;\n    await polyfills.localStorage.setItem(\n      cacheSettings.cacheKey,\n      JSON.stringify(Array.from(cache.entries()))\n    );\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n}\n\nasync function fetchFeaturesWithCache(\n  instance: GrowthBook,\n  allowStale?: boolean,\n  timeout?: number,\n  skipCache?: boolean\n): Promise<FeatureApiResponse | null> {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const now = new Date();\n\n  const minStaleAt = new Date(\n    now.getTime() - cacheSettings.maxAge + cacheSettings.staleTTL\n  );\n\n  await initializeCache();\n  const existing = cache.get(cacheKey);\n  if (\n    existing &&\n    !skipCache &&\n    (allowStale || existing.staleAt > now) &&\n    existing.staleAt > minStaleAt\n  ) {\n    // Restore from cache whether SSE is supported\n    if (existing.sse) supportsSSE.add(key);\n\n    // Reload features in the background if stale\n    if (existing.staleAt < now) {\n      fetchFeatures(instance);\n    }\n    // Otherwise, if we don't need to refresh now, start a background sync\n    else {\n      startAutoRefresh(instance);\n    }\n    return existing.data;\n  } else {\n    return await promiseTimeout(fetchFeatures(instance), timeout);\n  }\n}\n\nfunction getKey(instance: GrowthBook): string {\n  const [apiHost, clientKey] = instance.getApiInfo();\n  return `${apiHost}||${clientKey}`;\n}\n\nfunction getCacheKey(instance: GrowthBook): string {\n  const baseKey = getKey(instance);\n  if (!instance.isRemoteEval()) return baseKey;\n\n  const attributes = instance.getAttributes();\n  const cacheKeyAttributes =\n    instance.getCacheKeyAttributes() || Object.keys(instance.getAttributes());\n  const ca: Attributes = {};\n  cacheKeyAttributes.forEach((key) => {\n    ca[key] = attributes[key];\n  });\n\n  const fv = instance.getForcedVariations();\n  const url = instance.getUrl();\n\n  return `${baseKey}||${JSON.stringify({\n    ca,\n    fv,\n    url,\n  })}`;\n}\n\n// Guarantee the promise always resolves within {timeout} ms\n// Resolved value will be `null` when there's an error or it takes too long\n// Note: The promise will continue running in the background, even if the timeout is hit\nfunction promiseTimeout<T>(\n  promise: Promise<T>,\n  timeout?: number\n): Promise<T | null> {\n  return new Promise((resolve) => {\n    let resolved = false;\n    let timer: unknown;\n    const finish = (data?: T) => {\n      if (resolved) return;\n      resolved = true;\n      timer && clearTimeout(timer as NodeJS.Timer);\n      resolve(data || null);\n    };\n\n    if (timeout) {\n      timer = setTimeout(() => finish(), timeout);\n    }\n\n    promise.then((data) => finish(data)).catch(() => finish());\n  });\n}\n\n// Populate cache from localStorage (if available)\nasync function initializeCache(): Promise<void> {\n  if (cacheInitialized) return;\n  cacheInitialized = true;\n  try {\n    if (polyfills.localStorage) {\n      const value = await polyfills.localStorage.getItem(\n        cacheSettings.cacheKey\n      );\n      if (value) {\n        const parsed: [string, CacheEntry][] = JSON.parse(value);\n        if (parsed && Array.isArray(parsed)) {\n          parsed.forEach(([key, data]) => {\n            cache.set(key, {\n              ...data,\n              staleAt: new Date(data.staleAt),\n            });\n          });\n        }\n        cleanupCache();\n      }\n    }\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n  if (!cacheSettings.disableIdleStreams) {\n    const cleanupFn = helpers.startIdleListener();\n    if (cleanupFn) {\n      helpers.stopIdleListener = cleanupFn;\n    }\n  }\n}\n\n// Enforce the maxEntries limit\nfunction cleanupCache() {\n  const entriesWithTimestamps = Array.from(cache.entries())\n    .map(([key, value]) => ({\n      key,\n      staleAt: value.staleAt.getTime(),\n    }))\n    .sort((a, b) => a.staleAt - b.staleAt);\n\n  const entriesToRemoveCount = Math.min(\n    Math.max(0, cache.size - cacheSettings.maxEntries),\n    cache.size\n  );\n\n  for (let i = 0; i < entriesToRemoveCount; i++) {\n    cache.delete(entriesWithTimestamps[i].key);\n  }\n}\n\n// Called whenever new features are fetched from the API\nfunction onNewFeatureData(\n  key: string,\n  cacheKey: string,\n  data: FeatureApiResponse\n): void {\n  // If contents haven't changed, ignore the update, extend the stale TTL\n  const version = data.dateUpdated || \"\";\n  const staleAt = new Date(Date.now() + cacheSettings.staleTTL);\n  const existing = cache.get(cacheKey);\n  if (existing && version && existing.version === version) {\n    existing.staleAt = staleAt;\n    updatePersistentCache();\n    return;\n  }\n\n  // Update in-memory cache\n  cache.set(cacheKey, {\n    data,\n    version,\n    staleAt,\n    sse: supportsSSE.has(key),\n  });\n  cleanupCache();\n  // Update local storage (don't await this, just update asynchronously)\n  updatePersistentCache();\n\n  // Update features for all subscribed GrowthBook instances\n  const instances = subscribedInstances.get(key);\n  instances && instances.forEach((instance) => refreshInstance(instance, data));\n}\n\nasync function refreshInstance(\n  instance: GrowthBook,\n  data: FeatureApiResponse\n): Promise<void> {\n  data = await instance.decryptPayload(data, undefined, polyfills.SubtleCrypto);\n\n  await instance.refreshStickyBuckets(data);\n  instance.setExperiments(data.experiments || instance.getExperiments());\n  instance.setFeatures(data.features || instance.getFeatures());\n}\n\nasync function fetchFeatures(\n  instance: GrowthBook\n): Promise<FeatureApiResponse> {\n  const { apiHost, apiRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  const remoteEval = instance.isRemoteEval();\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n\n  let promise = activeFetches.get(cacheKey);\n  if (!promise) {\n    const fetcher: Promise<Response> = remoteEval\n      ? helpers.fetchRemoteEvalCall({\n          host: apiHost,\n          clientKey,\n          payload: {\n            attributes: instance.getAttributes(),\n            forcedVariations: instance.getForcedVariations(),\n            forcedFeatures: Array.from(instance.getForcedFeatures().entries()),\n            url: instance.getUrl(),\n          },\n          headers: apiRequestHeaders,\n        })\n      : helpers.fetchFeaturesCall({\n          host: apiHost,\n          clientKey,\n          headers: apiRequestHeaders,\n        });\n\n    // TODO: auto-retry if status code indicates a temporary error\n    promise = fetcher\n      .then((res) => {\n        if (res.headers.get(\"x-sse-support\") === \"enabled\") {\n          supportsSSE.add(key);\n        }\n        return res.json();\n      })\n      .then((data: FeatureApiResponse) => {\n        onNewFeatureData(key, cacheKey, data);\n        startAutoRefresh(instance);\n        activeFetches.delete(cacheKey);\n        return data;\n      })\n      .catch((e) => {\n        process.env.NODE_ENV !== \"production\" &&\n          instance.log(\"Error fetching features\", {\n            apiHost,\n            clientKey,\n            error: e ? e.message : null,\n          });\n        activeFetches.delete(cacheKey);\n        return Promise.resolve({});\n      });\n    activeFetches.set(cacheKey, promise);\n  }\n  return await promise;\n}\n\n// Watch a feature endpoint for changes\n// Will prefer SSE if enabled, otherwise fall back to cron\nfunction startAutoRefresh(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const { streamingHost, streamingHostRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  if (\n    cacheSettings.backgroundSync &&\n    supportsSSE.has(key) &&\n    polyfills.EventSource\n  ) {\n    if (streams.has(key)) return;\n    const channel: ScopedChannel = {\n      src: null,\n      host: streamingHost,\n      clientKey,\n      headers: streamingHostRequestHeaders,\n      cb: (event: MessageEvent<string>) => {\n        try {\n          if (event.type === \"features-updated\") {\n            const instances = subscribedInstances.get(key);\n            instances &&\n              instances.forEach((instance) => {\n                fetchFeatures(instance);\n              });\n          } else if (event.type === \"features\") {\n            const json: FeatureApiResponse = JSON.parse(event.data);\n            onNewFeatureData(key, cacheKey, json);\n          }\n          // Reset error count on success\n          channel.errors = 0;\n        } catch (e) {\n          process.env.NODE_ENV !== \"production\" &&\n            instance.log(\"SSE Error\", {\n              streamingHost,\n              clientKey,\n              error: e ? (e as Error).message : null,\n            });\n          onSSEError(channel);\n        }\n      },\n      errors: 0,\n      state: \"active\",\n    };\n    streams.set(key, channel);\n    enableChannel(channel);\n  }\n}\n\nfunction onSSEError(channel: ScopedChannel) {\n  if (channel.state === \"idle\") return;\n  channel.errors++;\n  if (channel.errors > 3 || (channel.src && channel.src.readyState === 2)) {\n    // exponential backoff after 4 errors, with jitter\n    const delay =\n      Math.pow(3, channel.errors - 3) * (1000 + Math.random() * 1000);\n    disableChannel(channel);\n    setTimeout(() => {\n      if ([\"idle\", \"active\"].includes(channel.state)) return;\n      enableChannel(channel);\n    }, Math.min(delay, 300000)); // 5 minutes max\n  }\n}\n\nfunction disableChannel(channel: ScopedChannel) {\n  if (!channel.src) return;\n  channel.src.onopen = null;\n  channel.src.onerror = null;\n  channel.src.close();\n  channel.src = null;\n  if (channel.state === \"active\") {\n    channel.state = \"disabled\";\n  }\n}\n\nfunction enableChannel(channel: ScopedChannel) {\n  channel.src = helpers.eventSourceCall({\n    host: channel.host,\n    clientKey: channel.clientKey,\n    headers: channel.headers,\n  }) as EventSource;\n  channel.state = \"active\";\n  channel.src.addEventListener(\"features\", channel.cb);\n  channel.src.addEventListener(\"features-updated\", channel.cb);\n  channel.src.onerror = () => onSSEError(channel);\n  channel.src.onopen = () => {\n    channel.errors = 0;\n  };\n}\n\nfunction destroyChannel(channel: ScopedChannel, key: string) {\n  disableChannel(channel);\n  streams.delete(key);\n}\n\nfunction clearAutoRefresh() {\n  // Clear list of which keys are auto-updated\n  supportsSSE.clear();\n\n  // Stop listening for any SSE events\n  streams.forEach(destroyChannel);\n\n  // Remove all references to GrowthBook instances\n  subscribedInstances.clear();\n\n  // Run the idle stream cleanup function\n  helpers.stopIdleListener();\n}\n", "var validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nvar nullController = {\n  revert: function revert() {}\n};\nvar elements = /*#__PURE__*/new Map();\nvar mutations = /*#__PURE__*/new Set();\n\nfunction getObserverInit(attr) {\n  return attr === 'html' ? {\n    childList: true,\n    subtree: true,\n    attributes: true,\n    characterData: true\n  } : {\n    childList: false,\n    subtree: false,\n    attributes: true,\n    attributeFilter: [attr]\n  };\n}\n\nfunction getElementRecord(element) {\n  var record = elements.get(element);\n\n  if (!record) {\n    record = {\n      element: element,\n      attributes: {}\n    };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(el, attr, getCurrentValue, setValue, mutationRunner) {\n  var currentValue = getCurrentValue(el);\n  var record = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el: el,\n    _positionTimeout: null,\n    observer: new MutationObserver(function () {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;else if (attr === 'position') record._positionTimeout = setTimeout(function () {\n        record._positionTimeout = null;\n      }, 1000);\n      var currentValue = getCurrentValue(el);\n      if (attr === 'position' && currentValue.parentNode === record.virtualValue.parentNode && currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode) return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner: mutationRunner,\n    setValue: setValue,\n    getCurrentValue: getCurrentValue\n  };\n\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n\n  return record;\n}\n\nfunction queueIfNeeded(val, record) {\n  var currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n\n  if (val && typeof val !== 'string') {\n    if (!currentVal || val.parentNode !== currentVal.parentNode || val.insertBeforeNode !== currentVal.insertBeforeNode) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(getTransformedHTML(val), record);\n}\n\nfunction classMutationRunner(record) {\n  var val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(function (m) {\n    return m.mutate(val);\n  });\n  queueIfNeeded(Array.from(val).filter(Boolean).join(' '), record);\n}\n\nfunction attrMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes(_ref) {\n  var parentSelector = _ref.parentSelector,\n      insertBeforeSelector = _ref.insertBeforeSelector;\n  var parentNode = document.querySelector(parentSelector);\n  if (!parentNode) return null;\n  var insertBeforeNode = insertBeforeSelector ? document.querySelector(insertBeforeSelector) : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode: parentNode,\n    insertBeforeNode: insertBeforeNode\n  };\n}\n\nfunction positionMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    var selectors = m.mutate();\n\n    var newNodes = _loadDOMNodes(selectors);\n\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nvar getHTMLValue = function getHTMLValue(el) {\n  return el.innerHTML;\n};\n\nvar setHTMLValue = function setHTMLValue(el, value) {\n  return el.innerHTML = value;\n};\n\nfunction getElementHTMLRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(element, 'html', getHTMLValue, setHTMLValue, htmlMutationRunner);\n  }\n\n  return elementRecord.html;\n}\n\nvar getElementPosition = function getElementPosition(el) {\n  return {\n    parentNode: el.parentElement,\n    insertBeforeNode: el.nextElementSibling\n  };\n};\n\nvar setElementPosition = function setElementPosition(el, value) {\n  if (value.insertBeforeNode && !value.parentNode.contains(value.insertBeforeNode)) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\n\nfunction getElementPositionRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(element, 'position', getElementPosition, setElementPosition, positionMutationRunner);\n  }\n\n  return elementRecord.position;\n}\n\nvar setClassValue = function setClassValue(el, val) {\n  return val ? el.className = val : el.removeAttribute('class');\n};\n\nvar getClassValue = function getClassValue(el) {\n  return el.className;\n};\n\nfunction getElementClassRecord(el) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(el, 'class', getClassValue, setClassValue, classMutationRunner);\n  }\n\n  return elementRecord.classes;\n}\n\nvar getAttrValue = function getAttrValue(attrName) {\n  return function (el) {\n    var _el$getAttribute;\n\n    return (_el$getAttribute = el.getAttribute(attrName)) != null ? _el$getAttribute : null;\n  };\n};\n\nvar setAttrValue = function setAttrValue(attrName) {\n  return function (el, val) {\n    return val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\n  };\n};\n\nfunction getElementAttributeRecord(el, attr) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(el, attr, getAttrValue(attr), setAttrValue(attr), attrMutationRunner);\n  }\n\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el, attr) {\n  var element = elements.get(el);\n  if (!element) return;\n\n  if (attr === 'html') {\n    var _element$html, _element$html$observe;\n\n    (_element$html = element.html) == null ? void 0 : (_element$html$observe = _element$html.observer) == null ? void 0 : _element$html$observe.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    var _element$classes, _element$classes$obse;\n\n    (_element$classes = element.classes) == null ? void 0 : (_element$classes$obse = _element$classes.observer) == null ? void 0 : _element$classes$obse.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    var _element$position, _element$position$obs;\n\n    (_element$position = element.position) == null ? void 0 : (_element$position$obs = _element$position.observer) == null ? void 0 : _element$position$obs.disconnect();\n    delete element.position;\n  } else {\n    var _element$attributes, _element$attributes$a, _element$attributes$a2;\n\n    (_element$attributes = element.attributes) == null ? void 0 : (_element$attributes$a = _element$attributes[attr]) == null ? void 0 : (_element$attributes$a2 = _element$attributes$a.observer) == null ? void 0 : _element$attributes$a2.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nvar transformContainer;\n\nfunction getTransformedHTML(html) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue(el, attr, m) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  var val = m.virtualValue;\n\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n\n  m.setValue(el, val);\n}\n\nfunction setValue(m, el) {\n  m.html && setPropertyValue(el, 'html', m.html);\n  m.classes && setPropertyValue(el, 'class', m.classes);\n  m.position && setPropertyValue(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(function (attr) {\n    setPropertyValue(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n} // find or create ElementPropertyRecord, add mutation to it, then run\n\n\nfunction startMutating(mutation, element) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n} // get (existing) ElementPropertyRecord, remove mutation from it, then run\n\n\nfunction stopMutating(mutation, el) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n\n  if (!record) return;\n  var index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n} // maintain list of elements associated with mutation\n\n\nfunction refreshElementsSet(mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n  var existingElements = new Set(mutation.elements);\n  var matchingElements = document.querySelectorAll(mutation.selector);\n  matchingElements.forEach(function (el) {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation) {\n  mutation.elements.forEach(function (el) {\n    return stopMutating(mutation, el);\n  });\n  mutation.elements.clear();\n  mutations[\"delete\"](mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n} // Observer for elements that don't exist in the DOM yet\n\n\nvar observer;\nfunction disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nfunction connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(function () {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false\n  });\n} // run on init\n\nconnectGlobalObserver();\n\nfunction newMutation(m) {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController; // add to global index of mutations\n\n  mutations.add(m); // run refresh on init to establish list of elements associated w/ mutation\n\n  refreshElementsSet(m);\n  return {\n    revert: function revert() {\n      revertMutation(m);\n    }\n  };\n}\n\nfunction html(selector, mutate) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction position(selector, mutate) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction classes(selector, mutate) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction attribute(selector, attribute, mutate) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, function (classnames) {\n      var mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames.split(/\\s+/g).filter(Boolean).forEach(function (c) {\n        return classnames.add(c);\n      });\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute: attribute,\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction declarative(_ref2) {\n  var selector = _ref2.selector,\n      action = _ref2.action,\n      value = _ref2.value,\n      attr = _ref2.attribute,\n      parentSelector = _ref2.parentSelector,\n      insertBeforeSelector = _ref2.insertBeforeSelector;\n\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, function (val) {\n        return val + (value != null ? value : '');\n      });\n    } else if (action === 'set') {\n      return html(selector, function () {\n        return value != null ? value : '';\n      });\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, function (val) {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, function (val) {\n        if (value) val[\"delete\"](value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, function (val) {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, function () {\n        return {\n          insertBeforeSelector: insertBeforeSelector,\n          parentSelector: parentSelector\n        };\n      });\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, function (val) {\n        return val !== null ? val + (value != null ? value : '') : value != null ? value : '';\n      });\n    } else if (action === 'set') {\n      return attribute(selector, attr, function () {\n        return value != null ? value : '';\n      });\n    } else if (action === 'remove') {\n      return attribute(selector, attr, function () {\n        return null;\n      });\n    }\n  }\n\n  return nullController;\n}\n\nvar index = {\n  html: html,\n  classes: classes,\n  attribute: attribute,\n  position: position,\n  declarative: declarative\n};\n\nexport default index;\nexport { connectGlobalObserver, disconnectGlobalObserver, validAttributeName };\n//# sourceMappingURL=dom-mutator.esm.js.map\n", "import { UrlTarget, UrlTargetType, VariationRange } from \"./types/growthbook\";\n\nfunction hashFnv32a(str: string): number {\n  let hval = 0x811c9dc5;\n  const l = str.length;\n\n  for (let i = 0; i < l; i++) {\n    hval ^= str.charCodeAt(i);\n    hval +=\n      (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);\n  }\n  return hval >>> 0;\n}\n\nexport function hash(\n  seed: string,\n  value: string,\n  version: number\n): number | null {\n  // New unbiased hashing algorithm\n  if (version === 2) {\n    return (hashFnv32a(hashFnv32a(seed + value) + \"\") % 10000) / 10000;\n  }\n  // Original biased hashing algorithm (keep for backwards compatibility)\n  if (version === 1) {\n    return (hashFnv32a(value + seed) % 1000) / 1000;\n  }\n\n  // Unknown hash version\n  return null;\n}\n\nexport function getEqualWeights(n: number): number[] {\n  if (n <= 0) return [];\n  return new Array(n).fill(1 / n);\n}\n\nexport function inRange(n: number, range: VariationRange): boolean {\n  return n >= range[0] && n < range[1];\n}\n\nexport function inNamespace(\n  hashValue: string,\n  namespace: [string, number, number]\n): boolean {\n  const n = hash(\"__\" + namespace[0], hashValue, 1);\n  if (n === null) return false;\n  return n >= namespace[1] && n < namespace[2];\n}\n\nexport function chooseVariation(n: number, ranges: VariationRange[]): number {\n  for (let i = 0; i < ranges.length; i++) {\n    if (inRange(n, ranges[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nexport function getUrlRegExp(regexString: string): RegExp | undefined {\n  try {\n    const escaped = regexString.replace(/([^\\\\])\\//g, \"$1\\\\/\");\n    return new RegExp(escaped);\n  } catch (e) {\n    console.error(e);\n    return undefined;\n  }\n}\n\nexport function isURLTargeted(url: string, targets: UrlTarget[]) {\n  if (!targets.length) return false;\n  let hasIncludeRules = false;\n  let isIncluded = false;\n\n  for (let i = 0; i < targets.length; i++) {\n    const match = _evalURLTarget(url, targets[i].type, targets[i].pattern);\n    if (targets[i].include === false) {\n      if (match) return false;\n    } else {\n      hasIncludeRules = true;\n      if (match) isIncluded = true;\n    }\n  }\n\n  return isIncluded || !hasIncludeRules;\n}\n\nfunction _evalSimpleUrlPart(\n  actual: string,\n  pattern: string,\n  isPath: boolean\n): boolean {\n  try {\n    // Escape special regex characters and change wildcard `_____` to `.*`\n    let escaped = pattern\n      .replace(/[*.+?^${}()|[\\]\\\\]/g, \"\\\\$&\")\n      .replace(/_____/g, \".*\");\n\n    if (isPath) {\n      // When matching pathname, make leading/trailing slashes optional\n      escaped = \"\\\\/?\" + escaped.replace(/(^\\/|\\/$)/g, \"\") + \"\\\\/?\";\n    }\n\n    const regex = new RegExp(\"^\" + escaped + \"$\", \"i\");\n    return regex.test(actual);\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalSimpleUrlTarget(actual: URL, pattern: string) {\n  try {\n    // If a protocol is missing, but a host is specified, add `https://` to the front\n    // Use \"_____\" as the wildcard since `*` is not a valid hostname in some browsers\n    const expected = new URL(\n      pattern.replace(/^([^:/?]*)\\./i, \"https://$1.\").replace(/\\*/g, \"_____\"),\n      \"https://_____\"\n    );\n\n    // Compare each part of the URL separately\n    const comps: Array<[string, string, boolean]> = [\n      [actual.host, expected.host, false],\n      [actual.pathname, expected.pathname, true],\n    ];\n    // We only want to compare hashes if it's explicitly being targeted\n    if (expected.hash) {\n      comps.push([actual.hash, expected.hash, false]);\n    }\n\n    expected.searchParams.forEach((v, k) => {\n      comps.push([actual.searchParams.get(k) || \"\", v, false]);\n    });\n\n    // If any comparisons fail, the whole thing fails\n    return !comps.some(\n      (data) => !_evalSimpleUrlPart(data[0], data[1], data[2])\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalURLTarget(\n  url: string,\n  type: UrlTargetType,\n  pattern: string\n): boolean {\n  try {\n    const parsed = new URL(url, \"https://_\");\n\n    if (type === \"regex\") {\n      const regex = getUrlRegExp(pattern);\n      if (!regex) return false;\n      return (\n        regex.test(parsed.href) ||\n        regex.test(parsed.href.substring(parsed.origin.length))\n      );\n    } else if (type === \"simple\") {\n      return _evalSimpleUrlTarget(parsed, pattern);\n    }\n\n    return false;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function getBucketRanges(\n  numVariations: number,\n  coverage: number | undefined,\n  weights?: number[]\n): VariationRange[] {\n  coverage = coverage === undefined ? 1 : coverage;\n\n  // Make sure coverage is within bounds\n  if (coverage < 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be greater than or equal to 0\");\n    }\n    coverage = 0;\n  } else if (coverage > 1) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be less than or equal to 1\");\n    }\n    coverage = 1;\n  }\n\n  // Default to equal weights if missing or invalid\n  const equal = getEqualWeights(numVariations);\n  weights = weights || equal;\n  if (weights.length !== numVariations) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\n        \"Experiment.weights array must be the same length as Experiment.variations\"\n      );\n    }\n    weights = equal;\n  }\n\n  // If weights don't add up to 1 (or close to it), default to equal weights\n  const totalWeight = weights.reduce((w, sum) => sum + w, 0);\n  if (totalWeight < 0.99 || totalWeight > 1.01) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.weights must add up to 1\");\n    }\n    weights = equal;\n  }\n\n  // Covert weights to ranges\n  let cumulative = 0;\n  return weights.map((w) => {\n    const start = cumulative;\n    cumulative += w;\n    return [start, start + (coverage as number) * w];\n  }) as VariationRange[];\n}\n\nexport function getQueryStringOverride(\n  id: string,\n  url: string,\n  numVariations: number\n) {\n  if (!url) {\n    return null;\n  }\n\n  const search = url.split(\"?\")[1];\n  if (!search) {\n    return null;\n  }\n\n  const match = search\n    .replace(/#.*/, \"\") // Get rid of anchor\n    .split(\"&\") // Split into key/value pairs\n    .map((kv) => kv.split(\"=\", 2))\n    .filter(([k]) => k === id) // Look for key that matches the experiment id\n    .map(([, v]) => parseInt(v)); // Parse the value into an integer\n\n  if (match.length > 0 && match[0] >= 0 && match[0] < numVariations)\n    return match[0];\n\n  return null;\n}\n\nexport function isIncluded(include: () => boolean) {\n  try {\n    return include();\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\n\nconst base64ToBuf = (b: string) =>\n  Uint8Array.from(atob(b), (c) => c.charCodeAt(0));\n\nexport async function decrypt(\n  encryptedString: string,\n  decryptionKey?: string,\n  subtle?: SubtleCrypto\n): Promise<string> {\n  decryptionKey = decryptionKey || \"\";\n  subtle = subtle || (globalThis.crypto && globalThis.crypto.subtle);\n  if (!subtle) {\n    throw new Error(\"No SubtleCrypto implementation found\");\n  }\n  try {\n    const key = await subtle.importKey(\n      \"raw\",\n      base64ToBuf(decryptionKey),\n      { name: \"AES-CBC\", length: 128 },\n      true,\n      [\"encrypt\", \"decrypt\"]\n    );\n    const [iv, cipherText] = encryptedString.split(\".\");\n    const plainTextBuffer = await subtle.decrypt(\n      { name: \"AES-CBC\", iv: base64ToBuf(iv) },\n      key,\n      base64ToBuf(cipherText)\n    );\n\n    return new TextDecoder().decode(plainTextBuffer);\n  } catch (e) {\n    throw new Error(\"Failed to decrypt\");\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function toString(input: any): string {\n  if (typeof input === \"string\") return input;\n  return JSON.stringify(input);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function paddedVersionString(input: any): string {\n  if (typeof input === \"number\") {\n    input = input + \"\";\n  }\n  if (!input || typeof input !== \"string\") {\n    input = \"0\";\n  }\n  // Remove build info and leading `v` if any\n  // Split version into parts (both core version numbers and pre-release tags)\n  // \"v1.2.3-rc.1+build123\" -> [\"1\",\"2\",\"3\",\"rc\",\"1\"]\n  const parts = (input as string).replace(/(^v|\\+.*$)/g, \"\").split(/[-.]/);\n\n  // If it's SemVer without a pre-release, add `~` to the end\n  // [\"1\",\"0\",\"0\"] -> [\"1\",\"0\",\"0\",\"~\"]\n  // \"~\" is the largest ASCII character, so this will make \"1.0.0\" greater than \"1.0.0-beta\" for example\n  if (parts.length === 3) {\n    parts.push(\"~\");\n  }\n\n  // Left pad each numeric part with spaces so string comparisons will work (\"9\">\"10\", but \" 9\"<\"10\")\n  // Then, join back together into a single string\n  return parts\n    .map((v) => (v.match(/^[0-9]+$/) ? v.padStart(5, \" \") : v))\n    .join(\"-\");\n}\n\nexport function loadSDKVersion(): string {\n  let version: string;\n  try {\n    // @ts-expect-error right-hand value to be replaced by build with string literal\n    version = __SDK_VERSION__;\n  } catch (e) {\n    version = \"\";\n  }\n  return version;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport {\n  ConditionInterface,\n  TestedObj,\n  ConditionValue,\n  Operator,\n  OperatorConditionValue,\n  VarType,\n} from \"./types/mongrule\";\nimport { paddedVersionString } from \"./util\";\n\nconst _regexCache: { [key: string]: RegExp } = {};\n\n// The top-level condition evaluation function\nexport function evalCondition(\n  obj: TestedObj,\n  condition: ConditionInterface\n): boolean {\n  // Recursive condition\n  if (\"$or\" in condition) {\n    return evalOr(obj, condition[\"$or\"] as ConditionInterface[]);\n  }\n  if (\"$nor\" in condition) {\n    return !evalOr(obj, condition[\"$nor\"] as ConditionInterface[]);\n  }\n  if (\"$and\" in condition) {\n    return evalAnd(obj, condition[\"$and\"] as ConditionInterface[]);\n  }\n  if (\"$not\" in condition) {\n    return !evalCondition(obj, condition[\"$not\"] as ConditionInterface);\n  }\n\n  // Condition is an object, keys are object paths, values are the condition for that path\n  for (const [k, v] of Object.entries(condition)) {\n    if (!evalConditionValue(v, getPath(obj, k))) return false;\n  }\n  return true;\n}\n\n// Return value at dot-separated path of an object\nfunction getPath(obj: TestedObj, path: string) {\n  const parts = path.split(\".\");\n  let current: any = obj;\n  for (let i = 0; i < parts.length; i++) {\n    if (current && typeof current === \"object\" && parts[i] in current) {\n      current = current[parts[i]];\n    } else {\n      return null;\n    }\n  }\n  return current;\n}\n\n// Transform a regex string into a real RegExp object\nfunction getRegex(regex: string): RegExp {\n  if (!_regexCache[regex]) {\n    _regexCache[regex] = new RegExp(regex.replace(/([^\\\\])\\//g, \"$1\\\\/\"));\n  }\n  return _regexCache[regex];\n}\n\n// Evaluate a single value against a condition\nfunction evalConditionValue(condition: ConditionValue, value: any) {\n  // Simple equality comparisons\n  if (typeof condition === \"string\") {\n    return value + \"\" === condition;\n  }\n  if (typeof condition === \"number\") {\n    return value * 1 === condition;\n  }\n  if (typeof condition === \"boolean\") {\n    return !!value === condition;\n  }\n\n  if (condition === null) {\n    return value === null;\n  }\n\n  if (Array.isArray(condition) || !isOperatorObject(condition)) {\n    return JSON.stringify(value) === JSON.stringify(condition);\n  }\n\n  // This is a special operator condition and we should evaluate each one separately\n  for (const op in condition) {\n    if (\n      !evalOperatorCondition(\n        op as Operator,\n        value,\n        condition[op as keyof OperatorConditionValue]\n      )\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// If the object has only keys that start with '$'\nfunction isOperatorObject(obj: any): boolean {\n  const keys = Object.keys(obj);\n  return (\n    keys.length > 0 && keys.filter((k) => k[0] === \"$\").length === keys.length\n  );\n}\n\n// Return the data type of a value\nfunction getType(v: any): VarType | \"unknown\" {\n  if (v === null) return \"null\";\n  if (Array.isArray(v)) return \"array\";\n  const t = typeof v;\n  if ([\"string\", \"number\", \"boolean\", \"object\", \"undefined\"].includes(t)) {\n    return t as VarType;\n  }\n  return \"unknown\";\n}\n\n// At least one element of actual must match the expected condition/value\nfunction elemMatch(actual: any, expected: any) {\n  if (!Array.isArray(actual)) return false;\n  const check = isOperatorObject(expected)\n    ? (v: any) => evalConditionValue(expected, v)\n    : (v: any) => evalCondition(v, expected);\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i] && check(actual[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isIn(actual: any, expected: Array<any>): boolean {\n  // Do an intersection is attribute is an array\n  if (Array.isArray(actual)) {\n    return actual.some((el) => expected.includes(el));\n  }\n  return expected.includes(actual);\n}\n\n// Evaluate a single operator condition\nfunction evalOperatorCondition(\n  operator: Operator,\n  actual: any,\n  expected: any\n): boolean {\n  switch (operator) {\n    case \"$veq\":\n      return paddedVersionString(actual) === paddedVersionString(expected);\n    case \"$vne\":\n      return paddedVersionString(actual) !== paddedVersionString(expected);\n    case \"$vgt\":\n      return paddedVersionString(actual) > paddedVersionString(expected);\n    case \"$vgte\":\n      return paddedVersionString(actual) >= paddedVersionString(expected);\n    case \"$vlt\":\n      return paddedVersionString(actual) < paddedVersionString(expected);\n    case \"$vlte\":\n      return paddedVersionString(actual) <= paddedVersionString(expected);\n    case \"$eq\":\n      return actual === expected;\n    case \"$ne\":\n      return actual !== expected;\n    case \"$lt\":\n      return actual < expected;\n    case \"$lte\":\n      return actual <= expected;\n    case \"$gt\":\n      return actual > expected;\n    case \"$gte\":\n      return actual >= expected;\n    case \"$exists\":\n      // Using `!=` and `==` instead of strict checks so it also matches for undefined\n      return expected ? actual != null : actual == null;\n    case \"$in\":\n      if (!Array.isArray(expected)) return false;\n      return isIn(actual, expected);\n    case \"$nin\":\n      if (!Array.isArray(expected)) return false;\n      return !isIn(actual, expected);\n    case \"$not\":\n      return !evalConditionValue(expected, actual);\n    case \"$size\":\n      if (!Array.isArray(actual)) return false;\n      return evalConditionValue(expected, actual.length);\n    case \"$elemMatch\":\n      return elemMatch(actual, expected);\n    case \"$all\":\n      if (!Array.isArray(actual)) return false;\n      for (let i = 0; i < expected.length; i++) {\n        let passed = false;\n        for (let j = 0; j < actual.length; j++) {\n          if (evalConditionValue(expected[i], actual[j])) {\n            passed = true;\n            break;\n          }\n        }\n        if (!passed) return false;\n      }\n      return true;\n    case \"$regex\":\n      try {\n        return getRegex(expected).test(actual);\n      } catch (e) {\n        return false;\n      }\n    case \"$type\":\n      return getType(actual) === expected;\n    default:\n      console.error(\"Unknown operator: \" + operator);\n      return false;\n  }\n}\n\n// Recursive $or rule\nfunction evalOr(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  if (!conditions.length) return true;\n  for (let i = 0; i < conditions.length; i++) {\n    if (evalCondition(obj, conditions[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// Recursive $and rule\nfunction evalAnd(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  for (let i = 0; i < conditions.length; i++) {\n    if (!evalCondition(obj, conditions[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n", "import mutate, { DeclarativeMutation } from \"dom-mutator\";\nimport type {\n  ApiHost,\n  Attributes,\n  AutoExperiment,\n  AutoExperimentVariation,\n  ClientKey,\n  Context,\n  Experiment,\n  FeatureApiResponse,\n  FeatureDefinition,\n  FeatureResult,\n  FeatureResultSource,\n  Filter,\n  LoadFeaturesOptions,\n  RealtimeUsageData,\n  RefreshFeaturesOptions,\n  Result,\n  StickyAssignments,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n  StickyExperimentKey,\n  SubscriptionFunction,\n  VariationMeta,\n  VariationRange,\n  WidenPrimitives,\n} from \"./types/growthbook\";\nimport type { ConditionInterface } from \"./types/mongrule\";\nimport {\n  chooseVariation,\n  decrypt,\n  getBucketRanges,\n  getQueryStringOverride,\n  getUrlRegExp,\n  hash,\n  inNamespace,\n  inRange,\n  isIncluded,\n  isURLTargeted,\n  loadSDKVersion,\n  toString,\n} from \"./util\";\nimport { evalCondition } from \"./mongrule\";\nimport { refreshFeatures, subscribe, unsubscribe } from \"./feature-repository\";\n\nconst isBrowser =\n  typeof window !== \"undefined\" && typeof document !== \"undefined\";\n\nconst SDK_VERSION = loadSDKVersion();\n\nexport class GrowthBook<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  AppFeatures extends Record<string, any> = Record<string, any>\n> {\n  // context is technically private, but some tools depend on it so we can't mangle the name\n  // _ctx below is a clone of this property that we use internally\n  private context: Context;\n  public debug: boolean;\n  public ready: boolean;\n  public version: string;\n\n  // Properties and methods that start with \"_\" are mangled by Terser (saves ~150 bytes)\n  private _ctx: Context;\n  private _renderer: null | (() => void);\n  private _trackedExperiments: Set<unknown>;\n  private _trackedFeatures: Record<string, string>;\n  private _subscriptions: Set<SubscriptionFunction>;\n  private _rtQueue: RealtimeUsageData[];\n  private _rtTimer: number;\n  private _assigned: Map<\n    string,\n    {\n      // eslint-disable-next-line\n      experiment: Experiment<any>;\n      // eslint-disable-next-line\n      result: Result<any>;\n    }\n  >;\n  // eslint-disable-next-line\n  private _forcedFeatureValues: Map<string, any>;\n  private _attributeOverrides: Attributes;\n  private _activeAutoExperiments: Map<\n    AutoExperiment,\n    { valueHash: string; undo: () => void }\n  >;\n  private _triggeredExpKeys: Set<string>;\n  private _loadFeaturesCalled: boolean;\n\n  constructor(context?: Context) {\n    context = context || {};\n    // These properties are all initialized in the constructor instead of above\n    // This saves ~80 bytes in the final output\n    this.version = SDK_VERSION;\n    this._ctx = this.context = context;\n    this._renderer = null;\n    this._trackedExperiments = new Set();\n    this._trackedFeatures = {};\n    this.debug = false;\n    this._subscriptions = new Set();\n    this._rtQueue = [];\n    this._rtTimer = 0;\n    this.ready = false;\n    this._assigned = new Map();\n    this._forcedFeatureValues = new Map();\n    this._attributeOverrides = {};\n    this._activeAutoExperiments = new Map();\n    this._triggeredExpKeys = new Set();\n    this._loadFeaturesCalled = false;\n\n    if (context.remoteEval) {\n      if (context.decryptionKey) {\n        throw new Error(\"Encryption is not available for remoteEval\");\n      }\n      if (!context.clientKey) {\n        throw new Error(\"Missing clientKey\");\n      }\n      let isGbHost = false;\n      try {\n        isGbHost = !!new URL(context.apiHost || \"\").hostname.match(\n          /growthbook\\.io$/i\n        );\n      } catch (e) {\n        // ignore invalid URLs\n      }\n      if (isGbHost) {\n        throw new Error(\"Cannot use remoteEval on GrowthBook Cloud\");\n      }\n    } else {\n      if (context.cacheKeyAttributes) {\n        throw new Error(\"cacheKeyAttributes are only used for remoteEval\");\n      }\n    }\n\n    if (context.features) {\n      this.ready = true;\n    }\n\n    if (isBrowser && context.enableDevMode) {\n      window._growthbook = this;\n      document.dispatchEvent(new Event(\"gbloaded\"));\n    }\n\n    if (context.experiments) {\n      this.ready = true;\n      this._updateAllAutoExperiments();\n    }\n\n    if (context.clientKey && !context.remoteEval) {\n      this._refresh({}, true, false);\n    }\n  }\n\n  public async loadFeatures(options?: LoadFeaturesOptions): Promise<void> {\n    if (options && options.autoRefresh) {\n      // interpret deprecated autoRefresh option as subscribeToChanges\n      this._ctx.subscribeToChanges = true;\n    }\n    this._loadFeaturesCalled = true;\n\n    await this._refresh(options, true, true);\n\n    if (this._canSubscribe()) {\n      subscribe(this);\n    }\n  }\n\n  public async refreshFeatures(\n    options?: RefreshFeaturesOptions\n  ): Promise<void> {\n    await this._refresh(options, false, true);\n  }\n\n  public getApiInfo(): [ApiHost, ClientKey] {\n    return [this.getApiHosts().apiHost, this.getClientKey()];\n  }\n  public getApiHosts(): {\n    apiHost: string;\n    streamingHost: string;\n    apiRequestHeaders?: Record<string, string>;\n    streamingHostRequestHeaders?: Record<string, string>;\n  } {\n    const defaultHost = this._ctx.apiHost || \"https://cdn.growthbook.io\";\n    return {\n      apiHost: defaultHost.replace(/\\/*$/, \"\"),\n      streamingHost: (this._ctx.streamingHost || defaultHost).replace(\n        /\\/*$/,\n        \"\"\n      ),\n      apiRequestHeaders: this._ctx.apiHostRequestHeaders,\n      streamingHostRequestHeaders: this._ctx.streamingHostRequestHeaders,\n    };\n  }\n  public getClientKey(): string {\n    return this._ctx.clientKey || \"\";\n  }\n\n  public isRemoteEval(): boolean {\n    return this._ctx.remoteEval || false;\n  }\n\n  public getCacheKeyAttributes(): (keyof Attributes)[] | undefined {\n    return this._ctx.cacheKeyAttributes;\n  }\n\n  private async _refresh(\n    options?: RefreshFeaturesOptions,\n    allowStale?: boolean,\n    updateInstance?: boolean\n  ) {\n    options = options || {};\n    if (!this._ctx.clientKey) {\n      throw new Error(\"Missing clientKey\");\n    }\n    await refreshFeatures(\n      this,\n      options.timeout,\n      options.skipCache || this._ctx.enableDevMode,\n      allowStale,\n      updateInstance,\n      this._ctx.backgroundSync !== false\n    );\n  }\n\n  private _render() {\n    if (this._renderer) {\n      this._renderer();\n    }\n  }\n\n  public setFeatures(features: Record<string, FeatureDefinition>) {\n    this._ctx.features = features;\n    this.ready = true;\n    this._render();\n  }\n\n  public async setEncryptedFeatures(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const featuresJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setFeatures(\n      JSON.parse(featuresJSON) as Record<string, FeatureDefinition>\n    );\n  }\n\n  public setExperiments(experiments: AutoExperiment[]): void {\n    this._ctx.experiments = experiments;\n    this.ready = true;\n    this._updateAllAutoExperiments();\n  }\n\n  public async setEncryptedExperiments(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const experimentsJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setExperiments(JSON.parse(experimentsJSON) as AutoExperiment[]);\n  }\n\n  public async decryptPayload(\n    data: FeatureApiResponse,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<FeatureApiResponse> {\n    if (data.encryptedFeatures) {\n      data.features = JSON.parse(\n        await decrypt(\n          data.encryptedFeatures,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedFeatures;\n    }\n    if (data.encryptedExperiments) {\n      data.experiments = JSON.parse(\n        await decrypt(\n          data.encryptedExperiments,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedExperiments;\n    }\n    return data;\n  }\n\n  public async setAttributes(attributes: Attributes) {\n    this._ctx.attributes = attributes;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setAttributeOverrides(overrides: Attributes) {\n    this._attributeOverrides = overrides;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setForcedVariations(vars: Record<string, number>) {\n    this._ctx.forcedVariations = vars || {};\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  // eslint-disable-next-line\n  public setForcedFeatures(map: Map<string, any>) {\n    this._forcedFeatureValues = map;\n    this._render();\n  }\n\n  public async setURL(url: string) {\n    this._ctx.url = url;\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      this._updateAllAutoExperiments(true);\n      return;\n    }\n    this._updateAllAutoExperiments(true);\n  }\n\n  public getAttributes() {\n    return { ...this._ctx.attributes, ...this._attributeOverrides };\n  }\n\n  public getForcedVariations() {\n    return this._ctx.forcedVariations || {};\n  }\n\n  public getForcedFeatures() {\n    // eslint-disable-next-line\n    return this._forcedFeatureValues || new Map<string, any>();\n  }\n\n  public getStickyBucketAssignmentDocs() {\n    return this._ctx.stickyBucketAssignmentDocs || {};\n  }\n\n  public getUrl() {\n    return this._ctx.url || \"\";\n  }\n\n  public getFeatures() {\n    return this._ctx.features || {};\n  }\n\n  public getExperiments() {\n    return this._ctx.experiments || [];\n  }\n\n  public subscribe(cb: SubscriptionFunction): () => void {\n    this._subscriptions.add(cb);\n    return () => {\n      this._subscriptions.delete(cb);\n    };\n  }\n\n  private _canSubscribe() {\n    return this._ctx.backgroundSync !== false && this._ctx.subscribeToChanges;\n  }\n\n  private async _refreshForRemoteEval() {\n    if (!this._ctx.remoteEval) return;\n    if (!this._loadFeaturesCalled) return;\n    await this._refresh({}, false, true).catch(() => {\n      // Ignore errors\n    });\n  }\n\n  public getAllResults() {\n    return new Map(this._assigned);\n  }\n\n  public destroy() {\n    // Release references to save memory\n    this._subscriptions.clear();\n    this._assigned.clear();\n    this._trackedExperiments.clear();\n    this._trackedFeatures = {};\n    this._rtQueue = [];\n    if (this._rtTimer) {\n      clearTimeout(this._rtTimer);\n    }\n    unsubscribe(this);\n\n    if (isBrowser && window._growthbook === this) {\n      delete window._growthbook;\n    }\n\n    // Undo any active auto experiments\n    this._activeAutoExperiments.forEach((exp) => {\n      exp.undo();\n    });\n    this._activeAutoExperiments.clear();\n    this._triggeredExpKeys.clear();\n  }\n\n  public setRenderer(renderer: () => void) {\n    this._renderer = renderer;\n  }\n\n  public forceVariation(key: string, variation: number) {\n    this._ctx.forcedVariations = this._ctx.forcedVariations || {};\n    this._ctx.forcedVariations[key] = variation;\n    if (this._ctx.remoteEval) {\n      this._refreshForRemoteEval();\n      return;\n    }\n    this._updateAllAutoExperiments();\n    this._render();\n  }\n\n  public run<T>(experiment: Experiment<T>): Result<T> {\n    const result = this._run(experiment, null);\n    this._fireSubscriptions(experiment, result);\n    return result;\n  }\n\n  public triggerExperiment(key: string) {\n    this._triggeredExpKeys.add(key);\n    if (!this._ctx.experiments) return null;\n    const experiments = this._ctx.experiments.filter((exp) => exp.key === key);\n    return experiments\n      .map((exp) => {\n        if (!exp.manual) return null;\n        return this._runAutoExperiment(exp);\n      })\n      .filter((res) => res !== null);\n  }\n\n  private _runAutoExperiment(experiment: AutoExperiment, forceRerun?: boolean) {\n    const existing = this._activeAutoExperiments.get(experiment);\n\n    // If this is a manual experiment and it's not already running, skip\n    if (\n      experiment.manual &&\n      !this._triggeredExpKeys.has(experiment.key) &&\n      !existing\n    )\n      return null;\n\n    // Run the experiment\n    const result = this.run(experiment);\n\n    // A hash to quickly tell if the assigned value changed\n    const valueHash = JSON.stringify(result.value);\n\n    // If the changes are already active, no need to re-apply them\n    if (\n      !forceRerun &&\n      result.inExperiment &&\n      existing &&\n      existing.valueHash === valueHash\n    ) {\n      return result;\n    }\n\n    // Undo any existing changes\n    if (existing) this._undoActiveAutoExperiment(experiment);\n\n    // Apply new changes\n    if (result.inExperiment) {\n      const undo = this._applyDOMChanges(result.value);\n      if (undo) {\n        this._activeAutoExperiments.set(experiment, {\n          undo,\n          valueHash,\n        });\n      }\n    }\n\n    return result;\n  }\n\n  private _undoActiveAutoExperiment(exp: AutoExperiment) {\n    const data = this._activeAutoExperiments.get(exp);\n    if (data) {\n      data.undo();\n      this._activeAutoExperiments.delete(exp);\n    }\n  }\n\n  private _updateAllAutoExperiments(forceRerun?: boolean) {\n    const experiments = this._ctx.experiments || [];\n\n    // Stop any experiments that are no longer defined\n    const keys = new Set(experiments);\n    this._activeAutoExperiments.forEach((v, k) => {\n      if (!keys.has(k)) {\n        v.undo();\n        this._activeAutoExperiments.delete(k);\n      }\n    });\n\n    // Re-run all new/updated experiments\n    experiments.forEach((exp) => {\n      this._runAutoExperiment(exp, forceRerun);\n    });\n  }\n\n  private _fireSubscriptions<T>(experiment: Experiment<T>, result: Result<T>) {\n    const key = experiment.key;\n\n    // If assigned variation has changed, fire subscriptions\n    const prev = this._assigned.get(key);\n    // TODO: what if the experiment definition has changed?\n    if (\n      !prev ||\n      prev.result.inExperiment !== result.inExperiment ||\n      prev.result.variationId !== result.variationId\n    ) {\n      this._assigned.set(key, { experiment, result });\n      this._subscriptions.forEach((cb) => {\n        try {\n          cb(experiment, result);\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }\n\n  private _trackFeatureUsage(key: string, res: FeatureResult): void {\n    // Don't track feature usage that was forced via an override\n    if (res.source === \"override\") return;\n\n    // Only track a feature once, unless the assigned value changed\n    const stringifiedValue = JSON.stringify(res.value);\n    if (this._trackedFeatures[key] === stringifiedValue) return;\n    this._trackedFeatures[key] = stringifiedValue;\n\n    // Fire user-supplied callback\n    if (this._ctx.onFeatureUsage) {\n      try {\n        this._ctx.onFeatureUsage(key, res);\n      } catch (e) {\n        // Ignore feature usage callback errors\n      }\n    }\n\n    // In browser environments, queue up feature usage to be tracked in batches\n    if (!isBrowser || !window.fetch) return;\n    this._rtQueue.push({\n      key,\n      on: res.on,\n    });\n    if (!this._rtTimer) {\n      this._rtTimer = window.setTimeout(() => {\n        // Reset the queue\n        this._rtTimer = 0;\n        const q = [...this._rtQueue];\n        this._rtQueue = [];\n\n        // Skip logging if a real-time usage key is not configured\n        if (!this._ctx.realtimeKey) return;\n\n        window\n          .fetch(\n            `https://rt.growthbook.io/?key=${\n              this._ctx.realtimeKey\n            }&events=${encodeURIComponent(JSON.stringify(q))}`,\n\n            {\n              cache: \"no-cache\",\n              mode: \"no-cors\",\n            }\n          )\n          .catch(() => {\n            // TODO: retry in case of network errors?\n          });\n      }, this._ctx.realtimeInterval || 2000);\n    }\n  }\n\n  private _getFeatureResult<T>(\n    key: string,\n    value: T,\n    source: FeatureResultSource,\n    ruleId?: string,\n    experiment?: Experiment<T>,\n    result?: Result<T>\n  ): FeatureResult<T> {\n    const ret: FeatureResult = {\n      value,\n      on: !!value,\n      off: !value,\n      source,\n      ruleId: ruleId || \"\",\n    };\n    if (experiment) ret.experiment = experiment;\n    if (result) ret.experimentResult = result;\n\n    // Track the usage of this feature in real-time\n    this._trackFeatureUsage(key, ret);\n\n    return ret;\n  }\n\n  public isOn<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).on;\n  }\n\n  public isOff<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).off;\n  }\n\n  public getFeatureValue<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(key: K, defaultValue: V): WidenPrimitives<V> {\n    const value = this.evalFeature<WidenPrimitives<V>, K>(key).value;\n    return value === null ? (defaultValue as WidenPrimitives<V>) : value;\n  }\n\n  /**\n   * @deprecated Use {@link evalFeature}\n   * @param id\n   */\n  // eslint-disable-next-line\n  public feature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    return this.evalFeature(id);\n  }\n\n  public evalFeature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    // Global override\n    if (this._forcedFeatureValues.has(id)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Global override\", {\n          id,\n          value: this._forcedFeatureValues.get(id),\n        });\n      return this._getFeatureResult(\n        id,\n        this._forcedFeatureValues.get(id),\n        \"override\"\n      );\n    }\n\n    // Unknown feature id\n    if (!this._ctx.features || !this._ctx.features[id]) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Unknown feature\", { id });\n      return this._getFeatureResult(id, null, \"unknownFeature\");\n    }\n\n    // Get the feature\n    const feature: FeatureDefinition<V> = this._ctx.features[id];\n\n    // Loop through the rules\n    if (feature.rules) {\n      for (const rule of feature.rules) {\n        // If there are filters for who is included (e.g. namespaces)\n        if (rule.filters && this._isFilteredOut(rule.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip rule because of filters\", {\n              id,\n              rule,\n            });\n          continue;\n        }\n\n        // Feature value is being forced\n        if (\"force\" in rule) {\n          // If it's a conditional rule, skip if the condition doesn't pass\n          if (rule.condition && !this._conditionPasses(rule.condition)) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because of condition ff\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          // If this is a percentage rollout, skip if not included\n          if (\n            !this._isIncludedInRollout(\n              rule.seed || id,\n              rule.hashAttribute,\n              this._ctx.stickyBucketService && !rule.disableStickyBucketing\n                ? rule.fallbackAttribute\n                : undefined,\n              rule.range,\n              rule.coverage,\n              rule.hashVersion\n            )\n          ) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because user not included in rollout\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Force value from rule\", {\n              id,\n              rule,\n            });\n\n          // If this was a remotely evaluated experiment, fire the tracking callbacks\n          if (rule.tracks) {\n            rule.tracks.forEach((t) => {\n              this._track(t.experiment, t.result);\n            });\n          }\n\n          return this._getFeatureResult(id, rule.force as V, \"force\", rule.id);\n        }\n        if (!rule.variations) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip invalid rule\", {\n              id,\n              rule,\n            });\n\n          continue;\n        }\n\n        // For experiment rules, run an experiment\n        const exp: Experiment<V> = {\n          variations: rule.variations as [V, V, ...V[]],\n          key: rule.key || id,\n        };\n        if (\"coverage\" in rule) exp.coverage = rule.coverage;\n        if (rule.weights) exp.weights = rule.weights;\n        if (rule.hashAttribute) exp.hashAttribute = rule.hashAttribute;\n        if (rule.fallbackAttribute)\n          exp.fallbackAttribute = rule.fallbackAttribute;\n        if (rule.disableStickyBucketing)\n          exp.disableStickyBucketing = rule.disableStickyBucketing;\n        if (rule.bucketVersion !== undefined)\n          exp.bucketVersion = rule.bucketVersion;\n        if (rule.minBucketVersion !== undefined)\n          exp.minBucketVersion = rule.minBucketVersion;\n        if (rule.namespace) exp.namespace = rule.namespace;\n        if (rule.meta) exp.meta = rule.meta;\n        if (rule.ranges) exp.ranges = rule.ranges;\n        if (rule.name) exp.name = rule.name;\n        if (rule.phase) exp.phase = rule.phase;\n        if (rule.seed) exp.seed = rule.seed;\n        if (rule.hashVersion) exp.hashVersion = rule.hashVersion;\n        if (rule.filters) exp.filters = rule.filters;\n        if (rule.condition) exp.condition = rule.condition;\n\n        // Only return a value if the user is part of the experiment\n        const res = this._run(exp, id);\n        this._fireSubscriptions(exp, res);\n        if (res.inExperiment && !res.passthrough) {\n          return this._getFeatureResult(\n            id,\n            res.value,\n            \"experiment\",\n            rule.id,\n            exp,\n            res\n          );\n        }\n      }\n    }\n\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"Use default value\", {\n        id,\n        value: feature.defaultValue,\n      });\n\n    // Fall back to using the default value\n    return this._getFeatureResult(\n      id,\n      feature.defaultValue === undefined ? null : feature.defaultValue,\n      \"defaultValue\"\n    );\n  }\n\n  private _isIncludedInRollout(\n    seed: string,\n    hashAttribute: string | undefined,\n    fallbackAttribute: string | undefined,\n    range: VariationRange | undefined,\n    coverage: number | undefined,\n    hashVersion: number | undefined\n  ): boolean {\n    if (!range && coverage === undefined) return true;\n\n    const { hashValue } = this._getHashAttribute(\n      hashAttribute,\n      fallbackAttribute\n    );\n    if (!hashValue) {\n      return false;\n    }\n\n    const n = hash(seed, hashValue, hashVersion || 1);\n    if (n === null) return false;\n\n    return range\n      ? inRange(n, range)\n      : coverage !== undefined\n      ? n <= coverage\n      : true;\n  }\n\n  private _conditionPasses(condition: ConditionInterface): boolean {\n    return evalCondition(this.getAttributes(), condition);\n  }\n\n  private _isFilteredOut(filters: Filter[]): boolean {\n    return filters.some((filter) => {\n      const { hashValue } = this._getHashAttribute(filter.attribute);\n      if (!hashValue) return true;\n      const n = hash(filter.seed, hashValue, filter.hashVersion || 2);\n      if (n === null) return true;\n      return !filter.ranges.some((r) => inRange(n, r));\n    });\n  }\n\n  private _run<T>(\n    experiment: Experiment<T>,\n    featureId: string | null\n  ): Result<T> {\n    const key = experiment.key;\n    const numVariations = experiment.variations.length;\n\n    // 1. If experiment has less than 2 variations, return immediately\n    if (numVariations < 2) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Invalid experiment\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2. If the context is disabled, return immediately\n    if (this._ctx.enabled === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Context disabled\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2.5. Merge in experiment overrides from the context\n    experiment = this._mergeOverrides(experiment);\n\n    // 2.6 New, more powerful URL targeting\n    if (\n      experiment.urlPatterns &&\n      !isURLTargeted(this._getContextUrl(), experiment.urlPatterns)\n    ) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url targeting\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 3. If a variation is forced from a querystring, return the forced variation\n    const qsOverride = getQueryStringOverride(\n      key,\n      this._getContextUrl(),\n      numVariations\n    );\n    if (qsOverride !== null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via querystring\", {\n          id: key,\n          variation: qsOverride,\n        });\n      return this._getResult(experiment, qsOverride, false, featureId);\n    }\n\n    // 4. If a variation is forced in the context, return the forced variation\n    if (this._ctx.forcedVariations && key in this._ctx.forcedVariations) {\n      const variation = this._ctx.forcedVariations[key];\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via dev tools\", {\n          id: key,\n          variation,\n        });\n      return this._getResult(experiment, variation, false, featureId);\n    }\n\n    // 5. Exclude if a draft experiment or not active\n    if (experiment.status === \"draft\" || experiment.active === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because inactive\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 6. Get the hash attribute and return if empty\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n    if (!hashValue) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because missing hashAttribute\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    let assigned = -1;\n\n    let foundStickyBucket = false;\n    let stickyBucketVersionIsBlocked = false;\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const { variation, versionIsBlocked } = this._getStickyBucketVariation(\n        experiment.key,\n        experiment.bucketVersion,\n        experiment.minBucketVersion,\n        experiment.meta\n      );\n      foundStickyBucket = variation >= 0;\n      assigned = variation;\n      stickyBucketVersionIsBlocked = !!versionIsBlocked;\n    }\n\n    // Some checks are not needed if we already have a sticky bucket\n    if (!foundStickyBucket) {\n      // 7. Exclude if user is filtered out (used to be called \"namespace\")\n      if (experiment.filters) {\n        if (this._isFilteredOut(experiment.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip because of filters\", {\n              id: key,\n            });\n          return this._getResult(experiment, -1, false, featureId);\n        }\n      } else if (\n        experiment.namespace &&\n        !inNamespace(hashValue, experiment.namespace)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of namespace\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 7.5. Exclude if experiment.include returns false or throws\n      if (experiment.include && !isIncluded(experiment.include)) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of include function\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8. Exclude if condition is false\n      if (\n        experiment.condition &&\n        !this._conditionPasses(experiment.condition)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of condition exp\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8.1. Exclude if user is not in a required group\n      if (\n        experiment.groups &&\n        !this._hasGroupOverlap(experiment.groups as string[])\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of groups\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n    }\n\n    // 8.2. Old style URL targeting\n    if (experiment.url && !this._urlIsValid(experiment.url as RegExp)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 9. Get the variation from the sticky bucket or get bucket ranges and choose variation\n    const n = hash(\n      experiment.seed || key,\n      hashValue,\n      experiment.hashVersion || 1\n    );\n    if (n === null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of invalid hash version\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    if (!foundStickyBucket) {\n      const ranges =\n        experiment.ranges ||\n        getBucketRanges(\n          numVariations,\n          experiment.coverage === undefined ? 1 : experiment.coverage,\n          experiment.weights\n        );\n      assigned = chooseVariation(n, ranges);\n    }\n\n    // 9.5 Unenroll if any prior sticky buckets are blocked by version\n    if (stickyBucketVersionIsBlocked) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because sticky bucket version is blocked\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId, undefined, true);\n    }\n\n    // 10. Return if not in experiment\n    if (assigned < 0) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of coverage\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 11. Experiment has a forced variation\n    if (\"force\" in experiment) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force variation\", {\n          id: key,\n          variation: experiment.force,\n        });\n      return this._getResult(\n        experiment,\n        experiment.force === undefined ? -1 : experiment.force,\n        false,\n        featureId\n      );\n    }\n\n    // 12. Exclude if in QA mode\n    if (this._ctx.qaMode) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because QA mode\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 12.5. Exclude if experiment is stopped\n    if (experiment.status === \"stopped\") {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because stopped\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 13. Build the result object\n    const result = this._getResult(\n      experiment,\n      assigned,\n      true,\n      featureId,\n      n,\n      foundStickyBucket\n    );\n\n    // 13.5. Persist sticky bucket\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const {\n        changed,\n        key: attrKey,\n        doc,\n      } = this._generateStickyBucketAssignmentDoc(\n        hashAttribute,\n        toString(hashValue),\n        {\n          [this._getStickyBucketExperimentKey(\n            experiment.key,\n            experiment.bucketVersion\n          )]: result.key,\n        }\n      );\n      if (changed) {\n        // update local docs\n        this._ctx.stickyBucketAssignmentDocs =\n          this._ctx.stickyBucketAssignmentDocs || {};\n        this._ctx.stickyBucketAssignmentDocs[attrKey] = doc;\n        // save doc\n        this._ctx.stickyBucketService.saveAssignments(doc);\n      }\n    }\n\n    // 14. Fire the tracking callback\n    this._track(experiment, result);\n\n    // 15. Return the result\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"In experiment\", {\n        id: key,\n        variation: result.variationId,\n      });\n    return result;\n  }\n\n  log(msg: string, ctx: Record<string, unknown>) {\n    if (!this.debug) return;\n    if (this._ctx.log) this._ctx.log(msg, ctx);\n    else console.log(msg, ctx);\n  }\n\n  private _track<T>(experiment: Experiment<T>, result: Result<T>) {\n    if (!this._ctx.trackingCallback) return;\n\n    const key = experiment.key;\n\n    // Make sure a tracking callback is only fired once per unique experiment\n    const k =\n      result.hashAttribute + result.hashValue + key + result.variationId;\n    if (this._trackedExperiments.has(k)) return;\n    this._trackedExperiments.add(k);\n\n    try {\n      this._ctx.trackingCallback(experiment, result);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  private _mergeOverrides<T>(experiment: Experiment<T>): Experiment<T> {\n    const key = experiment.key;\n    const o = this._ctx.overrides;\n    if (o && o[key]) {\n      experiment = Object.assign({}, experiment, o[key]);\n      if (typeof experiment.url === \"string\") {\n        experiment.url = getUrlRegExp(\n          // eslint-disable-next-line\n          experiment.url as any\n        );\n      }\n    }\n\n    return experiment;\n  }\n\n  private _getHashAttribute(attr?: string, fallback?: string) {\n    let hashAttribute = attr || \"id\";\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let hashValue: any = \"\";\n\n    if (this._attributeOverrides[hashAttribute]) {\n      hashValue = this._attributeOverrides[hashAttribute];\n    } else if (this._ctx.attributes) {\n      hashValue = this._ctx.attributes[hashAttribute] || \"\";\n    } else if (this._ctx.user) {\n      hashValue = this._ctx.user[hashAttribute] || \"\";\n    }\n\n    // if no match, try fallback\n    if (!hashValue && fallback) {\n      if (this._attributeOverrides[fallback]) {\n        hashValue = this._attributeOverrides[fallback];\n      } else if (this._ctx.attributes) {\n        hashValue = this._ctx.attributes[fallback] || \"\";\n      } else if (this._ctx.user) {\n        hashValue = this._ctx.user[fallback] || \"\";\n      }\n      if (hashValue) {\n        hashAttribute = fallback;\n      }\n    }\n\n    return { hashAttribute, hashValue };\n  }\n\n  private _getResult<T>(\n    experiment: Experiment<T>,\n    variationIndex: number,\n    hashUsed: boolean,\n    featureId: string | null,\n    bucket?: number,\n    stickyBucketUsed?: boolean\n  ): Result<T> {\n    let inExperiment = true;\n    // If assigned variation is not valid, use the baseline and mark the user as not in the experiment\n    if (variationIndex < 0 || variationIndex >= experiment.variations.length) {\n      variationIndex = 0;\n      inExperiment = false;\n    }\n\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n\n    const meta: Partial<VariationMeta> = experiment.meta\n      ? experiment.meta[variationIndex]\n      : {};\n\n    const res: Result<T> = {\n      key: meta.key || \"\" + variationIndex,\n      featureId,\n      inExperiment,\n      hashUsed,\n      variationId: variationIndex,\n      value: experiment.variations[variationIndex],\n      hashAttribute,\n      hashValue,\n      stickyBucketUsed: !!stickyBucketUsed,\n    };\n\n    if (meta.name) res.name = meta.name;\n    if (bucket !== undefined) res.bucket = bucket;\n    if (meta.passthrough) res.passthrough = meta.passthrough;\n\n    return res;\n  }\n\n  private _getContextUrl() {\n    return this._ctx.url || (isBrowser ? window.location.href : \"\");\n  }\n\n  private _urlIsValid(urlRegex: RegExp): boolean {\n    const url = this._getContextUrl();\n    if (!url) return false;\n\n    const pathOnly = url.replace(/^https?:\\/\\//, \"\").replace(/^[^/]*\\//, \"/\");\n\n    if (urlRegex.test(url)) return true;\n    if (urlRegex.test(pathOnly)) return true;\n    return false;\n  }\n\n  private _hasGroupOverlap(expGroups: string[]): boolean {\n    const groups = this._ctx.groups || {};\n    for (let i = 0; i < expGroups.length; i++) {\n      if (groups[expGroups[i]]) return true;\n    }\n    return false;\n  }\n\n  private _applyDOMChanges(changes: AutoExperimentVariation) {\n    if (!isBrowser) return;\n    const undo: (() => void)[] = [];\n    if (changes.css) {\n      const s = document.createElement(\"style\");\n      s.innerHTML = changes.css;\n      document.head.appendChild(s);\n      undo.push(() => s.remove());\n    }\n    if (changes.js) {\n      const script = document.createElement(\"script\");\n      script.innerHTML = changes.js;\n      document.head.appendChild(script);\n      undo.push(() => script.remove());\n    }\n    if (changes.domMutations) {\n      changes.domMutations.forEach((mutation) => {\n        undo.push(mutate.declarative(mutation as DeclarativeMutation).revert);\n      });\n    }\n    return () => {\n      undo.forEach((fn) => fn());\n    };\n  }\n\n  private _deriveStickyBucketIdentifierAttributes(data?: FeatureApiResponse) {\n    const attributes = new Set<string>();\n    const features = data && data.features ? data.features : this.getFeatures();\n    const experiments =\n      data && data.experiments ? data.experiments : this.getExperiments();\n    Object.keys(features).forEach((id) => {\n      const feature = features[id];\n      if (feature.rules) {\n        for (const rule of feature.rules) {\n          if (rule.variations) {\n            attributes.add(rule.hashAttribute || \"id\");\n            if (rule.fallbackAttribute) {\n              attributes.add(rule.fallbackAttribute);\n            }\n          }\n        }\n      }\n    });\n    experiments.map((experiment) => {\n      attributes.add(experiment.hashAttribute || \"id\");\n      if (experiment.fallbackAttribute) {\n        attributes.add(experiment.fallbackAttribute);\n      }\n    });\n    return Array.from(attributes);\n  }\n\n  public async refreshStickyBuckets(data?: FeatureApiResponse) {\n    if (this._ctx.stickyBucketService) {\n      const attributes = this._getStickyBucketAttributes(data);\n      this._ctx.stickyBucketAssignmentDocs = await this._ctx.stickyBucketService.getAllAssignments(\n        attributes\n      );\n    }\n  }\n\n  private _getStickyBucketAssignments(): StickyAssignments {\n    const mergedAssignments: StickyAssignments = {};\n    Object.values(this._ctx.stickyBucketAssignmentDocs || {}).forEach((doc) => {\n      if (doc.assignments) Object.assign(mergedAssignments, doc.assignments);\n    });\n    return mergedAssignments;\n  }\n\n  private _getStickyBucketVariation(\n    experimentKey: string,\n    experimentBucketVersion?: number,\n    minExperimentBucketVersion?: number,\n    meta?: VariationMeta[]\n  ): {\n    variation: number;\n    versionIsBlocked?: boolean;\n  } {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    minExperimentBucketVersion = minExperimentBucketVersion || 0;\n    meta = meta || [];\n    const id = this._getStickyBucketExperimentKey(\n      experimentKey,\n      experimentBucketVersion\n    );\n    const assignments = this._getStickyBucketAssignments();\n\n    // users with any blocked bucket version (0 to minExperimentBucketVersion) are excluded from the test\n    if (minExperimentBucketVersion > 0) {\n      for (let i = 0; i <= minExperimentBucketVersion; i++) {\n        const blockedKey = this._getStickyBucketExperimentKey(experimentKey, i);\n        if (assignments[blockedKey] !== undefined) {\n          return {\n            variation: -1,\n            versionIsBlocked: true,\n          };\n        }\n      }\n    }\n    const variationKey = assignments[id];\n    if (variationKey === undefined)\n      // no assignment found\n      return { variation: -1 };\n    const variation = meta.findIndex((m) => m.key === variationKey);\n    if (variation < 0)\n      // invalid assignment, treat as \"no assignment found\"\n      return { variation: -1 };\n\n    return { variation };\n  }\n\n  private _getStickyBucketExperimentKey(\n    experimentKey: string,\n    experimentBucketVersion?: number\n  ): StickyExperimentKey {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    return `${experimentKey}__${experimentBucketVersion}`;\n  }\n\n  private _getStickyBucketAttributes(\n    data?: FeatureApiResponse\n  ): Record<string, string> {\n    const attributes: Record<string, string> = {};\n    this._ctx.stickyBucketIdentifierAttributes = !this._ctx\n      .stickyBucketIdentifierAttributes\n      ? this._deriveStickyBucketIdentifierAttributes(data)\n      : this._ctx.stickyBucketIdentifierAttributes;\n    this._ctx.stickyBucketIdentifierAttributes.forEach((attr) => {\n      const { hashValue } = this._getHashAttribute(attr);\n      attributes[attr] = toString(hashValue);\n    });\n    return attributes;\n  }\n\n  private _generateStickyBucketAssignmentDoc(\n    attributeName: string,\n    attributeValue: string,\n    assignments: StickyAssignments\n  ): {\n    key: StickyAttributeKey;\n    doc: StickyAssignmentsDocument;\n    changed: boolean;\n  } {\n    const key = `${attributeName}||${attributeValue}`;\n    const existingAssignments =\n      this._ctx.stickyBucketAssignmentDocs &&\n      this._ctx.stickyBucketAssignmentDocs[key]\n        ? this._ctx.stickyBucketAssignmentDocs[key].assignments || {}\n        : {};\n    const newAssignments = { ...existingAssignments, ...assignments };\n    const changed =\n      JSON.stringify(existingAssignments) !== JSON.stringify(newAssignments);\n\n    return {\n      key,\n      doc: {\n        attributeName,\n        attributeValue,\n        assignments: newAssignments,\n      },\n      changed,\n    };\n  }\n}\n", "import {\n  LocalStorageCompat,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n} from \"./types/growthbook\";\n\nexport interface CookieAttributes {\n  expires?: number | Date | undefined;\n  path?: string | undefined;\n  domain?: string | undefined;\n  secure?: boolean | undefined;\n  sameSite?: \"strict\" | \"Strict\" | \"lax\" | \"Lax\" | \"none\" | \"None\" | undefined;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [property: string]: any;\n}\nexport interface JsCookiesCompat<T = string> {\n  set(\n    name: string,\n    value: string | T,\n    options?: CookieAttributes\n  ): string | undefined;\n  get(name: string): string | T | undefined;\n  get(): { [key: string]: string };\n  remove(name: string, options?: CookieAttributes): void;\n}\n\nexport interface IORedisCompat {\n  mget(...keys: string[]): Promise<string[]>;\n  set(key: string, value: string): Promise<string>;\n}\n\nexport interface RequestCompat {\n  cookies: Record<string, string>;\n  [key: string]: unknown;\n}\nexport interface ResponseCompat {\n  cookie(\n    name: string,\n    value: string,\n    options?: CookieAttributes\n  ): ResponseCompat;\n  [key: string]: unknown;\n}\n\n/**\n * Responsible for reading and writing documents which describe sticky bucket assignments.\n */\nexport abstract class StickyBucketService {\n  abstract getAssignments(\n    attributeName: string,\n    attributeValue: string\n  ): Promise<StickyAssignmentsDocument | null>;\n\n  abstract saveAssignments(doc: StickyAssignmentsDocument): Promise<unknown>;\n\n  /**\n   * The SDK calls getAllAssignments to populate sticky buckets. This in turn will\n   * typically loop through individual getAssignments calls. However, some StickyBucketService\n   * instances (i.e. Redis) will instead perform a multi-query inside getAllAssignments instead.\n   */\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<string, StickyAssignmentsDocument> = {};\n    (\n      await Promise.all(\n        Object.entries(attributes).map(([attributeName, attributeValue]) =>\n          this.getAssignments(attributeName, attributeValue)\n        )\n      )\n    ).forEach((doc) => {\n      if (doc) {\n        const key = `${doc.attributeName}||${doc.attributeValue}`;\n        docs[key] = doc;\n      }\n    });\n    return docs;\n  }\n}\n\nexport class LocalStorageStickyBucketService extends StickyBucketService {\n  private prefix: string;\n  private localStorage: LocalStorageCompat | undefined;\n  constructor(opts?: { prefix?: string; localStorage?: LocalStorageCompat }) {\n    opts = opts || {};\n    super();\n    this.prefix = opts.prefix || \"gbStickyBuckets__\";\n    try {\n      this.localStorage = opts.localStorage || globalThis.localStorage;\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.localStorage) return doc;\n    try {\n      const raw = (await this.localStorage.getItem(this.prefix + key)) || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.localStorage) return;\n    try {\n      await this.localStorage.setItem(this.prefix + key, JSON.stringify(doc));\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n}\n\nexport class ExpressCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with cookieParser() middleware from npm: 'cookie-parser'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value must be manually encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private req: RequestCompat;\n  private res: ResponseCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    req,\n    res,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    req: RequestCompat;\n    res: ResponseCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.req = req;\n    this.res = res;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.req) return doc;\n    try {\n      const raw = this.req.cookies[this.prefix + key] || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.res) return;\n    const str = JSON.stringify(doc);\n    this.res.cookie(\n      encodeURIComponent(this.prefix + key),\n      encodeURIComponent(str),\n      this.cookieAttributes\n    );\n  }\n}\n\nexport class BrowserCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with npm: 'js-cookie'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value is automatically encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private jsCookie: JsCookiesCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    jsCookie,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    jsCookie: JsCookiesCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.jsCookie = jsCookie;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.jsCookie) return doc;\n    try {\n      const raw = this.jsCookie.get(this.prefix + key);\n      const data = JSON.parse(raw || \"{}\");\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.jsCookie) return;\n    const str = JSON.stringify(doc);\n    this.jsCookie.set(this.prefix + key, str, this.cookieAttributes);\n  }\n}\n\nexport class RedisStickyBucketService extends StickyBucketService {\n  /** Intended to be used with npm: 'ioredis'. **/\n  private redis: IORedisCompat | undefined;\n  constructor({ redis }: { redis: IORedisCompat }) {\n    super();\n    this.redis = redis;\n  }\n\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<StickyAttributeKey, StickyAssignmentsDocument> = {};\n    const keys = Object.entries(attributes).map(\n      ([attributeName, attributeValue]) => `${attributeName}||${attributeValue}`\n    );\n    if (!this.redis) return docs;\n    this.redis.mget(...keys).then((values) => {\n      values.forEach((raw) => {\n        try {\n          const data = JSON.parse(raw || \"{}\");\n          if (data.attributeName && data.attributeValue && data.assignments) {\n            const key = `${data.attributeName}||${data.attributeValue}`;\n            docs[key] = data;\n          }\n        } catch (e) {\n          // ignore redis doc parse errors\n        }\n      });\n    });\n    return docs;\n  }\n\n  async getAssignments(_attributeName: string, _attributeValue: string) {\n    // not implemented\n    return null;\n  }\n\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.redis) return;\n    await this.redis.set(key, JSON.stringify(doc));\n  }\n}\n"], "names": ["cacheSettings", "staleTTL", "maxAge", "cache<PERSON>ey", "backgroundSync", "maxEntries", "disableIdleStreams", "idleStreamInterval", "polyfills", "fetch", "globalThis", "bind", "undefined", "SubtleCrypto", "crypto", "subtle", "EventSource", "helpers", "fetchFeaturesCall", "host", "client<PERSON>ey", "headers", "fetchRemoteEvalCall", "payload", "options", "method", "body", "JSON", "stringify", "eventSourceCall", "startIdleListener", "idleTimeout", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "onVisibilityChange", "visibilityState", "clearTimeout", "onVisible", "setTimeout", "onHidden", "addEventListener", "removeEventListener", "stopIdleListener", "localStorage", "e", "subscribedInstances", "Map", "cacheInitialized", "cache", "activeFetches", "streams", "supportsSSE", "Set", "setPolyfills", "overrides", "Object", "assign", "configure<PERSON>ache", "clearAutoRefresh", "clearCache", "clear", "updatePersistentCache", "refreshFeatures", "instance", "timeout", "<PERSON><PERSON><PERSON>", "allowStale", "updateInstance", "data", "fetchFeaturesWithCache", "refreshInstance", "subscribe", "key", "<PERSON><PERSON><PERSON>", "subs", "get", "add", "set", "unsubscribe", "for<PERSON>ach", "s", "delete", "channel", "state", "disableChannel", "enableChannel", "setItem", "Array", "from", "entries", "get<PERSON><PERSON><PERSON><PERSON>", "now", "Date", "minStaleAt", "getTime", "initializeCache", "existing", "staleAt", "sse", "fetchFeatures", "startAutoRefresh", "promiseTimeout", "apiHost", "getApiInfo", "baseKey", "isRemoteEval", "attributes", "getAttributes", "cacheKeyAttributes", "getCacheKeyAttributes", "keys", "ca", "fv", "getForcedVariations", "url", "getUrl", "promise", "Promise", "resolve", "resolved", "timer", "finish", "then", "catch", "value", "getItem", "parsed", "parse", "isArray", "cleanupCache", "cleanupFn", "entriesWithTimestamps", "map", "sort", "a", "b", "entriesToRemoveCount", "Math", "min", "max", "size", "i", "onNewFeatureData", "version", "dateUpdated", "has", "instances", "decryptPayload", "refreshStickyBuckets", "setExperiments", "experiments", "getExperiments", "setFeatures", "features", "getFeatures", "apiRequestHeaders", "getApiHosts", "getClientKey", "remoteEval", "fetcher", "forcedVariations", "forcedFeatures", "getForcedFeatures", "res", "json", "streamingHost", "streamingHostRequestHeaders", "src", "cb", "event", "type", "errors", "onSSEError", "readyState", "delay", "pow", "random", "includes", "onopen", "onerror", "close", "destroyChannel", "validAttributeName", "nullController", "revert", "elements", "mutations", "getObserverInit", "attr", "childList", "subtree", "characterData", "attributeFilter", "getElementRecord", "element", "record", "createElementPropertyRecord", "el", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "parentNode", "insertBeforeNode", "observe", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "m", "mutate", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "join", "attrMutationRunner", "_loadDOMNodes", "parentSelector", "insertBeforeSelector", "querySelector", "positionMutationRunner", "selectors", "newNodes", "getHTMLValue", "innerHTML", "setHTMLValue", "getElementHTMLRecord", "elementRecord", "html", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getAttrValue", "attrName", "getAttribute", "setAttrValue", "setAttribute", "getElementAttributeRecord", "deleteElementPropertyRecord", "disconnect", "transformContainer", "createElement", "setPropertyV<PERSON>ue", "length", "startMutating", "mutation", "kind", "attribute", "push", "stopMutating", "index", "indexOf", "splice", "refreshElementsSet", "existingElements", "matchingElements", "querySelectorAll", "selector", "revertMutation", "refreshAllElementSets", "connectGlobalObserver", "documentElement", "newMutation", "test", "mutatedClassnames", "classnames", "c", "declarative", "action", "hashFnv32a", "str", "hval", "l", "charCodeAt", "hash", "seed", "getEqualWeights", "n", "fill", "inRange", "range", "inNamespace", "hashValue", "namespace", "chooseVariation", "ranges", "getUrlRegExp", "regexString", "escaped", "replace", "RegExp", "console", "error", "isURLTargeted", "targets", "hasIncludeRules", "isIncluded", "match", "_evalURLTarget", "pattern", "include", "_evalSimpleUrlPart", "actual", "isPath", "regex", "_evalSimpleUrlTarget", "expected", "URL", "comps", "pathname", "searchParams", "v", "k", "some", "href", "substring", "origin", "getBucketRanges", "numVariations", "coverage", "weights", "equal", "totalWeight", "reduce", "w", "sum", "cumulative", "start", "getQueryStringOverride", "id", "search", "kv", "parseInt", "base64ToBuf", "Uint8Array", "atob", "decrypt", "encryptedString", "decryptionKey", "Error", "importKey", "name", "iv", "cipherText", "plainTextBuffer", "TextDecoder", "decode", "toString", "input", "paddedVersionString", "parts", "padStart", "loadSDKVersion", "_regexCache", "evalCondition", "obj", "condition", "evalOr", "evalAnd", "evalConditionValue", "<PERSON><PERSON><PERSON>", "path", "current", "getRegex", "isOperatorObject", "op", "evalOperatorCondition", "getType", "t", "elemMatch", "check", "isIn", "operator", "passed", "j", "conditions", "SDK_VERSION", "GrowthBook", "constructor", "context", "_ctx", "_renderer", "_trackedExperiments", "_trackedFeatures", "debug", "_subscriptions", "_rtQueue", "_rtTimer", "ready", "_assigned", "_forcedFeatureV<PERSON>ues", "_attributeOverrides", "_activeAutoExperiments", "_triggeredExpKeys", "_loadFeaturesCalled", "isGbHost", "hostname", "enableDevMode", "_growthbook", "dispatchEvent", "Event", "_updateAllAutoExperiments", "_refresh", "loadFeatures", "autoRefresh", "subscribeToChanges", "_canSubscribe", "defaultHost", "apiHostRequestHeaders", "_render", "setEncryptedFeatures", "featuresJSON", "setEncryptedExperiments", "experimentsJSON", "encryptedFeatures", "encryptedExperiments", "setAttributes", "stickyBucketService", "_refreshForRemoteEval", "setAttributeOverrides", "setForcedVariations", "vars", "setForcedFeatures", "setURL", "getStickyBucketAssignmentDocs", "stickyBucketAssignmentDocs", "getAllResults", "destroy", "exp", "undo", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "forceVariation", "variation", "run", "experiment", "result", "_run", "_fireSubscriptions", "triggerExperiment", "manual", "_runAutoExperiment", "forceRerun", "valueHash", "inExperiment", "_undoActiveAutoExperiment", "_applyDOMChanges", "prev", "variationId", "_trackFeatureUsage", "source", "stringifiedValue", "onFeatureUsage", "on", "q", "realtimeKey", "encodeURIComponent", "mode", "realtimeInterval", "_getFeatureResult", "ruleId", "ret", "off", "experimentResult", "isOn", "evalFeature", "isOff", "getFeatureValue", "defaultValue", "feature", "rules", "rule", "filters", "_isFilteredOut", "_conditionPasses", "_isIncludedInRollout", "hashAttribute", "disableStickyBucketing", "fallbackAttribute", "hashVersion", "tracks", "_track", "force", "variations", "bucketVersion", "minBucketVersion", "meta", "phase", "passthrough", "_getHashAttribute", "r", "featureId", "_getResult", "enabled", "_mergeOverrides", "urlPatterns", "_getContextUrl", "qsOverride", "status", "active", "assigned", "foundStickyBucket", "stickyBucketVersionIsBlocked", "versionIsBlocked", "_getStickyBucketVariation", "groups", "_hasGroupOverlap", "_urlIsValid", "qaMode", "changed", "attrKey", "doc", "_generateStickyBucketAssignmentDoc", "_getStickyBucketExperimentKey", "saveAssignments", "log", "msg", "ctx", "trackingCallback", "o", "fallback", "user", "variationIndex", "hashUsed", "bucket", "stickyBucketUsed", "location", "urlRegex", "pathOnly", "expGroups", "changes", "css", "head", "append<PERSON><PERSON><PERSON>", "remove", "js", "script", "domMutations", "fn", "_deriveStickyBucketIdentifierAttributes", "_getStickyBucketAttributes", "getAllAssignments", "_getStickyBucketAssignments", "mergedAssignments", "values", "assignments", "<PERSON><PERSON><PERSON>", "experimentBucketVersion", "minExperimentBucketVersion", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "findIndex", "stickyBucketIdentifierAttributes", "attributeName", "attributeValue", "existingAssignments", "newAssignments", "StickyBucketService", "docs", "all", "getAssignments", "LocalStorageStickyBucketService", "opts", "prefix", "raw", "ExpressCookieStickyBucketService", "req", "cookieAttributes", "cookies", "cookie", "BrowserCookieStickyBucketService", "js<PERSON><PERSON><PERSON>", "RedisStickyBucketService", "redis", "mget", "_attributeName", "_attributeValue"], "mappings": ";;;EAyBA;EACA,MAAMA,aAA4B,GAAG;EACnC;IACAC,QAAQ,EAAE,IAAI,GAAG,EAAE;EACnB;EACAC,EAAAA,MAAM,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC3BC,EAAAA,QAAQ,EAAE,iBAAiB;EAC3BC,EAAAA,cAAc,EAAE,IAAI;EACpBC,EAAAA,UAAU,EAAE,EAAE;EACdC,EAAAA,kBAAkB,EAAE,KAAK;EACzBC,EAAAA,kBAAkB,EAAE,KAAA;EACtB,CAAC,CAAA;EACD,MAAMC,SAAoB,GAAG;EAC3BC,EAAAA,KAAK,EAAEC,UAAU,CAACD,KAAK,GAAGC,UAAU,CAACD,KAAK,CAACE,IAAI,CAACD,UAAU,CAAC,GAAGE,SAAS;IACvEC,YAAY,EAAEH,UAAU,CAACI,MAAM,GAAGJ,UAAU,CAACI,MAAM,CAACC,MAAM,GAAGH,SAAS;IACtEI,WAAW,EAAEN,UAAU,CAACM,WAAAA;EAC1B,CAAC,CAAA;AACM,QAAMC,OAAgB,GAAG;EAC9BC,EAAAA,iBAAiB,EAAE,IAAkC,IAAA;MAAA,IAAjC;QAAEC,IAAI;QAAEC,SAAS;EAAEC,MAAAA,OAAAA;OAAS,GAAA,IAAA,CAAA;EAC9C,IAAA,OAAQb,SAAS,CAACC,KAAK,WAClBU,IAAI,EAAA,gBAAA,CAAA,CAAA,MAAA,CAAiBC,SAAS,CACjC,EAAA;EAAEC,MAAAA,OAAAA;EAAQ,KAAC,CACZ,CAAA;KACF;EACDC,EAAAA,mBAAmB,EAAE,KAA2C,IAAA;MAAA,IAA1C;QAAEH,IAAI;QAAEC,SAAS;QAAEG,OAAO;EAAEF,MAAAA,OAAAA;OAAS,GAAA,KAAA,CAAA;EACzD,IAAA,MAAMG,OAAO,GAAG;EACdC,MAAAA,MAAM,EAAE,MAAM;EACdJ,MAAAA,OAAO,EAAE;EAAE,QAAA,cAAc,EAAE,kBAAkB;UAAE,GAAGA,OAAAA;SAAS;EAC3DK,MAAAA,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,OAAO,CAAA;OAC7B,CAAA;MACD,OAAQf,SAAS,CAACC,KAAK,CAAA,EAAA,CAAA,MAAA,CAClBU,IAAI,EAAaC,YAAAA,CAAAA,CAAAA,MAAAA,CAAAA,SAAS,CAC7BI,EAAAA,OAAO,CACR,CAAA;KACF;EACDK,EAAAA,eAAe,EAAE,KAAkC,IAAA;MAAA,IAAjC;QAAEV,IAAI;QAAEC,SAAS;EAAEC,MAAAA,OAAAA;OAAS,GAAA,KAAA,CAAA;EAC5C,IAAA,IAAIA,OAAO,EAAE;QACX,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,EAAA,OAAA,CAAA,CAAA,MAAA,CAAQC,SAAS,CAAI,EAAA;EAC3DC,QAAAA,OAAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;MACA,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,EAAA,OAAA,CAAA,CAAA,MAAA,CAAQC,SAAS,CAAG,CAAA,CAAA;KAC7D;EACDU,EAAAA,iBAAiB,EAAE,MAAM;EACvB,IAAA,IAAIC,WAA+B,CAAA;MACnC,MAAMC,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,CAAA;MAClE,IAAI,CAACF,SAAS,EAAE,OAAA;MAChB,MAAMG,kBAAkB,GAAG,MAAM;EAC/B,MAAA,IAAID,QAAQ,CAACE,eAAe,KAAK,SAAS,EAAE;EAC1CH,QAAAA,MAAM,CAACI,YAAY,CAACN,WAAW,CAAC,CAAA;EAChCO,QAAAA,SAAS,EAAE,CAAA;EACb,OAAC,MAAM,IAAIJ,QAAQ,CAACE,eAAe,KAAK,QAAQ,EAAE;UAChDL,WAAW,GAAGE,MAAM,CAACM,UAAU,CAC7BC,QAAQ,EACRxC,aAAa,CAACO,kBAAkB,CACjC,CAAA;EACH,OAAA;OACD,CAAA;EACD2B,IAAAA,QAAQ,CAACO,gBAAgB,CAAC,kBAAkB,EAAEN,kBAAkB,CAAC,CAAA;MACjE,OAAO,MACLD,QAAQ,CAACQ,mBAAmB,CAAC,kBAAkB,EAAEP,kBAAkB,CAAC,CAAA;KACvE;EACDQ,EAAAA,gBAAgB,EAAE,MAAM;EACtB;EAAA,GAAA;EAEJ,EAAC;EAED,IAAI;IACF,IAAIjC,UAAU,CAACkC,YAAY,EAAE;EAC3BpC,IAAAA,SAAS,CAACoC,YAAY,GAAGlC,UAAU,CAACkC,YAAY,CAAA;EAClD,GAAA;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;EAAA,CAAA;;EAGF;EACA,MAAMC,mBAAiD,GAAG,IAAIC,GAAG,EAAE,CAAA;EACnE,IAAIC,gBAAgB,GAAG,KAAK,CAAA;EAC5B,MAAMC,KAA8B,GAAG,IAAIF,GAAG,EAAE,CAAA;EAChD,MAAMG,aAAuD,GAAG,IAAIH,GAAG,EAAE,CAAA;EACzE,MAAMI,OAAmC,GAAG,IAAIJ,GAAG,EAAE,CAAA;EACrD,MAAMK,WAAwB,GAAG,IAAIC,GAAG,EAAE,CAAA;;EAE1C;EACO,SAASC,YAAY,CAACC,SAA6B,EAAQ;EAChEC,EAAAA,MAAM,CAACC,MAAM,CAACjD,SAAS,EAAE+C,SAAS,CAAC,CAAA;EACrC,CAAA;EACO,SAASG,cAAc,CAACH,SAAiC,EAAQ;EACtEC,EAAAA,MAAM,CAACC,MAAM,CAACzD,aAAa,EAAEuD,SAAS,CAAC,CAAA;EACvC,EAAA,IAAI,CAACvD,aAAa,CAACI,cAAc,EAAE;EACjCuD,IAAAA,gBAAgB,EAAE,CAAA;EACpB,GAAA;EACF,CAAA;EAEO,eAAeC,UAAU,GAAkB;IAChDX,KAAK,CAACY,KAAK,EAAE,CAAA;IACbX,aAAa,CAACW,KAAK,EAAE,CAAA;EACrBF,EAAAA,gBAAgB,EAAE,CAAA;EAClBX,EAAAA,gBAAgB,GAAG,KAAK,CAAA;EACxB,EAAA,MAAMc,qBAAqB,EAAE,CAAA;EAC/B,CAAA;EAEO,eAAeC,eAAe,CACnCC,QAAoB,EACpBC,OAAgB,EAChBC,SAAmB,EACnBC,UAAoB,EACpBC,cAAwB,EACxBhE,cAAwB,EACT;IACf,IAAI,CAACA,cAAc,EAAE;MACnBJ,aAAa,CAACI,cAAc,GAAG,KAAK,CAAA;EACtC,GAAA;EAEA,EAAA,MAAMiE,IAAI,GAAG,MAAMC,sBAAsB,CACvCN,QAAQ,EACRG,UAAU,EACVF,OAAO,EACPC,SAAS,CACV,CAAA;IACDE,cAAc,IAAIC,IAAI,KAAK,MAAME,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC,CAAA;EACnE,CAAA;;EAEA;EACO,SAASG,SAAS,CAACR,QAAoB,EAAQ;EACpD,EAAA,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;IAC5B,MAAMW,IAAI,GAAG7B,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC,IAAI,IAAIpB,GAAG,EAAE,CAAA;EACtDsB,EAAAA,IAAI,CAACE,GAAG,CAACb,QAAQ,CAAC,CAAA;EAClBlB,EAAAA,mBAAmB,CAACgC,GAAG,CAACL,GAAG,EAAEE,IAAI,CAAC,CAAA;EACpC,CAAA;EACO,SAASI,WAAW,CAACf,QAAoB,EAAQ;IACtDlB,mBAAmB,CAACkC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClB,QAAQ,CAAC,CAAC,CAAA;EACxD,CAAA;EAEO,SAASxB,QAAQ,GAAG;EACzBW,EAAAA,OAAO,CAAC6B,OAAO,CAAEG,OAAO,IAAK;MAC3B,IAAI,CAACA,OAAO,EAAE,OAAA;MACdA,OAAO,CAACC,KAAK,GAAG,MAAM,CAAA;MACtBC,cAAc,CAACF,OAAO,CAAC,CAAA;EACzB,GAAC,CAAC,CAAA;EACJ,CAAA;EAEO,SAAS7C,SAAS,GAAG;EAC1Ba,EAAAA,OAAO,CAAC6B,OAAO,CAAEG,OAAO,IAAK;MAC3B,IAAI,CAACA,OAAO,EAAE,OAAA;EACd,IAAA,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE,OAAA;MAC9BE,aAAa,CAACH,OAAO,CAAC,CAAA;EACxB,GAAC,CAAC,CAAA;EACJ,CAAA;;EAEA;;EAEA,eAAerB,qBAAqB,GAAG;IACrC,IAAI;EACF,IAAA,IAAI,CAACtD,SAAS,CAACoC,YAAY,EAAE,OAAA;MAC7B,MAAMpC,SAAS,CAACoC,YAAY,CAAC2C,OAAO,CAClCvF,aAAa,CAACG,QAAQ,EACtBwB,IAAI,CAACC,SAAS,CAAC4D,KAAK,CAACC,IAAI,CAACxC,KAAK,CAACyC,OAAO,EAAE,CAAC,CAAC,CAC5C,CAAA;KACF,CAAC,OAAO7C,CAAC,EAAE;EACV;EAAA,GAAA;EAEJ,CAAA;EAEA,eAAeyB,sBAAsB,CACnCN,QAAoB,EACpBG,UAAoB,EACpBF,OAAgB,EAChBC,SAAmB,EACiB;EACpC,EAAA,MAAMO,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAC5B,EAAA,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC,CAAA;EACtC,EAAA,MAAM4B,GAAG,GAAG,IAAIC,IAAI,EAAE,CAAA;EAEtB,EAAA,MAAMC,UAAU,GAAG,IAAID,IAAI,CACzBD,GAAG,CAACG,OAAO,EAAE,GAAG/F,aAAa,CAACE,MAAM,GAAGF,aAAa,CAACC,QAAQ,CAC9D,CAAA;EAED,EAAA,MAAM+F,eAAe,EAAE,CAAA;EACvB,EAAA,MAAMC,QAAQ,GAAGhD,KAAK,CAAC2B,GAAG,CAACzE,QAAQ,CAAC,CAAA;EACpC,EAAA,IACE8F,QAAQ,IACR,CAAC/B,SAAS,KACTC,UAAU,IAAI8B,QAAQ,CAACC,OAAO,GAAGN,GAAG,CAAC,IACtCK,QAAQ,CAACC,OAAO,GAAGJ,UAAU,EAC7B;EACA;MACA,IAAIG,QAAQ,CAACE,GAAG,EAAE/C,WAAW,CAACyB,GAAG,CAACJ,GAAG,CAAC,CAAA;;EAEtC;EACA,IAAA,IAAIwB,QAAQ,CAACC,OAAO,GAAGN,GAAG,EAAE;QAC1BQ,aAAa,CAACpC,QAAQ,CAAC,CAAA;EACzB,KAAA;EACA;WACK;QACHqC,gBAAgB,CAACrC,QAAQ,CAAC,CAAA;EAC5B,KAAA;MACA,OAAOiC,QAAQ,CAAC5B,IAAI,CAAA;EACtB,GAAC,MAAM;MACL,OAAO,MAAMiC,cAAc,CAACF,aAAa,CAACpC,QAAQ,CAAC,EAAEC,OAAO,CAAC,CAAA;EAC/D,GAAA;EACF,CAAA;EAEA,SAASS,MAAM,CAACV,QAAoB,EAAU;IAC5C,MAAM,CAACuC,OAAO,EAAEnF,SAAS,CAAC,GAAG4C,QAAQ,CAACwC,UAAU,EAAE,CAAA;IAClD,OAAUD,EAAAA,CAAAA,MAAAA,CAAAA,OAAO,eAAKnF,SAAS,CAAA,CAAA;EACjC,CAAA;EAEA,SAASuE,WAAW,CAAC3B,QAAoB,EAAU;EACjD,EAAA,MAAMyC,OAAO,GAAG/B,MAAM,CAACV,QAAQ,CAAC,CAAA;EAChC,EAAA,IAAI,CAACA,QAAQ,CAAC0C,YAAY,EAAE,EAAE,OAAOD,OAAO,CAAA;EAE5C,EAAA,MAAME,UAAU,GAAG3C,QAAQ,CAAC4C,aAAa,EAAE,CAAA;EAC3C,EAAA,MAAMC,kBAAkB,GACtB7C,QAAQ,CAAC8C,qBAAqB,EAAE,IAAItD,MAAM,CAACuD,IAAI,CAAC/C,QAAQ,CAAC4C,aAAa,EAAE,CAAC,CAAA;IAC3E,MAAMI,EAAc,GAAG,EAAE,CAAA;EACzBH,EAAAA,kBAAkB,CAAC7B,OAAO,CAAEP,GAAG,IAAK;EAClCuC,IAAAA,EAAE,CAACvC,GAAG,CAAC,GAAGkC,UAAU,CAAClC,GAAG,CAAC,CAAA;EAC3B,GAAC,CAAC,CAAA;EAEF,EAAA,MAAMwC,EAAE,GAAGjD,QAAQ,CAACkD,mBAAmB,EAAE,CAAA;EACzC,EAAA,MAAMC,GAAG,GAAGnD,QAAQ,CAACoD,MAAM,EAAE,CAAA;EAE7B,EAAA,OAAA,EAAA,CAAA,MAAA,CAAUX,OAAO,EAAA,IAAA,CAAA,CAAA,MAAA,CAAK9E,IAAI,CAACC,SAAS,CAAC;MACnCoF,EAAE;MACFC,EAAE;EACFE,IAAAA,GAAAA;EACF,GAAC,CAAC,CAAA,CAAA;EACJ,CAAA;;EAEA;EACA;EACA;EACA,SAASb,cAAc,CACrBe,OAAmB,EACnBpD,OAAgB,EACG;EACnB,EAAA,OAAO,IAAIqD,OAAO,CAAEC,OAAO,IAAK;MAC9B,IAAIC,QAAQ,GAAG,KAAK,CAAA;EACpB,IAAA,IAAIC,KAAc,CAAA;MAClB,MAAMC,MAAM,GAAIrD,IAAQ,IAAK;EAC3B,MAAA,IAAImD,QAAQ,EAAE,OAAA;EACdA,MAAAA,QAAQ,GAAG,IAAI,CAAA;EACfC,MAAAA,KAAK,IAAIpF,YAAY,CAACoF,KAAK,CAAiB,CAAA;EAC5CF,MAAAA,OAAO,CAAClD,IAAI,IAAI,IAAI,CAAC,CAAA;OACtB,CAAA;EAED,IAAA,IAAIJ,OAAO,EAAE;QACXwD,KAAK,GAAGlF,UAAU,CAAC,MAAMmF,MAAM,EAAE,EAAEzD,OAAO,CAAC,CAAA;EAC7C,KAAA;EAEAoD,IAAAA,OAAO,CAACM,IAAI,CAAEtD,IAAI,IAAKqD,MAAM,CAACrD,IAAI,CAAC,CAAC,CAACuD,KAAK,CAAC,MAAMF,MAAM,EAAE,CAAC,CAAA;EAC5D,GAAC,CAAC,CAAA;EACJ,CAAA;;EAEA;EACA,eAAe1B,eAAe,GAAkB;EAC9C,EAAA,IAAIhD,gBAAgB,EAAE,OAAA;EACtBA,EAAAA,gBAAgB,GAAG,IAAI,CAAA;IACvB,IAAI;MACF,IAAIxC,SAAS,CAACoC,YAAY,EAAE;EAC1B,MAAA,MAAMiF,KAAK,GAAG,MAAMrH,SAAS,CAACoC,YAAY,CAACkF,OAAO,CAChD9H,aAAa,CAACG,QAAQ,CACvB,CAAA;EACD,MAAA,IAAI0H,KAAK,EAAE;EACT,QAAA,MAAME,MAA8B,GAAGpG,IAAI,CAACqG,KAAK,CAACH,KAAK,CAAC,CAAA;UACxD,IAAIE,MAAM,IAAIvC,KAAK,CAACyC,OAAO,CAACF,MAAM,CAAC,EAAE;YACnCA,MAAM,CAAC/C,OAAO,CAAC,KAAiB,IAAA;EAAA,YAAA,IAAhB,CAACP,GAAG,EAAEJ,IAAI,CAAC,GAAA,KAAA,CAAA;EACzBpB,YAAAA,KAAK,CAAC6B,GAAG,CAACL,GAAG,EAAE;EACb,cAAA,GAAGJ,IAAI;EACP6B,cAAAA,OAAO,EAAE,IAAIL,IAAI,CAACxB,IAAI,CAAC6B,OAAO,CAAA;EAChC,aAAC,CAAC,CAAA;EACJ,WAAC,CAAC,CAAA;EACJ,SAAA;EACAgC,QAAAA,YAAY,EAAE,CAAA;EAChB,OAAA;EACF,KAAA;KACD,CAAC,OAAOrF,CAAC,EAAE;EACV;EAAA,GAAA;EAEF,EAAA,IAAI,CAAC7C,aAAa,CAACM,kBAAkB,EAAE;EACrC,IAAA,MAAM6H,SAAS,GAAGlH,OAAO,CAACa,iBAAiB,EAAE,CAAA;EAC7C,IAAA,IAAIqG,SAAS,EAAE;QACblH,OAAO,CAAC0B,gBAAgB,GAAGwF,SAAS,CAAA;EACtC,KAAA;EACF,GAAA;EACF,CAAA;;EAEA;EACA,SAASD,YAAY,GAAG;EACtB,EAAA,MAAME,qBAAqB,GAAG5C,KAAK,CAACC,IAAI,CAACxC,KAAK,CAACyC,OAAO,EAAE,CAAC,CACtD2C,GAAG,CAAC,KAAA,IAAA;EAAA,IAAA,IAAC,CAAC5D,GAAG,EAAEoD,KAAK,CAAC,GAAA,KAAA,CAAA;MAAA,OAAM;QACtBpD,GAAG;EACHyB,MAAAA,OAAO,EAAE2B,KAAK,CAAC3B,OAAO,CAACH,OAAO,EAAA;OAC/B,CAAA;EAAA,GAAC,CAAC,CACFuC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrC,OAAO,GAAGsC,CAAC,CAACtC,OAAO,CAAC,CAAA;IAExC,MAAMuC,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CACnCD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE3F,KAAK,CAAC4F,IAAI,GAAG7I,aAAa,CAACK,UAAU,CAAC,EAClD4C,KAAK,CAAC4F,IAAI,CACX,CAAA;IAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,oBAAoB,EAAEK,CAAC,EAAE,EAAE;MAC7C7F,KAAK,CAACiC,MAAM,CAACkD,qBAAqB,CAACU,CAAC,CAAC,CAACrE,GAAG,CAAC,CAAA;EAC5C,GAAA;EACF,CAAA;;EAEA;EACA,SAASsE,gBAAgB,CACvBtE,GAAW,EACXtE,QAAgB,EAChBkE,IAAwB,EAClB;EACN;EACA,EAAA,MAAM2E,OAAO,GAAG3E,IAAI,CAAC4E,WAAW,IAAI,EAAE,CAAA;EACtC,EAAA,MAAM/C,OAAO,GAAG,IAAIL,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG5F,aAAa,CAACC,QAAQ,CAAC,CAAA;EAC7D,EAAA,MAAMgG,QAAQ,GAAGhD,KAAK,CAAC2B,GAAG,CAACzE,QAAQ,CAAC,CAAA;IACpC,IAAI8F,QAAQ,IAAI+C,OAAO,IAAI/C,QAAQ,CAAC+C,OAAO,KAAKA,OAAO,EAAE;MACvD/C,QAAQ,CAACC,OAAO,GAAGA,OAAO,CAAA;EAC1BpC,IAAAA,qBAAqB,EAAE,CAAA;EACvB,IAAA,OAAA;EACF,GAAA;;EAEA;EACAb,EAAAA,KAAK,CAAC6B,GAAG,CAAC3E,QAAQ,EAAE;MAClBkE,IAAI;MACJ2E,OAAO;MACP9C,OAAO;EACPC,IAAAA,GAAG,EAAE/C,WAAW,CAAC8F,GAAG,CAACzE,GAAG,CAAA;EAC1B,GAAC,CAAC,CAAA;EACFyD,EAAAA,YAAY,EAAE,CAAA;EACd;EACApE,EAAAA,qBAAqB,EAAE,CAAA;;EAEvB;EACA,EAAA,MAAMqF,SAAS,GAAGrG,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC,CAAA;EAC9C0E,EAAAA,SAAS,IAAIA,SAAS,CAACnE,OAAO,CAAEhB,QAAQ,IAAKO,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC,CAAA;EAC/E,CAAA;EAEA,eAAeE,eAAe,CAC5BP,QAAoB,EACpBK,IAAwB,EACT;EACfA,EAAAA,IAAI,GAAG,MAAML,QAAQ,CAACoF,cAAc,CAAC/E,IAAI,EAAEzD,SAAS,EAAEJ,SAAS,CAACK,YAAY,CAAC,CAAA;EAE7E,EAAA,MAAMmD,QAAQ,CAACqF,oBAAoB,CAAChF,IAAI,CAAC,CAAA;IACzCL,QAAQ,CAACsF,cAAc,CAACjF,IAAI,CAACkF,WAAW,IAAIvF,QAAQ,CAACwF,cAAc,EAAE,CAAC,CAAA;IACtExF,QAAQ,CAACyF,WAAW,CAACpF,IAAI,CAACqF,QAAQ,IAAI1F,QAAQ,CAAC2F,WAAW,EAAE,CAAC,CAAA;EAC/D,CAAA;EAEA,eAAevD,aAAa,CAC1BpC,QAAoB,EACS;IAC7B,MAAM;MAAEuC,OAAO;EAAEqD,IAAAA,iBAAAA;EAAkB,GAAC,GAAG5F,QAAQ,CAAC6F,WAAW,EAAE,CAAA;EAC7D,EAAA,MAAMzI,SAAS,GAAG4C,QAAQ,CAAC8F,YAAY,EAAE,CAAA;EACzC,EAAA,MAAMC,UAAU,GAAG/F,QAAQ,CAAC0C,YAAY,EAAE,CAAA;EAC1C,EAAA,MAAMjC,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAC5B,EAAA,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC,CAAA;EAEtC,EAAA,IAAIqD,OAAO,GAAGnE,aAAa,CAAC0B,GAAG,CAACzE,QAAQ,CAAC,CAAA;IACzC,IAAI,CAACkH,OAAO,EAAE;EACZ,IAAA,MAAM2C,OAA0B,GAAGD,UAAU,GACzC9I,OAAO,CAACK,mBAAmB,CAAC;EAC1BH,MAAAA,IAAI,EAAEoF,OAAO;QACbnF,SAAS;EACTG,MAAAA,OAAO,EAAE;EACPoF,QAAAA,UAAU,EAAE3C,QAAQ,CAAC4C,aAAa,EAAE;EACpCqD,QAAAA,gBAAgB,EAAEjG,QAAQ,CAACkD,mBAAmB,EAAE;EAChDgD,QAAAA,cAAc,EAAE1E,KAAK,CAACC,IAAI,CAACzB,QAAQ,CAACmG,iBAAiB,EAAE,CAACzE,OAAO,EAAE,CAAC;UAClEyB,GAAG,EAAEnD,QAAQ,CAACoD,MAAM,EAAA;SACrB;EACD/F,MAAAA,OAAO,EAAEuI,iBAAAA;EACX,KAAC,CAAC,GACF3I,OAAO,CAACC,iBAAiB,CAAC;EACxBC,MAAAA,IAAI,EAAEoF,OAAO;QACbnF,SAAS;EACTC,MAAAA,OAAO,EAAEuI,iBAAAA;EACX,KAAC,CAAC,CAAA;;EAEN;EACAvC,IAAAA,OAAO,GAAG2C,OAAO,CACdrC,IAAI,CAAEyC,GAAG,IAAK;QACb,IAAIA,GAAG,CAAC/I,OAAO,CAACuD,GAAG,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE;EAClDxB,QAAAA,WAAW,CAACyB,GAAG,CAACJ,GAAG,CAAC,CAAA;EACtB,OAAA;QACA,OAAO2F,GAAG,CAACC,IAAI,EAAE,CAAA;EACnB,KAAC,CAAC,CACD1C,IAAI,CAAEtD,IAAwB,IAAK;EAClC0E,MAAAA,gBAAgB,CAACtE,GAAG,EAAEtE,QAAQ,EAAEkE,IAAI,CAAC,CAAA;QACrCgC,gBAAgB,CAACrC,QAAQ,CAAC,CAAA;EAC1Bd,MAAAA,aAAa,CAACgC,MAAM,CAAC/E,QAAQ,CAAC,CAAA;EAC9B,MAAA,OAAOkE,IAAI,CAAA;EACb,KAAC,CAAC,CACDuD,KAAK,CAAE/E,CAAC,IAAK;EAOZK,MAAAA,aAAa,CAACgC,MAAM,CAAC/E,QAAQ,CAAC,CAAA;EAC9B,MAAA,OAAOmH,OAAO,CAACC,OAAO,CAAC,EAAE,CAAC,CAAA;EAC5B,KAAC,CAAC,CAAA;EACJrE,IAAAA,aAAa,CAAC4B,GAAG,CAAC3E,QAAQ,EAAEkH,OAAO,CAAC,CAAA;EACtC,GAAA;EACA,EAAA,OAAO,MAAMA,OAAO,CAAA;EACtB,CAAA;;EAEA;EACA;EACA,SAAShB,gBAAgB,CAACrC,QAAoB,EAAQ;EACpD,EAAA,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC,CAAA;EAC5B,EAAA,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC,CAAA;IACtC,MAAM;MAAEsG,aAAa;EAAEC,IAAAA,2BAAAA;EAA4B,GAAC,GAAGvG,QAAQ,CAAC6F,WAAW,EAAE,CAAA;EAC7E,EAAA,MAAMzI,SAAS,GAAG4C,QAAQ,CAAC8F,YAAY,EAAE,CAAA;EACzC,EAAA,IACE9J,aAAa,CAACI,cAAc,IAC5BgD,WAAW,CAAC8F,GAAG,CAACzE,GAAG,CAAC,IACpBjE,SAAS,CAACQ,WAAW,EACrB;EACA,IAAA,IAAImC,OAAO,CAAC+F,GAAG,CAACzE,GAAG,CAAC,EAAE,OAAA;EACtB,IAAA,MAAMU,OAAsB,GAAG;EAC7BqF,MAAAA,GAAG,EAAE,IAAI;EACTrJ,MAAAA,IAAI,EAAEmJ,aAAa;QACnBlJ,SAAS;EACTC,MAAAA,OAAO,EAAEkJ,2BAA2B;QACpCE,EAAE,EAAGC,KAA2B,IAAK;UACnC,IAAI;EACF,UAAA,IAAIA,KAAK,CAACC,IAAI,KAAK,kBAAkB,EAAE;EACrC,YAAA,MAAMxB,SAAS,GAAGrG,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC,CAAA;EAC9C0E,YAAAA,SAAS,IACPA,SAAS,CAACnE,OAAO,CAAEhB,QAAQ,IAAK;gBAC9BoC,aAAa,CAACpC,QAAQ,CAAC,CAAA;EACzB,aAAC,CAAC,CAAA;EACN,WAAC,MAAM,IAAI0G,KAAK,CAACC,IAAI,KAAK,UAAU,EAAE;cACpC,MAAMN,IAAwB,GAAG1I,IAAI,CAACqG,KAAK,CAAC0C,KAAK,CAACrG,IAAI,CAAC,CAAA;EACvD0E,YAAAA,gBAAgB,CAACtE,GAAG,EAAEtE,QAAQ,EAAEkK,IAAI,CAAC,CAAA;EACvC,WAAA;EACA;YACAlF,OAAO,CAACyF,MAAM,GAAG,CAAC,CAAA;WACnB,CAAC,OAAO/H,CAAC,EAAE;YAOVgI,UAAU,CAAC1F,OAAO,CAAC,CAAA;EACrB,SAAA;SACD;EACDyF,MAAAA,MAAM,EAAE,CAAC;EACTxF,MAAAA,KAAK,EAAE,QAAA;OACR,CAAA;EACDjC,IAAAA,OAAO,CAAC2B,GAAG,CAACL,GAAG,EAAEU,OAAO,CAAC,CAAA;MACzBG,aAAa,CAACH,OAAO,CAAC,CAAA;EACxB,GAAA;EACF,CAAA;EAEA,SAAS0F,UAAU,CAAC1F,OAAsB,EAAE;EAC1C,EAAA,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE,OAAA;IAC9BD,OAAO,CAACyF,MAAM,EAAE,CAAA;EAChB,EAAA,IAAIzF,OAAO,CAACyF,MAAM,GAAG,CAAC,IAAKzF,OAAO,CAACqF,GAAG,IAAIrF,OAAO,CAACqF,GAAG,CAACM,UAAU,KAAK,CAAE,EAAE;EACvE;MACA,MAAMC,KAAK,GACTrC,IAAI,CAACsC,GAAG,CAAC,CAAC,EAAE7F,OAAO,CAACyF,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,GAAGlC,IAAI,CAACuC,MAAM,EAAE,GAAG,IAAI,CAAC,CAAA;MACjE5F,cAAc,CAACF,OAAO,CAAC,CAAA;EACvB5C,IAAAA,UAAU,CAAC,MAAM;EACf,MAAA,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC2I,QAAQ,CAAC/F,OAAO,CAACC,KAAK,CAAC,EAAE,OAAA;QAChDE,aAAa,CAACH,OAAO,CAAC,CAAA;OACvB,EAAEuD,IAAI,CAACC,GAAG,CAACoC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;EAC9B,GAAA;EACF,CAAA;;EAEA,SAAS1F,cAAc,CAACF,OAAsB,EAAE;EAC9C,EAAA,IAAI,CAACA,OAAO,CAACqF,GAAG,EAAE,OAAA;EAClBrF,EAAAA,OAAO,CAACqF,GAAG,CAACW,MAAM,GAAG,IAAI,CAAA;EACzBhG,EAAAA,OAAO,CAACqF,GAAG,CAACY,OAAO,GAAG,IAAI,CAAA;EAC1BjG,EAAAA,OAAO,CAACqF,GAAG,CAACa,KAAK,EAAE,CAAA;IACnBlG,OAAO,CAACqF,GAAG,GAAG,IAAI,CAAA;EAClB,EAAA,IAAIrF,OAAO,CAACC,KAAK,KAAK,QAAQ,EAAE;MAC9BD,OAAO,CAACC,KAAK,GAAG,UAAU,CAAA;EAC5B,GAAA;EACF,CAAA;EAEA,SAASE,aAAa,CAACH,OAAsB,EAAE;EAC7CA,EAAAA,OAAO,CAACqF,GAAG,GAAGvJ,OAAO,CAACY,eAAe,CAAC;MACpCV,IAAI,EAAEgE,OAAO,CAAChE,IAAI;MAClBC,SAAS,EAAE+D,OAAO,CAAC/D,SAAS;MAC5BC,OAAO,EAAE8D,OAAO,CAAC9D,OAAAA;EACnB,GAAC,CAAgB,CAAA;IACjB8D,OAAO,CAACC,KAAK,GAAG,QAAQ,CAAA;IACxBD,OAAO,CAACqF,GAAG,CAAC/H,gBAAgB,CAAC,UAAU,EAAE0C,OAAO,CAACsF,EAAE,CAAC,CAAA;IACpDtF,OAAO,CAACqF,GAAG,CAAC/H,gBAAgB,CAAC,kBAAkB,EAAE0C,OAAO,CAACsF,EAAE,CAAC,CAAA;IAC5DtF,OAAO,CAACqF,GAAG,CAACY,OAAO,GAAG,MAAMP,UAAU,CAAC1F,OAAO,CAAC,CAAA;EAC/CA,EAAAA,OAAO,CAACqF,GAAG,CAACW,MAAM,GAAG,MAAM;MACzBhG,OAAO,CAACyF,MAAM,GAAG,CAAC,CAAA;KACnB,CAAA;EACH,CAAA;EAEA,SAASU,cAAc,CAACnG,OAAsB,EAAEV,GAAW,EAAE;IAC3DY,cAAc,CAACF,OAAO,CAAC,CAAA;EACvBhC,EAAAA,OAAO,CAAC+B,MAAM,CAACT,GAAG,CAAC,CAAA;EACrB,CAAA;EAEA,SAASd,gBAAgB,GAAG;EAC1B;IACAP,WAAW,CAACS,KAAK,EAAE,CAAA;;EAEnB;EACAV,EAAAA,OAAO,CAAC6B,OAAO,CAACsG,cAAc,CAAC,CAAA;;EAE/B;IACAxI,mBAAmB,CAACe,KAAK,EAAE,CAAA;;EAE3B;IACA5C,OAAO,CAAC0B,gBAAgB,EAAE,CAAA;EAC5B;;EC9hBa4I,IAAAA,kBAAkB,GAAG,8BAAA,CAAA;EAClC,IAAMC,cAAc,GAAuB;EACzCC,EAAAA,MAAM,EAAE,SAAA,MAAA,GAAA,EAAA;EADiC,CAA3C,CAAA;EAIA,IAAMC,QAAQ,gBAAgC,IAAI3I,GAAJ,EAA9C,CAAA;EACA,IAAM4I,SAAS,gBAAkB,IAAItI,GAAJ,EAAjC,CAAA;EAEA,SAASuI,eAAT,CAAyBC,IAAzB,EAAA;IACE,OAAOA,IAAI,KAAK,MAAT,GACH;EACEC,IAAAA,SAAS,EAAE,IADb;EAEEC,IAAAA,OAAO,EAAE,IAFX;EAGEpF,IAAAA,UAAU,EAAE,IAHd;EAIEqF,IAAAA,aAAa,EAAE,IAAA;EAJjB,GADG,GAOH;EACEF,IAAAA,SAAS,EAAE,KADb;EAEEC,IAAAA,OAAO,EAAE,KAFX;EAGEpF,IAAAA,UAAU,EAAE,IAHd;MAIEsF,eAAe,EAAE,CAACJ,IAAD,CAAA;KAXvB,CAAA;EAaD,CAAA;EAED,SAASK,gBAAT,CAA0BC,OAA1B,EAAA;EACE,EAAA,IAAIC,MAAM,GAAGV,QAAQ,CAAC9G,GAAT,CAAauH,OAAb,CAAb,CAAA;IAEA,IAAI,CAACC,MAAL,EAAa;EACXA,IAAAA,MAAM,GAAG;EAAED,MAAAA,OAAO,EAAPA,OAAF;EAAWxF,MAAAA,UAAU,EAAE,EAAA;OAAhC,CAAA;EACA+E,IAAAA,QAAQ,CAAC5G,GAAT,CAAaqH,OAAb,EAAsBC,MAAtB,CAAA,CAAA;EACD,GAAA;EAED,EAAA,OAAOA,MAAP,CAAA;EACD,CAAA;EAED,SAASC,2BAAT,CACEC,EADF,EAEET,IAFF,EAGEU,eAHF,EAIEC,QAJF,EAKEC,cALF,EAAA;EAOE,EAAA,IAAMC,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC,CAAA;EACA,EAAA,IAAMF,MAAM,GAAoC;EAC9CO,IAAAA,OAAO,EAAE,KADqC;EAE9CC,IAAAA,aAAa,EAAEF,YAF+B;EAG9CG,IAAAA,YAAY,EAAEH,YAHgC;EAI9Cf,IAAAA,SAAS,EAAE,EAJmC;EAK9CW,IAAAA,EAAE,EAAFA,EAL8C;EAM9CQ,IAAAA,gBAAgB,EAAE,IAN4B;MAO9CC,QAAQ,EAAE,IAAIC,gBAAJ,CAAqB,YAAA;EAC7B;EACA;EACA;EACA;QACA,IAAInB,IAAI,KAAK,UAAT,IAAuBO,MAAM,CAACU,gBAAlC,EAAoD,OAApD,KACK,IAAIjB,IAAI,KAAK,UAAb,EACHO,MAAM,CAACU,gBAAP,GAA0BvK,UAAU,CAAC,YAAA;UACnC6J,MAAM,CAACU,gBAAP,GAA0B,IAA1B,CAAA;SADkC,EAEjC,IAFiC,CAApC,CAAA;EAIF,MAAA,IAAMJ,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC,CAAA;QACA,IACET,IAAI,KAAK,UAAT,IACAa,YAAY,CAACO,UAAb,KAA4Bb,MAAM,CAACS,YAAP,CAAoBI,UADhD,IAEAP,YAAY,CAACQ,gBAAb,KAAkCd,MAAM,CAACS,YAAP,CAAoBK,gBAHxD,EAKE,OAAA;EACF,MAAA,IAAIR,YAAY,KAAKN,MAAM,CAACS,YAA5B,EAA0C,OAAA;QAC1CT,MAAM,CAACQ,aAAP,GAAuBF,YAAvB,CAAA;QACAD,cAAc,CAACL,MAAD,CAAd,CAAA;EACD,KArBS,CAPoC;EA6B9CK,IAAAA,cAAc,EAAdA,cA7B8C;EA8B9CD,IAAAA,QAAQ,EAARA,QA9B8C;EA+B9CD,IAAAA,eAAe,EAAfA,eAAAA;KA/BF,CAAA;EAiCA,EAAA,IAAIV,IAAI,KAAK,UAAT,IAAuBS,EAAE,CAACW,UAA9B,EAA0C;MACxCb,MAAM,CAACW,QAAP,CAAgBI,OAAhB,CAAwBb,EAAE,CAACW,UAA3B,EAAuC;EACrCnB,MAAAA,SAAS,EAAE,IAD0B;EAErCC,MAAAA,OAAO,EAAE,IAF4B;EAGrCpF,MAAAA,UAAU,EAAE,KAHyB;EAIrCqF,MAAAA,aAAa,EAAE,KAAA;OAJjB,CAAA,CAAA;EAMD,GAPD,MAOO;MACLI,MAAM,CAACW,QAAP,CAAgBI,OAAhB,CAAwBb,EAAxB,EAA4BV,eAAe,CAACC,IAAD,CAA3C,CAAA,CAAA;EACD,GAAA;EACD,EAAA,OAAOO,MAAP,CAAA;EACD,CAAA;EAED,SAASgB,aAAT,CACEC,GADF,EAEEjB,MAFF,EAAA;IAIE,IAAMkB,UAAU,GAAGlB,MAAM,CAACG,eAAP,CAAuBH,MAAM,CAACE,EAA9B,CAAnB,CAAA;IACAF,MAAM,CAACS,YAAP,GAAsBQ,GAAtB,CAAA;EACA,EAAA,IAAIA,GAAG,IAAI,OAAOA,GAAP,KAAe,QAA1B,EAAoC;EAClC,IAAA,IACE,CAACC,UAAD,IACAD,GAAG,CAACJ,UAAJ,KAAmBK,UAAU,CAACL,UAD9B,IAEAI,GAAG,CAACH,gBAAJ,KAAyBI,UAAU,CAACJ,gBAHtC,EAIE;QACAd,MAAM,CAACO,OAAP,GAAiB,IAAjB,CAAA;QACAY,aAAa,EAAA,CAAA;EACd,KAAA;EACF,GATD,MASO,IAAIF,GAAG,KAAKC,UAAZ,EAAwB;MAC7BlB,MAAM,CAACO,OAAP,GAAiB,IAAjB,CAAA;MACAY,aAAa,EAAA,CAAA;EACd,GAAA;EACF,CAAA;EAED,SAASC,kBAAT,CAA4BpB,MAA5B,EAAA;EACE,EAAA,IAAIiB,GAAG,GAAGjB,MAAM,CAACQ,aAAjB,CAAA;EACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EAAA,IAAA,OAAKqI,GAAG,GAAGI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAX,CAAA;KAA1B,CAAA,CAAA;EACAD,EAAAA,aAAa,CAACO,kBAAkB,CAACN,GAAD,CAAnB,EAA0BjB,MAA1B,CAAb,CAAA;EACD,CAAA;EACD,SAASwB,mBAAT,CAA6BxB,MAA7B,EAAA;EACE,EAAA,IAAMiB,GAAG,GAAG,IAAIhK,GAAJ,CAAQ+I,MAAM,CAACQ,aAAP,CAAqBiB,KAArB,CAA2B,KAA3B,CAAA,CAAkCC,MAAlC,CAAyCC,OAAzC,CAAR,CAAZ,CAAA;EACA3B,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EAAA,IAAA,OAAIyI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAJ,CAAA;KAA1B,CAAA,CAAA;EACAD,EAAAA,aAAa,CACX5H,KAAK,CAACC,IAAN,CAAW4H,GAAX,CACGS,CAAAA,MADH,CACUC,OADV,EAEGC,IAFH,CAEQ,GAFR,CADW,EAIX5B,MAJW,CAAb,CAAA;EAMD,CAAA;EAED,SAAS6B,kBAAT,CAA4B7B,MAA5B,EAAA;EACE,EAAA,IAAIiB,GAAG,GAAkBjB,MAAM,CAACQ,aAAhC,CAAA;EACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EAAA,IAAA,OAAKqI,GAAG,GAAGI,CAAC,CAACC,MAAF,CAASL,GAAT,CAAX,CAAA;KAA1B,CAAA,CAAA;EACAD,EAAAA,aAAa,CAACC,GAAD,EAAMjB,MAAN,CAAb,CAAA;EACD,CAAA;EAED,SAAS8B,aAAT,CAAA,IAAA,EAAA;EACEC,EAAAA,IAAAA,cAAAA,GAAAA,IAAAA,CAAAA,cAAAA;EACAC,IAAAA,oBAAAA,GAAAA,IAAAA,CAAAA,oBAAAA,CAAAA;EAEA,EAAA,IAAMnB,UAAU,GAAG/K,QAAQ,CAACmM,aAAT,CAAoCF,cAApC,CAAnB,CAAA;EACA,EAAA,IAAI,CAAClB,UAAL,EAAiB,OAAO,IAAP,CAAA;IACjB,IAAMC,gBAAgB,GAAGkB,oBAAoB,GACzClM,QAAQ,CAACmM,aAAT,CAAoCD,oBAApC,CADyC,GAEzC,IAFJ,CAAA;EAGA,EAAA,IAAIA,oBAAoB,IAAI,CAAClB,gBAA7B,EAA+C,OAAO,IAAP,CAAA;IAC/C,OAAO;EACLD,IAAAA,UAAU,EAAVA,UADK;EAELC,IAAAA,gBAAgB,EAAhBA,gBAAAA;KAFF,CAAA;EAID,CAAA;EAED,SAASoB,sBAAT,CAAgClC,MAAhC,EAAA;EACE,EAAA,IAAIiB,GAAG,GAAGjB,MAAM,CAACQ,aAAjB,CAAA;EACAR,EAAAA,MAAM,CAACT,SAAP,CAAiB3G,OAAjB,CAAyB,UAAC,CAAA,EAAA;EACxB,IAAA,IAAMuJ,SAAS,GAAGd,CAAC,CAACC,MAAF,EAAlB,CAAA;EACA,IAAA,IAAMc,QAAQ,GAAGN,aAAa,CAACK,SAAD,CAA9B,CAAA;MACAlB,GAAG,GAAGmB,QAAQ,IAAInB,GAAlB,CAAA;KAHF,CAAA,CAAA;EAKAD,EAAAA,aAAa,CAACC,GAAD,EAAMjB,MAAN,CAAb,CAAA;EACD,CAAA;EAED,IAAMqC,YAAY,GAAG,SAAfA,YAAe,CAACnC,EAAD,EAAA;IAAA,OAAiBA,EAAE,CAACoC,SAApB,CAAA;EAAA,CAArB,CAAA;EACA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACrC,EAAD,EAAczE,KAAd,EAAA;EAAA,EAAA,OAAiCyE,EAAE,CAACoC,SAAH,GAAe7G,KAAhD,CAAA;EAAA,CAArB,CAAA;EACA,SAAS+G,oBAAT,CAA8BzC,OAA9B,EAAA;EACE,EAAA,IAAM0C,aAAa,GAAG3C,gBAAgB,CAACC,OAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAAC0C,aAAa,CAACC,IAAnB,EAAyB;EACvBD,IAAAA,aAAa,CAACC,IAAd,GAAqBzC,2BAA2B,CAC9CF,OAD8C,EAE9C,MAF8C,EAG9CsC,YAH8C,EAI9CE,YAJ8C,EAK9CnB,kBAL8C,CAAhD,CAAA;EAOD,GAAA;IACD,OAAOqB,aAAa,CAACC,IAArB,CAAA;EACD,CAAA;EAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAACzC,EAAD,EAAA;IACzB,OAAO;MACLW,UAAU,EAAEX,EAAE,CAAC0C,aADV;MAEL9B,gBAAgB,EAAEZ,EAAE,CAAC2C,kBAAAA;KAFvB,CAAA;EAID,CALD,CAAA;EAMA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC5C,EAAD,EAAczE,KAAd,EAAA;EACzB,EAAA,IACEA,KAAK,CAACqF,gBAAN,IACA,CAACrF,KAAK,CAACoF,UAAN,CAAiBkC,QAAjB,CAA0BtH,KAAK,CAACqF,gBAAhC,CAFH,EAGE;EACA;EACA;EACA,IAAA,OAAA;EACD,GAAA;IACDrF,KAAK,CAACoF,UAAN,CAAiBmC,YAAjB,CAA8B9C,EAA9B,EAAkCzE,KAAK,CAACqF,gBAAxC,CAAA,CAAA;EACD,CAVD,CAAA;EAWA,SAASmC,wBAAT,CAAkClD,OAAlC,EAAA;EACE,EAAA,IAAM0C,aAAa,GAAG3C,gBAAgB,CAACC,OAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAAC0C,aAAa,CAACS,QAAnB,EAA6B;EAC3BT,IAAAA,aAAa,CAACS,QAAd,GAAyBjD,2BAA2B,CAClDF,OADkD,EAElD,UAFkD,EAGlD4C,kBAHkD,EAIlDG,kBAJkD,EAKlDZ,sBALkD,CAApD,CAAA;EAOD,GAAA;IACD,OAAOO,aAAa,CAACS,QAArB,CAAA;EACD,CAAA;EAED,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACjD,EAAD,EAAce,GAAd,EAAA;EAAA,EAAA,OACpBA,GAAG,GAAIf,EAAE,CAACkD,SAAH,GAAenC,GAAnB,GAA0Bf,EAAE,CAACmD,eAAH,CAAmB,OAAnB,CADT,CAAA;EAAA,CAAtB,CAAA;EAEA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACpD,EAAD,EAAA;IAAA,OAAiBA,EAAE,CAACkD,SAApB,CAAA;EAAA,CAAtB,CAAA;EACA,SAASG,qBAAT,CAA+BrD,EAA/B,EAAA;EACE,EAAA,IAAMuC,aAAa,GAAG3C,gBAAgB,CAACI,EAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAACuC,aAAa,CAACe,OAAnB,EAA4B;EAC1Bf,IAAAA,aAAa,CAACe,OAAd,GAAwBvD,2BAA2B,CACjDC,EADiD,EAEjD,OAFiD,EAGjDoD,aAHiD,EAIjDH,aAJiD,EAKjD3B,mBALiD,CAAnD,CAAA;EAOD,GAAA;IACD,OAAOiB,aAAa,CAACe,OAArB,CAAA;EACD,CAAA;EAED,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACC,QAAD,EAAA;EAAA,EAAA,OAAsB,UAACxD,EAAD,EAAA;EAAA,IAAA,IAAA,gBAAA,CAAA;EAAA,IAAA,OAAA,CAAA,gBAAA,GACzCA,EAAE,CAACyD,YAAH,CAAgBD,QAAhB,CADyC,+BACZ,IADY,CAAA;KAAtB,CAAA;EAAA,CAArB,CAAA;EAEA,IAAME,YAAY,GAAG,SAAfA,YAAe,CAACF,QAAD,EAAA;IAAA,OAAsB,UAACxD,EAAD,EAAce,GAAd,EAAA;EAAA,IAAA,OACzCA,GAAG,KAAK,IAAR,GAAef,EAAE,CAAC2D,YAAH,CAAgBH,QAAhB,EAA0BzC,GAA1B,CAAf,GAAgDf,EAAE,CAACmD,eAAH,CAAmBK,QAAnB,CADP,CAAA;KAAtB,CAAA;EAAA,CAArB,CAAA;EAEA,SAASI,yBAAT,CAAmC5D,EAAnC,EAAgDT,IAAhD,EAAA;EACE,EAAA,IAAMgD,aAAa,GAAG3C,gBAAgB,CAACI,EAAD,CAAtC,CAAA;EACA,EAAA,IAAI,CAACuC,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,CAAL,EAAqC;MACnCgD,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,IAAiCQ,2BAA2B,CAC1DC,EAD0D,EAE1DT,IAF0D,EAG1DgE,YAAY,CAAChE,IAAD,CAH8C,EAI1DmE,YAAY,CAACnE,IAAD,CAJ8C,EAK1DoC,kBAL0D,CAA5D,CAAA;EAOD,GAAA;EACD,EAAA,OAAOY,aAAa,CAAClI,UAAd,CAAyBkF,IAAzB,CAAP,CAAA;EACD,CAAA;EAED,SAASsE,2BAAT,CAAqC7D,EAArC,EAAkDT,IAAlD,EAAA;EACE,EAAA,IAAMM,OAAO,GAAGT,QAAQ,CAAC9G,GAAT,CAAa0H,EAAb,CAAhB,CAAA;IACA,IAAI,CAACH,OAAL,EAAc,OAAA;IACd,IAAIN,IAAI,KAAK,MAAb,EAAqB;EAAA,IAAA,IAAA,aAAA,EAAA,qBAAA,CAAA;EACnB,IAAA,CAAA,aAAA,GAAA,OAAO,CAACiD,IAAR,KAAc/B,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,aAAAA,CAAAA,QAAd,2CAAwBqD,UAAxB,EAAA,CAAA;MACA,OAAOjE,OAAO,CAAC2C,IAAf,CAAA;EACD,GAHD,MAGO,IAAIjD,IAAI,KAAK,OAAb,EAAsB;EAAA,IAAA,IAAA,gBAAA,EAAA,qBAAA,CAAA;EAC3B,IAAA,CAAA,gBAAA,GAAA,OAAO,CAAC+D,OAAR,KAAiB7C,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,gBAAAA,CAAAA,QAAjB,2CAA2BqD,UAA3B,EAAA,CAAA;MACA,OAAOjE,OAAO,CAACyD,OAAf,CAAA;EACD,GAHM,MAGA,IAAI/D,IAAI,KAAK,UAAb,EAAyB;EAAA,IAAA,IAAA,iBAAA,EAAA,qBAAA,CAAA;EAC9B,IAAA,CAAA,iBAAA,GAAA,OAAO,CAACyD,QAAR,KAAkBvC,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,qBAAAA,GAAAA,iBAAAA,CAAAA,QAAlB,2CAA4BqD,UAA5B,EAAA,CAAA;MACA,OAAOjE,OAAO,CAACmD,QAAf,CAAA;EACD,GAHM,MAGA;EAAA,IAAA,IAAA,mBAAA,EAAA,qBAAA,EAAA,sBAAA,CAAA;EACL,IAAA,CAAA,mBAAA,GAAA,OAAO,CAAC3I,UAAR,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,CAAA,qBAAA,GAAA,mBAAA,CAAqBkF,IAArB,CAA4BkB,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,CAAAA,sBAAAA,GAAAA,qBAAAA,CAAAA,QAA5B,4CAAsCqD,UAAtC,EAAA,CAAA;EACA,IAAA,OAAOjE,OAAO,CAACxF,UAAR,CAAmBkF,IAAnB,CAAP,CAAA;EACD,GAAA;EACF,CAAA;EAED,IAAIwE,kBAAJ,CAAA;EACA,SAAS1C,kBAAT,CAA4BmB,IAA5B,EAAA;IACE,IAAI,CAACuB,kBAAL,EAAyB;EACvBA,IAAAA,kBAAkB,GAAGnO,QAAQ,CAACoO,aAAT,CAAuB,KAAvB,CAArB,CAAA;EACD,GAAA;IACDD,kBAAkB,CAAC3B,SAAnB,GAA+BI,IAA/B,CAAA;IACA,OAAOuB,kBAAkB,CAAC3B,SAA1B,CAAA;EACD,CAAA;EAED,SAAS6B,gBAAT,CACEjE,EADF,EAEET,IAFF,EAGE4B,CAHF,EAAA;EAKE,EAAA,IAAI,CAACA,CAAC,CAACd,OAAP,EAAgB,OAAA;IAChBc,CAAC,CAACd,OAAF,GAAY,KAAZ,CAAA;EACA,EAAA,IAAMU,GAAG,GAAGI,CAAC,CAACZ,YAAd,CAAA;EACA,EAAA,IAAI,CAACY,CAAC,CAAC9B,SAAF,CAAY6E,MAAjB,EAAyB;EACvBL,IAAAA,2BAA2B,CAAC7D,EAAD,EAAKT,IAAL,CAA3B,CAAA;EACD,GAAA;EACD4B,EAAAA,CAAC,CAACjB,QAAF,CAAWF,EAAX,EAAee,GAAf,CAAA,CAAA;EACD,CAAA;EAED,SAASb,QAAT,CAAkBiB,CAAlB,EAAoCnB,EAApC,EAAA;EACEmB,EAAAA,CAAC,CAACqB,IAAF,IAAUyB,gBAAgB,CAAajE,EAAb,EAAiB,MAAjB,EAAyBmB,CAAC,CAACqB,IAA3B,CAA1B,CAAA;EACArB,EAAAA,CAAC,CAACmC,OAAF,IAAaW,gBAAgB,CAAkBjE,EAAlB,EAAsB,OAAtB,EAA+BmB,CAAC,CAACmC,OAAjC,CAA7B,CAAA;EACAnC,EAAAA,CAAC,CAAC6B,QAAF,IAAciB,gBAAgB,CAAiBjE,EAAjB,EAAqB,UAArB,EAAiCmB,CAAC,CAAC6B,QAAnC,CAA9B,CAAA;IACA9L,MAAM,CAACuD,IAAP,CAAY0G,CAAC,CAAC9G,UAAd,CAAA,CAA0B3B,OAA1B,CAAkC,UAAI,IAAA,EAAA;MACpCuL,gBAAgB,CAAkBjE,EAAlB,EAAsBT,IAAtB,EAA4B4B,CAAC,CAAC9G,UAAF,CAAakF,IAAb,CAA5B,CAAhB,CAAA;KADF,CAAA,CAAA;EAGD,CAAA;EAED,SAAS0B,aAAT,GAAA;IACE7B,QAAQ,CAAC1G,OAAT,CAAiBwH,QAAjB,CAAA,CAAA;EACD,CAAA;;EAGD,SAASiE,aAAT,CAAuBC,QAAvB,EAA2CvE,OAA3C,EAAA;IACE,IAAIC,MAAM,GAA2C,IAArD,CAAA;EACA,EAAA,IAAIsE,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;EAC5BvE,IAAAA,MAAM,GAAGwC,oBAAoB,CAACzC,OAAD,CAA7B,CAAA;EACD,GAFD,MAEO,IAAIuE,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;EACpCvE,IAAAA,MAAM,GAAGuD,qBAAqB,CAACxD,OAAD,CAA9B,CAAA;EACD,GAFM,MAEA,IAAIuE,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;MACxCvE,MAAM,GAAG8D,yBAAyB,CAAC/D,OAAD,EAAUuE,QAAQ,CAACE,SAAnB,CAAlC,CAAA;EACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;EACvCvE,IAAAA,MAAM,GAAGiD,wBAAwB,CAAClD,OAAD,CAAjC,CAAA;EACD,GAAA;IACD,IAAI,CAACC,MAAL,EAAa,OAAA;EACbA,EAAAA,MAAM,CAACT,SAAP,CAAiBkF,IAAjB,CAAsBH,QAAtB,CAAA,CAAA;IACAtE,MAAM,CAACK,cAAP,CAAsBL,MAAtB,CAAA,CAAA;EACD,CAAA;;EAGD,SAAS0E,YAAT,CAAsBJ,QAAtB,EAA0CpE,EAA1C,EAAA;IACE,IAAIF,MAAM,GAA2C,IAArD,CAAA;EACA,EAAA,IAAIsE,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;EAC5BvE,IAAAA,MAAM,GAAGwC,oBAAoB,CAACtC,EAAD,CAA7B,CAAA;EACD,GAFD,MAEO,IAAIoE,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;EACpCvE,IAAAA,MAAM,GAAGuD,qBAAqB,CAACrD,EAAD,CAA9B,CAAA;EACD,GAFM,MAEA,IAAIoE,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;MACxCvE,MAAM,GAAG8D,yBAAyB,CAAC5D,EAAD,EAAKoE,QAAQ,CAACE,SAAd,CAAlC,CAAA;EACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;EACvCvE,IAAAA,MAAM,GAAGiD,wBAAwB,CAAC/C,EAAD,CAAjC,CAAA;EACD,GAAA;IACD,IAAI,CAACF,MAAL,EAAa,OAAA;IACb,IAAM2E,KAAK,GAAG3E,MAAM,CAACT,SAAP,CAAiBqF,OAAjB,CAAyBN,QAAzB,CAAd,CAAA;EACA,EAAA,IAAIK,KAAK,KAAK,CAAC,CAAf,EAAkB3E,MAAM,CAACT,SAAP,CAAiBsF,MAAjB,CAAwBF,KAAxB,EAA+B,CAA/B,CAAA,CAAA;IAClB3E,MAAM,CAACK,cAAP,CAAsBL,MAAtB,CAAA,CAAA;EACD,CAAA;;EAGD,SAAS8E,kBAAT,CAA4BR,QAA5B,EAAA;EACE;EACA;EACA,EAAA,IAAIA,QAAQ,CAACC,IAAT,KAAkB,UAAlB,IAAgCD,QAAQ,CAAChF,QAAT,CAAkB7C,IAAlB,KAA2B,CAA/D,EAAkE,OAAA;IAElE,IAAMsI,gBAAgB,GAAG,IAAI9N,GAAJ,CAAQqN,QAAQ,CAAChF,QAAjB,CAAzB,CAAA;IACA,IAAM0F,gBAAgB,GAAGlP,QAAQ,CAACmP,gBAAT,CAA0BX,QAAQ,CAACY,QAAnC,CAAzB,CAAA;IAEAF,gBAAgB,CAACpM,OAAjB,CAAyB,UAAE,EAAA,EAAA;EACzB,IAAA,IAAI,CAACmM,gBAAgB,CAACjI,GAAjB,CAAqBoD,EAArB,CAAL,EAA+B;EAC7BoE,MAAAA,QAAQ,CAAChF,QAAT,CAAkB7G,GAAlB,CAAsByH,EAAtB,CAAA,CAAA;EACAmE,MAAAA,aAAa,CAACC,QAAD,EAAWpE,EAAX,CAAb,CAAA;EACD,KAAA;KAJH,CAAA,CAAA;EAMD,CAAA;EAED,SAASiF,cAAT,CAAwBb,QAAxB,EAAA;EACEA,EAAAA,QAAQ,CAAChF,QAAT,CAAkB1G,OAAlB,CAA0B,UAAE,EAAA,EAAA;EAAA,IAAA,OAAI8L,YAAY,CAACJ,QAAD,EAAWpE,EAAX,CAAhB,CAAA;KAA5B,CAAA,CAAA;IACAoE,QAAQ,CAAChF,QAAT,CAAkB7H,KAAlB,EAAA,CAAA;IACA8H,SAAS,CAAA,QAAA,CAAT,CAAiB+E,QAAjB,CAAA,CAAA;EACD,CAAA;EAED,SAASc,qBAAT,GAAA;IACE7F,SAAS,CAAC3G,OAAV,CAAkBkM,kBAAlB,CAAA,CAAA;EACD,CAAA;;EAGD,IAAInE,QAAJ,CAAA;EAIgB0E,SAAAA,qBAAAA,GAAAA;EACd,EAAA,IAAI,OAAOvP,QAAP,KAAoB,WAAxB,EAAqC,OAAA;IAErC,IAAI,CAAC6K,QAAL,EAAe;MACbA,QAAQ,GAAG,IAAIC,gBAAJ,CAAqB,YAAA;QAC9BwE,qBAAqB,EAAA,CAAA;EACtB,KAFU,CAAX,CAAA;EAGD,GAAA;IAEDA,qBAAqB,EAAA,CAAA;EACrBzE,EAAAA,QAAQ,CAACI,OAAT,CAAiBjL,QAAQ,CAACwP,eAA1B,EAA2C;EACzC5F,IAAAA,SAAS,EAAE,IAD8B;EAEzCC,IAAAA,OAAO,EAAE,IAFgC;EAGzCpF,IAAAA,UAAU,EAAE,KAH6B;EAIzCqF,IAAAA,aAAa,EAAE,KAAA;KAJjB,CAAA,CAAA;EAMD,CAAA;;EAGDyF,qBAAqB,EAAA,CAAA;EAErB,SAASE,WAAT,CAAqBlE,CAArB,EAAA;EACE;EACA,EAAA,IAAI,OAAOvL,QAAP,KAAoB,WAAxB,EAAqC,OAAOsJ,cAAP,CAAA;;IAErCG,SAAS,CAAC9G,GAAV,CAAc4I,CAAd,CAAA,CAAA;;IAEAyD,kBAAkB,CAACzD,CAAD,CAAlB,CAAA;IACA,OAAO;EACLhC,IAAAA,MAAM,EAAE,SAAA,MAAA,GAAA;QACN8F,cAAc,CAAC9D,CAAD,CAAd,CAAA;EACD,KAAA;KAHH,CAAA;EAKD,CAAA;EAED,SAASqB,IAAT,CACEwC,QADF,EAEE5D,MAFF,EAAA;EAIE,EAAA,OAAOiE,WAAW,CAAC;EACjBhB,IAAAA,IAAI,EAAE,MADW;MAEjBjF,QAAQ,EAAE,IAAIrI,GAAJ,EAFO;EAGjBqK,IAAAA,MAAM,EAANA,MAHiB;EAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;EAJiB,GAAD,CAAlB,CAAA;EAMD,CAAA;EAED,SAAShC,QAAT,CACEgC,QADF,EAEE5D,MAFF,EAAA;EAIE,EAAA,OAAOiE,WAAW,CAAC;EACjBhB,IAAAA,IAAI,EAAE,UADW;MAEjBjF,QAAQ,EAAE,IAAIrI,GAAJ,EAFO;EAGjBqK,IAAAA,MAAM,EAANA,MAHiB;EAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;EAJiB,GAAD,CAAlB,CAAA;EAMD,CAAA;EAED,SAAS1B,OAAT,CACE0B,QADF,EAEE5D,MAFF,EAAA;EAIE,EAAA,OAAOiE,WAAW,CAAC;EACjBhB,IAAAA,IAAI,EAAE,OADW;MAEjBjF,QAAQ,EAAE,IAAIrI,GAAJ,EAFO;EAGjBqK,IAAAA,MAAM,EAANA,MAHiB;EAIjB4D,IAAAA,QAAQ,EAARA,QAAAA;EAJiB,GAAD,CAAlB,CAAA;EAMD,CAAA;EAED,SAASV,SAAT,CACEU,QADF,EAEEV,SAFF,EAGElD,MAHF,EAAA;IAKE,IAAI,CAACnC,kBAAkB,CAACqG,IAAnB,CAAwBhB,SAAxB,CAAL,EAAyC,OAAOpF,cAAP,CAAA;EAEzC,EAAA,IAAIoF,SAAS,KAAK,OAAd,IAAyBA,SAAS,KAAK,WAA3C,EAAwD;EACtD,IAAA,OAAOhB,OAAO,CAAC0B,QAAD,EAAW,UAAU,UAAA,EAAA;EACjC,MAAA,IAAMO,iBAAiB,GAAGnE,MAAM,CAAClI,KAAK,CAACC,IAAN,CAAWqM,UAAX,CAAuB9D,CAAAA,IAAvB,CAA4B,GAA5B,CAAD,CAAhC,CAAA;EACA8D,MAAAA,UAAU,CAACjO,KAAX,EAAA,CAAA;QACA,IAAI,CAACgO,iBAAL,EAAwB,OAAA;QACxBA,iBAAiB,CACdhE,KADH,CACS,MADT,CAAA,CAEGC,MAFH,CAEUC,OAFV,CAAA,CAGG/I,OAHH,CAGW,UAAC,CAAA,EAAA;EAAA,QAAA,OAAI8M,UAAU,CAACjN,GAAX,CAAekN,CAAf,CAAJ,CAAA;SAHZ,CAAA,CAAA;EAID,KARa,CAAd,CAAA;EASD,GAAA;EAED,EAAA,OAAOJ,WAAW,CAAC;EACjBhB,IAAAA,IAAI,EAAE,WADW;EAEjBC,IAAAA,SAAS,EAATA,SAFiB;MAGjBlF,QAAQ,EAAE,IAAIrI,GAAJ,EAHO;EAIjBqK,IAAAA,MAAM,EAANA,MAJiB;EAKjB4D,IAAAA,QAAQ,EAARA,QAAAA;EALiB,GAAD,CAAlB,CAAA;EAOD,CAAA;EAED,SAASU,WAAT,CAAA,KAAA,EAAA;EACEV,EAAAA,IAAAA,QAAAA,GAAAA,KAAAA,CAAAA,QAAAA;EACAW,IAAAA,MAAAA,GAAAA,KAAAA,CAAAA,MAAAA;EACApK,IAAAA,KAAAA,GAAAA,KAAAA,CAAAA,KAAAA;MACWgE,IAAX+E,GAAAA,KAAAA,CAAAA,SAAAA;EACAzC,IAAAA,cAAAA,GAAAA,KAAAA,CAAAA,cAAAA;EACAC,IAAAA,oBAAAA,GAAAA,KAAAA,CAAAA,oBAAAA,CAAAA;IAEA,IAAIvC,IAAI,KAAK,MAAb,EAAqB;MACnB,IAAIoG,MAAM,KAAK,QAAf,EAAyB;EACvB,MAAA,OAAOnD,IAAI,CAACwC,QAAD,EAAW,UAAG,GAAA,EAAA;EAAA,QAAA,OAAIjE,GAAG,IAAIxF,KAAJ,WAAIA,KAAJ,GAAa,EAAb,CAAP,CAAA;EAAA,OAAd,CAAX,CAAA;EACD,KAFD,MAEO,IAAIoK,MAAM,KAAK,KAAf,EAAsB;QAC3B,OAAOnD,IAAI,CAACwC,QAAD,EAAW,YAAA;EAAA,QAAA,OAAMzJ,KAAN,IAAA,IAAA,GAAMA,KAAN,GAAe,EAAf,CAAA;EAAA,OAAX,CAAX,CAAA;EACD,KAAA;EACF,GAND,MAMO,IAAIgE,IAAI,KAAK,OAAb,EAAsB;MAC3B,IAAIoG,MAAM,KAAK,QAAf,EAAyB;EACvB,MAAA,OAAOrC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;EAC1B,QAAA,IAAIzJ,KAAJ,EAAWwF,GAAG,CAACxI,GAAJ,CAAQgD,KAAR,CAAA,CAAA;EACZ,OAFa,CAAd,CAAA;EAGD,KAJD,MAIO,IAAIoK,MAAM,KAAK,QAAf,EAAyB;EAC9B,MAAA,OAAOrC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;EAC1B,QAAA,IAAIzJ,KAAJ,EAAWwF,GAAG,CAAA,QAAA,CAAH,CAAWxF,KAAX,CAAA,CAAA;EACZ,OAFa,CAAd,CAAA;EAGD,KAJM,MAIA,IAAIoK,MAAM,KAAK,KAAf,EAAsB;EAC3B,MAAA,OAAOrC,OAAO,CAAC0B,QAAD,EAAW,UAAG,GAAA,EAAA;EAC1BjE,QAAAA,GAAG,CAACxJ,KAAJ,EAAA,CAAA;EACA,QAAA,IAAIgE,KAAJ,EAAWwF,GAAG,CAACxI,GAAJ,CAAQgD,KAAR,CAAA,CAAA;EACZ,OAHa,CAAd,CAAA;EAID,KAAA;EACF,GAfM,MAeA,IAAIgE,IAAI,KAAK,UAAb,EAAyB;EAC9B,IAAA,IAAIoG,MAAM,KAAK,KAAX,IAAoB9D,cAAxB,EAAwC;QACtC,OAAOmB,QAAQ,CAACgC,QAAD,EAAW,YAAA;UAAA,OAAO;EAC/BlD,UAAAA,oBAAoB,EAApBA,oBAD+B;EAE/BD,UAAAA,cAAc,EAAdA,cAAAA;WAFwB,CAAA;EAAA,OAAX,CAAf,CAAA;EAID,KAAA;EACF,GAPM,MAOA;MACL,IAAI8D,MAAM,KAAK,QAAf,EAAyB;EACvB,MAAA,OAAOrB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,UAAG,GAAA,EAAA;EAAA,QAAA,OAClCwB,GAAG,KAAK,IAAR,GAAeA,GAAG,IAAIxF,KAAJ,IAAA,IAAA,GAAIA,KAAJ,GAAa,EAAb,CAAlB,GAAqCA,KAArC,IAAqCA,IAAAA,GAAAA,KAArC,GAA8C,EADZ,CAAA;EAAA,OAApB,CAAhB,CAAA;EAGD,KAJD,MAIO,IAAIoK,MAAM,KAAK,KAAf,EAAsB;EAC3B,MAAA,OAAOrB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,YAAA;EAAA,QAAA,OAAMhE,KAAN,IAAA,IAAA,GAAMA,KAAN,GAAe,EAAf,CAAA;EAAA,OAAjB,CAAhB,CAAA;EACD,KAFM,MAEA,IAAIoK,MAAM,KAAK,QAAf,EAAyB;EAC9B,MAAA,OAAOrB,SAAS,CAACU,QAAD,EAAWzF,IAAX,EAAiB,YAAA;EAAA,QAAA,OAAM,IAAN,CAAA;EAAA,OAAjB,CAAhB,CAAA;EACD,KAAA;EACF,GAAA;EACD,EAAA,OAAOL,cAAP,CAAA;EACD,CAAA;EAeD,IAAe,KAAA,GAAA;EACbsD,EAAAA,IAAI,EAAJA,IADa;EAEbc,EAAAA,OAAO,EAAPA,OAFa;EAGbgB,EAAAA,SAAS,EAATA,SAHa;EAIbtB,EAAAA,QAAQ,EAARA,QAJa;EAKb0C,EAAAA,WAAW,EAAXA,WAAAA;EALa,CAAf;;ECzgBA,SAASE,UAAU,CAACC,GAAW,EAAU;IACvC,IAAIC,IAAI,GAAG,UAAU,CAAA;EACrB,EAAA,MAAMC,CAAC,GAAGF,GAAG,CAAC3B,MAAM,CAAA;IAEpB,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuJ,CAAC,EAAEvJ,CAAC,EAAE,EAAE;EAC1BsJ,IAAAA,IAAI,IAAID,GAAG,CAACG,UAAU,CAACxJ,CAAC,CAAC,CAAA;MACzBsJ,IAAI,IACF,CAACA,IAAI,IAAI,CAAC,KAAKA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,EAAE,CAAC,CAAA;EACxE,GAAA;IACA,OAAOA,IAAI,KAAK,CAAC,CAAA;EACnB,CAAA;EAEO,SAASG,IAAI,CAClBC,IAAY,EACZ3K,KAAa,EACbmB,OAAe,EACA;EACf;IACA,IAAIA,OAAO,KAAK,CAAC,EAAE;EACjB,IAAA,OAAQkJ,UAAU,CAACA,UAAU,CAACM,IAAI,GAAG3K,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,GAAI,KAAK,CAAA;EACpE,GAAA;EACA;IACA,IAAImB,OAAO,KAAK,CAAC,EAAE;MACjB,OAAQkJ,UAAU,CAACrK,KAAK,GAAG2K,IAAI,CAAC,GAAG,IAAI,GAAI,IAAI,CAAA;EACjD,GAAA;;EAEA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;EAEO,SAASC,eAAe,CAACC,CAAS,EAAY;EACnD,EAAA,IAAIA,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAA;IACrB,OAAO,IAAIlN,KAAK,CAACkN,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,GAAGD,CAAC,CAAC,CAAA;EACjC,CAAA;EAEO,SAASE,OAAO,CAACF,CAAS,EAAEG,KAAqB,EAAW;EACjE,EAAA,OAAOH,CAAC,IAAIG,KAAK,CAAC,CAAC,CAAC,IAAIH,CAAC,GAAGG,KAAK,CAAC,CAAC,CAAC,CAAA;EACtC,CAAA;EAEO,SAASC,WAAW,CACzBC,SAAiB,EACjBC,SAAmC,EAC1B;EACT,EAAA,MAAMN,CAAC,GAAGH,IAAI,CAAC,IAAI,GAAGS,SAAS,CAAC,CAAC,CAAC,EAAED,SAAS,EAAE,CAAC,CAAC,CAAA;EACjD,EAAA,IAAIL,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,CAAA;EAC5B,EAAA,OAAOA,CAAC,IAAIM,SAAS,CAAC,CAAC,CAAC,IAAIN,CAAC,GAAGM,SAAS,CAAC,CAAC,CAAC,CAAA;EAC9C,CAAA;EAEO,SAASC,eAAe,CAACP,CAAS,EAAEQ,MAAwB,EAAU;EAC3E,EAAA,KAAK,IAAIpK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoK,MAAM,CAAC1C,MAAM,EAAE1H,CAAC,EAAE,EAAE;MACtC,IAAI8J,OAAO,CAACF,CAAC,EAAEQ,MAAM,CAACpK,CAAC,CAAC,CAAC,EAAE;EACzB,MAAA,OAAOA,CAAC,CAAA;EACV,KAAA;EACF,GAAA;EACA,EAAA,OAAO,CAAC,CAAC,CAAA;EACX,CAAA;EAEO,SAASqK,YAAY,CAACC,WAAmB,EAAsB;IACpE,IAAI;MACF,MAAMC,OAAO,GAAGD,WAAW,CAACE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAA;EAC1D,IAAA,OAAO,IAAIC,MAAM,CAACF,OAAO,CAAC,CAAA;KAC3B,CAAC,OAAOxQ,CAAC,EAAE;EACV2Q,IAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;EAChB,IAAA,OAAOjC,SAAS,CAAA;EAClB,GAAA;EACF,CAAA;EAEO,SAAS8S,aAAa,CAACvM,GAAW,EAAEwM,OAAoB,EAAE;EAC/D,EAAA,IAAI,CAACA,OAAO,CAACnD,MAAM,EAAE,OAAO,KAAK,CAAA;IACjC,IAAIoD,eAAe,GAAG,KAAK,CAAA;IAC3B,IAAIC,UAAU,GAAG,KAAK,CAAA;EAEtB,EAAA,KAAK,IAAI/K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6K,OAAO,CAACnD,MAAM,EAAE1H,CAAC,EAAE,EAAE;EACvC,IAAA,MAAMgL,KAAK,GAAGC,cAAc,CAAC5M,GAAG,EAAEwM,OAAO,CAAC7K,CAAC,CAAC,CAAC6B,IAAI,EAAEgJ,OAAO,CAAC7K,CAAC,CAAC,CAACkL,OAAO,CAAC,CAAA;MACtE,IAAIL,OAAO,CAAC7K,CAAC,CAAC,CAACmL,OAAO,KAAK,KAAK,EAAE;QAChC,IAAIH,KAAK,EAAE,OAAO,KAAK,CAAA;EACzB,KAAC,MAAM;EACLF,MAAAA,eAAe,GAAG,IAAI,CAAA;EACtB,MAAA,IAAIE,KAAK,EAAED,UAAU,GAAG,IAAI,CAAA;EAC9B,KAAA;EACF,GAAA;IAEA,OAAOA,UAAU,IAAI,CAACD,eAAe,CAAA;EACvC,CAAA;EAEA,SAASM,kBAAkB,CACzBC,MAAc,EACdH,OAAe,EACfI,MAAe,EACN;IACT,IAAI;EACF;EACA,IAAA,IAAIf,OAAO,GAAGW,OAAO,CAClBV,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CACtCA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;EAE1B,IAAA,IAAIc,MAAM,EAAE;EACV;EACAf,MAAAA,OAAO,GAAG,MAAM,GAAGA,OAAO,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,GAAG,MAAM,CAAA;EAC/D,KAAA;EAEA,IAAA,MAAMe,KAAK,GAAG,IAAId,MAAM,CAAC,GAAG,GAAGF,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,CAAA;EAClD,IAAA,OAAOgB,KAAK,CAACzC,IAAI,CAACuC,MAAM,CAAC,CAAA;KAC1B,CAAC,OAAOtR,CAAC,EAAE;EACV,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEA,SAASyR,oBAAoB,CAACH,MAAW,EAAEH,OAAe,EAAE;IAC1D,IAAI;EACF;EACA;MACA,MAAMO,QAAQ,GAAG,IAAIC,GAAG,CACtBR,OAAO,CAACV,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,EACvE,eAAe,CAChB,CAAA;;EAED;MACA,MAAMmB,KAAuC,GAAG,CAC9C,CAACN,MAAM,CAAChT,IAAI,EAAEoT,QAAQ,CAACpT,IAAI,EAAE,KAAK,CAAC,EACnC,CAACgT,MAAM,CAACO,QAAQ,EAAEH,QAAQ,CAACG,QAAQ,EAAE,IAAI,CAAC,CAC3C,CAAA;EACD;MACA,IAAIH,QAAQ,CAAChC,IAAI,EAAE;EACjBkC,MAAAA,KAAK,CAAC5D,IAAI,CAAC,CAACsD,MAAM,CAAC5B,IAAI,EAAEgC,QAAQ,CAAChC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAA;EACjD,KAAA;MAEAgC,QAAQ,CAACI,YAAY,CAAC3P,OAAO,CAAC,CAAC4P,CAAC,EAAEC,CAAC,KAAK;EACtCJ,MAAAA,KAAK,CAAC5D,IAAI,CAAC,CAACsD,MAAM,CAACQ,YAAY,CAAC/P,GAAG,CAACiQ,CAAC,CAAC,IAAI,EAAE,EAAED,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;EAC1D,KAAC,CAAC,CAAA;;EAEF;MACA,OAAO,CAACH,KAAK,CAACK,IAAI,CACfzQ,IAAI,IAAK,CAAC6P,kBAAkB,CAAC7P,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD,CAAA;KACF,CAAC,OAAOxB,CAAC,EAAE;EACV,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEA,SAASkR,cAAc,CACrB5M,GAAW,EACXwD,IAAmB,EACnBqJ,OAAe,EACN;IACT,IAAI;MACF,MAAMjM,MAAM,GAAG,IAAIyM,GAAG,CAACrN,GAAG,EAAE,WAAW,CAAC,CAAA;MAExC,IAAIwD,IAAI,KAAK,OAAO,EAAE;EACpB,MAAA,MAAM0J,KAAK,GAAGlB,YAAY,CAACa,OAAO,CAAC,CAAA;EACnC,MAAA,IAAI,CAACK,KAAK,EAAE,OAAO,KAAK,CAAA;QACxB,OACEA,KAAK,CAACzC,IAAI,CAAC7J,MAAM,CAACgN,IAAI,CAAC,IACvBV,KAAK,CAACzC,IAAI,CAAC7J,MAAM,CAACgN,IAAI,CAACC,SAAS,CAACjN,MAAM,CAACkN,MAAM,CAACzE,MAAM,CAAC,CAAC,CAAA;EAE3D,KAAC,MAAM,IAAI7F,IAAI,KAAK,QAAQ,EAAE;EAC5B,MAAA,OAAO2J,oBAAoB,CAACvM,MAAM,EAAEiM,OAAO,CAAC,CAAA;EAC9C,KAAA;EAEA,IAAA,OAAO,KAAK,CAAA;KACb,CAAC,OAAOnR,CAAC,EAAE;EACV,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEO,SAASqS,eAAe,CAC7BC,aAAqB,EACrBC,QAA4B,EAC5BC,OAAkB,EACA;EAClBD,EAAAA,QAAQ,GAAGA,QAAQ,KAAKxU,SAAS,GAAG,CAAC,GAAGwU,QAAQ,CAAA;;EAEhD;IACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;EAIhBA,IAAAA,QAAQ,GAAG,CAAC,CAAA;EACd,GAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;EAIvBA,IAAAA,QAAQ,GAAG,CAAC,CAAA;EACd,GAAA;;EAEA;EACA,EAAA,MAAME,KAAK,GAAG7C,eAAe,CAAC0C,aAAa,CAAC,CAAA;IAC5CE,OAAO,GAAGA,OAAO,IAAIC,KAAK,CAAA;EAC1B,EAAA,IAAID,OAAO,CAAC7E,MAAM,KAAK2E,aAAa,EAAE;EAMpCE,IAAAA,OAAO,GAAGC,KAAK,CAAA;EACjB,GAAA;;EAEA;EACA,EAAA,MAAMC,WAAW,GAAGF,OAAO,CAACG,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKA,GAAG,GAAGD,CAAC,EAAE,CAAC,CAAC,CAAA;EAC1D,EAAA,IAAIF,WAAW,GAAG,IAAI,IAAIA,WAAW,GAAG,IAAI,EAAE;EAI5CF,IAAAA,OAAO,GAAGC,KAAK,CAAA;EACjB,GAAA;;EAEA;IACA,IAAIK,UAAU,GAAG,CAAC,CAAA;EAClB,EAAA,OAAON,OAAO,CAAChN,GAAG,CAAEoN,CAAC,IAAK;MACxB,MAAMG,KAAK,GAAGD,UAAU,CAAA;EACxBA,IAAAA,UAAU,IAAIF,CAAC,CAAA;MACf,OAAO,CAACG,KAAK,EAAEA,KAAK,GAAIR,QAAQ,GAAcK,CAAC,CAAC,CAAA;EAClD,GAAC,CAAC,CAAA;EACJ,CAAA;EAEO,SAASI,sBAAsB,CACpCC,EAAU,EACV3O,GAAW,EACXgO,aAAqB,EACrB;IACA,IAAI,CAAChO,GAAG,EAAE;EACR,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,MAAM4O,MAAM,GAAG5O,GAAG,CAAC0G,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAA;IAChC,IAAI,CAACkI,MAAM,EAAE;EACX,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,MAAMjC,KAAK,GAAGiC,MAAM,CACjBzC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAAC,GACnBzF,KAAK,CAAC,GAAG,CAAC;EAAC,GACXxF,GAAG,CAAE2N,EAAE,IAAKA,EAAE,CAACnI,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAC7BC,MAAM,CAAC,IAAA,IAAA;MAAA,IAAC,CAAC+G,CAAC,CAAC,GAAA,IAAA,CAAA;MAAA,OAAKA,CAAC,KAAKiB,EAAE,CAAA;EAAA,GAAA,CAAC;EAAC,GAC1BzN,GAAG,CAAC,KAAA,IAAA;MAAA,IAAC,GAAGuM,CAAC,CAAC,GAAA,KAAA,CAAA;MAAA,OAAKqB,QAAQ,CAACrB,CAAC,CAAC,CAAA;EAAA,GAAA,CAAC,CAAC;;IAE/B,IAAId,KAAK,CAACtD,MAAM,GAAG,CAAC,IAAIsD,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGqB,aAAa,EAC/D,OAAOrB,KAAK,CAAC,CAAC,CAAC,CAAA;EAEjB,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;EAEO,SAASD,UAAU,CAACI,OAAsB,EAAE;IACjD,IAAI;EACF,IAAA,OAAOA,OAAO,EAAE,CAAA;KACjB,CAAC,OAAOpR,CAAC,EAAE;EACV2Q,IAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;EAChB,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;EACF,CAAA;EAEA,MAAMqT,WAAW,GAAI1N,CAAS,IAC5B2N,UAAU,CAAC1Q,IAAI,CAAC2Q,IAAI,CAAC5N,CAAC,CAAC,EAAGuJ,CAAC,IAAKA,CAAC,CAACO,UAAU,CAAC,CAAC,CAAC,CAAC,CAAA;EAE3C,eAAe+D,OAAO,CAC3BC,eAAuB,EACvBC,aAAsB,EACtBxV,MAAqB,EACJ;IACjBwV,aAAa,GAAGA,aAAa,IAAI,EAAE,CAAA;IACnCxV,MAAM,GAAGA,MAAM,IAAKL,UAAU,CAACI,MAAM,IAAIJ,UAAU,CAACI,MAAM,CAACC,MAAO,CAAA;IAClE,IAAI,CAACA,MAAM,EAAE;EACX,IAAA,MAAM,IAAIyV,KAAK,CAAC,sCAAsC,CAAC,CAAA;EACzD,GAAA;IACA,IAAI;EACF,IAAA,MAAM/R,GAAG,GAAG,MAAM1D,MAAM,CAAC0V,SAAS,CAChC,KAAK,EACLP,WAAW,CAACK,aAAa,CAAC,EAC1B;EAAEG,MAAAA,IAAI,EAAE,SAAS;EAAElG,MAAAA,MAAM,EAAE,GAAA;OAAK,EAChC,IAAI,EACJ,CAAC,SAAS,EAAE,SAAS,CAAC,CACvB,CAAA;MACD,MAAM,CAACmG,EAAE,EAAEC,UAAU,CAAC,GAAGN,eAAe,CAACzI,KAAK,CAAC,GAAG,CAAC,CAAA;EACnD,IAAA,MAAMgJ,eAAe,GAAG,MAAM9V,MAAM,CAACsV,OAAO,CAC1C;EAAEK,MAAAA,IAAI,EAAE,SAAS;QAAEC,EAAE,EAAET,WAAW,CAACS,EAAE,CAAA;EAAE,KAAC,EACxClS,GAAG,EACHyR,WAAW,CAACU,UAAU,CAAC,CACxB,CAAA;EAED,IAAA,OAAO,IAAIE,WAAW,EAAE,CAACC,MAAM,CAACF,eAAe,CAAC,CAAA;KACjD,CAAC,OAAOhU,CAAC,EAAE;EACV,IAAA,MAAM,IAAI2T,KAAK,CAAC,mBAAmB,CAAC,CAAA;EACtC,GAAA;EACF,CAAA;;EAEA;EACO,SAASQ,QAAQ,CAACC,KAAU,EAAU;EAC3C,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK,CAAA;EAC3C,EAAA,OAAOtV,IAAI,CAACC,SAAS,CAACqV,KAAK,CAAC,CAAA;EAC9B,CAAA;;EAEA;EACO,SAASC,mBAAmB,CAACD,KAAU,EAAU;EACtD,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGA,KAAK,GAAG,EAAE,CAAA;EACpB,GAAA;EACA,EAAA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;EACvCA,IAAAA,KAAK,GAAG,GAAG,CAAA;EACb,GAAA;EACA;EACA;EACA;EACA,EAAA,MAAME,KAAK,GAAIF,KAAK,CAAY3D,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAACzF,KAAK,CAAC,MAAM,CAAC,CAAA;;EAExE;EACA;EACA;EACA,EAAA,IAAIsJ,KAAK,CAAC3G,MAAM,KAAK,CAAC,EAAE;EACtB2G,IAAAA,KAAK,CAACtG,IAAI,CAAC,GAAG,CAAC,CAAA;EACjB,GAAA;;EAEA;EACA;EACA,EAAA,OAAOsG,KAAK,CACT9O,GAAG,CAAEuM,CAAC,IAAMA,CAAC,CAACd,KAAK,CAAC,UAAU,CAAC,GAAGc,CAAC,CAACwC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAGxC,CAAE,CAAC,CAC1D5G,IAAI,CAAC,GAAG,CAAC,CAAA;EACd,CAAA;EAEO,SAASqJ,cAAc,GAAW;EACvC,EAAA,IAAIrO,OAAe,CAAA;IACnB,IAAI;EACF;EACAA,IAAAA,OAAO,GAAkB,QAAA,CAAA;KAC1B,CAAC,OAAOnG,CAAC,EAAE;EACVmG,IAAAA,OAAO,GAAG,EAAE,CAAA;EACd,GAAA;EACA,EAAA,OAAOA,OAAO,CAAA;EAChB;;ECzUA;EAYA,MAAMsO,WAAsC,GAAG,EAAE,CAAA;;EAEjD;EACO,SAASC,aAAa,CAC3BC,GAAc,EACdC,SAA6B,EACpB;EACT;IACA,IAAI,KAAK,IAAIA,SAAS,EAAE;MACtB,OAAOC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,KAAK,CAAC,CAAyB,CAAA;EAC9D,GAAA;IACA,IAAI,MAAM,IAAIA,SAAS,EAAE;MACvB,OAAO,CAACC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB,CAAA;EAChE,GAAA;IACA,IAAI,MAAM,IAAIA,SAAS,EAAE;MACvB,OAAOE,OAAO,CAACH,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB,CAAA;EAChE,GAAA;IACA,IAAI,MAAM,IAAIA,SAAS,EAAE;MACvB,OAAO,CAACF,aAAa,CAACC,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAuB,CAAA;EACrE,GAAA;;EAEA;EACA,EAAA,KAAK,MAAM,CAAC5C,CAAC,EAAED,CAAC,CAAC,IAAIpR,MAAM,CAACkC,OAAO,CAAC+R,SAAS,CAAC,EAAE;EAC9C,IAAA,IAAI,CAACG,kBAAkB,CAAChD,CAAC,EAAEiD,OAAO,CAACL,GAAG,EAAE3C,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK,CAAA;EAC3D,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;;EAEA;EACA,SAASgD,OAAO,CAACL,GAAc,EAAEM,IAAY,EAAE;EAC7C,EAAA,MAAMX,KAAK,GAAGW,IAAI,CAACjK,KAAK,CAAC,GAAG,CAAC,CAAA;IAC7B,IAAIkK,OAAY,GAAGP,GAAG,CAAA;EACtB,EAAA,KAAK,IAAI1O,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqO,KAAK,CAAC3G,MAAM,EAAE1H,CAAC,EAAE,EAAE;EACrC,IAAA,IAAIiP,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIZ,KAAK,CAACrO,CAAC,CAAC,IAAIiP,OAAO,EAAE;EACjEA,MAAAA,OAAO,GAAGA,OAAO,CAACZ,KAAK,CAACrO,CAAC,CAAC,CAAC,CAAA;EAC7B,KAAC,MAAM;EACL,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACF,GAAA;EACA,EAAA,OAAOiP,OAAO,CAAA;EAChB,CAAA;;EAEA;EACA,SAASC,QAAQ,CAAC3D,KAAa,EAAU;EACvC,EAAA,IAAI,CAACiD,WAAW,CAACjD,KAAK,CAAC,EAAE;EACvBiD,IAAAA,WAAW,CAACjD,KAAK,CAAC,GAAG,IAAId,MAAM,CAACc,KAAK,CAACf,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC,CAAA;EACvE,GAAA;IACA,OAAOgE,WAAW,CAACjD,KAAK,CAAC,CAAA;EAC3B,CAAA;;EAEA;EACA,SAASuD,kBAAkB,CAACH,SAAyB,EAAE5P,KAAU,EAAE;EACjE;EACA,EAAA,IAAI,OAAO4P,SAAS,KAAK,QAAQ,EAAE;EACjC,IAAA,OAAO5P,KAAK,GAAG,EAAE,KAAK4P,SAAS,CAAA;EACjC,GAAA;EACA,EAAA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;EACjC,IAAA,OAAO5P,KAAK,GAAG,CAAC,KAAK4P,SAAS,CAAA;EAChC,GAAA;EACA,EAAA,IAAI,OAAOA,SAAS,KAAK,SAAS,EAAE;EAClC,IAAA,OAAO,CAAC,CAAC5P,KAAK,KAAK4P,SAAS,CAAA;EAC9B,GAAA;IAEA,IAAIA,SAAS,KAAK,IAAI,EAAE;MACtB,OAAO5P,KAAK,KAAK,IAAI,CAAA;EACvB,GAAA;EAEA,EAAA,IAAIrC,KAAK,CAACyC,OAAO,CAACwP,SAAS,CAAC,IAAI,CAACQ,gBAAgB,CAACR,SAAS,CAAC,EAAE;EAC5D,IAAA,OAAO9V,IAAI,CAACC,SAAS,CAACiG,KAAK,CAAC,KAAKlG,IAAI,CAACC,SAAS,CAAC6V,SAAS,CAAC,CAAA;EAC5D,GAAA;;EAEA;EACA,EAAA,KAAK,MAAMS,EAAE,IAAIT,SAAS,EAAE;EAC1B,IAAA,IACE,CAACU,qBAAqB,CACpBD,EAAE,EACFrQ,KAAK,EACL4P,SAAS,CAACS,EAAE,CAAiC,CAC9C,EACD;EACA,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EACF,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb,CAAA;;EAEA;EACA,SAASD,gBAAgB,CAACT,GAAQ,EAAW;EAC3C,EAAA,MAAMzQ,IAAI,GAAGvD,MAAM,CAACuD,IAAI,CAACyQ,GAAG,CAAC,CAAA;IAC7B,OACEzQ,IAAI,CAACyJ,MAAM,GAAG,CAAC,IAAIzJ,IAAI,CAAC+G,MAAM,CAAE+G,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAACrE,MAAM,KAAKzJ,IAAI,CAACyJ,MAAM,CAAA;EAE9E,CAAA;;EAEA;EACA,SAAS4H,OAAO,CAACxD,CAAM,EAAuB;EAC5C,EAAA,IAAIA,CAAC,KAAK,IAAI,EAAE,OAAO,MAAM,CAAA;IAC7B,IAAIpP,KAAK,CAACyC,OAAO,CAAC2M,CAAC,CAAC,EAAE,OAAO,OAAO,CAAA;IACpC,MAAMyD,CAAC,GAAG,OAAOzD,CAAC,CAAA;EAClB,EAAA,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC1J,QAAQ,CAACmN,CAAC,CAAC,EAAE;EACtE,IAAA,OAAOA,CAAC,CAAA;EACV,GAAA;EACA,EAAA,OAAO,SAAS,CAAA;EAClB,CAAA;;EAEA;EACA,SAASC,SAAS,CAACnE,MAAW,EAAEI,QAAa,EAAE;IAC7C,IAAI,CAAC/O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;IACxC,MAAMoE,KAAK,GAAGN,gBAAgB,CAAC1D,QAAQ,CAAC,GACnCK,CAAM,IAAKgD,kBAAkB,CAACrD,QAAQ,EAAEK,CAAC,CAAC,GAC1CA,CAAM,IAAK2C,aAAa,CAAC3C,CAAC,EAAEL,QAAQ,CAAC,CAAA;EAC1C,EAAA,KAAK,IAAIzL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqL,MAAM,CAAC3D,MAAM,EAAE1H,CAAC,EAAE,EAAE;EACtC,IAAA,IAAIqL,MAAM,CAACrL,CAAC,CAAC,IAAIyP,KAAK,CAACpE,MAAM,CAACrL,CAAC,CAAC,CAAC,EAAE;EACjC,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACF,GAAA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAA;EAEA,SAAS0P,IAAI,CAACrE,MAAW,EAAEI,QAAoB,EAAW;EACxD;EACA,EAAA,IAAI/O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE;EACzB,IAAA,OAAOA,MAAM,CAACW,IAAI,CAAExI,EAAE,IAAKiI,QAAQ,CAACrJ,QAAQ,CAACoB,EAAE,CAAC,CAAC,CAAA;EACnD,GAAA;EACA,EAAA,OAAOiI,QAAQ,CAACrJ,QAAQ,CAACiJ,MAAM,CAAC,CAAA;EAClC,CAAA;;EAEA;EACA,SAASgE,qBAAqB,CAC5BM,QAAkB,EAClBtE,MAAW,EACXI,QAAa,EACJ;EACT,EAAA,QAAQkE,QAAQ;EACd,IAAA,KAAK,MAAM;QACT,OAAOvB,mBAAmB,CAAC/C,MAAM,CAAC,KAAK+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACtE,IAAA,KAAK,MAAM;QACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,KAAK+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACtE,IAAA,KAAK,MAAM;QACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,GAAG+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACpE,IAAA,KAAK,OAAO;QACV,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,IAAI+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACrE,IAAA,KAAK,MAAM;QACT,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,GAAG+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACpE,IAAA,KAAK,OAAO;QACV,OAAO2C,mBAAmB,CAAC/C,MAAM,CAAC,IAAI+C,mBAAmB,CAAC3C,QAAQ,CAAC,CAAA;EACrE,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,KAAKI,QAAQ,CAAA;EAC5B,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,KAAKI,QAAQ,CAAA;EAC5B,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,GAAGI,QAAQ,CAAA;EAC1B,IAAA,KAAK,MAAM;QACT,OAAOJ,MAAM,IAAII,QAAQ,CAAA;EAC3B,IAAA,KAAK,KAAK;QACR,OAAOJ,MAAM,GAAGI,QAAQ,CAAA;EAC1B,IAAA,KAAK,MAAM;QACT,OAAOJ,MAAM,IAAII,QAAQ,CAAA;EAC3B,IAAA,KAAK,SAAS;EACZ;QACA,OAAOA,QAAQ,GAAGJ,MAAM,IAAI,IAAI,GAAGA,MAAM,IAAI,IAAI,CAAA;EACnD,IAAA,KAAK,KAAK;QACR,IAAI,CAAC3O,KAAK,CAACyC,OAAO,CAACsM,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAA;EAC1C,MAAA,OAAOiE,IAAI,CAACrE,MAAM,EAAEI,QAAQ,CAAC,CAAA;EAC/B,IAAA,KAAK,MAAM;QACT,IAAI,CAAC/O,KAAK,CAACyC,OAAO,CAACsM,QAAQ,CAAC,EAAE,OAAO,KAAK,CAAA;EAC1C,MAAA,OAAO,CAACiE,IAAI,CAACrE,MAAM,EAAEI,QAAQ,CAAC,CAAA;EAChC,IAAA,KAAK,MAAM;EACT,MAAA,OAAO,CAACqD,kBAAkB,CAACrD,QAAQ,EAAEJ,MAAM,CAAC,CAAA;EAC9C,IAAA,KAAK,OAAO;QACV,IAAI,CAAC3O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;EACxC,MAAA,OAAOyD,kBAAkB,CAACrD,QAAQ,EAAEJ,MAAM,CAAC3D,MAAM,CAAC,CAAA;EACpD,IAAA,KAAK,YAAY;EACf,MAAA,OAAO8H,SAAS,CAACnE,MAAM,EAAEI,QAAQ,CAAC,CAAA;EACpC,IAAA,KAAK,MAAM;QACT,IAAI,CAAC/O,KAAK,CAACyC,OAAO,CAACkM,MAAM,CAAC,EAAE,OAAO,KAAK,CAAA;EACxC,MAAA,KAAK,IAAIrL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyL,QAAQ,CAAC/D,MAAM,EAAE1H,CAAC,EAAE,EAAE;UACxC,IAAI4P,MAAM,GAAG,KAAK,CAAA;EAClB,QAAA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxE,MAAM,CAAC3D,MAAM,EAAEmI,CAAC,EAAE,EAAE;EACtC,UAAA,IAAIf,kBAAkB,CAACrD,QAAQ,CAACzL,CAAC,CAAC,EAAEqL,MAAM,CAACwE,CAAC,CAAC,CAAC,EAAE;EAC9CD,YAAAA,MAAM,GAAG,IAAI,CAAA;EACb,YAAA,MAAA;EACF,WAAA;EACF,SAAA;EACA,QAAA,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK,CAAA;EAC3B,OAAA;EACA,MAAA,OAAO,IAAI,CAAA;EACb,IAAA,KAAK,QAAQ;QACX,IAAI;UACF,OAAOV,QAAQ,CAACzD,QAAQ,CAAC,CAAC3C,IAAI,CAACuC,MAAM,CAAC,CAAA;SACvC,CAAC,OAAOtR,CAAC,EAAE;EACV,QAAA,OAAO,KAAK,CAAA;EACd,OAAA;EACF,IAAA,KAAK,OAAO;EACV,MAAA,OAAOuV,OAAO,CAACjE,MAAM,CAAC,KAAKI,QAAQ,CAAA;EACrC,IAAA;EACEf,MAAAA,OAAO,CAACC,KAAK,CAAC,oBAAoB,GAAGgF,QAAQ,CAAC,CAAA;EAC9C,MAAA,OAAO,KAAK,CAAA;EAAC,GAAA;EAEnB,CAAA;;EAEA;EACA,SAASf,MAAM,CAACF,GAAc,EAAEoB,UAAgC,EAAW;EACzE,EAAA,IAAI,CAACA,UAAU,CAACpI,MAAM,EAAE,OAAO,IAAI,CAAA;EACnC,EAAA,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,UAAU,CAACpI,MAAM,EAAE1H,CAAC,EAAE,EAAE;MAC1C,IAAIyO,aAAa,CAACC,GAAG,EAAEoB,UAAU,CAAC9P,CAAC,CAAC,CAAC,EAAE;EACrC,MAAA,OAAO,IAAI,CAAA;EACb,KAAA;EACF,GAAA;EACA,EAAA,OAAO,KAAK,CAAA;EACd,CAAA;;EAEA;EACA,SAAS6O,OAAO,CAACH,GAAc,EAAEoB,UAAgC,EAAW;EAC1E,EAAA,KAAK,IAAI9P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,UAAU,CAACpI,MAAM,EAAE1H,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACyO,aAAa,CAACC,GAAG,EAAEoB,UAAU,CAAC9P,CAAC,CAAC,CAAC,EAAE;EACtC,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;EACF,GAAA;EACA,EAAA,OAAO,IAAI,CAAA;EACb;;EC3LA,MAAM9G,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW,CAAA;EAElE,MAAM2W,WAAW,GAAGxB,cAAc,EAAE,CAAA;EAE7B,MAAMyB,UAAU,CAGrB;EACA;EACA;;EAMA;;EAiBA;;IAUAC,WAAW,CAACC,OAAiB,EAAE;EAC7BA,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvB;EACA;MACA,IAAI,CAAChQ,OAAO,GAAG6P,WAAW,CAAA;EAC1B,IAAA,IAAI,CAACI,IAAI,GAAG,IAAI,CAACD,OAAO,GAAGA,OAAO,CAAA;MAClC,IAAI,CAACE,SAAS,GAAG,IAAI,CAAA;EACrB,IAAA,IAAI,CAACC,mBAAmB,GAAG,IAAI9V,GAAG,EAAE,CAAA;EACpC,IAAA,IAAI,CAAC+V,gBAAgB,GAAG,EAAE,CAAA;MAC1B,IAAI,CAACC,KAAK,GAAG,KAAK,CAAA;EAClB,IAAA,IAAI,CAACC,cAAc,GAAG,IAAIjW,GAAG,EAAE,CAAA;MAC/B,IAAI,CAACkW,QAAQ,GAAG,EAAE,CAAA;MAClB,IAAI,CAACC,QAAQ,GAAG,CAAC,CAAA;MACjB,IAAI,CAACC,KAAK,GAAG,KAAK,CAAA;EAClB,IAAA,IAAI,CAACC,SAAS,GAAG,IAAI3W,GAAG,EAAE,CAAA;EAC1B,IAAA,IAAI,CAAC4W,oBAAoB,GAAG,IAAI5W,GAAG,EAAE,CAAA;EACrC,IAAA,IAAI,CAAC6W,mBAAmB,GAAG,EAAE,CAAA;EAC7B,IAAA,IAAI,CAACC,sBAAsB,GAAG,IAAI9W,GAAG,EAAE,CAAA;EACvC,IAAA,IAAI,CAAC+W,iBAAiB,GAAG,IAAIzW,GAAG,EAAE,CAAA;MAClC,IAAI,CAAC0W,mBAAmB,GAAG,KAAK,CAAA;MAEhC,IAAIf,OAAO,CAACjP,UAAU,EAAE;QACtB,IAAIiP,OAAO,CAACzC,aAAa,EAAE;EACzB,QAAA,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC,CAAA;EAC/D,OAAA;EACA,MAAA,IAAI,CAACwC,OAAO,CAAC5X,SAAS,EAAE;EACtB,QAAA,MAAM,IAAIoV,KAAK,CAAC,mBAAmB,CAAC,CAAA;EACtC,OAAA;QACA,IAAIwD,QAAQ,GAAG,KAAK,CAAA;QACpB,IAAI;EACFA,QAAAA,QAAQ,GAAG,CAAC,CAAC,IAAIxF,GAAG,CAACwE,OAAO,CAACzS,OAAO,IAAI,EAAE,CAAC,CAAC0T,QAAQ,CAACnG,KAAK,CACxD,kBAAkB,CACnB,CAAA;SACF,CAAC,OAAOjR,CAAC,EAAE;EACV;EAAA,OAAA;EAEF,MAAA,IAAImX,QAAQ,EAAE;EACZ,QAAA,MAAM,IAAIxD,KAAK,CAAC,2CAA2C,CAAC,CAAA;EAC9D,OAAA;EACF,KAAC,MAAM;QACL,IAAIwC,OAAO,CAACnS,kBAAkB,EAAE;EAC9B,QAAA,MAAM,IAAI2P,KAAK,CAAC,iDAAiD,CAAC,CAAA;EACpE,OAAA;EACF,KAAA;MAEA,IAAIwC,OAAO,CAACtP,QAAQ,EAAE;QACpB,IAAI,CAAC+P,KAAK,GAAG,IAAI,CAAA;EACnB,KAAA;EAEA,IAAA,IAAIzX,SAAS,IAAIgX,OAAO,CAACkB,aAAa,EAAE;QACtCjY,MAAM,CAACkY,WAAW,GAAG,IAAI,CAAA;QACzBjY,QAAQ,CAACkY,aAAa,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAA;EAC/C,KAAA;MAEA,IAAIrB,OAAO,CAACzP,WAAW,EAAE;QACvB,IAAI,CAACkQ,KAAK,GAAG,IAAI,CAAA;QACjB,IAAI,CAACa,yBAAyB,EAAE,CAAA;EAClC,KAAA;MAEA,IAAItB,OAAO,CAAC5X,SAAS,IAAI,CAAC4X,OAAO,CAACjP,UAAU,EAAE;QAC5C,IAAI,CAACwQ,QAAQ,CAAC,EAAE,EAAE,IAAI,EAAE,KAAK,CAAC,CAAA;EAChC,KAAA;EACF,GAAA;IAEA,MAAaC,YAAY,CAAChZ,OAA6B,EAAiB;EACtE,IAAA,IAAIA,OAAO,IAAIA,OAAO,CAACiZ,WAAW,EAAE;EAClC;EACA,MAAA,IAAI,CAACxB,IAAI,CAACyB,kBAAkB,GAAG,IAAI,CAAA;EACrC,KAAA;MACA,IAAI,CAACX,mBAAmB,GAAG,IAAI,CAAA;MAE/B,MAAM,IAAI,CAACQ,QAAQ,CAAC/Y,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;EAExC,IAAA,IAAI,IAAI,CAACmZ,aAAa,EAAE,EAAE;QACxBnW,SAAS,CAAC,IAAI,CAAC,CAAA;EACjB,KAAA;EACF,GAAA;IAEA,MAAaT,eAAe,CAC1BvC,OAAgC,EACjB;MACf,MAAM,IAAI,CAAC+Y,QAAQ,CAAC/Y,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;EAC3C,GAAA;EAEOgF,EAAAA,UAAU,GAAyB;EACxC,IAAA,OAAO,CAAC,IAAI,CAACqD,WAAW,EAAE,CAACtD,OAAO,EAAE,IAAI,CAACuD,YAAY,EAAE,CAAC,CAAA;EAC1D,GAAA;EACOD,EAAAA,WAAW,GAKhB;MACA,MAAM+Q,WAAW,GAAG,IAAI,CAAC3B,IAAI,CAAC1S,OAAO,IAAI,2BAA2B,CAAA;MACpE,OAAO;QACLA,OAAO,EAAEqU,WAAW,CAACtH,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;EACxChJ,MAAAA,aAAa,EAAE,CAAC,IAAI,CAAC2O,IAAI,CAAC3O,aAAa,IAAIsQ,WAAW,EAAEtH,OAAO,CAC7D,MAAM,EACN,EAAE,CACH;EACD1J,MAAAA,iBAAiB,EAAE,IAAI,CAACqP,IAAI,CAAC4B,qBAAqB;EAClDtQ,MAAAA,2BAA2B,EAAE,IAAI,CAAC0O,IAAI,CAAC1O,2BAAAA;OACxC,CAAA;EACH,GAAA;EACOT,EAAAA,YAAY,GAAW;EAC5B,IAAA,OAAO,IAAI,CAACmP,IAAI,CAAC7X,SAAS,IAAI,EAAE,CAAA;EAClC,GAAA;EAEOsF,EAAAA,YAAY,GAAY;EAC7B,IAAA,OAAO,IAAI,CAACuS,IAAI,CAAClP,UAAU,IAAI,KAAK,CAAA;EACtC,GAAA;EAEOjD,EAAAA,qBAAqB,GAAqC;EAC/D,IAAA,OAAO,IAAI,CAACmS,IAAI,CAACpS,kBAAkB,CAAA;EACrC,GAAA;EAEA,EAAA,MAAc0T,QAAQ,CACpB/Y,OAAgC,EAChC2C,UAAoB,EACpBC,cAAwB,EACxB;EACA5C,IAAAA,OAAO,GAAGA,OAAO,IAAI,EAAE,CAAA;EACvB,IAAA,IAAI,CAAC,IAAI,CAACyX,IAAI,CAAC7X,SAAS,EAAE;EACxB,MAAA,MAAM,IAAIoV,KAAK,CAAC,mBAAmB,CAAC,CAAA;EACtC,KAAA;EACA,IAAA,MAAMzS,eAAe,CACnB,IAAI,EACJvC,OAAO,CAACyC,OAAO,EACfzC,OAAO,CAAC0C,SAAS,IAAI,IAAI,CAAC+U,IAAI,CAACiB,aAAa,EAC5C/V,UAAU,EACVC,cAAc,EACd,IAAI,CAAC6U,IAAI,CAAC7Y,cAAc,KAAK,KAAK,CACnC,CAAA;EACH,GAAA;EAEQ0a,EAAAA,OAAO,GAAG;MAChB,IAAI,IAAI,CAAC5B,SAAS,EAAE;QAClB,IAAI,CAACA,SAAS,EAAE,CAAA;EAClB,KAAA;EACF,GAAA;IAEOzP,WAAW,CAACC,QAA2C,EAAE;EAC9D,IAAA,IAAI,CAACuP,IAAI,CAACvP,QAAQ,GAAGA,QAAQ,CAAA;MAC7B,IAAI,CAAC+P,KAAK,GAAG,IAAI,CAAA;MACjB,IAAI,CAACqB,OAAO,EAAE,CAAA;EAChB,GAAA;EAEA,EAAA,MAAaC,oBAAoB,CAC/BzE,eAAuB,EACvBC,aAAsB,EACtBxV,MAAqB,EACN;EACf,IAAA,MAAMia,YAAY,GAAG,MAAM3E,OAAO,CAChCC,eAAe,EACfC,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CAAA;MACD,IAAI,CAAC0I,WAAW,CACd9H,IAAI,CAACqG,KAAK,CAACgT,YAAY,CAAC,CACzB,CAAA;EACH,GAAA;IAEO1R,cAAc,CAACC,WAA6B,EAAQ;EACzD,IAAA,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,GAAGA,WAAW,CAAA;MACnC,IAAI,CAACkQ,KAAK,GAAG,IAAI,CAAA;MACjB,IAAI,CAACa,yBAAyB,EAAE,CAAA;EAClC,GAAA;EAEA,EAAA,MAAaW,uBAAuB,CAClC3E,eAAuB,EACvBC,aAAsB,EACtBxV,MAAqB,EACN;EACf,IAAA,MAAMma,eAAe,GAAG,MAAM7E,OAAO,CACnCC,eAAe,EACfC,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CAAA;MACD,IAAI,CAACuI,cAAc,CAAC3H,IAAI,CAACqG,KAAK,CAACkT,eAAe,CAAC,CAAqB,CAAA;EACtE,GAAA;EAEA,EAAA,MAAa9R,cAAc,CACzB/E,IAAwB,EACxBkS,aAAsB,EACtBxV,MAAqB,EACQ;MAC7B,IAAIsD,IAAI,CAAC8W,iBAAiB,EAAE;QAC1B9W,IAAI,CAACqF,QAAQ,GAAG/H,IAAI,CAACqG,KAAK,CACxB,MAAMqO,OAAO,CACXhS,IAAI,CAAC8W,iBAAiB,EACtB5E,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CACF,CAAA;QACD,OAAOsD,IAAI,CAAC8W,iBAAiB,CAAA;EAC/B,KAAA;MACA,IAAI9W,IAAI,CAAC+W,oBAAoB,EAAE;QAC7B/W,IAAI,CAACkF,WAAW,GAAG5H,IAAI,CAACqG,KAAK,CAC3B,MAAMqO,OAAO,CACXhS,IAAI,CAAC+W,oBAAoB,EACzB7E,aAAa,IAAI,IAAI,CAAC0C,IAAI,CAAC1C,aAAa,EACxCxV,MAAM,CACP,CACF,CAAA;QACD,OAAOsD,IAAI,CAAC+W,oBAAoB,CAAA;EAClC,KAAA;EACA,IAAA,OAAO/W,IAAI,CAAA;EACb,GAAA;IAEA,MAAagX,aAAa,CAAC1U,UAAsB,EAAE;EACjD,IAAA,IAAI,CAACsS,IAAI,CAACtS,UAAU,GAAGA,UAAU,CAAA;EACjC,IAAA,IAAI,IAAI,CAACsS,IAAI,CAACqC,mBAAmB,EAAE;QACjC,MAAM,IAAI,CAACjS,oBAAoB,EAAE,CAAA;EACnC,KAAA;EACA,IAAA,IAAI,IAAI,CAAC4P,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACT,OAAO,EAAE,CAAA;MACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;EAClC,GAAA;IAEA,MAAakB,qBAAqB,CAACjY,SAAqB,EAAE;MACxD,IAAI,CAACqW,mBAAmB,GAAGrW,SAAS,CAAA;EACpC,IAAA,IAAI,IAAI,CAAC0V,IAAI,CAACqC,mBAAmB,EAAE;QACjC,MAAM,IAAI,CAACjS,oBAAoB,EAAE,CAAA;EACnC,KAAA;EACA,IAAA,IAAI,IAAI,CAAC4P,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACT,OAAO,EAAE,CAAA;MACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;EAClC,GAAA;IAEA,MAAamB,mBAAmB,CAACC,IAA4B,EAAE;MAC7D,IAAI,CAACzC,IAAI,CAAChP,gBAAgB,GAAGyR,IAAI,IAAI,EAAE,CAAA;EACvC,IAAA,IAAI,IAAI,CAACzC,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACT,OAAO,EAAE,CAAA;MACd,IAAI,CAACR,yBAAyB,EAAE,CAAA;EAClC,GAAA;;EAEA;IACOqB,iBAAiB,CAACtT,GAAqB,EAAE;MAC9C,IAAI,CAACsR,oBAAoB,GAAGtR,GAAG,CAAA;MAC/B,IAAI,CAACyS,OAAO,EAAE,CAAA;EAChB,GAAA;IAEA,MAAac,MAAM,CAACzU,GAAW,EAAE;EAC/B,IAAA,IAAI,CAAC8R,IAAI,CAAC9R,GAAG,GAAGA,GAAG,CAAA;EACnB,IAAA,IAAI,IAAI,CAAC8R,IAAI,CAAClP,UAAU,EAAE;QACxB,MAAM,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAClC,MAAA,IAAI,CAACjB,yBAAyB,CAAC,IAAI,CAAC,CAAA;EACpC,MAAA,OAAA;EACF,KAAA;EACA,IAAA,IAAI,CAACA,yBAAyB,CAAC,IAAI,CAAC,CAAA;EACtC,GAAA;EAEO1T,EAAAA,aAAa,GAAG;MACrB,OAAO;EAAE,MAAA,GAAG,IAAI,CAACqS,IAAI,CAACtS,UAAU;EAAE,MAAA,GAAG,IAAI,CAACiT,mBAAAA;OAAqB,CAAA;EACjE,GAAA;EAEO1S,EAAAA,mBAAmB,GAAG;EAC3B,IAAA,OAAO,IAAI,CAAC+R,IAAI,CAAChP,gBAAgB,IAAI,EAAE,CAAA;EACzC,GAAA;EAEOE,EAAAA,iBAAiB,GAAG;EACzB;EACA,IAAA,OAAO,IAAI,CAACwP,oBAAoB,IAAI,IAAI5W,GAAG,EAAe,CAAA;EAC5D,GAAA;EAEO8Y,EAAAA,6BAA6B,GAAG;EACrC,IAAA,OAAO,IAAI,CAAC5C,IAAI,CAAC6C,0BAA0B,IAAI,EAAE,CAAA;EACnD,GAAA;EAEO1U,EAAAA,MAAM,GAAG;EACd,IAAA,OAAO,IAAI,CAAC6R,IAAI,CAAC9R,GAAG,IAAI,EAAE,CAAA;EAC5B,GAAA;EAEOwC,EAAAA,WAAW,GAAG;EACnB,IAAA,OAAO,IAAI,CAACsP,IAAI,CAACvP,QAAQ,IAAI,EAAE,CAAA;EACjC,GAAA;EAEOF,EAAAA,cAAc,GAAG;EACtB,IAAA,OAAO,IAAI,CAACyP,IAAI,CAAC1P,WAAW,IAAI,EAAE,CAAA;EACpC,GAAA;IAEO/E,SAAS,CAACiG,EAAwB,EAAc;EACrD,IAAA,IAAI,CAAC6O,cAAc,CAACzU,GAAG,CAAC4F,EAAE,CAAC,CAAA;EAC3B,IAAA,OAAO,MAAM;EACX,MAAA,IAAI,CAAC6O,cAAc,CAACpU,MAAM,CAACuF,EAAE,CAAC,CAAA;OAC/B,CAAA;EACH,GAAA;EAEQkQ,EAAAA,aAAa,GAAG;EACtB,IAAA,OAAO,IAAI,CAAC1B,IAAI,CAAC7Y,cAAc,KAAK,KAAK,IAAI,IAAI,CAAC6Y,IAAI,CAACyB,kBAAkB,CAAA;EAC3E,GAAA;EAEA,EAAA,MAAca,qBAAqB,GAAG;EACpC,IAAA,IAAI,CAAC,IAAI,CAACtC,IAAI,CAAClP,UAAU,EAAE,OAAA;EAC3B,IAAA,IAAI,CAAC,IAAI,CAACgQ,mBAAmB,EAAE,OAAA;EAC/B,IAAA,MAAM,IAAI,CAACQ,QAAQ,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC3S,KAAK,CAAC,MAAM;EAC/C;EAAA,KACD,CAAC,CAAA;EACJ,GAAA;EAEOmU,EAAAA,aAAa,GAAG;EACrB,IAAA,OAAO,IAAIhZ,GAAG,CAAC,IAAI,CAAC2W,SAAS,CAAC,CAAA;EAChC,GAAA;EAEOsC,EAAAA,OAAO,GAAG;EACf;EACA,IAAA,IAAI,CAAC1C,cAAc,CAACzV,KAAK,EAAE,CAAA;EAC3B,IAAA,IAAI,CAAC6V,SAAS,CAAC7V,KAAK,EAAE,CAAA;EACtB,IAAA,IAAI,CAACsV,mBAAmB,CAACtV,KAAK,EAAE,CAAA;EAChC,IAAA,IAAI,CAACuV,gBAAgB,GAAG,EAAE,CAAA;MAC1B,IAAI,CAACG,QAAQ,GAAG,EAAE,CAAA;MAClB,IAAI,IAAI,CAACC,QAAQ,EAAE;EACjBnX,MAAAA,YAAY,CAAC,IAAI,CAACmX,QAAQ,CAAC,CAAA;EAC7B,KAAA;MACAzU,WAAW,CAAC,IAAI,CAAC,CAAA;EAEjB,IAAA,IAAI/C,SAAS,IAAIC,MAAM,CAACkY,WAAW,KAAK,IAAI,EAAE;QAC5C,OAAOlY,MAAM,CAACkY,WAAW,CAAA;EAC3B,KAAA;;EAEA;EACA,IAAA,IAAI,CAACN,sBAAsB,CAAC7U,OAAO,CAAEiX,GAAG,IAAK;QAC3CA,GAAG,CAACC,IAAI,EAAE,CAAA;EACZ,KAAC,CAAC,CAAA;EACF,IAAA,IAAI,CAACrC,sBAAsB,CAAChW,KAAK,EAAE,CAAA;EACnC,IAAA,IAAI,CAACiW,iBAAiB,CAACjW,KAAK,EAAE,CAAA;EAChC,GAAA;IAEOsY,WAAW,CAACC,QAAoB,EAAE;MACvC,IAAI,CAAClD,SAAS,GAAGkD,QAAQ,CAAA;EAC3B,GAAA;EAEOC,EAAAA,cAAc,CAAC5X,GAAW,EAAE6X,SAAiB,EAAE;EACpD,IAAA,IAAI,CAACrD,IAAI,CAAChP,gBAAgB,GAAG,IAAI,CAACgP,IAAI,CAAChP,gBAAgB,IAAI,EAAE,CAAA;MAC7D,IAAI,CAACgP,IAAI,CAAChP,gBAAgB,CAACxF,GAAG,CAAC,GAAG6X,SAAS,CAAA;EAC3C,IAAA,IAAI,IAAI,CAACrD,IAAI,CAAClP,UAAU,EAAE;QACxB,IAAI,CAACwR,qBAAqB,EAAE,CAAA;EAC5B,MAAA,OAAA;EACF,KAAA;MACA,IAAI,CAACjB,yBAAyB,EAAE,CAAA;MAChC,IAAI,CAACQ,OAAO,EAAE,CAAA;EAChB,GAAA;IAEOyB,GAAG,CAAIC,UAAyB,EAAa;MAClD,MAAMC,MAAM,GAAG,IAAI,CAACC,IAAI,CAACF,UAAU,EAAE,IAAI,CAAC,CAAA;EAC1C,IAAA,IAAI,CAACG,kBAAkB,CAACH,UAAU,EAAEC,MAAM,CAAC,CAAA;EAC3C,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;IAEOG,iBAAiB,CAACnY,GAAW,EAAE;EACpC,IAAA,IAAI,CAACqV,iBAAiB,CAACjV,GAAG,CAACJ,GAAG,CAAC,CAAA;MAC/B,IAAI,CAAC,IAAI,CAACwU,IAAI,CAAC1P,WAAW,EAAE,OAAO,IAAI,CAAA;EACvC,IAAA,MAAMA,WAAW,GAAG,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,CAACuE,MAAM,CAAEmO,GAAG,IAAKA,GAAG,CAACxX,GAAG,KAAKA,GAAG,CAAC,CAAA;EAC1E,IAAA,OAAO8E,WAAW,CACflB,GAAG,CAAE4T,GAAG,IAAK;EACZ,MAAA,IAAI,CAACA,GAAG,CAACY,MAAM,EAAE,OAAO,IAAI,CAAA;EAC5B,MAAA,OAAO,IAAI,CAACC,kBAAkB,CAACb,GAAG,CAAC,CAAA;OACpC,CAAC,CACDnO,MAAM,CAAE1D,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC,CAAA;EAClC,GAAA;EAEQ0S,EAAAA,kBAAkB,CAACN,UAA0B,EAAEO,UAAoB,EAAE;MAC3E,MAAM9W,QAAQ,GAAG,IAAI,CAAC4T,sBAAsB,CAACjV,GAAG,CAAC4X,UAAU,CAAC,CAAA;;EAE5D;MACA,IACEA,UAAU,CAACK,MAAM,IACjB,CAAC,IAAI,CAAC/C,iBAAiB,CAAC5Q,GAAG,CAACsT,UAAU,CAAC/X,GAAG,CAAC,IAC3C,CAACwB,QAAQ,EAET,OAAO,IAAI,CAAA;;EAEb;EACA,IAAA,MAAMwW,MAAM,GAAG,IAAI,CAACF,GAAG,CAACC,UAAU,CAAC,CAAA;;EAEnC;MACA,MAAMQ,SAAS,GAAGrb,IAAI,CAACC,SAAS,CAAC6a,MAAM,CAAC5U,KAAK,CAAC,CAAA;;EAE9C;EACA,IAAA,IACE,CAACkV,UAAU,IACXN,MAAM,CAACQ,YAAY,IACnBhX,QAAQ,IACRA,QAAQ,CAAC+W,SAAS,KAAKA,SAAS,EAChC;EACA,MAAA,OAAOP,MAAM,CAAA;EACf,KAAA;;EAEA;EACA,IAAA,IAAIxW,QAAQ,EAAE,IAAI,CAACiX,yBAAyB,CAACV,UAAU,CAAC,CAAA;;EAExD;MACA,IAAIC,MAAM,CAACQ,YAAY,EAAE;QACvB,MAAMf,IAAI,GAAG,IAAI,CAACiB,gBAAgB,CAACV,MAAM,CAAC5U,KAAK,CAAC,CAAA;EAChD,MAAA,IAAIqU,IAAI,EAAE;EACR,QAAA,IAAI,CAACrC,sBAAsB,CAAC/U,GAAG,CAAC0X,UAAU,EAAE;YAC1CN,IAAI;EACJc,UAAAA,SAAAA;EACF,SAAC,CAAC,CAAA;EACJ,OAAA;EACF,KAAA;EAEA,IAAA,OAAOP,MAAM,CAAA;EACf,GAAA;IAEQS,yBAAyB,CAACjB,GAAmB,EAAE;MACrD,MAAM5X,IAAI,GAAG,IAAI,CAACwV,sBAAsB,CAACjV,GAAG,CAACqX,GAAG,CAAC,CAAA;EACjD,IAAA,IAAI5X,IAAI,EAAE;QACRA,IAAI,CAAC6X,IAAI,EAAE,CAAA;EACX,MAAA,IAAI,CAACrC,sBAAsB,CAAC3U,MAAM,CAAC+W,GAAG,CAAC,CAAA;EACzC,KAAA;EACF,GAAA;IAEQ3B,yBAAyB,CAACyC,UAAoB,EAAE;MACtD,MAAMxT,WAAW,GAAG,IAAI,CAAC0P,IAAI,CAAC1P,WAAW,IAAI,EAAE,CAAA;;EAE/C;EACA,IAAA,MAAMxC,IAAI,GAAG,IAAI1D,GAAG,CAACkG,WAAW,CAAC,CAAA;MACjC,IAAI,CAACsQ,sBAAsB,CAAC7U,OAAO,CAAC,CAAC4P,CAAC,EAAEC,CAAC,KAAK;EAC5C,MAAA,IAAI,CAAC9N,IAAI,CAACmC,GAAG,CAAC2L,CAAC,CAAC,EAAE;UAChBD,CAAC,CAACsH,IAAI,EAAE,CAAA;EACR,QAAA,IAAI,CAACrC,sBAAsB,CAAC3U,MAAM,CAAC2P,CAAC,CAAC,CAAA;EACvC,OAAA;EACF,KAAC,CAAC,CAAA;;EAEF;EACAtL,IAAAA,WAAW,CAACvE,OAAO,CAAEiX,GAAG,IAAK;EAC3B,MAAA,IAAI,CAACa,kBAAkB,CAACb,GAAG,EAAEc,UAAU,CAAC,CAAA;EAC1C,KAAC,CAAC,CAAA;EACJ,GAAA;EAEQJ,EAAAA,kBAAkB,CAAIH,UAAyB,EAAEC,MAAiB,EAAE;EAC1E,IAAA,MAAMhY,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;;EAE1B;MACA,MAAM2Y,IAAI,GAAG,IAAI,CAAC1D,SAAS,CAAC9U,GAAG,CAACH,GAAG,CAAC,CAAA;EACpC;MACA,IACE,CAAC2Y,IAAI,IACLA,IAAI,CAACX,MAAM,CAACQ,YAAY,KAAKR,MAAM,CAACQ,YAAY,IAChDG,IAAI,CAACX,MAAM,CAACY,WAAW,KAAKZ,MAAM,CAACY,WAAW,EAC9C;EACA,MAAA,IAAI,CAAC3D,SAAS,CAAC5U,GAAG,CAACL,GAAG,EAAE;UAAE+X,UAAU;EAAEC,QAAAA,MAAAA;EAAO,OAAC,CAAC,CAAA;EAC/C,MAAA,IAAI,CAACnD,cAAc,CAACtU,OAAO,CAAEyF,EAAE,IAAK;UAClC,IAAI;EACFA,UAAAA,EAAE,CAAC+R,UAAU,EAAEC,MAAM,CAAC,CAAA;WACvB,CAAC,OAAO5Z,CAAC,EAAE;EACV2Q,UAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;EAClB,SAAA;EACF,OAAC,CAAC,CAAA;EACJ,KAAA;EACF,GAAA;EAEQya,EAAAA,kBAAkB,CAAC7Y,GAAW,EAAE2F,GAAkB,EAAQ;EAChE;EACA,IAAA,IAAIA,GAAG,CAACmT,MAAM,KAAK,UAAU,EAAE,OAAA;;EAE/B;MACA,MAAMC,gBAAgB,GAAG7b,IAAI,CAACC,SAAS,CAACwI,GAAG,CAACvC,KAAK,CAAC,CAAA;MAClD,IAAI,IAAI,CAACuR,gBAAgB,CAAC3U,GAAG,CAAC,KAAK+Y,gBAAgB,EAAE,OAAA;EACrD,IAAA,IAAI,CAACpE,gBAAgB,CAAC3U,GAAG,CAAC,GAAG+Y,gBAAgB,CAAA;;EAE7C;EACA,IAAA,IAAI,IAAI,CAACvE,IAAI,CAACwE,cAAc,EAAE;QAC5B,IAAI;UACF,IAAI,CAACxE,IAAI,CAACwE,cAAc,CAAChZ,GAAG,EAAE2F,GAAG,CAAC,CAAA;SACnC,CAAC,OAAOvH,CAAC,EAAE;EACV;EAAA,OAAA;EAEJ,KAAA;;EAEA;EACA,IAAA,IAAI,CAACb,SAAS,IAAI,CAACC,MAAM,CAACxB,KAAK,EAAE,OAAA;EACjC,IAAA,IAAI,CAAC8Y,QAAQ,CAAC1I,IAAI,CAAC;QACjBpM,GAAG;QACHiZ,EAAE,EAAEtT,GAAG,CAACsT,EAAAA;EACV,KAAC,CAAC,CAAA;EACF,IAAA,IAAI,CAAC,IAAI,CAAClE,QAAQ,EAAE;EAClB,MAAA,IAAI,CAACA,QAAQ,GAAGvX,MAAM,CAACM,UAAU,CAAC,MAAM;EACtC;UACA,IAAI,CAACiX,QAAQ,GAAG,CAAC,CAAA;EACjB,QAAA,MAAMmE,CAAC,GAAG,CAAC,GAAG,IAAI,CAACpE,QAAQ,CAAC,CAAA;UAC5B,IAAI,CAACA,QAAQ,GAAG,EAAE,CAAA;;EAElB;EACA,QAAA,IAAI,CAAC,IAAI,CAACN,IAAI,CAAC2E,WAAW,EAAE,OAAA;EAE5B3b,QAAAA,MAAM,CACHxB,KAAK,CAAA,gCAAA,CAAA,MAAA,CAEF,IAAI,CAACwY,IAAI,CAAC2E,WAAW,EAAA,UAAA,CAAA,CAAA,MAAA,CACZC,kBAAkB,CAAClc,IAAI,CAACC,SAAS,CAAC+b,CAAC,CAAC,CAAC,CAEhD,EAAA;EACE1a,UAAAA,KAAK,EAAE,UAAU;EACjB6a,UAAAA,IAAI,EAAE,SAAA;EACR,SAAC,CACF,CACAlW,KAAK,CAAC,MAAM;EACX;EAAA,SACD,CAAC,CAAA;SACL,EAAE,IAAI,CAACqR,IAAI,CAAC8E,gBAAgB,IAAI,IAAI,CAAC,CAAA;EACxC,KAAA;EACF,GAAA;EAEQC,EAAAA,iBAAiB,CACvBvZ,GAAW,EACXoD,KAAQ,EACR0V,MAA2B,EAC3BU,MAAe,EACfzB,UAA0B,EAC1BC,MAAkB,EACA;EAClB,IAAA,MAAMyB,GAAkB,GAAG;QACzBrW,KAAK;QACL6V,EAAE,EAAE,CAAC,CAAC7V,KAAK;QACXsW,GAAG,EAAE,CAACtW,KAAK;QACX0V,MAAM;QACNU,MAAM,EAAEA,MAAM,IAAI,EAAA;OACnB,CAAA;EACD,IAAA,IAAIzB,UAAU,EAAE0B,GAAG,CAAC1B,UAAU,GAAGA,UAAU,CAAA;EAC3C,IAAA,IAAIC,MAAM,EAAEyB,GAAG,CAACE,gBAAgB,GAAG3B,MAAM,CAAA;;EAEzC;EACA,IAAA,IAAI,CAACa,kBAAkB,CAAC7Y,GAAG,EAAEyZ,GAAG,CAAC,CAAA;EAEjC,IAAA,OAAOA,GAAG,CAAA;EACZ,GAAA;IAEOG,IAAI,CAAgD5Z,GAAM,EAAW;EAC1E,IAAA,OAAO,IAAI,CAAC6Z,WAAW,CAAC7Z,GAAG,CAAC,CAACiZ,EAAE,CAAA;EACjC,GAAA;IAEOa,KAAK,CAAgD9Z,GAAM,EAAW;EAC3E,IAAA,OAAO,IAAI,CAAC6Z,WAAW,CAAC7Z,GAAG,CAAC,CAAC0Z,GAAG,CAAA;EAClC,GAAA;EAEOK,EAAAA,eAAe,CAGpB/Z,GAAM,EAAEga,YAAe,EAAsB;MAC7C,MAAM5W,KAAK,GAAG,IAAI,CAACyW,WAAW,CAAwB7Z,GAAG,CAAC,CAACoD,KAAK,CAAA;EAChE,IAAA,OAAOA,KAAK,KAAK,IAAI,GAAI4W,YAAY,GAA0B5W,KAAK,CAAA;EACtE,GAAA;;EAEA;EACF;EACA;EACA;EACE;IACO6W,OAAO,CAGZ5I,EAAK,EAA2B;EAChC,IAAA,OAAO,IAAI,CAACwI,WAAW,CAACxI,EAAE,CAAC,CAAA;EAC7B,GAAA;IAEOwI,WAAW,CAGhBxI,EAAK,EAA2B;EAChC;MACA,IAAI,IAAI,CAAC6D,oBAAoB,CAACzQ,GAAG,CAAC4M,EAAE,CAAC,EAAE;EAMrC,MAAA,OAAO,IAAI,CAACkI,iBAAiB,CAC3BlI,EAAE,EACF,IAAI,CAAC6D,oBAAoB,CAAC/U,GAAG,CAACkR,EAAE,CAAC,EACjC,UAAU,CACX,CAAA;EACH,KAAA;;EAEA;EACA,IAAA,IAAI,CAAC,IAAI,CAACmD,IAAI,CAACvP,QAAQ,IAAI,CAAC,IAAI,CAACuP,IAAI,CAACvP,QAAQ,CAACoM,EAAE,CAAC,EAAE;QAGlD,OAAO,IAAI,CAACkI,iBAAiB,CAAClI,EAAE,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAA;EAC3D,KAAA;;EAEA;MACA,MAAM4I,OAA6B,GAAG,IAAI,CAACzF,IAAI,CAACvP,QAAQ,CAACoM,EAAE,CAAC,CAAA;;EAE5D;MACA,IAAI4I,OAAO,CAACC,KAAK,EAAE;EACjB,MAAA,KAAK,MAAMC,IAAI,IAAIF,OAAO,CAACC,KAAK,EAAE;EAChC;EACA,QAAA,IAAIC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,cAAc,CAACF,IAAI,CAACC,OAAO,CAAC,EAAE;EAMrD,UAAA,SAAA;EACF,SAAA;;EAEA;UACA,IAAI,OAAO,IAAID,IAAI,EAAE;EACnB;EACA,UAAA,IAAIA,IAAI,CAACnH,SAAS,IAAI,CAAC,IAAI,CAACsH,gBAAgB,CAACH,IAAI,CAACnH,SAAS,CAAC,EAAE;EAM5D,YAAA,SAAA;EACF,WAAA;;EAEA;YACA,IACE,CAAC,IAAI,CAACuH,oBAAoB,CACxBJ,IAAI,CAACpM,IAAI,IAAIsD,EAAE,EACf8I,IAAI,CAACK,aAAa,EAClB,IAAI,CAAChG,IAAI,CAACqC,mBAAmB,IAAI,CAACsD,IAAI,CAACM,sBAAsB,GACzDN,IAAI,CAACO,iBAAiB,GACtBve,SAAS,EACbge,IAAI,CAAC/L,KAAK,EACV+L,IAAI,CAACxJ,QAAQ,EACbwJ,IAAI,CAACQ,WAAW,CACjB,EACD;EAMA,YAAA,SAAA;EACF,WAAA;;EAQA;YACA,IAAIR,IAAI,CAACS,MAAM,EAAE;EACfT,YAAAA,IAAI,CAACS,MAAM,CAACra,OAAO,CAAEqT,CAAC,IAAK;gBACzB,IAAI,CAACiH,MAAM,CAACjH,CAAC,CAACmE,UAAU,EAAEnE,CAAC,CAACoE,MAAM,CAAC,CAAA;EACrC,aAAC,CAAC,CAAA;EACJ,WAAA;EAEA,UAAA,OAAO,IAAI,CAACuB,iBAAiB,CAAClI,EAAE,EAAE8I,IAAI,CAACW,KAAK,EAAO,OAAO,EAAEX,IAAI,CAAC9I,EAAE,CAAC,CAAA;EACtE,SAAA;EACA,QAAA,IAAI,CAAC8I,IAAI,CAACY,UAAU,EAAE;EAOpB,UAAA,SAAA;EACF,SAAA;;EAEA;EACA,QAAA,MAAMvD,GAAkB,GAAG;YACzBuD,UAAU,EAAEZ,IAAI,CAACY,UAA4B;EAC7C/a,UAAAA,GAAG,EAAEma,IAAI,CAACna,GAAG,IAAIqR,EAAAA;WAClB,CAAA;UACD,IAAI,UAAU,IAAI8I,IAAI,EAAE3C,GAAG,CAAC7G,QAAQ,GAAGwJ,IAAI,CAACxJ,QAAQ,CAAA;UACpD,IAAIwJ,IAAI,CAACvJ,OAAO,EAAE4G,GAAG,CAAC5G,OAAO,GAAGuJ,IAAI,CAACvJ,OAAO,CAAA;UAC5C,IAAIuJ,IAAI,CAACK,aAAa,EAAEhD,GAAG,CAACgD,aAAa,GAAGL,IAAI,CAACK,aAAa,CAAA;UAC9D,IAAIL,IAAI,CAACO,iBAAiB,EACxBlD,GAAG,CAACkD,iBAAiB,GAAGP,IAAI,CAACO,iBAAiB,CAAA;UAChD,IAAIP,IAAI,CAACM,sBAAsB,EAC7BjD,GAAG,CAACiD,sBAAsB,GAAGN,IAAI,CAACM,sBAAsB,CAAA;EAC1D,QAAA,IAAIN,IAAI,CAACa,aAAa,KAAK7e,SAAS,EAClCqb,GAAG,CAACwD,aAAa,GAAGb,IAAI,CAACa,aAAa,CAAA;EACxC,QAAA,IAAIb,IAAI,CAACc,gBAAgB,KAAK9e,SAAS,EACrCqb,GAAG,CAACyD,gBAAgB,GAAGd,IAAI,CAACc,gBAAgB,CAAA;UAC9C,IAAId,IAAI,CAAC5L,SAAS,EAAEiJ,GAAG,CAACjJ,SAAS,GAAG4L,IAAI,CAAC5L,SAAS,CAAA;UAClD,IAAI4L,IAAI,CAACe,IAAI,EAAE1D,GAAG,CAAC0D,IAAI,GAAGf,IAAI,CAACe,IAAI,CAAA;UACnC,IAAIf,IAAI,CAAC1L,MAAM,EAAE+I,GAAG,CAAC/I,MAAM,GAAG0L,IAAI,CAAC1L,MAAM,CAAA;UACzC,IAAI0L,IAAI,CAAClI,IAAI,EAAEuF,GAAG,CAACvF,IAAI,GAAGkI,IAAI,CAAClI,IAAI,CAAA;UACnC,IAAIkI,IAAI,CAACgB,KAAK,EAAE3D,GAAG,CAAC2D,KAAK,GAAGhB,IAAI,CAACgB,KAAK,CAAA;UACtC,IAAIhB,IAAI,CAACpM,IAAI,EAAEyJ,GAAG,CAACzJ,IAAI,GAAGoM,IAAI,CAACpM,IAAI,CAAA;UACnC,IAAIoM,IAAI,CAACQ,WAAW,EAAEnD,GAAG,CAACmD,WAAW,GAAGR,IAAI,CAACQ,WAAW,CAAA;UACxD,IAAIR,IAAI,CAACC,OAAO,EAAE5C,GAAG,CAAC4C,OAAO,GAAGD,IAAI,CAACC,OAAO,CAAA;UAC5C,IAAID,IAAI,CAACnH,SAAS,EAAEwE,GAAG,CAACxE,SAAS,GAAGmH,IAAI,CAACnH,SAAS,CAAA;;EAElD;UACA,MAAMrN,GAAG,GAAG,IAAI,CAACsS,IAAI,CAACT,GAAG,EAAEnG,EAAE,CAAC,CAAA;EAC9B,QAAA,IAAI,CAAC6G,kBAAkB,CAACV,GAAG,EAAE7R,GAAG,CAAC,CAAA;UACjC,IAAIA,GAAG,CAAC6S,YAAY,IAAI,CAAC7S,GAAG,CAACyV,WAAW,EAAE;EACxC,UAAA,OAAO,IAAI,CAAC7B,iBAAiB,CAC3BlI,EAAE,EACF1L,GAAG,CAACvC,KAAK,EACT,YAAY,EACZ+W,IAAI,CAAC9I,EAAE,EACPmG,GAAG,EACH7R,GAAG,CACJ,CAAA;EACH,SAAA;EACF,OAAA;EACF,KAAA;;EAQA;EACA,IAAA,OAAO,IAAI,CAAC4T,iBAAiB,CAC3BlI,EAAE,EACF4I,OAAO,CAACD,YAAY,KAAK7d,SAAS,GAAG,IAAI,GAAG8d,OAAO,CAACD,YAAY,EAChE,cAAc,CACf,CAAA;EACH,GAAA;EAEQO,EAAAA,oBAAoB,CAC1BxM,IAAY,EACZyM,aAAiC,EACjCE,iBAAqC,EACrCtM,KAAiC,EACjCuC,QAA4B,EAC5BgK,WAA+B,EACtB;MACT,IAAI,CAACvM,KAAK,IAAIuC,QAAQ,KAAKxU,SAAS,EAAE,OAAO,IAAI,CAAA;MAEjD,MAAM;EAAEmS,MAAAA,SAAAA;OAAW,GAAG,IAAI,CAAC+M,iBAAiB,CAC1Cb,aAAa,EACbE,iBAAiB,CAClB,CAAA;MACD,IAAI,CAACpM,SAAS,EAAE;EACd,MAAA,OAAO,KAAK,CAAA;EACd,KAAA;MAEA,MAAML,CAAC,GAAGH,IAAI,CAACC,IAAI,EAAEO,SAAS,EAAEqM,WAAW,IAAI,CAAC,CAAC,CAAA;EACjD,IAAA,IAAI1M,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK,CAAA;EAE5B,IAAA,OAAOG,KAAK,GACRD,OAAO,CAACF,CAAC,EAAEG,KAAK,CAAC,GACjBuC,QAAQ,KAAKxU,SAAS,GACtB8R,CAAC,IAAI0C,QAAQ,GACb,IAAI,CAAA;EACV,GAAA;IAEQ2J,gBAAgB,CAACtH,SAA6B,EAAW;MAC/D,OAAOF,aAAa,CAAC,IAAI,CAAC3Q,aAAa,EAAE,EAAE6Q,SAAS,CAAC,CAAA;EACvD,GAAA;IAEQqH,cAAc,CAACD,OAAiB,EAAW;EACjD,IAAA,OAAOA,OAAO,CAAC/J,IAAI,CAAEhH,MAAM,IAAK;QAC9B,MAAM;EAAEiF,QAAAA,SAAAA;SAAW,GAAG,IAAI,CAAC+M,iBAAiB,CAAChS,MAAM,CAAC8C,SAAS,CAAC,CAAA;EAC9D,MAAA,IAAI,CAACmC,SAAS,EAAE,OAAO,IAAI,CAAA;EAC3B,MAAA,MAAML,CAAC,GAAGH,IAAI,CAACzE,MAAM,CAAC0E,IAAI,EAAEO,SAAS,EAAEjF,MAAM,CAACsR,WAAW,IAAI,CAAC,CAAC,CAAA;EAC/D,MAAA,IAAI1M,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,CAAA;EAC3B,MAAA,OAAO,CAAC5E,MAAM,CAACoF,MAAM,CAAC4B,IAAI,CAAEiL,CAAC,IAAKnN,OAAO,CAACF,CAAC,EAAEqN,CAAC,CAAC,CAAC,CAAA;EAClD,KAAC,CAAC,CAAA;EACJ,GAAA;EAEQrD,EAAAA,IAAI,CACVF,UAAyB,EACzBwD,SAAwB,EACb;EACX,IAAA,MAAMvb,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;EAC1B,IAAA,MAAM0Q,aAAa,GAAGqH,UAAU,CAACgD,UAAU,CAAChP,MAAM,CAAA;;EAElD;MACA,IAAI2E,aAAa,GAAG,CAAC,EAAE;EAGrB,MAAA,OAAO,IAAI,CAAC8K,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,IAAI,IAAI,CAAC/G,IAAI,CAACiH,OAAO,KAAK,KAAK,EAAE;EAG/B,MAAA,OAAO,IAAI,CAACD,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACAxD,IAAAA,UAAU,GAAG,IAAI,CAAC2D,eAAe,CAAC3D,UAAU,CAAC,CAAA;;EAE7C;EACA,IAAA,IACEA,UAAU,CAAC4D,WAAW,IACtB,CAAC1M,aAAa,CAAC,IAAI,CAAC2M,cAAc,EAAE,EAAE7D,UAAU,CAAC4D,WAAW,CAAC,EAC7D;EAKA,MAAA,OAAO,IAAI,CAACH,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,MAAMM,UAAU,GAAGzK,sBAAsB,CACvCpR,GAAG,EACH,IAAI,CAAC4b,cAAc,EAAE,EACrBlL,aAAa,CACd,CAAA;MACD,IAAImL,UAAU,KAAK,IAAI,EAAE;QAMvB,OAAO,IAAI,CAACL,UAAU,CAACzD,UAAU,EAAE8D,UAAU,EAAE,KAAK,EAAEN,SAAS,CAAC,CAAA;EAClE,KAAA;;EAEA;EACA,IAAA,IAAI,IAAI,CAAC/G,IAAI,CAAChP,gBAAgB,IAAIxF,GAAG,IAAI,IAAI,CAACwU,IAAI,CAAChP,gBAAgB,EAAE;QACnE,MAAMqS,SAAS,GAAG,IAAI,CAACrD,IAAI,CAAChP,gBAAgB,CAACxF,GAAG,CAAC,CAAA;QAMjD,OAAO,IAAI,CAACwb,UAAU,CAACzD,UAAU,EAAEF,SAAS,EAAE,KAAK,EAAE0D,SAAS,CAAC,CAAA;EACjE,KAAA;;EAEA;MACA,IAAIxD,UAAU,CAAC+D,MAAM,KAAK,OAAO,IAAI/D,UAAU,CAACgE,MAAM,KAAK,KAAK,EAAE;EAKhE,MAAA,OAAO,IAAI,CAACP,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;MACA,MAAM;QAAEf,aAAa;EAAElM,MAAAA,SAAAA;OAAW,GAAG,IAAI,CAAC+M,iBAAiB,CACzDtD,UAAU,CAACyC,aAAa,EACxB,IAAI,CAAChG,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,GAC/D1C,UAAU,CAAC2C,iBAAiB,GAC5Bve,SAAS,CACd,CAAA;MACD,IAAI,CAACmS,SAAS,EAAE;EAKd,MAAA,OAAO,IAAI,CAACkN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;MAEA,IAAIS,QAAQ,GAAG,CAAC,CAAC,CAAA;MAEjB,IAAIC,iBAAiB,GAAG,KAAK,CAAA;MAC7B,IAAIC,4BAA4B,GAAG,KAAK,CAAA;MACxC,IAAI,IAAI,CAAC1H,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,EAAE;QACvE,MAAM;UAAE5C,SAAS;EAAEsE,QAAAA,gBAAAA;SAAkB,GAAG,IAAI,CAACC,yBAAyB,CACpErE,UAAU,CAAC/X,GAAG,EACd+X,UAAU,CAACiD,aAAa,EACxBjD,UAAU,CAACkD,gBAAgB,EAC3BlD,UAAU,CAACmD,IAAI,CAChB,CAAA;QACDe,iBAAiB,GAAGpE,SAAS,IAAI,CAAC,CAAA;EAClCmE,MAAAA,QAAQ,GAAGnE,SAAS,CAAA;QACpBqE,4BAA4B,GAAG,CAAC,CAACC,gBAAgB,CAAA;EACnD,KAAA;;EAEA;MACA,IAAI,CAACF,iBAAiB,EAAE;EACtB;QACA,IAAIlE,UAAU,CAACqC,OAAO,EAAE;UACtB,IAAI,IAAI,CAACC,cAAc,CAACtC,UAAU,CAACqC,OAAO,CAAC,EAAE;EAK3C,UAAA,OAAO,IAAI,CAACoB,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,SAAA;EACF,OAAC,MAAM,IACLxD,UAAU,CAACxJ,SAAS,IACpB,CAACF,WAAW,CAACC,SAAS,EAAEyJ,UAAU,CAACxJ,SAAS,CAAC,EAC7C;EAKA,QAAA,OAAO,IAAI,CAACiN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;;EAEA;QACA,IAAIxD,UAAU,CAACvI,OAAO,IAAI,CAACJ,UAAU,CAAC2I,UAAU,CAACvI,OAAO,CAAC,EAAE;EAKzD,QAAA,OAAO,IAAI,CAACgM,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;;EAEA;EACA,MAAA,IACExD,UAAU,CAAC/E,SAAS,IACpB,CAAC,IAAI,CAACsH,gBAAgB,CAACvC,UAAU,CAAC/E,SAAS,CAAC,EAC5C;EAKA,QAAA,OAAO,IAAI,CAACwI,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;;EAEA;EACA,MAAA,IACExD,UAAU,CAACsE,MAAM,IACjB,CAAC,IAAI,CAACC,gBAAgB,CAACvE,UAAU,CAACsE,MAAM,CAAa,EACrD;EAKA,QAAA,OAAO,IAAI,CAACb,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAIxD,UAAU,CAACrV,GAAG,IAAI,CAAC,IAAI,CAAC6Z,WAAW,CAACxE,UAAU,CAACrV,GAAG,CAAW,EAAE;EAKjE,MAAA,OAAO,IAAI,CAAC8Y,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,MAAMtN,CAAC,GAAGH,IAAI,CACZiK,UAAU,CAAChK,IAAI,IAAI/N,GAAG,EACtBsO,SAAS,EACTyJ,UAAU,CAAC4C,WAAW,IAAI,CAAC,CAC5B,CAAA;MACD,IAAI1M,CAAC,KAAK,IAAI,EAAE;EAKd,MAAA,OAAO,IAAI,CAACuN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;MAEA,IAAI,CAACU,iBAAiB,EAAE;QACtB,MAAMxN,MAAM,GACVsJ,UAAU,CAACtJ,MAAM,IACjBgC,eAAe,CACbC,aAAa,EACbqH,UAAU,CAACpH,QAAQ,KAAKxU,SAAS,GAAG,CAAC,GAAG4b,UAAU,CAACpH,QAAQ,EAC3DoH,UAAU,CAACnH,OAAO,CACnB,CAAA;EACHoL,MAAAA,QAAQ,GAAGxN,eAAe,CAACP,CAAC,EAAEQ,MAAM,CAAC,CAAA;EACvC,KAAA;;EAEA;EACA,IAAA,IAAIyN,4BAA4B,EAAE;EAKhC,MAAA,OAAO,IAAI,CAACV,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,EAAEpf,SAAS,EAAE,IAAI,CAAC,CAAA;EAC3E,KAAA;;EAEA;MACA,IAAI6f,QAAQ,GAAG,CAAC,EAAE;EAKhB,MAAA,OAAO,IAAI,CAACR,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;MACA,IAAI,OAAO,IAAIxD,UAAU,EAAE;QAMzB,OAAO,IAAI,CAACyD,UAAU,CACpBzD,UAAU,EACVA,UAAU,CAAC+C,KAAK,KAAK3e,SAAS,GAAG,CAAC,CAAC,GAAG4b,UAAU,CAAC+C,KAAK,EACtD,KAAK,EACLS,SAAS,CACV,CAAA;EACH,KAAA;;EAEA;EACA,IAAA,IAAI,IAAI,CAAC/G,IAAI,CAACgI,MAAM,EAAE;EAKpB,MAAA,OAAO,IAAI,CAAChB,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,IAAIxD,UAAU,CAAC+D,MAAM,KAAK,SAAS,EAAE;EAKnC,MAAA,OAAO,IAAI,CAACN,UAAU,CAACzD,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAEwD,SAAS,CAAC,CAAA;EAC1D,KAAA;;EAEA;EACA,IAAA,MAAMvD,MAAM,GAAG,IAAI,CAACwD,UAAU,CAC5BzD,UAAU,EACViE,QAAQ,EACR,IAAI,EACJT,SAAS,EACTtN,CAAC,EACDgO,iBAAiB,CAClB,CAAA;;EAED;MACA,IAAI,IAAI,CAACzH,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,EAAE;QACvE,MAAM;UACJgC,OAAO;EACPzc,QAAAA,GAAG,EAAE0c,OAAO;EACZC,QAAAA,GAAAA;SACD,GAAG,IAAI,CAACC,kCAAkC,CACzCpC,aAAa,EACbjI,QAAQ,CAACjE,SAAS,CAAC,EACnB;EACE,QAAA,CAAC,IAAI,CAACuO,6BAA6B,CACjC9E,UAAU,CAAC/X,GAAG,EACd+X,UAAU,CAACiD,aAAa,CACzB,GAAGhD,MAAM,CAAChY,GAAAA;EACb,OAAC,CACF,CAAA;EACD,MAAA,IAAIyc,OAAO,EAAE;EACX;EACA,QAAA,IAAI,CAACjI,IAAI,CAAC6C,0BAA0B,GAClC,IAAI,CAAC7C,IAAI,CAAC6C,0BAA0B,IAAI,EAAE,CAAA;UAC5C,IAAI,CAAC7C,IAAI,CAAC6C,0BAA0B,CAACqF,OAAO,CAAC,GAAGC,GAAG,CAAA;EACnD;UACA,IAAI,CAACnI,IAAI,CAACqC,mBAAmB,CAACiG,eAAe,CAACH,GAAG,CAAC,CAAA;EACpD,OAAA;EACF,KAAA;;EAEA;EACA,IAAA,IAAI,CAAC9B,MAAM,CAAC9C,UAAU,EAAEC,MAAM,CAAC,CAAA;EAQ/B,IAAA,OAAOA,MAAM,CAAA;EACf,GAAA;EAEA+E,EAAAA,GAAG,CAACC,GAAW,EAAEC,GAA4B,EAAE;EAC7C,IAAA,IAAI,CAAC,IAAI,CAACrI,KAAK,EAAE,OAAA;MACjB,IAAI,IAAI,CAACJ,IAAI,CAACuI,GAAG,EAAE,IAAI,CAACvI,IAAI,CAACuI,GAAG,CAACC,GAAG,EAAEC,GAAG,CAAC,CAAC,KACtClO,OAAO,CAACgO,GAAG,CAACC,GAAG,EAAEC,GAAG,CAAC,CAAA;EAC5B,GAAA;EAEQpC,EAAAA,MAAM,CAAI9C,UAAyB,EAAEC,MAAiB,EAAE;EAC9D,IAAA,IAAI,CAAC,IAAI,CAACxD,IAAI,CAAC0I,gBAAgB,EAAE,OAAA;EAEjC,IAAA,MAAMld,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;;EAE1B;EACA,IAAA,MAAMoQ,CAAC,GACL4H,MAAM,CAACwC,aAAa,GAAGxC,MAAM,CAAC1J,SAAS,GAAGtO,GAAG,GAAGgY,MAAM,CAACY,WAAW,CAAA;MACpE,IAAI,IAAI,CAAClE,mBAAmB,CAACjQ,GAAG,CAAC2L,CAAC,CAAC,EAAE,OAAA;EACrC,IAAA,IAAI,CAACsE,mBAAmB,CAACtU,GAAG,CAACgQ,CAAC,CAAC,CAAA;MAE/B,IAAI;QACF,IAAI,CAACoE,IAAI,CAAC0I,gBAAgB,CAACnF,UAAU,EAAEC,MAAM,CAAC,CAAA;OAC/C,CAAC,OAAO5Z,CAAC,EAAE;EACV2Q,MAAAA,OAAO,CAACC,KAAK,CAAC5Q,CAAC,CAAC,CAAA;EAClB,KAAA;EACF,GAAA;IAEQsd,eAAe,CAAI3D,UAAyB,EAAiB;EACnE,IAAA,MAAM/X,GAAG,GAAG+X,UAAU,CAAC/X,GAAG,CAAA;EAC1B,IAAA,MAAMmd,CAAC,GAAG,IAAI,CAAC3I,IAAI,CAAC1V,SAAS,CAAA;EAC7B,IAAA,IAAIqe,CAAC,IAAIA,CAAC,CAACnd,GAAG,CAAC,EAAE;EACf+X,MAAAA,UAAU,GAAGhZ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE+Y,UAAU,EAAEoF,CAAC,CAACnd,GAAG,CAAC,CAAC,CAAA;EAClD,MAAA,IAAI,OAAO+X,UAAU,CAACrV,GAAG,KAAK,QAAQ,EAAE;UACtCqV,UAAU,CAACrV,GAAG,GAAGgM,YAAY;EAC3B;UACAqJ,UAAU,CAACrV,GAAG,CACf,CAAA;EACH,OAAA;EACF,KAAA;EAEA,IAAA,OAAOqV,UAAU,CAAA;EACnB,GAAA;EAEQsD,EAAAA,iBAAiB,CAACjU,IAAa,EAAEgW,QAAiB,EAAE;EAC1D,IAAA,IAAI5C,aAAa,GAAGpT,IAAI,IAAI,IAAI,CAAA;EAChC;MACA,IAAIkH,SAAc,GAAG,EAAE,CAAA;EAEvB,IAAA,IAAI,IAAI,CAAC6G,mBAAmB,CAACqF,aAAa,CAAC,EAAE;EAC3ClM,MAAAA,SAAS,GAAG,IAAI,CAAC6G,mBAAmB,CAACqF,aAAa,CAAC,CAAA;EACrD,KAAC,MAAM,IAAI,IAAI,CAAChG,IAAI,CAACtS,UAAU,EAAE;QAC/BoM,SAAS,GAAG,IAAI,CAACkG,IAAI,CAACtS,UAAU,CAACsY,aAAa,CAAC,IAAI,EAAE,CAAA;EACvD,KAAC,MAAM,IAAI,IAAI,CAAChG,IAAI,CAAC6I,IAAI,EAAE;QACzB/O,SAAS,GAAG,IAAI,CAACkG,IAAI,CAAC6I,IAAI,CAAC7C,aAAa,CAAC,IAAI,EAAE,CAAA;EACjD,KAAA;;EAEA;EACA,IAAA,IAAI,CAAClM,SAAS,IAAI8O,QAAQ,EAAE;EAC1B,MAAA,IAAI,IAAI,CAACjI,mBAAmB,CAACiI,QAAQ,CAAC,EAAE;EACtC9O,QAAAA,SAAS,GAAG,IAAI,CAAC6G,mBAAmB,CAACiI,QAAQ,CAAC,CAAA;EAChD,OAAC,MAAM,IAAI,IAAI,CAAC5I,IAAI,CAACtS,UAAU,EAAE;UAC/BoM,SAAS,GAAG,IAAI,CAACkG,IAAI,CAACtS,UAAU,CAACkb,QAAQ,CAAC,IAAI,EAAE,CAAA;EAClD,OAAC,MAAM,IAAI,IAAI,CAAC5I,IAAI,CAAC6I,IAAI,EAAE;UACzB/O,SAAS,GAAG,IAAI,CAACkG,IAAI,CAAC6I,IAAI,CAACD,QAAQ,CAAC,IAAI,EAAE,CAAA;EAC5C,OAAA;EACA,MAAA,IAAI9O,SAAS,EAAE;EACbkM,QAAAA,aAAa,GAAG4C,QAAQ,CAAA;EAC1B,OAAA;EACF,KAAA;MAEA,OAAO;QAAE5C,aAAa;EAAElM,MAAAA,SAAAA;OAAW,CAAA;EACrC,GAAA;EAEQkN,EAAAA,UAAU,CAChBzD,UAAyB,EACzBuF,cAAsB,EACtBC,QAAiB,EACjBhC,SAAwB,EACxBiC,MAAe,EACfC,gBAA0B,EACf;MACX,IAAIjF,YAAY,GAAG,IAAI,CAAA;EACvB;MACA,IAAI8E,cAAc,GAAG,CAAC,IAAIA,cAAc,IAAIvF,UAAU,CAACgD,UAAU,CAAChP,MAAM,EAAE;EACxEuR,MAAAA,cAAc,GAAG,CAAC,CAAA;EAClB9E,MAAAA,YAAY,GAAG,KAAK,CAAA;EACtB,KAAA;MAEA,MAAM;QAAEgC,aAAa;EAAElM,MAAAA,SAAAA;OAAW,GAAG,IAAI,CAAC+M,iBAAiB,CACzDtD,UAAU,CAACyC,aAAa,EACxB,IAAI,CAAChG,IAAI,CAACqC,mBAAmB,IAAI,CAACkB,UAAU,CAAC0C,sBAAsB,GAC/D1C,UAAU,CAAC2C,iBAAiB,GAC5Bve,SAAS,CACd,CAAA;EAED,IAAA,MAAM+e,IAA4B,GAAGnD,UAAU,CAACmD,IAAI,GAChDnD,UAAU,CAACmD,IAAI,CAACoC,cAAc,CAAC,GAC/B,EAAE,CAAA;EAEN,IAAA,MAAM3X,GAAc,GAAG;EACrB3F,MAAAA,GAAG,EAAEkb,IAAI,CAAClb,GAAG,IAAI,EAAE,GAAGsd,cAAc;QACpC/B,SAAS;QACT/C,YAAY;QACZ+E,QAAQ;EACR3E,MAAAA,WAAW,EAAE0E,cAAc;EAC3Bla,MAAAA,KAAK,EAAE2U,UAAU,CAACgD,UAAU,CAACuC,cAAc,CAAC;QAC5C9C,aAAa;QACblM,SAAS;QACTmP,gBAAgB,EAAE,CAAC,CAACA,gBAAAA;OACrB,CAAA;MAED,IAAIvC,IAAI,CAACjJ,IAAI,EAAEtM,GAAG,CAACsM,IAAI,GAAGiJ,IAAI,CAACjJ,IAAI,CAAA;MACnC,IAAIuL,MAAM,KAAKrhB,SAAS,EAAEwJ,GAAG,CAAC6X,MAAM,GAAGA,MAAM,CAAA;MAC7C,IAAItC,IAAI,CAACE,WAAW,EAAEzV,GAAG,CAACyV,WAAW,GAAGF,IAAI,CAACE,WAAW,CAAA;EAExD,IAAA,OAAOzV,GAAG,CAAA;EACZ,GAAA;EAEQiW,EAAAA,cAAc,GAAG;EACvB,IAAA,OAAO,IAAI,CAACpH,IAAI,CAAC9R,GAAG,KAAKnF,SAAS,GAAGC,MAAM,CAACkgB,QAAQ,CAACpN,IAAI,GAAG,EAAE,CAAC,CAAA;EACjE,GAAA;IAEQiM,WAAW,CAACoB,QAAgB,EAAW;EAC7C,IAAA,MAAMjb,GAAG,GAAG,IAAI,CAACkZ,cAAc,EAAE,CAAA;EACjC,IAAA,IAAI,CAAClZ,GAAG,EAAE,OAAO,KAAK,CAAA;EAEtB,IAAA,MAAMkb,QAAQ,GAAGlb,GAAG,CAACmM,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC,CAAA;MAEzE,IAAI8O,QAAQ,CAACxQ,IAAI,CAACzK,GAAG,CAAC,EAAE,OAAO,IAAI,CAAA;MACnC,IAAIib,QAAQ,CAACxQ,IAAI,CAACyQ,QAAQ,CAAC,EAAE,OAAO,IAAI,CAAA;EACxC,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;IAEQtB,gBAAgB,CAACuB,SAAmB,EAAW;MACrD,MAAMxB,MAAM,GAAG,IAAI,CAAC7H,IAAI,CAAC6H,MAAM,IAAI,EAAE,CAAA;EACrC,IAAA,KAAK,IAAIhY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwZ,SAAS,CAAC9R,MAAM,EAAE1H,CAAC,EAAE,EAAE;QACzC,IAAIgY,MAAM,CAACwB,SAAS,CAACxZ,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAA;EACvC,KAAA;EACA,IAAA,OAAO,KAAK,CAAA;EACd,GAAA;IAEQqU,gBAAgB,CAACoF,OAAgC,EAAE;MACzD,IAAI,CAACvgB,SAAS,EAAE,OAAA;MAChB,MAAMka,IAAoB,GAAG,EAAE,CAAA;MAC/B,IAAIqG,OAAO,CAACC,GAAG,EAAE;EACf,MAAA,MAAMvd,CAAC,GAAG/C,QAAQ,CAACoO,aAAa,CAAC,OAAO,CAAC,CAAA;EACzCrL,MAAAA,CAAC,CAACyJ,SAAS,GAAG6T,OAAO,CAACC,GAAG,CAAA;EACzBtgB,MAAAA,QAAQ,CAACugB,IAAI,CAACC,WAAW,CAACzd,CAAC,CAAC,CAAA;QAC5BiX,IAAI,CAACrL,IAAI,CAAC,MAAM5L,CAAC,CAAC0d,MAAM,EAAE,CAAC,CAAA;EAC7B,KAAA;MACA,IAAIJ,OAAO,CAACK,EAAE,EAAE;EACd,MAAA,MAAMC,MAAM,GAAG3gB,QAAQ,CAACoO,aAAa,CAAC,QAAQ,CAAC,CAAA;EAC/CuS,MAAAA,MAAM,CAACnU,SAAS,GAAG6T,OAAO,CAACK,EAAE,CAAA;EAC7B1gB,MAAAA,QAAQ,CAACugB,IAAI,CAACC,WAAW,CAACG,MAAM,CAAC,CAAA;QACjC3G,IAAI,CAACrL,IAAI,CAAC,MAAMgS,MAAM,CAACF,MAAM,EAAE,CAAC,CAAA;EAClC,KAAA;MACA,IAAIJ,OAAO,CAACO,YAAY,EAAE;EACxBP,MAAAA,OAAO,CAACO,YAAY,CAAC9d,OAAO,CAAE0L,QAAQ,IAAK;UACzCwL,IAAI,CAACrL,IAAI,CAACnD,KAAM,CAACsE,WAAW,CAACtB,QAAQ,CAAwB,CAACjF,MAAM,CAAC,CAAA;EACvE,OAAC,CAAC,CAAA;EACJ,KAAA;EACA,IAAA,OAAO,MAAM;EACXyQ,MAAAA,IAAI,CAAClX,OAAO,CAAE+d,EAAE,IAAKA,EAAE,EAAE,CAAC,CAAA;OAC3B,CAAA;EACH,GAAA;IAEQC,uCAAuC,CAAC3e,IAAyB,EAAE;EACzE,IAAA,MAAMsC,UAAU,GAAG,IAAItD,GAAG,EAAU,CAAA;EACpC,IAAA,MAAMqG,QAAQ,GAAGrF,IAAI,IAAIA,IAAI,CAACqF,QAAQ,GAAGrF,IAAI,CAACqF,QAAQ,GAAG,IAAI,CAACC,WAAW,EAAE,CAAA;EAC3E,IAAA,MAAMJ,WAAW,GACflF,IAAI,IAAIA,IAAI,CAACkF,WAAW,GAAGlF,IAAI,CAACkF,WAAW,GAAG,IAAI,CAACC,cAAc,EAAE,CAAA;MACrEhG,MAAM,CAACuD,IAAI,CAAC2C,QAAQ,CAAC,CAAC1E,OAAO,CAAE8Q,EAAE,IAAK;EACpC,MAAA,MAAM4I,OAAO,GAAGhV,QAAQ,CAACoM,EAAE,CAAC,CAAA;QAC5B,IAAI4I,OAAO,CAACC,KAAK,EAAE;EACjB,QAAA,KAAK,MAAMC,IAAI,IAAIF,OAAO,CAACC,KAAK,EAAE;YAChC,IAAIC,IAAI,CAACY,UAAU,EAAE;cACnB7Y,UAAU,CAAC9B,GAAG,CAAC+Z,IAAI,CAACK,aAAa,IAAI,IAAI,CAAC,CAAA;cAC1C,IAAIL,IAAI,CAACO,iBAAiB,EAAE;EAC1BxY,cAAAA,UAAU,CAAC9B,GAAG,CAAC+Z,IAAI,CAACO,iBAAiB,CAAC,CAAA;EACxC,aAAA;EACF,WAAA;EACF,SAAA;EACF,OAAA;EACF,KAAC,CAAC,CAAA;EACF5V,IAAAA,WAAW,CAAClB,GAAG,CAAEmU,UAAU,IAAK;QAC9B7V,UAAU,CAAC9B,GAAG,CAAC2X,UAAU,CAACyC,aAAa,IAAI,IAAI,CAAC,CAAA;QAChD,IAAIzC,UAAU,CAAC2C,iBAAiB,EAAE;EAChCxY,QAAAA,UAAU,CAAC9B,GAAG,CAAC2X,UAAU,CAAC2C,iBAAiB,CAAC,CAAA;EAC9C,OAAA;EACF,KAAC,CAAC,CAAA;EACF,IAAA,OAAO3Z,KAAK,CAACC,IAAI,CAACkB,UAAU,CAAC,CAAA;EAC/B,GAAA;IAEA,MAAa0C,oBAAoB,CAAChF,IAAyB,EAAE;EAC3D,IAAA,IAAI,IAAI,CAAC4U,IAAI,CAACqC,mBAAmB,EAAE;EACjC,MAAA,MAAM3U,UAAU,GAAG,IAAI,CAACsc,0BAA0B,CAAC5e,IAAI,CAAC,CAAA;EACxD,MAAA,IAAI,CAAC4U,IAAI,CAAC6C,0BAA0B,GAAG,MAAM,IAAI,CAAC7C,IAAI,CAACqC,mBAAmB,CAAC4H,iBAAiB,CAC1Fvc,UAAU,CACX,CAAA;EACH,KAAA;EACF,GAAA;EAEQwc,EAAAA,2BAA2B,GAAsB;MACvD,MAAMC,iBAAoC,GAAG,EAAE,CAAA;EAC/C5f,IAAAA,MAAM,CAAC6f,MAAM,CAAC,IAAI,CAACpK,IAAI,CAAC6C,0BAA0B,IAAI,EAAE,CAAC,CAAC9W,OAAO,CAAEoc,GAAG,IAAK;EACzE,MAAA,IAAIA,GAAG,CAACkC,WAAW,EAAE9f,MAAM,CAACC,MAAM,CAAC2f,iBAAiB,EAAEhC,GAAG,CAACkC,WAAW,CAAC,CAAA;EACxE,KAAC,CAAC,CAAA;EACF,IAAA,OAAOF,iBAAiB,CAAA;EAC1B,GAAA;IAEQvC,yBAAyB,CAC/B0C,aAAqB,EACrBC,uBAAgC,EAChCC,0BAAmC,EACnC9D,IAAsB,EAItB;MACA6D,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC,CAAA;MACtDC,0BAA0B,GAAGA,0BAA0B,IAAI,CAAC,CAAA;MAC5D9D,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;MACjB,MAAM7J,EAAE,GAAG,IAAI,CAACwL,6BAA6B,CAC3CiC,aAAa,EACbC,uBAAuB,CACxB,CAAA;EACD,IAAA,MAAMF,WAAW,GAAG,IAAI,CAACH,2BAA2B,EAAE,CAAA;;EAEtD;MACA,IAAIM,0BAA0B,GAAG,CAAC,EAAE;QAClC,KAAK,IAAI3a,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI2a,0BAA0B,EAAE3a,CAAC,EAAE,EAAE;UACpD,MAAM4a,UAAU,GAAG,IAAI,CAACpC,6BAA6B,CAACiC,aAAa,EAAEza,CAAC,CAAC,CAAA;EACvE,QAAA,IAAIwa,WAAW,CAACI,UAAU,CAAC,KAAK9iB,SAAS,EAAE;YACzC,OAAO;cACL0b,SAAS,EAAE,CAAC,CAAC;EACbsE,YAAAA,gBAAgB,EAAE,IAAA;aACnB,CAAA;EACH,SAAA;EACF,OAAA;EACF,KAAA;EACA,IAAA,MAAM+C,YAAY,GAAGL,WAAW,CAACxN,EAAE,CAAC,CAAA;MACpC,IAAI6N,YAAY,KAAK/iB,SAAS;EAC5B;QACA,OAAO;EAAE0b,QAAAA,SAAS,EAAE,CAAC,CAAA;SAAG,CAAA;EAC1B,IAAA,MAAMA,SAAS,GAAGqD,IAAI,CAACiE,SAAS,CAAEnW,CAAC,IAAKA,CAAC,CAAChJ,GAAG,KAAKkf,YAAY,CAAC,CAAA;MAC/D,IAAIrH,SAAS,GAAG,CAAC;EACf;QACA,OAAO;EAAEA,QAAAA,SAAS,EAAE,CAAC,CAAA;SAAG,CAAA;MAE1B,OAAO;EAAEA,MAAAA,SAAAA;OAAW,CAAA;EACtB,GAAA;EAEQgF,EAAAA,6BAA6B,CACnCiC,aAAqB,EACrBC,uBAAgC,EACX;MACrBA,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC,CAAA;MACtD,OAAUD,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,eAAKC,uBAAuB,CAAA,CAAA;EACrD,GAAA;IAEQP,0BAA0B,CAChC5e,IAAyB,EACD;MACxB,MAAMsC,UAAkC,GAAG,EAAE,CAAA;MAC7C,IAAI,CAACsS,IAAI,CAAC4K,gCAAgC,GAAG,CAAC,IAAI,CAAC5K,IAAI,CACpD4K,gCAAgC,GAC/B,IAAI,CAACb,uCAAuC,CAAC3e,IAAI,CAAC,GAClD,IAAI,CAAC4U,IAAI,CAAC4K,gCAAgC,CAAA;MAC9C,IAAI,CAAC5K,IAAI,CAAC4K,gCAAgC,CAAC7e,OAAO,CAAE6G,IAAI,IAAK;QAC3D,MAAM;EAAEkH,QAAAA,SAAAA;EAAU,OAAC,GAAG,IAAI,CAAC+M,iBAAiB,CAACjU,IAAI,CAAC,CAAA;EAClDlF,MAAAA,UAAU,CAACkF,IAAI,CAAC,GAAGmL,QAAQ,CAACjE,SAAS,CAAC,CAAA;EACxC,KAAC,CAAC,CAAA;EACF,IAAA,OAAOpM,UAAU,CAAA;EACnB,GAAA;EAEQ0a,EAAAA,kCAAkC,CACxCyC,aAAqB,EACrBC,cAAsB,EACtBT,WAA8B,EAK9B;EACA,IAAA,MAAM7e,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;EACjD,IAAA,MAAMC,mBAAmB,GACvB,IAAI,CAAC/K,IAAI,CAAC6C,0BAA0B,IACpC,IAAI,CAAC7C,IAAI,CAAC6C,0BAA0B,CAACrX,GAAG,CAAC,GACrC,IAAI,CAACwU,IAAI,CAAC6C,0BAA0B,CAACrX,GAAG,CAAC,CAAC6e,WAAW,IAAI,EAAE,GAC3D,EAAE,CAAA;EACR,IAAA,MAAMW,cAAc,GAAG;EAAE,MAAA,GAAGD,mBAAmB;QAAE,GAAGV,WAAAA;OAAa,CAAA;EACjE,IAAA,MAAMpC,OAAO,GACXvf,IAAI,CAACC,SAAS,CAACoiB,mBAAmB,CAAC,KAAKriB,IAAI,CAACC,SAAS,CAACqiB,cAAc,CAAC,CAAA;MAExE,OAAO;QACLxf,GAAG;EACH2c,MAAAA,GAAG,EAAE;UACH0C,aAAa;UACbC,cAAc;EACdT,QAAAA,WAAW,EAAEW,cAAAA;SACd;EACD/C,MAAAA,OAAAA;OACD,CAAA;EACH,GAAA;EACF;;ECv3CA;EACA;EACA;EACO,MAAegD,mBAAmB,CAAC;EAQxC;EACF;EACA;EACA;EACA;IACE,MAAMhB,iBAAiB,CACrBvc,UAAkC,EAC8B;MAChE,MAAMwd,IAA+C,GAAG,EAAE,CAAA;EAC1D,IAAA,CACE,MAAM7c,OAAO,CAAC8c,GAAG,CACf5gB,MAAM,CAACkC,OAAO,CAACiB,UAAU,CAAC,CAAC0B,GAAG,CAAC,IAAA,IAAA;EAAA,MAAA,IAAC,CAACyb,aAAa,EAAEC,cAAc,CAAC,GAAA,IAAA,CAAA;EAAA,MAAA,OAC7D,IAAI,CAACM,cAAc,CAACP,aAAa,EAAEC,cAAc,CAAC,CAAA;EAAA,KAAA,CACnD,CACF,EACD/e,OAAO,CAAEoc,GAAG,IAAK;EACjB,MAAA,IAAIA,GAAG,EAAE;UACP,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;EACzDI,QAAAA,IAAI,CAAC1f,GAAG,CAAC,GAAG2c,GAAG,CAAA;EACjB,OAAA;EACF,KAAC,CAAC,CAAA;EACF,IAAA,OAAO+C,IAAI,CAAA;EACb,GAAA;EACF,CAAA;EAEO,MAAMG,+BAA+B,SAASJ,mBAAmB,CAAC;IAGvEnL,WAAW,CAACwL,IAA6D,EAAE;EACzEA,IAAAA,IAAI,GAAGA,IAAI,IAAI,EAAE,CAAA;EACjB,IAAA,KAAK,EAAE,CAAA;EACP,IAAA,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACC,MAAM,IAAI,mBAAmB,CAAA;MAChD,IAAI;QACF,IAAI,CAAC5hB,YAAY,GAAG2hB,IAAI,CAAC3hB,YAAY,IAAIlC,UAAU,CAACkC,YAAY,CAAA;OACjE,CAAC,OAAOC,CAAC,EAAE;EACV;EAAA,KAAA;EAEJ,GAAA;EACA,EAAA,MAAMwhB,cAAc,CAACP,aAAqB,EAAEC,cAAsB,EAAE;EAClE,IAAA,MAAMtf,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;MACjD,IAAI3C,GAAqC,GAAG,IAAI,CAAA;EAChD,IAAA,IAAI,CAAC,IAAI,CAACxe,YAAY,EAAE,OAAOwe,GAAG,CAAA;MAClC,IAAI;EACF,MAAA,MAAMqD,GAAG,GAAG,CAAC,MAAM,IAAI,CAAC7hB,YAAY,CAACkF,OAAO,CAAC,IAAI,CAAC0c,MAAM,GAAG/f,GAAG,CAAC,KAAK,IAAI,CAAA;EACxE,MAAA,MAAMJ,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,CAAC,CAAA;QAC5B,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;EACjElC,QAAAA,GAAG,GAAG/c,IAAI,CAAA;EACZ,OAAA;OACD,CAAC,OAAOxB,CAAC,EAAE;EACV;EAAA,KAAA;EAEF,IAAA,OAAOue,GAAG,CAAA;EACZ,GAAA;IACA,MAAMG,eAAe,CAACH,GAA8B,EAAE;MACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;EACzD,IAAA,IAAI,CAAC,IAAI,CAACnhB,YAAY,EAAE,OAAA;MACxB,IAAI;EACF,MAAA,MAAM,IAAI,CAACA,YAAY,CAAC2C,OAAO,CAAC,IAAI,CAACif,MAAM,GAAG/f,GAAG,EAAE9C,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAC,CAAA;OACxE,CAAC,OAAOve,CAAC,EAAE;EACV;EAAA,KAAA;EAEJ,GAAA;EACF,CAAA;EAEO,MAAM6hB,gCAAgC,SAASR,mBAAmB,CAAC;EACxE;EACF;EACA;EACA;EACA;EACA;EACA;;EAKEnL,EAAAA,WAAW,CAUR,KAAA,EAAA;MAAA,IAVS;EACVyL,MAAAA,MAAM,GAAG,mBAAmB;QAC5BG,GAAG;QACHva,GAAG;EACHwa,MAAAA,gBAAgB,GAAG,EAAC;OAMrB,GAAA,KAAA,CAAA;EACC,IAAA,KAAK,EAAE,CAAA;MACP,IAAI,CAACJ,MAAM,GAAGA,MAAM,CAAA;MACpB,IAAI,CAACG,GAAG,GAAGA,GAAG,CAAA;MACd,IAAI,CAACva,GAAG,GAAGA,GAAG,CAAA;MACd,IAAI,CAACwa,gBAAgB,GAAGA,gBAAgB,CAAA;EAC1C,GAAA;EACA,EAAA,MAAMP,cAAc,CAACP,aAAqB,EAAEC,cAAsB,EAAE;EAClE,IAAA,MAAMtf,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;MACjD,IAAI3C,GAAqC,GAAG,IAAI,CAAA;EAChD,IAAA,IAAI,CAAC,IAAI,CAACuD,GAAG,EAAE,OAAOvD,GAAG,CAAA;MACzB,IAAI;EACF,MAAA,MAAMqD,GAAG,GAAG,IAAI,CAACE,GAAG,CAACE,OAAO,CAAC,IAAI,CAACL,MAAM,GAAG/f,GAAG,CAAC,IAAI,IAAI,CAAA;EACvD,MAAA,MAAMJ,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,CAAC,CAAA;QAC5B,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;EACjElC,QAAAA,GAAG,GAAG/c,IAAI,CAAA;EACZ,OAAA;OACD,CAAC,OAAOxB,CAAC,EAAE;EACV;EAAA,KAAA;EAEF,IAAA,OAAOue,GAAG,CAAA;EACZ,GAAA;IACA,MAAMG,eAAe,CAACH,GAA8B,EAAE;MACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;EACzD,IAAA,IAAI,CAAC,IAAI,CAAC3Z,GAAG,EAAE,OAAA;EACf,IAAA,MAAM+H,GAAG,GAAGxQ,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAA;MAC/B,IAAI,CAAChX,GAAG,CAAC0a,MAAM,CACbjH,kBAAkB,CAAC,IAAI,CAAC2G,MAAM,GAAG/f,GAAG,CAAC,EACrCoZ,kBAAkB,CAAC1L,GAAG,CAAC,EACvB,IAAI,CAACyS,gBAAgB,CACtB,CAAA;EACH,GAAA;EACF,CAAA;EAEO,MAAMG,gCAAgC,SAASb,mBAAmB,CAAC;EACxE;EACF;EACA;EACA;EACA;EACA;EACA;;EAIEnL,EAAAA,WAAW,CAQR,KAAA,EAAA;MAAA,IARS;EACVyL,MAAAA,MAAM,GAAG,mBAAmB;QAC5BQ,QAAQ;EACRJ,MAAAA,gBAAgB,GAAG,EAAC;OAKrB,GAAA,KAAA,CAAA;EACC,IAAA,KAAK,EAAE,CAAA;MACP,IAAI,CAACJ,MAAM,GAAGA,MAAM,CAAA;MACpB,IAAI,CAACQ,QAAQ,GAAGA,QAAQ,CAAA;MACxB,IAAI,CAACJ,gBAAgB,GAAGA,gBAAgB,CAAA;EAC1C,GAAA;EACA,EAAA,MAAMP,cAAc,CAACP,aAAqB,EAAEC,cAAsB,EAAE;EAClE,IAAA,MAAMtf,GAAG,GAAA,EAAA,CAAA,MAAA,CAAMqf,aAAa,EAAA,IAAA,CAAA,CAAA,MAAA,CAAKC,cAAc,CAAE,CAAA;MACjD,IAAI3C,GAAqC,GAAG,IAAI,CAAA;EAChD,IAAA,IAAI,CAAC,IAAI,CAAC4D,QAAQ,EAAE,OAAO5D,GAAG,CAAA;MAC9B,IAAI;EACF,MAAA,MAAMqD,GAAG,GAAG,IAAI,CAACO,QAAQ,CAACpgB,GAAG,CAAC,IAAI,CAAC4f,MAAM,GAAG/f,GAAG,CAAC,CAAA;QAChD,MAAMJ,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,IAAI,IAAI,CAAC,CAAA;QACpC,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;EACjElC,QAAAA,GAAG,GAAG/c,IAAI,CAAA;EACZ,OAAA;OACD,CAAC,OAAOxB,CAAC,EAAE;EACV;EAAA,KAAA;EAEF,IAAA,OAAOue,GAAG,CAAA;EACZ,GAAA;IACA,MAAMG,eAAe,CAACH,GAA8B,EAAE;MACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;EACzD,IAAA,IAAI,CAAC,IAAI,CAACiB,QAAQ,EAAE,OAAA;EACpB,IAAA,MAAM7S,GAAG,GAAGxQ,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAA;EAC/B,IAAA,IAAI,CAAC4D,QAAQ,CAAClgB,GAAG,CAAC,IAAI,CAAC0f,MAAM,GAAG/f,GAAG,EAAE0N,GAAG,EAAE,IAAI,CAACyS,gBAAgB,CAAC,CAAA;EAClE,GAAA;EACF,CAAA;EAEO,MAAMK,wBAAwB,SAASf,mBAAmB,CAAC;EAChE;;EAEAnL,EAAAA,WAAW,CAAsC,KAAA,EAAA;MAAA,IAArC;EAAEmM,MAAAA,KAAAA;OAAiC,GAAA,KAAA,CAAA;EAC7C,IAAA,KAAK,EAAE,CAAA;MACP,IAAI,CAACA,KAAK,GAAGA,KAAK,CAAA;EACpB,GAAA;IAEA,MAAMhC,iBAAiB,CACrBvc,UAAkC,EAC8B;MAChE,MAAMwd,IAA2D,GAAG,EAAE,CAAA;MACtE,MAAMpd,IAAI,GAAGvD,MAAM,CAACkC,OAAO,CAACiB,UAAU,CAAC,CAAC0B,GAAG,CACzC,KAAA,IAAA;EAAA,MAAA,IAAC,CAACyb,aAAa,EAAEC,cAAc,CAAC,GAAA,KAAA,CAAA;QAAA,OAAQD,EAAAA,CAAAA,MAAAA,CAAAA,aAAa,eAAKC,cAAc,CAAA,CAAA;EAAA,KAAE,CAC3E,CAAA;EACD,IAAA,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,OAAOf,IAAI,CAAA;EAC5B,IAAA,IAAI,CAACe,KAAK,CAACC,IAAI,CAAC,GAAGpe,IAAI,CAAC,CAACY,IAAI,CAAE0b,MAAM,IAAK;EACxCA,MAAAA,MAAM,CAACre,OAAO,CAAEyf,GAAG,IAAK;UACtB,IAAI;YACF,MAAMpgB,IAAI,GAAG1C,IAAI,CAACqG,KAAK,CAACyc,GAAG,IAAI,IAAI,CAAC,CAAA;YACpC,IAAIpgB,IAAI,CAACyf,aAAa,IAAIzf,IAAI,CAAC0f,cAAc,IAAI1f,IAAI,CAACif,WAAW,EAAE;cACjE,MAAM7e,GAAG,aAAMJ,IAAI,CAACyf,aAAa,EAAKzf,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,IAAI,CAAC0f,cAAc,CAAE,CAAA;EAC3DI,YAAAA,IAAI,CAAC1f,GAAG,CAAC,GAAGJ,IAAI,CAAA;EAClB,WAAA;WACD,CAAC,OAAOxB,CAAC,EAAE;EACV;EAAA,SAAA;EAEJ,OAAC,CAAC,CAAA;EACJ,KAAC,CAAC,CAAA;EACF,IAAA,OAAOshB,IAAI,CAAA;EACb,GAAA;EAEA,EAAA,MAAME,cAAc,CAACe,cAAsB,EAAEC,eAAuB,EAAE;EACpE;EACA,IAAA,OAAO,IAAI,CAAA;EACb,GAAA;IAEA,MAAM9D,eAAe,CAACH,GAA8B,EAAE;MACpD,MAAM3c,GAAG,aAAM2c,GAAG,CAAC0C,aAAa,EAAK1C,IAAAA,CAAAA,CAAAA,MAAAA,CAAAA,GAAG,CAAC2C,cAAc,CAAE,CAAA;EACzD,IAAA,IAAI,CAAC,IAAI,CAACmB,KAAK,EAAE,OAAA;EACjB,IAAA,MAAM,IAAI,CAACA,KAAK,CAACpgB,GAAG,CAACL,GAAG,EAAE9C,IAAI,CAACC,SAAS,CAACwf,GAAG,CAAC,CAAC,CAAA;EAChD,GAAA;EACF;;;;;;;;;;;;;;;;;;;;;;;;"}