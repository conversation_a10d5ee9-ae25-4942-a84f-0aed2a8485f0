{"version": 3, "file": "esm.min.js", "sources": ["../../src/feature-repository.ts", "../../../../node_modules/dom-mutator/dist/dom-mutator.esm.js", "../../src/util.ts", "../../src/mongrule.ts", "../../src/GrowthBook.ts", "../../src/sticky-bucket-service.ts"], "sourcesContent": ["import {\n  Attributes,\n  CacheSettings,\n  FeatureApiResponse,\n  Helpers,\n  Polyfills,\n} from \"./types/growthbook\";\nimport type { GrowthBook } from \".\";\n\ntype CacheEntry = {\n  data: FeatureApiResponse;\n  sse?: boolean;\n  version: string;\n  staleAt: Date;\n};\ntype ScopedChannel = {\n  src: EventSource | null;\n  cb: (event: MessageEvent<string>) => void;\n  host: string;\n  clientKey: string;\n  headers?: Record<string, string>;\n  errors: number;\n  state: \"active\" | \"idle\" | \"disabled\";\n};\n\n// Config settings\nconst cacheSettings: CacheSettings = {\n  // Consider a fetch stale after 1 minute\n  staleTTL: 1000 * 60,\n  // Max time to keep a fetch in cache (24 hours default)\n  maxAge: 1000 * 60 * 60 * 24,\n  cacheKey: \"gbFeaturesCache\",\n  backgroundSync: true,\n  maxEntries: 10,\n  disableIdleStreams: false,\n  idleStreamInterval: 20000,\n};\nconst polyfills: Polyfills = {\n  fetch: globalThis.fetch ? globalThis.fetch.bind(globalThis) : undefined,\n  SubtleCrypto: globalThis.crypto ? globalThis.crypto.subtle : undefined,\n  EventSource: globalThis.EventSource,\n};\nexport const helpers: Helpers = {\n  fetchFeaturesCall: ({ host, clientKey, headers }) => {\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/features/${clientKey}`,\n      { headers }\n    );\n  },\n  fetchRemoteEvalCall: ({ host, clientKey, payload, headers }) => {\n    const options = {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\", ...headers },\n      body: JSON.stringify(payload),\n    };\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/eval/${clientKey}`,\n      options\n    );\n  },\n  eventSourceCall: ({ host, clientKey, headers }) => {\n    if (headers) {\n      return new polyfills.EventSource(`${host}/sub/${clientKey}`, {\n        headers,\n      });\n    }\n    return new polyfills.EventSource(`${host}/sub/${clientKey}`);\n  },\n  startIdleListener: () => {\n    let idleTimeout: number | undefined;\n    const isBrowser =\n      typeof window !== \"undefined\" && typeof document !== \"undefined\";\n    if (!isBrowser) return;\n    const onVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        window.clearTimeout(idleTimeout);\n        onVisible();\n      } else if (document.visibilityState === \"hidden\") {\n        idleTimeout = window.setTimeout(\n          onHidden,\n          cacheSettings.idleStreamInterval\n        );\n      }\n    };\n    document.addEventListener(\"visibilitychange\", onVisibilityChange);\n    return () =>\n      document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n  },\n  stopIdleListener: () => {\n    // No-op, replaced by startIdleListener\n  },\n};\n\ntry {\n  if (globalThis.localStorage) {\n    polyfills.localStorage = globalThis.localStorage;\n  }\n} catch (e) {\n  // Ignore localStorage errors\n}\n\n// Global state\nconst subscribedInstances: Map<string, Set<GrowthBook>> = new Map();\nlet cacheInitialized = false;\nconst cache: Map<string, CacheEntry> = new Map();\nconst activeFetches: Map<string, Promise<FeatureApiResponse>> = new Map();\nconst streams: Map<string, ScopedChannel> = new Map();\nconst supportsSSE: Set<string> = new Set();\n\n// Public functions\nexport function setPolyfills(overrides: Partial<Polyfills>): void {\n  Object.assign(polyfills, overrides);\n}\nexport function configureCache(overrides: Partial<CacheSettings>): void {\n  Object.assign(cacheSettings, overrides);\n  if (!cacheSettings.backgroundSync) {\n    clearAutoRefresh();\n  }\n}\n\nexport async function clearCache(): Promise<void> {\n  cache.clear();\n  activeFetches.clear();\n  clearAutoRefresh();\n  cacheInitialized = false;\n  await updatePersistentCache();\n}\n\nexport async function refreshFeatures(\n  instance: GrowthBook,\n  timeout?: number,\n  skipCache?: boolean,\n  allowStale?: boolean,\n  updateInstance?: boolean,\n  backgroundSync?: boolean\n): Promise<void> {\n  if (!backgroundSync) {\n    cacheSettings.backgroundSync = false;\n  }\n\n  const data = await fetchFeaturesWithCache(\n    instance,\n    allowStale,\n    timeout,\n    skipCache\n  );\n  updateInstance && data && (await refreshInstance(instance, data));\n}\n\n// Subscribe a GrowthBook instance to feature changes\nexport function subscribe(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const subs = subscribedInstances.get(key) || new Set();\n  subs.add(instance);\n  subscribedInstances.set(key, subs);\n}\nexport function unsubscribe(instance: GrowthBook): void {\n  subscribedInstances.forEach((s) => s.delete(instance));\n}\n\nexport function onHidden() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    channel.state = \"idle\";\n    disableChannel(channel);\n  });\n}\n\nexport function onVisible() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    if (channel.state !== \"idle\") return;\n    enableChannel(channel);\n  });\n}\n\n// Private functions\n\nasync function updatePersistentCache() {\n  try {\n    if (!polyfills.localStorage) return;\n    await polyfills.localStorage.setItem(\n      cacheSettings.cacheKey,\n      JSON.stringify(Array.from(cache.entries()))\n    );\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n}\n\nasync function fetchFeaturesWithCache(\n  instance: GrowthBook,\n  allowStale?: boolean,\n  timeout?: number,\n  skipCache?: boolean\n): Promise<FeatureApiResponse | null> {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const now = new Date();\n\n  const minStaleAt = new Date(\n    now.getTime() - cacheSettings.maxAge + cacheSettings.staleTTL\n  );\n\n  await initializeCache();\n  const existing = cache.get(cacheKey);\n  if (\n    existing &&\n    !skipCache &&\n    (allowStale || existing.staleAt > now) &&\n    existing.staleAt > minStaleAt\n  ) {\n    // Restore from cache whether SSE is supported\n    if (existing.sse) supportsSSE.add(key);\n\n    // Reload features in the background if stale\n    if (existing.staleAt < now) {\n      fetchFeatures(instance);\n    }\n    // Otherwise, if we don't need to refresh now, start a background sync\n    else {\n      startAutoRefresh(instance);\n    }\n    return existing.data;\n  } else {\n    return await promiseTimeout(fetchFeatures(instance), timeout);\n  }\n}\n\nfunction getKey(instance: GrowthBook): string {\n  const [apiHost, clientKey] = instance.getApiInfo();\n  return `${apiHost}||${clientKey}`;\n}\n\nfunction getCacheKey(instance: GrowthBook): string {\n  const baseKey = getKey(instance);\n  if (!instance.isRemoteEval()) return baseKey;\n\n  const attributes = instance.getAttributes();\n  const cacheKeyAttributes =\n    instance.getCacheKeyAttributes() || Object.keys(instance.getAttributes());\n  const ca: Attributes = {};\n  cacheKeyAttributes.forEach((key) => {\n    ca[key] = attributes[key];\n  });\n\n  const fv = instance.getForcedVariations();\n  const url = instance.getUrl();\n\n  return `${baseKey}||${JSON.stringify({\n    ca,\n    fv,\n    url,\n  })}`;\n}\n\n// Guarantee the promise always resolves within {timeout} ms\n// Resolved value will be `null` when there's an error or it takes too long\n// Note: The promise will continue running in the background, even if the timeout is hit\nfunction promiseTimeout<T>(\n  promise: Promise<T>,\n  timeout?: number\n): Promise<T | null> {\n  return new Promise((resolve) => {\n    let resolved = false;\n    let timer: unknown;\n    const finish = (data?: T) => {\n      if (resolved) return;\n      resolved = true;\n      timer && clearTimeout(timer as NodeJS.Timer);\n      resolve(data || null);\n    };\n\n    if (timeout) {\n      timer = setTimeout(() => finish(), timeout);\n    }\n\n    promise.then((data) => finish(data)).catch(() => finish());\n  });\n}\n\n// Populate cache from localStorage (if available)\nasync function initializeCache(): Promise<void> {\n  if (cacheInitialized) return;\n  cacheInitialized = true;\n  try {\n    if (polyfills.localStorage) {\n      const value = await polyfills.localStorage.getItem(\n        cacheSettings.cacheKey\n      );\n      if (value) {\n        const parsed: [string, CacheEntry][] = JSON.parse(value);\n        if (parsed && Array.isArray(parsed)) {\n          parsed.forEach(([key, data]) => {\n            cache.set(key, {\n              ...data,\n              staleAt: new Date(data.staleAt),\n            });\n          });\n        }\n        cleanupCache();\n      }\n    }\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n  if (!cacheSettings.disableIdleStreams) {\n    const cleanupFn = helpers.startIdleListener();\n    if (cleanupFn) {\n      helpers.stopIdleListener = cleanupFn;\n    }\n  }\n}\n\n// Enforce the maxEntries limit\nfunction cleanupCache() {\n  const entriesWithTimestamps = Array.from(cache.entries())\n    .map(([key, value]) => ({\n      key,\n      staleAt: value.staleAt.getTime(),\n    }))\n    .sort((a, b) => a.staleAt - b.staleAt);\n\n  const entriesToRemoveCount = Math.min(\n    Math.max(0, cache.size - cacheSettings.maxEntries),\n    cache.size\n  );\n\n  for (let i = 0; i < entriesToRemoveCount; i++) {\n    cache.delete(entriesWithTimestamps[i].key);\n  }\n}\n\n// Called whenever new features are fetched from the API\nfunction onNewFeatureData(\n  key: string,\n  cacheKey: string,\n  data: FeatureApiResponse\n): void {\n  // If contents haven't changed, ignore the update, extend the stale TTL\n  const version = data.dateUpdated || \"\";\n  const staleAt = new Date(Date.now() + cacheSettings.staleTTL);\n  const existing = cache.get(cacheKey);\n  if (existing && version && existing.version === version) {\n    existing.staleAt = staleAt;\n    updatePersistentCache();\n    return;\n  }\n\n  // Update in-memory cache\n  cache.set(cacheKey, {\n    data,\n    version,\n    staleAt,\n    sse: supportsSSE.has(key),\n  });\n  cleanupCache();\n  // Update local storage (don't await this, just update asynchronously)\n  updatePersistentCache();\n\n  // Update features for all subscribed GrowthBook instances\n  const instances = subscribedInstances.get(key);\n  instances && instances.forEach((instance) => refreshInstance(instance, data));\n}\n\nasync function refreshInstance(\n  instance: GrowthBook,\n  data: FeatureApiResponse\n): Promise<void> {\n  data = await instance.decryptPayload(data, undefined, polyfills.SubtleCrypto);\n\n  await instance.refreshStickyBuckets(data);\n  instance.setExperiments(data.experiments || instance.getExperiments());\n  instance.setFeatures(data.features || instance.getFeatures());\n}\n\nasync function fetchFeatures(\n  instance: GrowthBook\n): Promise<FeatureApiResponse> {\n  const { apiHost, apiRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  const remoteEval = instance.isRemoteEval();\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n\n  let promise = activeFetches.get(cacheKey);\n  if (!promise) {\n    const fetcher: Promise<Response> = remoteEval\n      ? helpers.fetchRemoteEvalCall({\n          host: apiHost,\n          clientKey,\n          payload: {\n            attributes: instance.getAttributes(),\n            forcedVariations: instance.getForcedVariations(),\n            forcedFeatures: Array.from(instance.getForcedFeatures().entries()),\n            url: instance.getUrl(),\n          },\n          headers: apiRequestHeaders,\n        })\n      : helpers.fetchFeaturesCall({\n          host: apiHost,\n          clientKey,\n          headers: apiRequestHeaders,\n        });\n\n    // TODO: auto-retry if status code indicates a temporary error\n    promise = fetcher\n      .then((res) => {\n        if (res.headers.get(\"x-sse-support\") === \"enabled\") {\n          supportsSSE.add(key);\n        }\n        return res.json();\n      })\n      .then((data: FeatureApiResponse) => {\n        onNewFeatureData(key, cacheKey, data);\n        startAutoRefresh(instance);\n        activeFetches.delete(cacheKey);\n        return data;\n      })\n      .catch((e) => {\n        process.env.NODE_ENV !== \"production\" &&\n          instance.log(\"Error fetching features\", {\n            apiHost,\n            clientKey,\n            error: e ? e.message : null,\n          });\n        activeFetches.delete(cacheKey);\n        return Promise.resolve({});\n      });\n    activeFetches.set(cacheKey, promise);\n  }\n  return await promise;\n}\n\n// Watch a feature endpoint for changes\n// Will prefer SSE if enabled, otherwise fall back to cron\nfunction startAutoRefresh(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const { streamingHost, streamingHostRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  if (\n    cacheSettings.backgroundSync &&\n    supportsSSE.has(key) &&\n    polyfills.EventSource\n  ) {\n    if (streams.has(key)) return;\n    const channel: ScopedChannel = {\n      src: null,\n      host: streamingHost,\n      clientKey,\n      headers: streamingHostRequestHeaders,\n      cb: (event: MessageEvent<string>) => {\n        try {\n          if (event.type === \"features-updated\") {\n            const instances = subscribedInstances.get(key);\n            instances &&\n              instances.forEach((instance) => {\n                fetchFeatures(instance);\n              });\n          } else if (event.type === \"features\") {\n            const json: FeatureApiResponse = JSON.parse(event.data);\n            onNewFeatureData(key, cacheKey, json);\n          }\n          // Reset error count on success\n          channel.errors = 0;\n        } catch (e) {\n          process.env.NODE_ENV !== \"production\" &&\n            instance.log(\"SSE Error\", {\n              streamingHost,\n              clientKey,\n              error: e ? (e as Error).message : null,\n            });\n          onSSEError(channel);\n        }\n      },\n      errors: 0,\n      state: \"active\",\n    };\n    streams.set(key, channel);\n    enableChannel(channel);\n  }\n}\n\nfunction onSSEError(channel: ScopedChannel) {\n  if (channel.state === \"idle\") return;\n  channel.errors++;\n  if (channel.errors > 3 || (channel.src && channel.src.readyState === 2)) {\n    // exponential backoff after 4 errors, with jitter\n    const delay =\n      Math.pow(3, channel.errors - 3) * (1000 + Math.random() * 1000);\n    disableChannel(channel);\n    setTimeout(() => {\n      if ([\"idle\", \"active\"].includes(channel.state)) return;\n      enableChannel(channel);\n    }, Math.min(delay, 300000)); // 5 minutes max\n  }\n}\n\nfunction disableChannel(channel: ScopedChannel) {\n  if (!channel.src) return;\n  channel.src.onopen = null;\n  channel.src.onerror = null;\n  channel.src.close();\n  channel.src = null;\n  if (channel.state === \"active\") {\n    channel.state = \"disabled\";\n  }\n}\n\nfunction enableChannel(channel: ScopedChannel) {\n  channel.src = helpers.eventSourceCall({\n    host: channel.host,\n    clientKey: channel.clientKey,\n    headers: channel.headers,\n  }) as EventSource;\n  channel.state = \"active\";\n  channel.src.addEventListener(\"features\", channel.cb);\n  channel.src.addEventListener(\"features-updated\", channel.cb);\n  channel.src.onerror = () => onSSEError(channel);\n  channel.src.onopen = () => {\n    channel.errors = 0;\n  };\n}\n\nfunction destroyChannel(channel: ScopedChannel, key: string) {\n  disableChannel(channel);\n  streams.delete(key);\n}\n\nfunction clearAutoRefresh() {\n  // Clear list of which keys are auto-updated\n  supportsSSE.clear();\n\n  // Stop listening for any SSE events\n  streams.forEach(destroyChannel);\n\n  // Remove all references to GrowthBook instances\n  subscribedInstances.clear();\n\n  // Run the idle stream cleanup function\n  helpers.stopIdleListener();\n}\n", "var validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nvar nullController = {\n  revert: function revert() {}\n};\nvar elements = /*#__PURE__*/new Map();\nvar mutations = /*#__PURE__*/new Set();\n\nfunction getObserverInit(attr) {\n  return attr === 'html' ? {\n    childList: true,\n    subtree: true,\n    attributes: true,\n    characterData: true\n  } : {\n    childList: false,\n    subtree: false,\n    attributes: true,\n    attributeFilter: [attr]\n  };\n}\n\nfunction getElementRecord(element) {\n  var record = elements.get(element);\n\n  if (!record) {\n    record = {\n      element: element,\n      attributes: {}\n    };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(el, attr, getCurrentValue, setValue, mutationRunner) {\n  var currentValue = getCurrentValue(el);\n  var record = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el: el,\n    _positionTimeout: null,\n    observer: new MutationObserver(function () {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;else if (attr === 'position') record._positionTimeout = setTimeout(function () {\n        record._positionTimeout = null;\n      }, 1000);\n      var currentValue = getCurrentValue(el);\n      if (attr === 'position' && currentValue.parentNode === record.virtualValue.parentNode && currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode) return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner: mutationRunner,\n    setValue: setValue,\n    getCurrentValue: getCurrentValue\n  };\n\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n\n  return record;\n}\n\nfunction queueIfNeeded(val, record) {\n  var currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n\n  if (val && typeof val !== 'string') {\n    if (!currentVal || val.parentNode !== currentVal.parentNode || val.insertBeforeNode !== currentVal.insertBeforeNode) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(getTransformedHTML(val), record);\n}\n\nfunction classMutationRunner(record) {\n  var val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(function (m) {\n    return m.mutate(val);\n  });\n  queueIfNeeded(Array.from(val).filter(Boolean).join(' '), record);\n}\n\nfunction attrMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes(_ref) {\n  var parentSelector = _ref.parentSelector,\n      insertBeforeSelector = _ref.insertBeforeSelector;\n  var parentNode = document.querySelector(parentSelector);\n  if (!parentNode) return null;\n  var insertBeforeNode = insertBeforeSelector ? document.querySelector(insertBeforeSelector) : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode: parentNode,\n    insertBeforeNode: insertBeforeNode\n  };\n}\n\nfunction positionMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    var selectors = m.mutate();\n\n    var newNodes = _loadDOMNodes(selectors);\n\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nvar getHTMLValue = function getHTMLValue(el) {\n  return el.innerHTML;\n};\n\nvar setHTMLValue = function setHTMLValue(el, value) {\n  return el.innerHTML = value;\n};\n\nfunction getElementHTMLRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(element, 'html', getHTMLValue, setHTMLValue, htmlMutationRunner);\n  }\n\n  return elementRecord.html;\n}\n\nvar getElementPosition = function getElementPosition(el) {\n  return {\n    parentNode: el.parentElement,\n    insertBeforeNode: el.nextElementSibling\n  };\n};\n\nvar setElementPosition = function setElementPosition(el, value) {\n  if (value.insertBeforeNode && !value.parentNode.contains(value.insertBeforeNode)) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\n\nfunction getElementPositionRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(element, 'position', getElementPosition, setElementPosition, positionMutationRunner);\n  }\n\n  return elementRecord.position;\n}\n\nvar setClassValue = function setClassValue(el, val) {\n  return val ? el.className = val : el.removeAttribute('class');\n};\n\nvar getClassValue = function getClassValue(el) {\n  return el.className;\n};\n\nfunction getElementClassRecord(el) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(el, 'class', getClassValue, setClassValue, classMutationRunner);\n  }\n\n  return elementRecord.classes;\n}\n\nvar getAttrValue = function getAttrValue(attrName) {\n  return function (el) {\n    var _el$getAttribute;\n\n    return (_el$getAttribute = el.getAttribute(attrName)) != null ? _el$getAttribute : null;\n  };\n};\n\nvar setAttrValue = function setAttrValue(attrName) {\n  return function (el, val) {\n    return val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\n  };\n};\n\nfunction getElementAttributeRecord(el, attr) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(el, attr, getAttrValue(attr), setAttrValue(attr), attrMutationRunner);\n  }\n\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el, attr) {\n  var element = elements.get(el);\n  if (!element) return;\n\n  if (attr === 'html') {\n    var _element$html, _element$html$observe;\n\n    (_element$html = element.html) == null ? void 0 : (_element$html$observe = _element$html.observer) == null ? void 0 : _element$html$observe.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    var _element$classes, _element$classes$obse;\n\n    (_element$classes = element.classes) == null ? void 0 : (_element$classes$obse = _element$classes.observer) == null ? void 0 : _element$classes$obse.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    var _element$position, _element$position$obs;\n\n    (_element$position = element.position) == null ? void 0 : (_element$position$obs = _element$position.observer) == null ? void 0 : _element$position$obs.disconnect();\n    delete element.position;\n  } else {\n    var _element$attributes, _element$attributes$a, _element$attributes$a2;\n\n    (_element$attributes = element.attributes) == null ? void 0 : (_element$attributes$a = _element$attributes[attr]) == null ? void 0 : (_element$attributes$a2 = _element$attributes$a.observer) == null ? void 0 : _element$attributes$a2.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nvar transformContainer;\n\nfunction getTransformedHTML(html) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue(el, attr, m) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  var val = m.virtualValue;\n\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n\n  m.setValue(el, val);\n}\n\nfunction setValue(m, el) {\n  m.html && setPropertyValue(el, 'html', m.html);\n  m.classes && setPropertyValue(el, 'class', m.classes);\n  m.position && setPropertyValue(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(function (attr) {\n    setPropertyValue(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n} // find or create ElementPropertyRecord, add mutation to it, then run\n\n\nfunction startMutating(mutation, element) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n} // get (existing) ElementPropertyRecord, remove mutation from it, then run\n\n\nfunction stopMutating(mutation, el) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n\n  if (!record) return;\n  var index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n} // maintain list of elements associated with mutation\n\n\nfunction refreshElementsSet(mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n  var existingElements = new Set(mutation.elements);\n  var matchingElements = document.querySelectorAll(mutation.selector);\n  matchingElements.forEach(function (el) {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation) {\n  mutation.elements.forEach(function (el) {\n    return stopMutating(mutation, el);\n  });\n  mutation.elements.clear();\n  mutations[\"delete\"](mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n} // Observer for elements that don't exist in the DOM yet\n\n\nvar observer;\nfunction disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nfunction connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(function () {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false\n  });\n} // run on init\n\nconnectGlobalObserver();\n\nfunction newMutation(m) {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController; // add to global index of mutations\n\n  mutations.add(m); // run refresh on init to establish list of elements associated w/ mutation\n\n  refreshElementsSet(m);\n  return {\n    revert: function revert() {\n      revertMutation(m);\n    }\n  };\n}\n\nfunction html(selector, mutate) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction position(selector, mutate) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction classes(selector, mutate) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction attribute(selector, attribute, mutate) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, function (classnames) {\n      var mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames.split(/\\s+/g).filter(Boolean).forEach(function (c) {\n        return classnames.add(c);\n      });\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute: attribute,\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction declarative(_ref2) {\n  var selector = _ref2.selector,\n      action = _ref2.action,\n      value = _ref2.value,\n      attr = _ref2.attribute,\n      parentSelector = _ref2.parentSelector,\n      insertBeforeSelector = _ref2.insertBeforeSelector;\n\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, function (val) {\n        return val + (value != null ? value : '');\n      });\n    } else if (action === 'set') {\n      return html(selector, function () {\n        return value != null ? value : '';\n      });\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, function (val) {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, function (val) {\n        if (value) val[\"delete\"](value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, function (val) {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, function () {\n        return {\n          insertBeforeSelector: insertBeforeSelector,\n          parentSelector: parentSelector\n        };\n      });\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, function (val) {\n        return val !== null ? val + (value != null ? value : '') : value != null ? value : '';\n      });\n    } else if (action === 'set') {\n      return attribute(selector, attr, function () {\n        return value != null ? value : '';\n      });\n    } else if (action === 'remove') {\n      return attribute(selector, attr, function () {\n        return null;\n      });\n    }\n  }\n\n  return nullController;\n}\n\nvar index = {\n  html: html,\n  classes: classes,\n  attribute: attribute,\n  position: position,\n  declarative: declarative\n};\n\nexport default index;\nexport { connectGlobalObserver, disconnectGlobalObserver, validAttributeName };\n//# sourceMappingURL=dom-mutator.esm.js.map\n", "import { UrlTarget, UrlTargetType, VariationRange } from \"./types/growthbook\";\n\nfunction hashFnv32a(str: string): number {\n  let hval = 0x811c9dc5;\n  const l = str.length;\n\n  for (let i = 0; i < l; i++) {\n    hval ^= str.charCodeAt(i);\n    hval +=\n      (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);\n  }\n  return hval >>> 0;\n}\n\nexport function hash(\n  seed: string,\n  value: string,\n  version: number\n): number | null {\n  // New unbiased hashing algorithm\n  if (version === 2) {\n    return (hashFnv32a(hashFnv32a(seed + value) + \"\") % 10000) / 10000;\n  }\n  // Original biased hashing algorithm (keep for backwards compatibility)\n  if (version === 1) {\n    return (hashFnv32a(value + seed) % 1000) / 1000;\n  }\n\n  // Unknown hash version\n  return null;\n}\n\nexport function getEqualWeights(n: number): number[] {\n  if (n <= 0) return [];\n  return new Array(n).fill(1 / n);\n}\n\nexport function inRange(n: number, range: VariationRange): boolean {\n  return n >= range[0] && n < range[1];\n}\n\nexport function inNamespace(\n  hashValue: string,\n  namespace: [string, number, number]\n): boolean {\n  const n = hash(\"__\" + namespace[0], hashValue, 1);\n  if (n === null) return false;\n  return n >= namespace[1] && n < namespace[2];\n}\n\nexport function chooseVariation(n: number, ranges: VariationRange[]): number {\n  for (let i = 0; i < ranges.length; i++) {\n    if (inRange(n, ranges[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nexport function getUrlRegExp(regexString: string): RegExp | undefined {\n  try {\n    const escaped = regexString.replace(/([^\\\\])\\//g, \"$1\\\\/\");\n    return new RegExp(escaped);\n  } catch (e) {\n    console.error(e);\n    return undefined;\n  }\n}\n\nexport function isURLTargeted(url: string, targets: UrlTarget[]) {\n  if (!targets.length) return false;\n  let hasIncludeRules = false;\n  let isIncluded = false;\n\n  for (let i = 0; i < targets.length; i++) {\n    const match = _evalURLTarget(url, targets[i].type, targets[i].pattern);\n    if (targets[i].include === false) {\n      if (match) return false;\n    } else {\n      hasIncludeRules = true;\n      if (match) isIncluded = true;\n    }\n  }\n\n  return isIncluded || !hasIncludeRules;\n}\n\nfunction _evalSimpleUrlPart(\n  actual: string,\n  pattern: string,\n  isPath: boolean\n): boolean {\n  try {\n    // Escape special regex characters and change wildcard `_____` to `.*`\n    let escaped = pattern\n      .replace(/[*.+?^${}()|[\\]\\\\]/g, \"\\\\$&\")\n      .replace(/_____/g, \".*\");\n\n    if (isPath) {\n      // When matching pathname, make leading/trailing slashes optional\n      escaped = \"\\\\/?\" + escaped.replace(/(^\\/|\\/$)/g, \"\") + \"\\\\/?\";\n    }\n\n    const regex = new RegExp(\"^\" + escaped + \"$\", \"i\");\n    return regex.test(actual);\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalSimpleUrlTarget(actual: URL, pattern: string) {\n  try {\n    // If a protocol is missing, but a host is specified, add `https://` to the front\n    // Use \"_____\" as the wildcard since `*` is not a valid hostname in some browsers\n    const expected = new URL(\n      pattern.replace(/^([^:/?]*)\\./i, \"https://$1.\").replace(/\\*/g, \"_____\"),\n      \"https://_____\"\n    );\n\n    // Compare each part of the URL separately\n    const comps: Array<[string, string, boolean]> = [\n      [actual.host, expected.host, false],\n      [actual.pathname, expected.pathname, true],\n    ];\n    // We only want to compare hashes if it's explicitly being targeted\n    if (expected.hash) {\n      comps.push([actual.hash, expected.hash, false]);\n    }\n\n    expected.searchParams.forEach((v, k) => {\n      comps.push([actual.searchParams.get(k) || \"\", v, false]);\n    });\n\n    // If any comparisons fail, the whole thing fails\n    return !comps.some(\n      (data) => !_evalSimpleUrlPart(data[0], data[1], data[2])\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalURLTarget(\n  url: string,\n  type: UrlTargetType,\n  pattern: string\n): boolean {\n  try {\n    const parsed = new URL(url, \"https://_\");\n\n    if (type === \"regex\") {\n      const regex = getUrlRegExp(pattern);\n      if (!regex) return false;\n      return (\n        regex.test(parsed.href) ||\n        regex.test(parsed.href.substring(parsed.origin.length))\n      );\n    } else if (type === \"simple\") {\n      return _evalSimpleUrlTarget(parsed, pattern);\n    }\n\n    return false;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function getBucketRanges(\n  numVariations: number,\n  coverage: number | undefined,\n  weights?: number[]\n): VariationRange[] {\n  coverage = coverage === undefined ? 1 : coverage;\n\n  // Make sure coverage is within bounds\n  if (coverage < 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be greater than or equal to 0\");\n    }\n    coverage = 0;\n  } else if (coverage > 1) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be less than or equal to 1\");\n    }\n    coverage = 1;\n  }\n\n  // Default to equal weights if missing or invalid\n  const equal = getEqualWeights(numVariations);\n  weights = weights || equal;\n  if (weights.length !== numVariations) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\n        \"Experiment.weights array must be the same length as Experiment.variations\"\n      );\n    }\n    weights = equal;\n  }\n\n  // If weights don't add up to 1 (or close to it), default to equal weights\n  const totalWeight = weights.reduce((w, sum) => sum + w, 0);\n  if (totalWeight < 0.99 || totalWeight > 1.01) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.weights must add up to 1\");\n    }\n    weights = equal;\n  }\n\n  // Covert weights to ranges\n  let cumulative = 0;\n  return weights.map((w) => {\n    const start = cumulative;\n    cumulative += w;\n    return [start, start + (coverage as number) * w];\n  }) as VariationRange[];\n}\n\nexport function getQueryStringOverride(\n  id: string,\n  url: string,\n  numVariations: number\n) {\n  if (!url) {\n    return null;\n  }\n\n  const search = url.split(\"?\")[1];\n  if (!search) {\n    return null;\n  }\n\n  const match = search\n    .replace(/#.*/, \"\") // Get rid of anchor\n    .split(\"&\") // Split into key/value pairs\n    .map((kv) => kv.split(\"=\", 2))\n    .filter(([k]) => k === id) // Look for key that matches the experiment id\n    .map(([, v]) => parseInt(v)); // Parse the value into an integer\n\n  if (match.length > 0 && match[0] >= 0 && match[0] < numVariations)\n    return match[0];\n\n  return null;\n}\n\nexport function isIncluded(include: () => boolean) {\n  try {\n    return include();\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\n\nconst base64ToBuf = (b: string) =>\n  Uint8Array.from(atob(b), (c) => c.charCodeAt(0));\n\nexport async function decrypt(\n  encryptedString: string,\n  decryptionKey?: string,\n  subtle?: SubtleCrypto\n): Promise<string> {\n  decryptionKey = decryptionKey || \"\";\n  subtle = subtle || (globalThis.crypto && globalThis.crypto.subtle);\n  if (!subtle) {\n    throw new Error(\"No SubtleCrypto implementation found\");\n  }\n  try {\n    const key = await subtle.importKey(\n      \"raw\",\n      base64ToBuf(decryptionKey),\n      { name: \"AES-CBC\", length: 128 },\n      true,\n      [\"encrypt\", \"decrypt\"]\n    );\n    const [iv, cipherText] = encryptedString.split(\".\");\n    const plainTextBuffer = await subtle.decrypt(\n      { name: \"AES-CBC\", iv: base64ToBuf(iv) },\n      key,\n      base64ToBuf(cipherText)\n    );\n\n    return new TextDecoder().decode(plainTextBuffer);\n  } catch (e) {\n    throw new Error(\"Failed to decrypt\");\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function toString(input: any): string {\n  if (typeof input === \"string\") return input;\n  return JSON.stringify(input);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function paddedVersionString(input: any): string {\n  if (typeof input === \"number\") {\n    input = input + \"\";\n  }\n  if (!input || typeof input !== \"string\") {\n    input = \"0\";\n  }\n  // Remove build info and leading `v` if any\n  // Split version into parts (both core version numbers and pre-release tags)\n  // \"v1.2.3-rc.1+build123\" -> [\"1\",\"2\",\"3\",\"rc\",\"1\"]\n  const parts = (input as string).replace(/(^v|\\+.*$)/g, \"\").split(/[-.]/);\n\n  // If it's SemVer without a pre-release, add `~` to the end\n  // [\"1\",\"0\",\"0\"] -> [\"1\",\"0\",\"0\",\"~\"]\n  // \"~\" is the largest ASCII character, so this will make \"1.0.0\" greater than \"1.0.0-beta\" for example\n  if (parts.length === 3) {\n    parts.push(\"~\");\n  }\n\n  // Left pad each numeric part with spaces so string comparisons will work (\"9\">\"10\", but \" 9\"<\"10\")\n  // Then, join back together into a single string\n  return parts\n    .map((v) => (v.match(/^[0-9]+$/) ? v.padStart(5, \" \") : v))\n    .join(\"-\");\n}\n\nexport function loadSDKVersion(): string {\n  let version: string;\n  try {\n    // @ts-expect-error right-hand value to be replaced by build with string literal\n    version = __SDK_VERSION__;\n  } catch (e) {\n    version = \"\";\n  }\n  return version;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport {\n  ConditionInterface,\n  TestedObj,\n  ConditionValue,\n  Operator,\n  OperatorConditionValue,\n  VarType,\n} from \"./types/mongrule\";\nimport { paddedVersionString } from \"./util\";\n\nconst _regexCache: { [key: string]: RegExp } = {};\n\n// The top-level condition evaluation function\nexport function evalCondition(\n  obj: TestedObj,\n  condition: ConditionInterface\n): boolean {\n  // Recursive condition\n  if (\"$or\" in condition) {\n    return evalOr(obj, condition[\"$or\"] as ConditionInterface[]);\n  }\n  if (\"$nor\" in condition) {\n    return !evalOr(obj, condition[\"$nor\"] as ConditionInterface[]);\n  }\n  if (\"$and\" in condition) {\n    return evalAnd(obj, condition[\"$and\"] as ConditionInterface[]);\n  }\n  if (\"$not\" in condition) {\n    return !evalCondition(obj, condition[\"$not\"] as ConditionInterface);\n  }\n\n  // Condition is an object, keys are object paths, values are the condition for that path\n  for (const [k, v] of Object.entries(condition)) {\n    if (!evalConditionValue(v, getPath(obj, k))) return false;\n  }\n  return true;\n}\n\n// Return value at dot-separated path of an object\nfunction getPath(obj: TestedObj, path: string) {\n  const parts = path.split(\".\");\n  let current: any = obj;\n  for (let i = 0; i < parts.length; i++) {\n    if (current && typeof current === \"object\" && parts[i] in current) {\n      current = current[parts[i]];\n    } else {\n      return null;\n    }\n  }\n  return current;\n}\n\n// Transform a regex string into a real RegExp object\nfunction getRegex(regex: string): RegExp {\n  if (!_regexCache[regex]) {\n    _regexCache[regex] = new RegExp(regex.replace(/([^\\\\])\\//g, \"$1\\\\/\"));\n  }\n  return _regexCache[regex];\n}\n\n// Evaluate a single value against a condition\nfunction evalConditionValue(condition: ConditionValue, value: any) {\n  // Simple equality comparisons\n  if (typeof condition === \"string\") {\n    return value + \"\" === condition;\n  }\n  if (typeof condition === \"number\") {\n    return value * 1 === condition;\n  }\n  if (typeof condition === \"boolean\") {\n    return !!value === condition;\n  }\n\n  if (condition === null) {\n    return value === null;\n  }\n\n  if (Array.isArray(condition) || !isOperatorObject(condition)) {\n    return JSON.stringify(value) === JSON.stringify(condition);\n  }\n\n  // This is a special operator condition and we should evaluate each one separately\n  for (const op in condition) {\n    if (\n      !evalOperatorCondition(\n        op as Operator,\n        value,\n        condition[op as keyof OperatorConditionValue]\n      )\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// If the object has only keys that start with '$'\nfunction isOperatorObject(obj: any): boolean {\n  const keys = Object.keys(obj);\n  return (\n    keys.length > 0 && keys.filter((k) => k[0] === \"$\").length === keys.length\n  );\n}\n\n// Return the data type of a value\nfunction getType(v: any): VarType | \"unknown\" {\n  if (v === null) return \"null\";\n  if (Array.isArray(v)) return \"array\";\n  const t = typeof v;\n  if ([\"string\", \"number\", \"boolean\", \"object\", \"undefined\"].includes(t)) {\n    return t as VarType;\n  }\n  return \"unknown\";\n}\n\n// At least one element of actual must match the expected condition/value\nfunction elemMatch(actual: any, expected: any) {\n  if (!Array.isArray(actual)) return false;\n  const check = isOperatorObject(expected)\n    ? (v: any) => evalConditionValue(expected, v)\n    : (v: any) => evalCondition(v, expected);\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i] && check(actual[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isIn(actual: any, expected: Array<any>): boolean {\n  // Do an intersection is attribute is an array\n  if (Array.isArray(actual)) {\n    return actual.some((el) => expected.includes(el));\n  }\n  return expected.includes(actual);\n}\n\n// Evaluate a single operator condition\nfunction evalOperatorCondition(\n  operator: Operator,\n  actual: any,\n  expected: any\n): boolean {\n  switch (operator) {\n    case \"$veq\":\n      return paddedVersionString(actual) === paddedVersionString(expected);\n    case \"$vne\":\n      return paddedVersionString(actual) !== paddedVersionString(expected);\n    case \"$vgt\":\n      return paddedVersionString(actual) > paddedVersionString(expected);\n    case \"$vgte\":\n      return paddedVersionString(actual) >= paddedVersionString(expected);\n    case \"$vlt\":\n      return paddedVersionString(actual) < paddedVersionString(expected);\n    case \"$vlte\":\n      return paddedVersionString(actual) <= paddedVersionString(expected);\n    case \"$eq\":\n      return actual === expected;\n    case \"$ne\":\n      return actual !== expected;\n    case \"$lt\":\n      return actual < expected;\n    case \"$lte\":\n      return actual <= expected;\n    case \"$gt\":\n      return actual > expected;\n    case \"$gte\":\n      return actual >= expected;\n    case \"$exists\":\n      // Using `!=` and `==` instead of strict checks so it also matches for undefined\n      return expected ? actual != null : actual == null;\n    case \"$in\":\n      if (!Array.isArray(expected)) return false;\n      return isIn(actual, expected);\n    case \"$nin\":\n      if (!Array.isArray(expected)) return false;\n      return !isIn(actual, expected);\n    case \"$not\":\n      return !evalConditionValue(expected, actual);\n    case \"$size\":\n      if (!Array.isArray(actual)) return false;\n      return evalConditionValue(expected, actual.length);\n    case \"$elemMatch\":\n      return elemMatch(actual, expected);\n    case \"$all\":\n      if (!Array.isArray(actual)) return false;\n      for (let i = 0; i < expected.length; i++) {\n        let passed = false;\n        for (let j = 0; j < actual.length; j++) {\n          if (evalConditionValue(expected[i], actual[j])) {\n            passed = true;\n            break;\n          }\n        }\n        if (!passed) return false;\n      }\n      return true;\n    case \"$regex\":\n      try {\n        return getRegex(expected).test(actual);\n      } catch (e) {\n        return false;\n      }\n    case \"$type\":\n      return getType(actual) === expected;\n    default:\n      console.error(\"Unknown operator: \" + operator);\n      return false;\n  }\n}\n\n// Recursive $or rule\nfunction evalOr(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  if (!conditions.length) return true;\n  for (let i = 0; i < conditions.length; i++) {\n    if (evalCondition(obj, conditions[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// Recursive $and rule\nfunction evalAnd(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  for (let i = 0; i < conditions.length; i++) {\n    if (!evalCondition(obj, conditions[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n", "import mutate, { DeclarativeMutation } from \"dom-mutator\";\nimport type {\n  ApiHost,\n  Attributes,\n  AutoExperiment,\n  AutoExperimentVariation,\n  ClientKey,\n  Context,\n  Experiment,\n  FeatureApiResponse,\n  FeatureDefinition,\n  FeatureResult,\n  FeatureResultSource,\n  Filter,\n  LoadFeaturesOptions,\n  RealtimeUsageData,\n  RefreshFeaturesOptions,\n  Result,\n  StickyAssignments,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n  StickyExperimentKey,\n  SubscriptionFunction,\n  VariationMeta,\n  VariationRange,\n  WidenPrimitives,\n} from \"./types/growthbook\";\nimport type { ConditionInterface } from \"./types/mongrule\";\nimport {\n  chooseVariation,\n  decrypt,\n  getBucketRanges,\n  getQueryStringOverride,\n  getUrlRegExp,\n  hash,\n  inNamespace,\n  inRange,\n  isIncluded,\n  isURLTargeted,\n  loadSDKVersion,\n  toString,\n} from \"./util\";\nimport { evalCondition } from \"./mongrule\";\nimport { refreshFeatures, subscribe, unsubscribe } from \"./feature-repository\";\n\nconst isBrowser =\n  typeof window !== \"undefined\" && typeof document !== \"undefined\";\n\nconst SDK_VERSION = loadSDKVersion();\n\nexport class GrowthBook<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  AppFeatures extends Record<string, any> = Record<string, any>\n> {\n  // context is technically private, but some tools depend on it so we can't mangle the name\n  // _ctx below is a clone of this property that we use internally\n  private context: Context;\n  public debug: boolean;\n  public ready: boolean;\n  public version: string;\n\n  // Properties and methods that start with \"_\" are mangled by Terser (saves ~150 bytes)\n  private _ctx: Context;\n  private _renderer: null | (() => void);\n  private _trackedExperiments: Set<unknown>;\n  private _trackedFeatures: Record<string, string>;\n  private _subscriptions: Set<SubscriptionFunction>;\n  private _rtQueue: RealtimeUsageData[];\n  private _rtTimer: number;\n  private _assigned: Map<\n    string,\n    {\n      // eslint-disable-next-line\n      experiment: Experiment<any>;\n      // eslint-disable-next-line\n      result: Result<any>;\n    }\n  >;\n  // eslint-disable-next-line\n  private _forcedFeatureValues: Map<string, any>;\n  private _attributeOverrides: Attributes;\n  private _activeAutoExperiments: Map<\n    AutoExperiment,\n    { valueHash: string; undo: () => void }\n  >;\n  private _triggeredExpKeys: Set<string>;\n  private _loadFeaturesCalled: boolean;\n\n  constructor(context?: Context) {\n    context = context || {};\n    // These properties are all initialized in the constructor instead of above\n    // This saves ~80 bytes in the final output\n    this.version = SDK_VERSION;\n    this._ctx = this.context = context;\n    this._renderer = null;\n    this._trackedExperiments = new Set();\n    this._trackedFeatures = {};\n    this.debug = false;\n    this._subscriptions = new Set();\n    this._rtQueue = [];\n    this._rtTimer = 0;\n    this.ready = false;\n    this._assigned = new Map();\n    this._forcedFeatureValues = new Map();\n    this._attributeOverrides = {};\n    this._activeAutoExperiments = new Map();\n    this._triggeredExpKeys = new Set();\n    this._loadFeaturesCalled = false;\n\n    if (context.remoteEval) {\n      if (context.decryptionKey) {\n        throw new Error(\"Encryption is not available for remoteEval\");\n      }\n      if (!context.clientKey) {\n        throw new Error(\"Missing clientKey\");\n      }\n      let isGbHost = false;\n      try {\n        isGbHost = !!new URL(context.apiHost || \"\").hostname.match(\n          /growthbook\\.io$/i\n        );\n      } catch (e) {\n        // ignore invalid URLs\n      }\n      if (isGbHost) {\n        throw new Error(\"Cannot use remoteEval on GrowthBook Cloud\");\n      }\n    } else {\n      if (context.cacheKeyAttributes) {\n        throw new Error(\"cacheKeyAttributes are only used for remoteEval\");\n      }\n    }\n\n    if (context.features) {\n      this.ready = true;\n    }\n\n    if (isBrowser && context.enableDevMode) {\n      window._growthbook = this;\n      document.dispatchEvent(new Event(\"gbloaded\"));\n    }\n\n    if (context.experiments) {\n      this.ready = true;\n      this._updateAllAutoExperiments();\n    }\n\n    if (context.clientKey && !context.remoteEval) {\n      this._refresh({}, true, false);\n    }\n  }\n\n  public async loadFeatures(options?: LoadFeaturesOptions): Promise<void> {\n    if (options && options.autoRefresh) {\n      // interpret deprecated autoRefresh option as subscribeToChanges\n      this._ctx.subscribeToChanges = true;\n    }\n    this._loadFeaturesCalled = true;\n\n    await this._refresh(options, true, true);\n\n    if (this._canSubscribe()) {\n      subscribe(this);\n    }\n  }\n\n  public async refreshFeatures(\n    options?: RefreshFeaturesOptions\n  ): Promise<void> {\n    await this._refresh(options, false, true);\n  }\n\n  public getApiInfo(): [ApiHost, ClientKey] {\n    return [this.getApiHosts().apiHost, this.getClientKey()];\n  }\n  public getApiHosts(): {\n    apiHost: string;\n    streamingHost: string;\n    apiRequestHeaders?: Record<string, string>;\n    streamingHostRequestHeaders?: Record<string, string>;\n  } {\n    const defaultHost = this._ctx.apiHost || \"https://cdn.growthbook.io\";\n    return {\n      apiHost: defaultHost.replace(/\\/*$/, \"\"),\n      streamingHost: (this._ctx.streamingHost || defaultHost).replace(\n        /\\/*$/,\n        \"\"\n      ),\n      apiRequestHeaders: this._ctx.apiHostRequestHeaders,\n      streamingHostRequestHeaders: this._ctx.streamingHostRequestHeaders,\n    };\n  }\n  public getClientKey(): string {\n    return this._ctx.clientKey || \"\";\n  }\n\n  public isRemoteEval(): boolean {\n    return this._ctx.remoteEval || false;\n  }\n\n  public getCacheKeyAttributes(): (keyof Attributes)[] | undefined {\n    return this._ctx.cacheKeyAttributes;\n  }\n\n  private async _refresh(\n    options?: RefreshFeaturesOptions,\n    allowStale?: boolean,\n    updateInstance?: boolean\n  ) {\n    options = options || {};\n    if (!this._ctx.clientKey) {\n      throw new Error(\"Missing clientKey\");\n    }\n    await refreshFeatures(\n      this,\n      options.timeout,\n      options.skipCache || this._ctx.enableDevMode,\n      allowStale,\n      updateInstance,\n      this._ctx.backgroundSync !== false\n    );\n  }\n\n  private _render() {\n    if (this._renderer) {\n      this._renderer();\n    }\n  }\n\n  public setFeatures(features: Record<string, FeatureDefinition>) {\n    this._ctx.features = features;\n    this.ready = true;\n    this._render();\n  }\n\n  public async setEncryptedFeatures(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const featuresJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setFeatures(\n      JSON.parse(featuresJSON) as Record<string, FeatureDefinition>\n    );\n  }\n\n  public setExperiments(experiments: AutoExperiment[]): void {\n    this._ctx.experiments = experiments;\n    this.ready = true;\n    this._updateAllAutoExperiments();\n  }\n\n  public async setEncryptedExperiments(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const experimentsJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setExperiments(JSON.parse(experimentsJSON) as AutoExperiment[]);\n  }\n\n  public async decryptPayload(\n    data: FeatureApiResponse,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<FeatureApiResponse> {\n    if (data.encryptedFeatures) {\n      data.features = JSON.parse(\n        await decrypt(\n          data.encryptedFeatures,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedFeatures;\n    }\n    if (data.encryptedExperiments) {\n      data.experiments = JSON.parse(\n        await decrypt(\n          data.encryptedExperiments,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedExperiments;\n    }\n    return data;\n  }\n\n  public async setAttributes(attributes: Attributes) {\n    this._ctx.attributes = attributes;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setAttributeOverrides(overrides: Attributes) {\n    this._attributeOverrides = overrides;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setForcedVariations(vars: Record<string, number>) {\n    this._ctx.forcedVariations = vars || {};\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  // eslint-disable-next-line\n  public setForcedFeatures(map: Map<string, any>) {\n    this._forcedFeatureValues = map;\n    this._render();\n  }\n\n  public async setURL(url: string) {\n    this._ctx.url = url;\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      this._updateAllAutoExperiments(true);\n      return;\n    }\n    this._updateAllAutoExperiments(true);\n  }\n\n  public getAttributes() {\n    return { ...this._ctx.attributes, ...this._attributeOverrides };\n  }\n\n  public getForcedVariations() {\n    return this._ctx.forcedVariations || {};\n  }\n\n  public getForcedFeatures() {\n    // eslint-disable-next-line\n    return this._forcedFeatureValues || new Map<string, any>();\n  }\n\n  public getStickyBucketAssignmentDocs() {\n    return this._ctx.stickyBucketAssignmentDocs || {};\n  }\n\n  public getUrl() {\n    return this._ctx.url || \"\";\n  }\n\n  public getFeatures() {\n    return this._ctx.features || {};\n  }\n\n  public getExperiments() {\n    return this._ctx.experiments || [];\n  }\n\n  public subscribe(cb: SubscriptionFunction): () => void {\n    this._subscriptions.add(cb);\n    return () => {\n      this._subscriptions.delete(cb);\n    };\n  }\n\n  private _canSubscribe() {\n    return this._ctx.backgroundSync !== false && this._ctx.subscribeToChanges;\n  }\n\n  private async _refreshForRemoteEval() {\n    if (!this._ctx.remoteEval) return;\n    if (!this._loadFeaturesCalled) return;\n    await this._refresh({}, false, true).catch(() => {\n      // Ignore errors\n    });\n  }\n\n  public getAllResults() {\n    return new Map(this._assigned);\n  }\n\n  public destroy() {\n    // Release references to save memory\n    this._subscriptions.clear();\n    this._assigned.clear();\n    this._trackedExperiments.clear();\n    this._trackedFeatures = {};\n    this._rtQueue = [];\n    if (this._rtTimer) {\n      clearTimeout(this._rtTimer);\n    }\n    unsubscribe(this);\n\n    if (isBrowser && window._growthbook === this) {\n      delete window._growthbook;\n    }\n\n    // Undo any active auto experiments\n    this._activeAutoExperiments.forEach((exp) => {\n      exp.undo();\n    });\n    this._activeAutoExperiments.clear();\n    this._triggeredExpKeys.clear();\n  }\n\n  public setRenderer(renderer: () => void) {\n    this._renderer = renderer;\n  }\n\n  public forceVariation(key: string, variation: number) {\n    this._ctx.forcedVariations = this._ctx.forcedVariations || {};\n    this._ctx.forcedVariations[key] = variation;\n    if (this._ctx.remoteEval) {\n      this._refreshForRemoteEval();\n      return;\n    }\n    this._updateAllAutoExperiments();\n    this._render();\n  }\n\n  public run<T>(experiment: Experiment<T>): Result<T> {\n    const result = this._run(experiment, null);\n    this._fireSubscriptions(experiment, result);\n    return result;\n  }\n\n  public triggerExperiment(key: string) {\n    this._triggeredExpKeys.add(key);\n    if (!this._ctx.experiments) return null;\n    const experiments = this._ctx.experiments.filter((exp) => exp.key === key);\n    return experiments\n      .map((exp) => {\n        if (!exp.manual) return null;\n        return this._runAutoExperiment(exp);\n      })\n      .filter((res) => res !== null);\n  }\n\n  private _runAutoExperiment(experiment: AutoExperiment, forceRerun?: boolean) {\n    const existing = this._activeAutoExperiments.get(experiment);\n\n    // If this is a manual experiment and it's not already running, skip\n    if (\n      experiment.manual &&\n      !this._triggeredExpKeys.has(experiment.key) &&\n      !existing\n    )\n      return null;\n\n    // Run the experiment\n    const result = this.run(experiment);\n\n    // A hash to quickly tell if the assigned value changed\n    const valueHash = JSON.stringify(result.value);\n\n    // If the changes are already active, no need to re-apply them\n    if (\n      !forceRerun &&\n      result.inExperiment &&\n      existing &&\n      existing.valueHash === valueHash\n    ) {\n      return result;\n    }\n\n    // Undo any existing changes\n    if (existing) this._undoActiveAutoExperiment(experiment);\n\n    // Apply new changes\n    if (result.inExperiment) {\n      const undo = this._applyDOMChanges(result.value);\n      if (undo) {\n        this._activeAutoExperiments.set(experiment, {\n          undo,\n          valueHash,\n        });\n      }\n    }\n\n    return result;\n  }\n\n  private _undoActiveAutoExperiment(exp: AutoExperiment) {\n    const data = this._activeAutoExperiments.get(exp);\n    if (data) {\n      data.undo();\n      this._activeAutoExperiments.delete(exp);\n    }\n  }\n\n  private _updateAllAutoExperiments(forceRerun?: boolean) {\n    const experiments = this._ctx.experiments || [];\n\n    // Stop any experiments that are no longer defined\n    const keys = new Set(experiments);\n    this._activeAutoExperiments.forEach((v, k) => {\n      if (!keys.has(k)) {\n        v.undo();\n        this._activeAutoExperiments.delete(k);\n      }\n    });\n\n    // Re-run all new/updated experiments\n    experiments.forEach((exp) => {\n      this._runAutoExperiment(exp, forceRerun);\n    });\n  }\n\n  private _fireSubscriptions<T>(experiment: Experiment<T>, result: Result<T>) {\n    const key = experiment.key;\n\n    // If assigned variation has changed, fire subscriptions\n    const prev = this._assigned.get(key);\n    // TODO: what if the experiment definition has changed?\n    if (\n      !prev ||\n      prev.result.inExperiment !== result.inExperiment ||\n      prev.result.variationId !== result.variationId\n    ) {\n      this._assigned.set(key, { experiment, result });\n      this._subscriptions.forEach((cb) => {\n        try {\n          cb(experiment, result);\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }\n\n  private _trackFeatureUsage(key: string, res: FeatureResult): void {\n    // Don't track feature usage that was forced via an override\n    if (res.source === \"override\") return;\n\n    // Only track a feature once, unless the assigned value changed\n    const stringifiedValue = JSON.stringify(res.value);\n    if (this._trackedFeatures[key] === stringifiedValue) return;\n    this._trackedFeatures[key] = stringifiedValue;\n\n    // Fire user-supplied callback\n    if (this._ctx.onFeatureUsage) {\n      try {\n        this._ctx.onFeatureUsage(key, res);\n      } catch (e) {\n        // Ignore feature usage callback errors\n      }\n    }\n\n    // In browser environments, queue up feature usage to be tracked in batches\n    if (!isBrowser || !window.fetch) return;\n    this._rtQueue.push({\n      key,\n      on: res.on,\n    });\n    if (!this._rtTimer) {\n      this._rtTimer = window.setTimeout(() => {\n        // Reset the queue\n        this._rtTimer = 0;\n        const q = [...this._rtQueue];\n        this._rtQueue = [];\n\n        // Skip logging if a real-time usage key is not configured\n        if (!this._ctx.realtimeKey) return;\n\n        window\n          .fetch(\n            `https://rt.growthbook.io/?key=${\n              this._ctx.realtimeKey\n            }&events=${encodeURIComponent(JSON.stringify(q))}`,\n\n            {\n              cache: \"no-cache\",\n              mode: \"no-cors\",\n            }\n          )\n          .catch(() => {\n            // TODO: retry in case of network errors?\n          });\n      }, this._ctx.realtimeInterval || 2000);\n    }\n  }\n\n  private _getFeatureResult<T>(\n    key: string,\n    value: T,\n    source: FeatureResultSource,\n    ruleId?: string,\n    experiment?: Experiment<T>,\n    result?: Result<T>\n  ): FeatureResult<T> {\n    const ret: FeatureResult = {\n      value,\n      on: !!value,\n      off: !value,\n      source,\n      ruleId: ruleId || \"\",\n    };\n    if (experiment) ret.experiment = experiment;\n    if (result) ret.experimentResult = result;\n\n    // Track the usage of this feature in real-time\n    this._trackFeatureUsage(key, ret);\n\n    return ret;\n  }\n\n  public isOn<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).on;\n  }\n\n  public isOff<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).off;\n  }\n\n  public getFeatureValue<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(key: K, defaultValue: V): WidenPrimitives<V> {\n    const value = this.evalFeature<WidenPrimitives<V>, K>(key).value;\n    return value === null ? (defaultValue as WidenPrimitives<V>) : value;\n  }\n\n  /**\n   * @deprecated Use {@link evalFeature}\n   * @param id\n   */\n  // eslint-disable-next-line\n  public feature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    return this.evalFeature(id);\n  }\n\n  public evalFeature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    // Global override\n    if (this._forcedFeatureValues.has(id)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Global override\", {\n          id,\n          value: this._forcedFeatureValues.get(id),\n        });\n      return this._getFeatureResult(\n        id,\n        this._forcedFeatureValues.get(id),\n        \"override\"\n      );\n    }\n\n    // Unknown feature id\n    if (!this._ctx.features || !this._ctx.features[id]) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Unknown feature\", { id });\n      return this._getFeatureResult(id, null, \"unknownFeature\");\n    }\n\n    // Get the feature\n    const feature: FeatureDefinition<V> = this._ctx.features[id];\n\n    // Loop through the rules\n    if (feature.rules) {\n      for (const rule of feature.rules) {\n        // If there are filters for who is included (e.g. namespaces)\n        if (rule.filters && this._isFilteredOut(rule.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip rule because of filters\", {\n              id,\n              rule,\n            });\n          continue;\n        }\n\n        // Feature value is being forced\n        if (\"force\" in rule) {\n          // If it's a conditional rule, skip if the condition doesn't pass\n          if (rule.condition && !this._conditionPasses(rule.condition)) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because of condition ff\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          // If this is a percentage rollout, skip if not included\n          if (\n            !this._isIncludedInRollout(\n              rule.seed || id,\n              rule.hashAttribute,\n              this._ctx.stickyBucketService && !rule.disableStickyBucketing\n                ? rule.fallbackAttribute\n                : undefined,\n              rule.range,\n              rule.coverage,\n              rule.hashVersion\n            )\n          ) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because user not included in rollout\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Force value from rule\", {\n              id,\n              rule,\n            });\n\n          // If this was a remotely evaluated experiment, fire the tracking callbacks\n          if (rule.tracks) {\n            rule.tracks.forEach((t) => {\n              this._track(t.experiment, t.result);\n            });\n          }\n\n          return this._getFeatureResult(id, rule.force as V, \"force\", rule.id);\n        }\n        if (!rule.variations) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip invalid rule\", {\n              id,\n              rule,\n            });\n\n          continue;\n        }\n\n        // For experiment rules, run an experiment\n        const exp: Experiment<V> = {\n          variations: rule.variations as [V, V, ...V[]],\n          key: rule.key || id,\n        };\n        if (\"coverage\" in rule) exp.coverage = rule.coverage;\n        if (rule.weights) exp.weights = rule.weights;\n        if (rule.hashAttribute) exp.hashAttribute = rule.hashAttribute;\n        if (rule.fallbackAttribute)\n          exp.fallbackAttribute = rule.fallbackAttribute;\n        if (rule.disableStickyBucketing)\n          exp.disableStickyBucketing = rule.disableStickyBucketing;\n        if (rule.bucketVersion !== undefined)\n          exp.bucketVersion = rule.bucketVersion;\n        if (rule.minBucketVersion !== undefined)\n          exp.minBucketVersion = rule.minBucketVersion;\n        if (rule.namespace) exp.namespace = rule.namespace;\n        if (rule.meta) exp.meta = rule.meta;\n        if (rule.ranges) exp.ranges = rule.ranges;\n        if (rule.name) exp.name = rule.name;\n        if (rule.phase) exp.phase = rule.phase;\n        if (rule.seed) exp.seed = rule.seed;\n        if (rule.hashVersion) exp.hashVersion = rule.hashVersion;\n        if (rule.filters) exp.filters = rule.filters;\n        if (rule.condition) exp.condition = rule.condition;\n\n        // Only return a value if the user is part of the experiment\n        const res = this._run(exp, id);\n        this._fireSubscriptions(exp, res);\n        if (res.inExperiment && !res.passthrough) {\n          return this._getFeatureResult(\n            id,\n            res.value,\n            \"experiment\",\n            rule.id,\n            exp,\n            res\n          );\n        }\n      }\n    }\n\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"Use default value\", {\n        id,\n        value: feature.defaultValue,\n      });\n\n    // Fall back to using the default value\n    return this._getFeatureResult(\n      id,\n      feature.defaultValue === undefined ? null : feature.defaultValue,\n      \"defaultValue\"\n    );\n  }\n\n  private _isIncludedInRollout(\n    seed: string,\n    hashAttribute: string | undefined,\n    fallbackAttribute: string | undefined,\n    range: VariationRange | undefined,\n    coverage: number | undefined,\n    hashVersion: number | undefined\n  ): boolean {\n    if (!range && coverage === undefined) return true;\n\n    const { hashValue } = this._getHashAttribute(\n      hashAttribute,\n      fallbackAttribute\n    );\n    if (!hashValue) {\n      return false;\n    }\n\n    const n = hash(seed, hashValue, hashVersion || 1);\n    if (n === null) return false;\n\n    return range\n      ? inRange(n, range)\n      : coverage !== undefined\n      ? n <= coverage\n      : true;\n  }\n\n  private _conditionPasses(condition: ConditionInterface): boolean {\n    return evalCondition(this.getAttributes(), condition);\n  }\n\n  private _isFilteredOut(filters: Filter[]): boolean {\n    return filters.some((filter) => {\n      const { hashValue } = this._getHashAttribute(filter.attribute);\n      if (!hashValue) return true;\n      const n = hash(filter.seed, hashValue, filter.hashVersion || 2);\n      if (n === null) return true;\n      return !filter.ranges.some((r) => inRange(n, r));\n    });\n  }\n\n  private _run<T>(\n    experiment: Experiment<T>,\n    featureId: string | null\n  ): Result<T> {\n    const key = experiment.key;\n    const numVariations = experiment.variations.length;\n\n    // 1. If experiment has less than 2 variations, return immediately\n    if (numVariations < 2) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Invalid experiment\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2. If the context is disabled, return immediately\n    if (this._ctx.enabled === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Context disabled\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2.5. Merge in experiment overrides from the context\n    experiment = this._mergeOverrides(experiment);\n\n    // 2.6 New, more powerful URL targeting\n    if (\n      experiment.urlPatterns &&\n      !isURLTargeted(this._getContextUrl(), experiment.urlPatterns)\n    ) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url targeting\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 3. If a variation is forced from a querystring, return the forced variation\n    const qsOverride = getQueryStringOverride(\n      key,\n      this._getContextUrl(),\n      numVariations\n    );\n    if (qsOverride !== null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via querystring\", {\n          id: key,\n          variation: qsOverride,\n        });\n      return this._getResult(experiment, qsOverride, false, featureId);\n    }\n\n    // 4. If a variation is forced in the context, return the forced variation\n    if (this._ctx.forcedVariations && key in this._ctx.forcedVariations) {\n      const variation = this._ctx.forcedVariations[key];\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via dev tools\", {\n          id: key,\n          variation,\n        });\n      return this._getResult(experiment, variation, false, featureId);\n    }\n\n    // 5. Exclude if a draft experiment or not active\n    if (experiment.status === \"draft\" || experiment.active === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because inactive\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 6. Get the hash attribute and return if empty\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n    if (!hashValue) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because missing hashAttribute\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    let assigned = -1;\n\n    let foundStickyBucket = false;\n    let stickyBucketVersionIsBlocked = false;\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const { variation, versionIsBlocked } = this._getStickyBucketVariation(\n        experiment.key,\n        experiment.bucketVersion,\n        experiment.minBucketVersion,\n        experiment.meta\n      );\n      foundStickyBucket = variation >= 0;\n      assigned = variation;\n      stickyBucketVersionIsBlocked = !!versionIsBlocked;\n    }\n\n    // Some checks are not needed if we already have a sticky bucket\n    if (!foundStickyBucket) {\n      // 7. Exclude if user is filtered out (used to be called \"namespace\")\n      if (experiment.filters) {\n        if (this._isFilteredOut(experiment.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip because of filters\", {\n              id: key,\n            });\n          return this._getResult(experiment, -1, false, featureId);\n        }\n      } else if (\n        experiment.namespace &&\n        !inNamespace(hashValue, experiment.namespace)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of namespace\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 7.5. Exclude if experiment.include returns false or throws\n      if (experiment.include && !isIncluded(experiment.include)) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of include function\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8. Exclude if condition is false\n      if (\n        experiment.condition &&\n        !this._conditionPasses(experiment.condition)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of condition exp\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8.1. Exclude if user is not in a required group\n      if (\n        experiment.groups &&\n        !this._hasGroupOverlap(experiment.groups as string[])\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of groups\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n    }\n\n    // 8.2. Old style URL targeting\n    if (experiment.url && !this._urlIsValid(experiment.url as RegExp)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 9. Get the variation from the sticky bucket or get bucket ranges and choose variation\n    const n = hash(\n      experiment.seed || key,\n      hashValue,\n      experiment.hashVersion || 1\n    );\n    if (n === null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of invalid hash version\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    if (!foundStickyBucket) {\n      const ranges =\n        experiment.ranges ||\n        getBucketRanges(\n          numVariations,\n          experiment.coverage === undefined ? 1 : experiment.coverage,\n          experiment.weights\n        );\n      assigned = chooseVariation(n, ranges);\n    }\n\n    // 9.5 Unenroll if any prior sticky buckets are blocked by version\n    if (stickyBucketVersionIsBlocked) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because sticky bucket version is blocked\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId, undefined, true);\n    }\n\n    // 10. Return if not in experiment\n    if (assigned < 0) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of coverage\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 11. Experiment has a forced variation\n    if (\"force\" in experiment) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force variation\", {\n          id: key,\n          variation: experiment.force,\n        });\n      return this._getResult(\n        experiment,\n        experiment.force === undefined ? -1 : experiment.force,\n        false,\n        featureId\n      );\n    }\n\n    // 12. Exclude if in QA mode\n    if (this._ctx.qaMode) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because QA mode\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 12.5. Exclude if experiment is stopped\n    if (experiment.status === \"stopped\") {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because stopped\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 13. Build the result object\n    const result = this._getResult(\n      experiment,\n      assigned,\n      true,\n      featureId,\n      n,\n      foundStickyBucket\n    );\n\n    // 13.5. Persist sticky bucket\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const {\n        changed,\n        key: attrKey,\n        doc,\n      } = this._generateStickyBucketAssignmentDoc(\n        hashAttribute,\n        toString(hashValue),\n        {\n          [this._getStickyBucketExperimentKey(\n            experiment.key,\n            experiment.bucketVersion\n          )]: result.key,\n        }\n      );\n      if (changed) {\n        // update local docs\n        this._ctx.stickyBucketAssignmentDocs =\n          this._ctx.stickyBucketAssignmentDocs || {};\n        this._ctx.stickyBucketAssignmentDocs[attrKey] = doc;\n        // save doc\n        this._ctx.stickyBucketService.saveAssignments(doc);\n      }\n    }\n\n    // 14. Fire the tracking callback\n    this._track(experiment, result);\n\n    // 15. Return the result\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"In experiment\", {\n        id: key,\n        variation: result.variationId,\n      });\n    return result;\n  }\n\n  log(msg: string, ctx: Record<string, unknown>) {\n    if (!this.debug) return;\n    if (this._ctx.log) this._ctx.log(msg, ctx);\n    else console.log(msg, ctx);\n  }\n\n  private _track<T>(experiment: Experiment<T>, result: Result<T>) {\n    if (!this._ctx.trackingCallback) return;\n\n    const key = experiment.key;\n\n    // Make sure a tracking callback is only fired once per unique experiment\n    const k =\n      result.hashAttribute + result.hashValue + key + result.variationId;\n    if (this._trackedExperiments.has(k)) return;\n    this._trackedExperiments.add(k);\n\n    try {\n      this._ctx.trackingCallback(experiment, result);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  private _mergeOverrides<T>(experiment: Experiment<T>): Experiment<T> {\n    const key = experiment.key;\n    const o = this._ctx.overrides;\n    if (o && o[key]) {\n      experiment = Object.assign({}, experiment, o[key]);\n      if (typeof experiment.url === \"string\") {\n        experiment.url = getUrlRegExp(\n          // eslint-disable-next-line\n          experiment.url as any\n        );\n      }\n    }\n\n    return experiment;\n  }\n\n  private _getHashAttribute(attr?: string, fallback?: string) {\n    let hashAttribute = attr || \"id\";\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let hashValue: any = \"\";\n\n    if (this._attributeOverrides[hashAttribute]) {\n      hashValue = this._attributeOverrides[hashAttribute];\n    } else if (this._ctx.attributes) {\n      hashValue = this._ctx.attributes[hashAttribute] || \"\";\n    } else if (this._ctx.user) {\n      hashValue = this._ctx.user[hashAttribute] || \"\";\n    }\n\n    // if no match, try fallback\n    if (!hashValue && fallback) {\n      if (this._attributeOverrides[fallback]) {\n        hashValue = this._attributeOverrides[fallback];\n      } else if (this._ctx.attributes) {\n        hashValue = this._ctx.attributes[fallback] || \"\";\n      } else if (this._ctx.user) {\n        hashValue = this._ctx.user[fallback] || \"\";\n      }\n      if (hashValue) {\n        hashAttribute = fallback;\n      }\n    }\n\n    return { hashAttribute, hashValue };\n  }\n\n  private _getResult<T>(\n    experiment: Experiment<T>,\n    variationIndex: number,\n    hashUsed: boolean,\n    featureId: string | null,\n    bucket?: number,\n    stickyBucketUsed?: boolean\n  ): Result<T> {\n    let inExperiment = true;\n    // If assigned variation is not valid, use the baseline and mark the user as not in the experiment\n    if (variationIndex < 0 || variationIndex >= experiment.variations.length) {\n      variationIndex = 0;\n      inExperiment = false;\n    }\n\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n\n    const meta: Partial<VariationMeta> = experiment.meta\n      ? experiment.meta[variationIndex]\n      : {};\n\n    const res: Result<T> = {\n      key: meta.key || \"\" + variationIndex,\n      featureId,\n      inExperiment,\n      hashUsed,\n      variationId: variationIndex,\n      value: experiment.variations[variationIndex],\n      hashAttribute,\n      hashValue,\n      stickyBucketUsed: !!stickyBucketUsed,\n    };\n\n    if (meta.name) res.name = meta.name;\n    if (bucket !== undefined) res.bucket = bucket;\n    if (meta.passthrough) res.passthrough = meta.passthrough;\n\n    return res;\n  }\n\n  private _getContextUrl() {\n    return this._ctx.url || (isBrowser ? window.location.href : \"\");\n  }\n\n  private _urlIsValid(urlRegex: RegExp): boolean {\n    const url = this._getContextUrl();\n    if (!url) return false;\n\n    const pathOnly = url.replace(/^https?:\\/\\//, \"\").replace(/^[^/]*\\//, \"/\");\n\n    if (urlRegex.test(url)) return true;\n    if (urlRegex.test(pathOnly)) return true;\n    return false;\n  }\n\n  private _hasGroupOverlap(expGroups: string[]): boolean {\n    const groups = this._ctx.groups || {};\n    for (let i = 0; i < expGroups.length; i++) {\n      if (groups[expGroups[i]]) return true;\n    }\n    return false;\n  }\n\n  private _applyDOMChanges(changes: AutoExperimentVariation) {\n    if (!isBrowser) return;\n    const undo: (() => void)[] = [];\n    if (changes.css) {\n      const s = document.createElement(\"style\");\n      s.innerHTML = changes.css;\n      document.head.appendChild(s);\n      undo.push(() => s.remove());\n    }\n    if (changes.js) {\n      const script = document.createElement(\"script\");\n      script.innerHTML = changes.js;\n      document.head.appendChild(script);\n      undo.push(() => script.remove());\n    }\n    if (changes.domMutations) {\n      changes.domMutations.forEach((mutation) => {\n        undo.push(mutate.declarative(mutation as DeclarativeMutation).revert);\n      });\n    }\n    return () => {\n      undo.forEach((fn) => fn());\n    };\n  }\n\n  private _deriveStickyBucketIdentifierAttributes(data?: FeatureApiResponse) {\n    const attributes = new Set<string>();\n    const features = data && data.features ? data.features : this.getFeatures();\n    const experiments =\n      data && data.experiments ? data.experiments : this.getExperiments();\n    Object.keys(features).forEach((id) => {\n      const feature = features[id];\n      if (feature.rules) {\n        for (const rule of feature.rules) {\n          if (rule.variations) {\n            attributes.add(rule.hashAttribute || \"id\");\n            if (rule.fallbackAttribute) {\n              attributes.add(rule.fallbackAttribute);\n            }\n          }\n        }\n      }\n    });\n    experiments.map((experiment) => {\n      attributes.add(experiment.hashAttribute || \"id\");\n      if (experiment.fallbackAttribute) {\n        attributes.add(experiment.fallbackAttribute);\n      }\n    });\n    return Array.from(attributes);\n  }\n\n  public async refreshStickyBuckets(data?: FeatureApiResponse) {\n    if (this._ctx.stickyBucketService) {\n      const attributes = this._getStickyBucketAttributes(data);\n      this._ctx.stickyBucketAssignmentDocs = await this._ctx.stickyBucketService.getAllAssignments(\n        attributes\n      );\n    }\n  }\n\n  private _getStickyBucketAssignments(): StickyAssignments {\n    const mergedAssignments: StickyAssignments = {};\n    Object.values(this._ctx.stickyBucketAssignmentDocs || {}).forEach((doc) => {\n      if (doc.assignments) Object.assign(mergedAssignments, doc.assignments);\n    });\n    return mergedAssignments;\n  }\n\n  private _getStickyBucketVariation(\n    experimentKey: string,\n    experimentBucketVersion?: number,\n    minExperimentBucketVersion?: number,\n    meta?: VariationMeta[]\n  ): {\n    variation: number;\n    versionIsBlocked?: boolean;\n  } {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    minExperimentBucketVersion = minExperimentBucketVersion || 0;\n    meta = meta || [];\n    const id = this._getStickyBucketExperimentKey(\n      experimentKey,\n      experimentBucketVersion\n    );\n    const assignments = this._getStickyBucketAssignments();\n\n    // users with any blocked bucket version (0 to minExperimentBucketVersion) are excluded from the test\n    if (minExperimentBucketVersion > 0) {\n      for (let i = 0; i <= minExperimentBucketVersion; i++) {\n        const blockedKey = this._getStickyBucketExperimentKey(experimentKey, i);\n        if (assignments[blockedKey] !== undefined) {\n          return {\n            variation: -1,\n            versionIsBlocked: true,\n          };\n        }\n      }\n    }\n    const variationKey = assignments[id];\n    if (variationKey === undefined)\n      // no assignment found\n      return { variation: -1 };\n    const variation = meta.findIndex((m) => m.key === variationKey);\n    if (variation < 0)\n      // invalid assignment, treat as \"no assignment found\"\n      return { variation: -1 };\n\n    return { variation };\n  }\n\n  private _getStickyBucketExperimentKey(\n    experimentKey: string,\n    experimentBucketVersion?: number\n  ): StickyExperimentKey {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    return `${experimentKey}__${experimentBucketVersion}`;\n  }\n\n  private _getStickyBucketAttributes(\n    data?: FeatureApiResponse\n  ): Record<string, string> {\n    const attributes: Record<string, string> = {};\n    this._ctx.stickyBucketIdentifierAttributes = !this._ctx\n      .stickyBucketIdentifierAttributes\n      ? this._deriveStickyBucketIdentifierAttributes(data)\n      : this._ctx.stickyBucketIdentifierAttributes;\n    this._ctx.stickyBucketIdentifierAttributes.forEach((attr) => {\n      const { hashValue } = this._getHashAttribute(attr);\n      attributes[attr] = toString(hashValue);\n    });\n    return attributes;\n  }\n\n  private _generateStickyBucketAssignmentDoc(\n    attributeName: string,\n    attributeValue: string,\n    assignments: StickyAssignments\n  ): {\n    key: StickyAttributeKey;\n    doc: StickyAssignmentsDocument;\n    changed: boolean;\n  } {\n    const key = `${attributeName}||${attributeValue}`;\n    const existingAssignments =\n      this._ctx.stickyBucketAssignmentDocs &&\n      this._ctx.stickyBucketAssignmentDocs[key]\n        ? this._ctx.stickyBucketAssignmentDocs[key].assignments || {}\n        : {};\n    const newAssignments = { ...existingAssignments, ...assignments };\n    const changed =\n      JSON.stringify(existingAssignments) !== JSON.stringify(newAssignments);\n\n    return {\n      key,\n      doc: {\n        attributeName,\n        attributeValue,\n        assignments: newAssignments,\n      },\n      changed,\n    };\n  }\n}\n", "import {\n  LocalStorageCompat,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n} from \"./types/growthbook\";\n\nexport interface CookieAttributes {\n  expires?: number | Date | undefined;\n  path?: string | undefined;\n  domain?: string | undefined;\n  secure?: boolean | undefined;\n  sameSite?: \"strict\" | \"Strict\" | \"lax\" | \"Lax\" | \"none\" | \"None\" | undefined;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [property: string]: any;\n}\nexport interface JsCookiesCompat<T = string> {\n  set(\n    name: string,\n    value: string | T,\n    options?: CookieAttributes\n  ): string | undefined;\n  get(name: string): string | T | undefined;\n  get(): { [key: string]: string };\n  remove(name: string, options?: CookieAttributes): void;\n}\n\nexport interface IORedisCompat {\n  mget(...keys: string[]): Promise<string[]>;\n  set(key: string, value: string): Promise<string>;\n}\n\nexport interface RequestCompat {\n  cookies: Record<string, string>;\n  [key: string]: unknown;\n}\nexport interface ResponseCompat {\n  cookie(\n    name: string,\n    value: string,\n    options?: CookieAttributes\n  ): ResponseCompat;\n  [key: string]: unknown;\n}\n\n/**\n * Responsible for reading and writing documents which describe sticky bucket assignments.\n */\nexport abstract class StickyBucketService {\n  abstract getAssignments(\n    attributeName: string,\n    attributeValue: string\n  ): Promise<StickyAssignmentsDocument | null>;\n\n  abstract saveAssignments(doc: StickyAssignmentsDocument): Promise<unknown>;\n\n  /**\n   * The SDK calls getAllAssignments to populate sticky buckets. This in turn will\n   * typically loop through individual getAssignments calls. However, some StickyBucketService\n   * instances (i.e. Redis) will instead perform a multi-query inside getAllAssignments instead.\n   */\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<string, StickyAssignmentsDocument> = {};\n    (\n      await Promise.all(\n        Object.entries(attributes).map(([attributeName, attributeValue]) =>\n          this.getAssignments(attributeName, attributeValue)\n        )\n      )\n    ).forEach((doc) => {\n      if (doc) {\n        const key = `${doc.attributeName}||${doc.attributeValue}`;\n        docs[key] = doc;\n      }\n    });\n    return docs;\n  }\n}\n\nexport class LocalStorageStickyBucketService extends StickyBucketService {\n  private prefix: string;\n  private localStorage: LocalStorageCompat | undefined;\n  constructor(opts?: { prefix?: string; localStorage?: LocalStorageCompat }) {\n    opts = opts || {};\n    super();\n    this.prefix = opts.prefix || \"gbStickyBuckets__\";\n    try {\n      this.localStorage = opts.localStorage || globalThis.localStorage;\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.localStorage) return doc;\n    try {\n      const raw = (await this.localStorage.getItem(this.prefix + key)) || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.localStorage) return;\n    try {\n      await this.localStorage.setItem(this.prefix + key, JSON.stringify(doc));\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n}\n\nexport class ExpressCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with cookieParser() middleware from npm: 'cookie-parser'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value must be manually encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private req: RequestCompat;\n  private res: ResponseCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    req,\n    res,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    req: RequestCompat;\n    res: ResponseCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.req = req;\n    this.res = res;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.req) return doc;\n    try {\n      const raw = this.req.cookies[this.prefix + key] || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.res) return;\n    const str = JSON.stringify(doc);\n    this.res.cookie(\n      encodeURIComponent(this.prefix + key),\n      encodeURIComponent(str),\n      this.cookieAttributes\n    );\n  }\n}\n\nexport class BrowserCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with npm: 'js-cookie'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value is automatically encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private jsCookie: JsCookiesCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    jsCookie,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    jsCookie: JsCookiesCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.jsCookie = jsCookie;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.jsCookie) return doc;\n    try {\n      const raw = this.jsCookie.get(this.prefix + key);\n      const data = JSON.parse(raw || \"{}\");\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.jsCookie) return;\n    const str = JSON.stringify(doc);\n    this.jsCookie.set(this.prefix + key, str, this.cookieAttributes);\n  }\n}\n\nexport class RedisStickyBucketService extends StickyBucketService {\n  /** Intended to be used with npm: 'ioredis'. **/\n  private redis: IORedisCompat | undefined;\n  constructor({ redis }: { redis: IORedisCompat }) {\n    super();\n    this.redis = redis;\n  }\n\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<StickyAttributeKey, StickyAssignmentsDocument> = {};\n    const keys = Object.entries(attributes).map(\n      ([attributeName, attributeValue]) => `${attributeName}||${attributeValue}`\n    );\n    if (!this.redis) return docs;\n    this.redis.mget(...keys).then((values) => {\n      values.forEach((raw) => {\n        try {\n          const data = JSON.parse(raw || \"{}\");\n          if (data.attributeName && data.attributeValue && data.assignments) {\n            const key = `${data.attributeName}||${data.attributeValue}`;\n            docs[key] = data;\n          }\n        } catch (e) {\n          // ignore redis doc parse errors\n        }\n      });\n    });\n    return docs;\n  }\n\n  async getAssignments(_attributeName: string, _attributeValue: string) {\n    // not implemented\n    return null;\n  }\n\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.redis) return;\n    await this.redis.set(key, JSON.stringify(doc));\n  }\n}\n"], "names": ["cacheSettings", "staleTTL", "maxAge", "cache<PERSON>ey", "backgroundSync", "maxEntries", "disableIdleStreams", "idleStreamInterval", "polyfills", "fetch", "globalThis", "bind", "undefined", "SubtleCrypto", "crypto", "subtle", "EventSource", "helpers", "fetchFeaturesCall", "_ref", "host", "client<PERSON>ey", "headers", "concat", "fetchRemoteEvalCall", "_ref2", "payload", "options", "method", "body", "JSON", "stringify", "eventSourceCall", "_ref3", "startIdleListener", "idleTimeout", "window", "document", "onVisibilityChange", "visibilityState", "clearTimeout", "onVisible", "setTimeout", "onHidden", "addEventListener", "removeEventListener", "stopIdleListener", "localStorage", "e", "subscribedInstances", "Map", "cacheInitialized", "cache", "activeFetches", "streams", "supportsSSE", "Set", "setPolyfills", "overrides", "Object", "assign", "configure<PERSON>ache", "clearAutoRefresh", "async", "clearCache", "clear", "updatePersistentCache", "for<PERSON>ach", "channel", "state", "disableChannel", "enableChannel", "setItem", "Array", "from", "entries", "<PERSON><PERSON><PERSON>", "instance", "apiHost", "getApiInfo", "get<PERSON><PERSON><PERSON><PERSON>", "baseKey", "isRemoteEval", "attributes", "getAttributes", "cacheKeyAttributes", "getCacheKeyAttributes", "keys", "ca", "key", "fv", "getForcedVariations", "url", "getUrl", "cleanupCache", "entriesWithTimestamps", "map", "_ref5", "value", "staleAt", "getTime", "sort", "a", "b", "entriesToRemoveCount", "Math", "min", "max", "size", "i", "delete", "onNewFeatureData", "data", "version", "dateUpdated", "Date", "now", "existing", "get", "set", "sse", "has", "instances", "refreshInstance", "decryptPayload", "refreshStickyBuckets", "setExperiments", "experiments", "getExperiments", "setFeatures", "features", "getFeatures", "fetchFeatures", "apiRequestHeaders", "getApiHosts", "getClientKey", "remoteEval", "promise", "forcedVariations", "forcedFeatures", "getForcedFeatures", "then", "res", "add", "json", "startAutoRefresh", "catch", "Promise", "resolve", "streamingHost", "streamingHostRequestHeaders", "src", "cb", "event", "type", "parse", "errors", "onSSEError", "readyState", "delay", "pow", "random", "includes", "onopen", "onerror", "close", "destroyChannel", "validAttributeName", "nullController", "revert", "elements", "mutations", "getElementRecord", "element", "record", "createElementPropertyRecord", "el", "attr", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "parentNode", "insertBeforeNode", "observe", "childList", "subtree", "characterData", "attributeFilter", "getObserverInit", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "m", "mutate", "html", "transformContainer", "createElement", "innerHTML", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "join", "attrMutationRunner", "positionMutationRunner", "newNodes", "parentSelector", "insertBeforeSelector", "querySelector", "_loadDOMNodes", "getHTMLValue", "setHTMLValue", "getElementHTMLRecord", "elementRecord", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getElementAttributeRecord", "attrName", "_el$getAttribute", "getAttribute", "setAttribute", "setAttrValue", "setPropertyV<PERSON>ue", "length", "_element$html", "_element$html$observe", "disconnect", "_element$classes", "_element$classes$obse", "_element$position", "_element$position$obs", "_element$attributes", "_element$attributes$a", "_element$attributes$a2", "deleteElementPropertyRecord", "refreshElementsSet", "mutation", "kind", "existingElements", "querySelectorAll", "selector", "attribute", "push", "startMutating", "refreshAllElementSets", "newMutation", "index", "indexOf", "splice", "stopMutating", "test", "classnames", "mutatedClassnames", "c", "hashFnv32a", "str", "hval", "l", "charCodeAt", "hash", "seed", "inRange", "n", "range", "getUrlRegExp", "regexString", "escaped", "replace", "RegExp", "console", "error", "isURLTargeted", "targets", "hasIncludeRules", "isIncluded", "match", "_evalURLTarget", "pattern", "include", "parsed", "URL", "regex", "href", "substring", "origin", "actual", "expected", "comps", "pathname", "searchParams", "v", "k", "some", "isPath", "_evalSimpleUrlPart", "_evalSimpleUrlTarget", "documentElement", "base64ToBuf", "Uint8Array", "atob", "decrypt", "encryptedString", "decryptionKey", "Error", "importKey", "name", "iv", "cipherText", "plainTextBuffer", "TextDecoder", "decode", "toString", "input", "paddedVersionString", "parts", "padStart", "_regexCache", "evalCondition", "obj", "condition", "evalOr", "conditions", "evalAnd", "evalConditionValue", "<PERSON><PERSON><PERSON>", "path", "current", "isArray", "isOperatorObject", "op", "evalOperatorCondition", "isIn", "operator", "check", "elemMatch", "passed", "j", "t", "getType", "<PERSON><PERSON><PERSON><PERSON>", "SDK_VERSION", "loadSDKVersion", "GrowthBook", "constructor", "context", "this", "_ctx", "_renderer", "_trackedExperiments", "_trackedFeatures", "debug", "_subscriptions", "_rtQueue", "_rtTimer", "ready", "_assigned", "_forcedFeatureV<PERSON>ues", "_attributeOverrides", "_activeAutoExperiments", "_triggeredExpKeys", "_loadFeaturesCalled", "isGbHost", "hostname", "enableDevMode", "_growthbook", "dispatchEvent", "Event", "_updateAllAutoExperiments", "_refresh", "autoRefresh", "subscribeToChanges", "_canSubscribe", "subs", "subscribe", "defaultHost", "apiHostRequestHeaders", "allowStale", "updateInstance", "timeout", "<PERSON><PERSON><PERSON>", "minStaleAt", "getItem", "_ref4", "cleanupFn", "initializeCache", "timer", "resolved", "finish", "promiseTimeout", "fetchFeaturesWithCache", "refreshFeatures", "_render", "featuresJSON", "experimentsJSON", "encryptedFeatures", "encryptedExperiments", "stickyBucketService", "_refreshForRemoteEval", "vars", "setForcedFeatures", "getStickyBucketAssignmentDocs", "stickyBucketAssignmentDocs", "getAllResults", "destroy", "s", "exp", "undo", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "forceVariation", "variation", "run", "experiment", "result", "_run", "_fireSubscriptions", "triggerExperiment", "manual", "_runAutoExperiment", "forceRerun", "valueHash", "inExperiment", "_undoActiveAutoExperiment", "_applyDOMChanges", "prev", "variationId", "_trackFeatureUsage", "source", "stringifiedValue", "onFeatureUsage", "on", "q", "realtimeKey", "encodeURIComponent", "mode", "realtimeInterval", "_getFeatureResult", "ruleId", "ret", "off", "experimentResult", "isOn", "evalFeature", "isOff", "getFeatureValue", "defaultValue", "feature", "id", "rules", "rule", "filters", "_isFilteredOut", "_conditionPasses", "_isIncludedInRollout", "hashAttribute", "disableStickyBucketing", "fallbackAttribute", "coverage", "hashVersion", "tracks", "_track", "force", "variations", "weights", "bucketVersion", "minBucketVersion", "namespace", "meta", "ranges", "phase", "passthrough", "hashValue", "_getHashAttribute", "r", "featureId", "numVariations", "_getResult", "enabled", "_mergeOverrides", "urlPatterns", "_getContextUrl", "qsOverride", "search", "kv", "parseInt", "getQueryStringOverride", "status", "active", "assigned", "foundStickyBucket", "stickyBucketVersionIsBlocked", "versionIsBlocked", "_getStickyBucketVariation", "inNamespace", "groups", "_hasGroupOverlap", "_urlIsValid", "chooseVariation", "equal", "fill", "totalWeight", "reduce", "w", "sum", "cumulative", "start", "getBucketRanges", "qaMode", "changed", "attrKey", "doc", "_generateStickyBucketAssignmentDoc", "_getStickyBucketExperimentKey", "saveAssignments", "log", "msg", "ctx", "trackingCallback", "o", "fallback", "user", "variationIndex", "hashUsed", "bucket", "stickyBucketUsed", "location", "urlRegex", "pathOnly", "expGroups", "changes", "css", "head", "append<PERSON><PERSON><PERSON>", "remove", "js", "script", "domMutations", "action", "fn", "_deriveStickyBucketIdentifierAttributes", "_getStickyBucketAttributes", "getAllAssignments", "_getStickyBucketAssignments", "mergedAssignments", "values", "assignments", "<PERSON><PERSON><PERSON>", "experimentBucketVersion", "minExperimentBucketVersion", "<PERSON><PERSON><PERSON>", "findIndex", "stickyBucketIdentifierAttributes", "attributeName", "attributeValue", "existingAssignments", "newAssignments", "StickyBucketService", "docs", "all", "getAssignments", "LocalStorageStickyBucketService", "opts", "super", "prefix", "raw", "ExpressCookieStickyBucketService", "req", "cookieAttributes", "cookies", "cookie", "BrowserCookieStickyBucketService", "js<PERSON><PERSON><PERSON>", "RedisStickyBucketService", "redis", "mget", "_attributeName", "_attributeValue"], "mappings": "AA0BA,MAAMA,EAA+B,CAEnCC,SAAU,IAEVC,OAAQ,MACRC,SAAU,kBACVC,gBAAgB,EAChBC,WAAY,GACZC,oBAAoB,EACpBC,mBAAoB,KAEhBC,EAAuB,CAC3BC,MAAOC,WAAWD,MAAQC,WAAWD,MAAME,KAAKD,iBAAcE,EAC9DC,aAAcH,WAAWI,OAASJ,WAAWI,OAAOC,YAASH,EAC7DI,YAAaN,WAAWM,aAEbC,EAAmB,CAC9BC,kBAAmBC,IAAkC,IAAjCC,KAAEA,EAAIC,UAAEA,EAASC,QAAEA,GAASH,EAC9C,OAAQX,EAAUC,gBACbW,EAAI,kBAAAG,OAAiBF,GACxB,CAAEC,WACH,EAEHE,oBAAqBC,IAA2C,IAA1CL,KAAEA,EAAIC,UAAEA,EAASK,QAAEA,EAAOJ,QAAEA,GAASG,EACzD,MAAME,EAAU,CACdC,OAAQ,OACRN,QAAS,CAAE,eAAgB,sBAAuBA,GAClDO,KAAMC,KAAKC,UAAUL,IAEvB,OAAQlB,EAAUC,MAAK,GAAAc,OAClBH,EAAiBC,cAAAA,OAAAA,GACpBM,EACD,EAEHK,gBAAiBC,IAAkC,IAAjCb,KAAEA,EAAIC,UAAEA,EAASC,QAAEA,GAASW,EAC5C,OAAIX,EACK,IAAId,EAAUQ,sBAAeI,EAAI,SAAAG,OAAQF,GAAa,CAC3DC,YAGG,IAAId,EAAUQ,sBAAeI,EAAI,SAAAG,OAAQF,GAAY,EAE9Da,kBAAmB,KACjB,IAAIC,EAGJ,GADoB,oBAAXC,QAA8C,oBAAbC,SAC1B,OAChB,MAAMC,EAAqB,KACQ,YAA7BD,SAASE,iBACXH,OAAOI,aAAaL,GACpBM,KACsC,WAA7BJ,SAASE,kBAClBJ,EAAcC,OAAOM,WACnBC,EACA3C,EAAcO,oBAElB,EAGF,OADA8B,SAASO,iBAAiB,mBAAoBN,GACvC,IACLD,SAASQ,oBAAoB,mBAAoBP,EAAmB,EAExEQ,iBAAkB,QAKpB,IACMpC,WAAWqC,eACbvC,EAAUuC,aAAerC,WAAWqC,aAGtC,CADA,MAAOC,GACP,CAIF,MAAMC,EAAoD,IAAIC,IAC9D,IAAIC,GAAmB,EACvB,MAAMC,EAAiC,IAAIF,IACrCG,EAA0D,IAAIH,IAC9DI,EAAsC,IAAIJ,IAC1CK,EAA2B,IAAIC,IAG9B,SAASC,EAAaC,GAC3BC,OAAOC,OAAOpD,EAAWkD,EAC3B,CACO,SAASG,EAAeH,GAC7BC,OAAOC,OAAO5D,EAAe0D,GACxB1D,EAAcI,gBACjB0D,GAEJ,CAEOC,eAAeC,IACpBZ,EAAMa,QACNZ,EAAcY,QACdH,IACAX,GAAmB,QACbe,GACR,CAkCO,SAASvB,IACdW,EAAQa,SAASC,IACVA,IACLA,EAAQC,MAAQ,OAChBC,EAAeF,GAAQ,GAE3B,CAEO,SAAS3B,IACda,EAAQa,SAASC,IACVA,GACiB,SAAlBA,EAAQC,OACZE,EAAcH,EAAQ,GAE1B,CAIAL,eAAeG,IACb,IACE,IAAK1D,EAAUuC,aAAc,aACvBvC,EAAUuC,aAAayB,QAC3BxE,EAAcG,SACd2B,KAAKC,UAAU0C,MAAMC,KAAKtB,EAAMuB,YAGlC,CADA,MAAO3B,GACP,CAEJ,CAyCA,SAAS4B,EAAOC,GACd,MAAOC,EAASzD,GAAawD,EAASE,aACtC,MAAUD,GAAAA,OAAAA,eAAYzD,EACxB,CAEA,SAAS2D,EAAYH,GACnB,MAAMI,EAAUL,EAAOC,GACvB,IAAKA,EAASK,eAAgB,OAAOD,EAErC,MAAME,EAAaN,EAASO,gBACtBC,EACJR,EAASS,yBAA2B3B,OAAO4B,KAAKV,EAASO,iBACrDI,EAAiB,CAAA,EACvBH,EAAmBlB,SAASsB,IAC1BD,EAAGC,GAAON,EAAWM,EAAI,IAG3B,MAAMC,EAAKb,EAASc,sBACdC,EAAMf,EAASgB,SAErB,MAAA,GAAAtE,OAAU0D,EAAO,MAAA1D,OAAKO,KAAKC,UAAU,CACnCyD,KACAE,KACAE,QAEJ,CA6DA,SAASE,IACP,MAAMC,EAAwBtB,MAAMC,KAAKtB,EAAMuB,WAC5CqB,KAAIC,IAAA,IAAER,EAAKS,GAAMD,EAAA,MAAM,CACtBR,MACAU,QAASD,EAAMC,QAAQC,UACxB,IACAC,MAAK,CAACC,EAAGC,IAAMD,EAAEH,QAAUI,EAAEJ,UAE1BK,EAAuBC,KAAKC,IAChCD,KAAKE,IAAI,EAAGvD,EAAMwD,KAAO5G,EAAcK,YACvC+C,EAAMwD,MAGR,IAAK,IAAIC,EAAI,EAAGA,EAAIL,EAAsBK,IACxCzD,EAAM0D,OAAOf,EAAsBc,GAAGpB,IAE1C,CAGA,SAASsB,EACPtB,EACAtF,EACA6G,GAGA,MAAMC,EAAUD,EAAKE,aAAe,GAC9Bf,EAAU,IAAIgB,KAAKA,KAAKC,MAAQpH,EAAcC,UAC9CoH,EAAWjE,EAAMkE,IAAInH,GAC3B,GAAIkH,GAAYJ,GAAWI,EAASJ,UAAYA,EAG9C,OAFAI,EAASlB,QAAUA,OACnBjC,IAKFd,EAAMmE,IAAIpH,EAAU,CAClB6G,OACAC,UACAd,UACAqB,IAAKjE,EAAYkE,IAAIhC,KAEvBK,IAEA5B,IAGA,MAAMwD,EAAYzE,EAAoBqE,IAAI7B,GAC1CiC,GAAaA,EAAUvD,SAASU,GAAa8C,EAAgB9C,EAAUmC,IACzE,CAEAjD,eAAe4D,EACb9C,EACAmC,GAEAA,QAAanC,EAAS+C,eAAeZ,OAAMpG,EAAWJ,EAAUK,oBAE1DgE,EAASgD,qBAAqBb,GACpCnC,EAASiD,eAAed,EAAKe,aAAelD,EAASmD,kBACrDnD,EAASoD,YAAYjB,EAAKkB,UAAYrD,EAASsD,cACjD,CAEApE,eAAeqE,EACbvD,GAEA,MAAMC,QAAEA,EAAOuD,kBAAEA,GAAsBxD,EAASyD,cAC1CjH,EAAYwD,EAAS0D,eACrBC,EAAa3D,EAASK,eACtBO,EAAMb,EAAOC,GACb1E,EAAW6E,EAAYH,GAE7B,IAAI4D,EAAUpF,EAAciE,IAAInH,GA8ChC,OA7CKsI,IAoBHA,GAnBmCD,EAC/BvH,EAAQO,oBAAoB,CAC1BJ,KAAM0D,EACNzD,YACAK,QAAS,CACPyD,WAAYN,EAASO,gBACrBsD,iBAAkB7D,EAASc,sBAC3BgD,eAAgBlE,MAAMC,KAAKG,EAAS+D,oBAAoBjE,WACxDiB,IAAKf,EAASgB,UAEhBvE,QAAS+G,IAEXpH,EAAQC,kBAAkB,CACxBE,KAAM0D,EACNzD,YACAC,QAAS+G,KAKZQ,MAAMC,IACoC,YAArCA,EAAIxH,QAAQgG,IAAI,kBAClB/D,EAAYwF,IAAItD,GAEXqD,EAAIE,UAEZH,MAAM7B,IACLD,EAAiBtB,EAAKtF,EAAU6G,GAChCiC,EAAiBpE,GACjBxB,EAAcyD,OAAO3G,GACd6G,KAERkC,OAAOlG,IAONK,EAAcyD,OAAO3G,GACdgJ,QAAQC,QAAQ,CAAA,MAE3B/F,EAAckE,IAAIpH,EAAUsI,UAEjBA,CACf,CAIA,SAASQ,EAAiBpE,GACxB,MAAMY,EAAMb,EAAOC,GACb1E,EAAW6E,EAAYH,IACvBwE,cAAEA,EAAaC,4BAAEA,GAAgCzE,EAASyD,cAC1DjH,EAAYwD,EAAS0D,eAC3B,GACEvI,EAAcI,gBACdmD,EAAYkE,IAAIhC,IAChBjF,EAAUQ,YACV,CACA,GAAIsC,EAAQmE,IAAIhC,GAAM,OACtB,MAAMrB,EAAyB,CAC7BmF,IAAK,KACLnI,KAAMiI,EACNhI,YACAC,QAASgI,EACTE,GAAKC,IACH,IACE,GAAmB,qBAAfA,EAAMC,KAA6B,CACrC,MAAMhC,EAAYzE,EAAoBqE,IAAI7B,GAC1CiC,GACEA,EAAUvD,SAASU,IACjBuD,EAAcvD,EAAS,GAE7B,MAAO,GAAmB,aAAf4E,EAAMC,KAAqB,CACpC,MAAMV,EAA2BlH,KAAK6H,MAAMF,EAAMzC,MAClDD,EAAiBtB,EAAKtF,EAAU6I,EAClC,CAEA5E,EAAQwF,OAAS,CASnB,CARE,MAAO5G,GAOP6G,EAAWzF,EACb,GAEFwF,OAAQ,EACRvF,MAAO,UAETf,EAAQiE,IAAI9B,EAAKrB,GACjBG,EAAcH,EAChB,CACF,CAEA,SAASyF,EAAWzF,GAClB,GAAsB,SAAlBA,EAAQC,QACZD,EAAQwF,SACJxF,EAAQwF,OAAS,GAAMxF,EAAQmF,KAAkC,IAA3BnF,EAAQmF,IAAIO,YAAmB,CAEvE,MAAMC,EACJtD,KAAKuD,IAAI,EAAG5F,EAAQwF,OAAS,IAAM,IAAuB,IAAhBnD,KAAKwD,UACjD3F,EAAeF,GACf1B,YAAW,KACL,CAAC,OAAQ,UAAUwH,SAAS9F,EAAQC,QACxCE,EAAcH,EAAQ,GACrBqC,KAAKC,IAAIqD,EAAO,KACrB,CACF,CAEA,SAASzF,EAAeF,GACjBA,EAAQmF,MACbnF,EAAQmF,IAAIY,OAAS,KACrB/F,EAAQmF,IAAIa,QAAU,KACtBhG,EAAQmF,IAAIc,QACZjG,EAAQmF,IAAM,KACQ,WAAlBnF,EAAQC,QACVD,EAAQC,MAAQ,YAEpB,CAEA,SAASE,EAAcH,GACrBA,EAAQmF,IAAMtI,EAAQe,gBAAgB,CACpCZ,KAAMgD,EAAQhD,KACdC,UAAW+C,EAAQ/C,UACnBC,QAAS8C,EAAQ9C,UAEnB8C,EAAQC,MAAQ,SAChBD,EAAQmF,IAAI3G,iBAAiB,WAAYwB,EAAQoF,IACjDpF,EAAQmF,IAAI3G,iBAAiB,mBAAoBwB,EAAQoF,IACzDpF,EAAQmF,IAAIa,QAAU,IAAMP,EAAWzF,GACvCA,EAAQmF,IAAIY,OAAS,KACnB/F,EAAQwF,OAAS,CAAC,CAEtB,CAEA,SAASU,EAAelG,EAAwBqB,GAC9CnB,EAAeF,GACfd,EAAQwD,OAAOrB,EACjB,CAEA,SAAS3B,IAEPP,EAAYU,QAGZX,EAAQa,QAAQmG,GAGhBrH,EAAoBgB,QAGpBhD,EAAQ6B,kBACV,CC9hBayH,IAAAA,EAAqB,+BAC5BC,EAAqC,CACzCC,OAAQ,WAAA,GAGJC,EAAwC,IAAIxH,IAC5CyH,EAA2B,IAAInH,IAkBrC,SAASoH,EAAiBC,GACxB,IAAIC,EAASJ,EAASpD,IAAIuD,GAO1B,OALKC,GAEHJ,EAASnD,IAAIsD,EADbC,EAAS,CAAED,QAAAA,EAAS1F,WAAY,CAAA,IAI3B2F,CACR,CAED,SAASC,EACPC,EACAC,EACAC,EACAC,EACAC,GAEA,IAAMC,EAAeH,EAAgBF,GAC/BF,EAA0C,CAC9CQ,SAAS,EACTC,cAAeF,EACfG,aAAcH,EACdV,UAAW,GACXK,GAAAA,EACAS,EAAkB,KAClBC,SAAU,IAAIC,kBAAiB,WAK7B,GAAa,aAATV,IAAuBH,EAAOW,EAAlC,CACkB,aAATR,IACPH,EAAOW,EAAmB/I,YAAW,WACnCoI,EAAOW,EAAmB,IADQ,GAEjC,MAEL,IAAMJ,EAAeH,EAAgBF,GAE1B,aAATC,GACAI,EAAaO,aAAed,EAAOU,aAAaI,YAChDP,EAAaQ,mBAAqBf,EAAOU,aAAaK,kBAGpDR,IAAiBP,EAAOU,eAC5BV,EAAOS,cAAgBF,EACvBD,EAAeN,GAbb,CAcH,IACDM,eAAAA,EACAD,SAAAA,EACAD,gBAAAA,GAYF,MAVa,aAATD,GAAuBD,EAAGY,WAC5Bd,EAAOY,SAASI,QAAQd,EAAGY,WAAY,CACrCG,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ8G,eAAe,IAGjBnB,EAAOY,SAASI,QAAQd,EA5E5B,SAAyBC,GACvB,MAAgB,SAATA,EACH,CACEc,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ8G,eAAe,GAEjB,CACEF,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ+G,gBAAiB,CAACjB,GAEzB,CA8D+BkB,CAAgBlB,IAEvCH,CACR,CAED,SAASsB,EACPC,EACAvB,GAEA,IAAMwB,EAAaxB,EAAOI,gBAAgBJ,EAAOE,IACjDF,EAAOU,aAAea,EAClBA,GAAsB,iBAARA,EAEbC,GACDD,EAAIT,aAAeU,EAAWV,YAC9BS,EAAIR,mBAAqBS,EAAWT,mBAEpCf,EAAOQ,SAAU,EACjBiB,KAEOF,IAAQC,IACjBxB,EAAOQ,SAAU,EACjBiB,IAEH,CAED,SAASC,EAAmB1B,GAC1B,IAAIuB,EAAMvB,EAAOS,cACjBT,EAAOH,UAAUxG,SAAQ,SAACsI,GAAA,OAAKJ,EAAMI,EAAEC,OAAOL,MAC9CD,EAkJF,SAA4BO,GAK1B,OAJKC,IACHA,EAAqBvK,SAASwK,cAAc,QAE9CD,EAAmBE,UAAYH,EACxBC,EAAmBE,SAC3B,CAxJeC,CAAmBV,GAAMvB,EACxC,CACD,SAASkC,EAAoBlC,GAC3B,IAAMuB,EAAM,IAAI7I,IAAIsH,EAAOS,cAAc0B,MAAM,OAAOC,OAAOC,UAC7DrC,EAAOH,UAAUxG,SAAQ,SAACsI,GAAA,OAAIA,EAAEC,OAAOL,MACvCD,EACE3H,MAAMC,KAAK2H,GACRa,OAAOC,SACPC,KAAK,KACRtC,EAEH,CAED,SAASuC,EAAmBvC,GAC1B,IAAIuB,EAAqBvB,EAAOS,cAChCT,EAAOH,UAAUxG,SAAQ,SAACsI,GAAA,OAAKJ,EAAMI,EAAEC,OAAOL,MAC9CD,EAAcC,EAAKvB,EACpB,CAkBD,SAASwC,EAAuBxC,GAC9B,IAAIuB,EAAMvB,EAAOS,cACjBT,EAAOH,UAAUxG,SAAQ,SAACsI,GACxB,IACMc,EApBV,SAAApM,GACEqM,IACAC,EAAAA,EAAAA,qBAEM7B,EAAavJ,SAASqL,cAH5BF,EAAAA,gBAIA,IAAK5B,EAAY,OAAO,KACxB,IAAMC,EAAmB4B,EACrBpL,SAASqL,cAA2BD,GACpC,KACJ,OAAIA,IAAyB5B,EAAyB,KAC/C,CACLD,WAAAA,EACAC,iBAAAA,EAEH,CAMoB8B,CADClB,EAAEC,UAEpBL,EAAMkB,GAAYlB,KAEpBD,EAAcC,EAAKvB,EACpB,CAED,IAAM8C,EAAe,SAAC5C,GAAD,OAAiBA,EAAG8B,SAApB,EACfe,EAAe,SAAC7C,EAAa9E,GAAd,OAAiC8E,EAAG8B,UAAY5G,CAAhD,EACrB,SAAS4H,EAAqBjD,GAC5B,IAAMkD,EAAgBnD,EAAiBC,GAUvC,OATKkD,EAAcpB,OACjBoB,EAAcpB,KAAO5B,EACnBF,EACA,OACA+C,EACAC,EACArB,IAGGuB,EAAcpB,IACtB,CAED,IAAMqB,EAAqB,SAAChD,GAC1B,MAAO,CACLY,WAAYZ,EAAGiD,cACfpC,iBAAkBb,EAAGkD,mBAExB,EACKC,EAAqB,SAACnD,EAAa9E,GAErCA,EAAM2F,mBACL3F,EAAM0F,WAAWwC,SAASlI,EAAM2F,mBAMnC3F,EAAM0F,WAAWyC,aAAarD,EAAI9E,EAAM2F,iBACzC,EACD,SAASyC,EAAyBzD,GAChC,IAAMkD,EAAgBnD,EAAiBC,GAUvC,OATKkD,EAAcQ,WACjBR,EAAcQ,SAAWxD,EACvBF,EACA,WACAmD,EACAG,EACAb,IAGGS,EAAcQ,QACtB,CAED,IAqDI3B,EAmGAlB,EAxJE8C,EAAgB,SAACxD,EAAaqB,GAAd,OACpBA,EAAOrB,EAAGyD,UAAYpC,EAAOrB,EAAG0D,gBAAgB,QAD5B,EAEhBC,EAAgB,SAAC3D,GAAD,OAAiBA,EAAGyD,SAApB,EACtB,SAASG,EAAsB5D,GAC7B,IAAM+C,EAAgBnD,EAAiBI,GAUvC,OATK+C,EAAcc,UACjBd,EAAcc,QAAU9D,EACtBC,EACA,QACA2D,EACAH,EACAxB,IAGGe,EAAcc,OACtB,CAMD,SAASC,EAA0B9D,EAAaC,GAC9C,IALoB8D,EAKdhB,EAAgBnD,EAAiBI,GAUvC,OATK+C,EAAc5I,WAAW8F,KAC5B8C,EAAc5I,WAAW8F,GAAQF,EAC/BC,EACAC,GATgB8D,EAUH9D,EAVwB,SAACD,GAAD,IAAAgE,EAAA,cAAAA,EACzChE,EAAGiE,aAAaF,MAAa,OACV,SAACA,GAAD,OAAsB,SAAC/D,EAAaqB,GAAd,OACjC,OAARA,EAAerB,EAAGkE,aAAaH,EAAU1C,GAAOrB,EAAG0D,gBAAgBK,GADhD,CASfI,CAAalE,GACboC,IAGGU,EAAc5I,WAAW8F,EACjC,CA6BD,SAASmE,EACPpE,EACAC,EACAwB,GAEA,GAAKA,EAAEnB,QAAP,CACAmB,EAAEnB,SAAU,EACZ,IAAMe,EAAMI,EAAEjB,aACTiB,EAAE9B,UAAU0E,QAnCnB,SAAqCrE,EAAaC,GAChD,IAEqBqE,EAAAC,EAFf1E,EAAUH,EAASpD,IAAI0D,GAC7B,GAAKH,EACL,GAAa,SAATI,EACYS,OAAd4D,EAAAzE,EAAQ8B,cAAMjB,EAAAA,EAAAA,aAAU8D,oBACjB3E,EAAQ8B,UACV,GAAa,UAAT1B,EAAkB,CAAA,IAAAwE,EAAAC,EACVhE,OAAjB+D,EAAA5E,EAAQgE,iBAASnD,EAAAA,EAAAA,aAAU8D,oBACpB3E,EAAQgE,OAChB,MAAM,GAAa,aAAT5D,EAAqB,CAAA,IAAA0E,EAAAC,EACZlE,OAAlBiE,EAAA9E,EAAQ0D,kBAAU7C,EAAAA,EAAAA,aAAU8D,oBACrB3E,EAAQ0D,QAChB,KAAM,CAAA,IAAAsB,EAAAC,EAAAC,EACL,OAAAF,EAAAhF,EAAQ1F,aAAoBuG,OAA5BoE,EAAAD,EAAqB5E,YAAOS,EAAAA,EAAAA,aAAU8D,oBAC/B3E,EAAQ1F,WAAW8F,EAC3B,CACF,CAoBG+E,CAA4BhF,EAAIC,GAElCwB,EAAEtB,SAASH,EAAIqB,EANC,CAOjB,CAED,SAASlB,EAASsB,EAAkBzB,GAClCyB,EAAEE,MAAQyC,EAA6BpE,EAAI,OAAQyB,EAAEE,MACrDF,EAAEoC,SAAWO,EAAkCpE,EAAI,QAASyB,EAAEoC,SAC9DpC,EAAE8B,UAAYa,EAAiCpE,EAAI,WAAYyB,EAAE8B,UACjE5K,OAAO4B,KAAKkH,EAAEtH,YAAYhB,SAAQ,SAAI8G,GACpCmE,EAAkCpE,EAAIC,EAAMwB,EAAEtH,WAAW8F,MAE5D,CAED,SAASsB,IACP7B,EAASvG,QAAQgH,EAClB,CAsCD,SAAS8E,EAAmBC,GAG1B,GAAsB,aAAlBA,EAASC,MAAkD,IAA3BD,EAASxF,SAAS9D,KAAtD,CAEA,IAAMwJ,EAAmB,IAAI5M,IAAI0M,EAASxF,UACjBrI,SAASgO,iBAAiBH,EAASI,UAE3CnM,SAAQ,SAAE6G,GACpBoF,EAAiB3I,IAAIuD,KACxBkF,EAASxF,SAAS3B,IAAIiC,GA7C5B,SAAuBkF,EAAoBrF,GACzC,IAAIC,EAAiD,KAC/B,SAAlBoF,EAASC,KACXrF,EAASgD,EAAqBjD,GACH,UAAlBqF,EAASC,KAClBrF,EAAS8D,EAAsB/D,GACJ,cAAlBqF,EAASC,KAClBrF,EAASgE,EAA0BjE,EAASqF,EAASK,WAC1B,aAAlBL,EAASC,OAClBrF,EAASwD,EAAyBzD,IAE/BC,IACLA,EAAOH,UAAU6F,KAAKN,GACtBpF,EAAOM,eAAeN,GACvB,CAgCK2F,CAAcP,EAAUlF,MARsC,CAWnE,CAQD,SAAS0F,KACP/F,EAAUxG,QAAQ8L,EACnB,CA4BD,SAASU,GAAYlE,GAEnB,MAAwB,oBAAbpK,SAAiCmI,GAE5CG,EAAU5B,IAAI0D,GAEdwD,EAAmBxD,GACZ,CACLhC,OAAQ,WA5CZ,IAAwByF,KA6CHzD,GA5CV/B,SAASvG,SAAQ,SAAE6G,GAAA,OAnC9B,SAAsBkF,EAAoBlF,GACxC,IAAIF,EAAiD,KAUrD,GATsB,SAAlBoF,EAASC,KACXrF,EAASgD,EAAqB9C,GACH,UAAlBkF,EAASC,KAClBrF,EAAS8D,EAAsB5D,GACJ,cAAlBkF,EAASC,KAClBrF,EAASgE,EAA0B9D,EAAIkF,EAASK,WACrB,aAAlBL,EAASC,OAClBrF,EAASwD,EAAyBtD,IAE/BF,EAAL,CACA,IAAM8F,EAAQ9F,EAAOH,UAAUkG,QAAQX,IACxB,IAAXU,GAAc9F,EAAOH,UAAUmG,OAAOF,EAAO,GACjD9F,EAAOM,eAAeN,EAHT,CAId,CAoBiCiG,CAAab,EAAUlF,MACvDkF,EAASxF,SAASzG,QAClB0G,EAAS,OAAQuF,EA2Cd,GAEJ,CAED,SAASvD,GACP2D,EACA5D,GAEA,OAAOiE,GAAY,CACjBR,KAAM,OACNzF,SAAU,IAAIlH,IACdkJ,OAAAA,EACA4D,SAAAA,GAEH,CAcD,SAASzB,GACPyB,EACA5D,GAEA,OAAOiE,GAAY,CACjBR,KAAM,QACNzF,SAAU,IAAIlH,IACdkJ,OAAAA,EACA4D,SAAAA,GAEH,CAED,SAASC,GACPD,EACAC,EACA7D,GAEA,OAAKnC,EAAmByG,KAAKT,GAEX,UAAdA,GAAuC,cAAdA,EACpB1B,GAAQyB,GAAU,SAAUW,GACjC,IAAMC,EAAoBxE,EAAOjI,MAAMC,KAAKuM,GAAY7D,KAAK,MAC7D6D,EAAWhN,QACNiN,GACLA,EACGjE,MAAM,QACNC,OAAOC,SACPhJ,SAAQ,SAACgN,GAAA,OAAIF,EAAWlI,IAAIoI,KAChC,IAGIR,GAAY,CACjBR,KAAM,YACNI,UAAAA,EACA7F,SAAU,IAAIlH,IACdkJ,OAAAA,EACA4D,SAAAA,IAnB8C9F,CAqBjD,CCxcD,SAAS4G,GAAWC,GAClB,IAAIC,EAAO,WACX,MAAMC,EAAIF,EAAIhC,OAEd,IAAK,IAAIxI,EAAI,EAAGA,EAAI0K,EAAG1K,IACrByK,GAAQD,EAAIG,WAAW3K,GACvByK,IACGA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAErE,OAAOA,IAAS,CAClB,CAEO,SAASG,GACdC,EACAxL,EACAe,GAGA,OAAgB,IAAZA,EACMmK,GAAWA,GAAWM,EAAOxL,GAAS,IAAM,IAAS,IAG/C,IAAZe,EACMmK,GAAWlL,EAAQwL,GAAQ,IAAQ,IAItC,IACT,CAOO,SAASC,GAAQC,EAAWC,GACjC,OAAOD,GAAKC,EAAM,IAAMD,EAAIC,EAAM,EACpC,CAoBO,SAASC,GAAaC,GAC3B,IACE,MAAMC,EAAUD,EAAYE,QAAQ,aAAc,SAClD,OAAO,IAAIC,OAAOF,EAIpB,CAHE,MAAOhP,GAEP,YADAmP,QAAQC,MAAMpP,EAEhB,CACF,CAEO,SAASqP,GAAczM,EAAa0M,GACzC,IAAKA,EAAQjD,OAAQ,OAAO,EAC5B,IAAIkD,GAAkB,EAClBC,GAAa,EAEjB,IAAK,IAAI3L,EAAI,EAAGA,EAAIyL,EAAQjD,OAAQxI,IAAK,CACvC,MAAM4L,EAAQC,GAAe9M,EAAK0M,EAAQzL,GAAG6C,KAAM4I,EAAQzL,GAAG8L,SAC9D,IAA2B,IAAvBL,EAAQzL,GAAG+L,SACb,GAAIH,EAAO,OAAO,OAElBF,GAAkB,EACdE,IAAOD,GAAa,EAE5B,CAEA,OAAOA,IAAeD,CACxB,CAyDA,SAASG,GACP9M,EACA8D,EACAiJ,GAEA,IACE,MAAME,EAAS,IAAIC,IAAIlN,EAAK,aAE5B,GAAa,UAAT8D,EAAkB,CACpB,MAAMqJ,EAAQjB,GAAaa,GAC3B,QAAKI,IAEHA,EAAM/B,KAAK6B,EAAOG,OAClBD,EAAM/B,KAAK6B,EAAOG,KAAKC,UAAUJ,EAAOK,OAAO7D,SAEnD,CAAO,MAAa,WAAT3F,GA/Cf,SAA8ByJ,EAAaR,GACzC,IAGE,MAAMS,EAAW,IAAIN,IACnBH,EAAQV,QAAQ,gBAAiB,eAAeA,QAAQ,MAAO,SAC/D,iBAIIoB,EAA0C,CAC9C,CAACF,EAAO/R,KAAMgS,EAAShS,MAAM,GAC7B,CAAC+R,EAAOG,SAAUF,EAASE,UAAU,IAYvC,OATIF,EAAS3B,MACX4B,EAAM7C,KAAK,CAAC2C,EAAO1B,KAAM2B,EAAS3B,MAAM,IAG1C2B,EAASG,aAAapP,SAAQ,CAACqP,EAAGC,KAChCJ,EAAM7C,KAAK,CAAC2C,EAAOI,aAAajM,IAAImM,IAAM,GAAID,GAAG,GAAO,KAIlDH,EAAMK,MACX1M,IAhDP,SACEmM,EACAR,EACAgB,GAEA,IAEE,IAAI3B,EAAUW,EACXV,QAAQ,sBAAuB,QAC/BA,QAAQ,SAAU,MAQrB,OANI0B,IAEF3B,EAAU,OAASA,EAAQC,QAAQ,aAAc,IAAM,QAG3C,IAAIC,OAAO,IAAMF,EAAU,IAAK,KACjChB,KAAKmC,EAGpB,CAFE,MAAOnQ,GACP,OAAO,CACT,CACF,CA2BiB4Q,CAAmB5M,EAAK,GAAIA,EAAK,GAAIA,EAAK,KAIzD,CAFE,MAAOhE,GACP,OAAO,CACT,CACF,CAkBa6Q,CAAqBhB,EAAQF,EAMxC,CAFE,MAAO3P,GACP,OAAO,CACT,CACF,CDqM0B,oBAAbX,WAENqJ,IACHA,EAAW,IAAIC,kBAAiB,WAC9B+E,IACD,KAGHA,KACAhF,EAASI,QAAQzJ,SAASyR,gBAAiB,CACzC/H,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ8G,eAAe,KC1HnB,MAAM8H,GAAexN,GACnByN,WAAWtP,KAAKuP,KAAK1N,IAAK4K,GAAMA,EAAEK,WAAW,KAExCzN,eAAemQ,GACpBC,EACAC,EACArT,GAIA,GAFAqT,EAAgBA,GAAiB,KACjCrT,EAASA,GAAWL,WAAWI,QAAUJ,WAAWI,OAAOC,QAEzD,MAAM,IAAIsT,MAAM,wCAElB,IACE,MAAM5O,QAAY1E,EAAOuT,UACvB,MACAP,GAAYK,GACZ,CAAEG,KAAM,UAAWlF,OAAQ,MAC3B,EACA,CAAC,UAAW,aAEPmF,EAAIC,GAAcN,EAAgBlH,MAAM,KACzCyH,QAAwB3T,EAAOmT,QACnC,CAAEK,KAAM,UAAWC,GAAIT,GAAYS,IACnC/O,EACAsO,GAAYU,IAGd,OAAO,IAAIE,aAAcC,OAAOF,EAGlC,CAFE,MAAO1R,GACP,MAAM,IAAIqR,MAAM,oBAClB,CACF,CAGO,SAASQ,GAASC,GACvB,MAAqB,iBAAVA,EAA2BA,EAC/BhT,KAAKC,UAAU+S,EACxB,CAGO,SAASC,GAAoBD,GACb,iBAAVA,IACTA,GAAgB,IAEbA,GAA0B,iBAAVA,IACnBA,EAAQ,KAKV,MAAME,EAASF,EAAiB7C,QAAQ,cAAe,IAAIhF,MAAM,QAWjE,OANqB,IAAjB+H,EAAM3F,QACR2F,EAAMxE,KAAK,KAKNwE,EACJhP,KAAKwN,GAAOA,EAAEf,MAAM,YAAce,EAAEyB,SAAS,EAAG,KAAOzB,IACvDpG,KAAK,IACV,CClTA,MAAM8H,GAAyC,CAAA,EAGxC,SAASC,GACdC,EACAC,GAGA,GAAI,QAASA,EACX,OAAOC,GAAOF,EAAKC,EAAe,KAEpC,GAAI,SAAUA,EACZ,OAAQC,GAAOF,EAAKC,EAAgB,MAEtC,GAAI,SAAUA,EACZ,OAsMJ,SAAiBD,EAAgBG,GAC/B,IAAK,IAAI1O,EAAI,EAAGA,EAAI0O,EAAWlG,OAAQxI,IACrC,IAAKsO,GAAcC,EAAKG,EAAW1O,IACjC,OAAO,EAGX,OAAO,CACT,CA7MW2O,CAAQJ,EAAKC,EAAgB,MAEtC,GAAI,SAAUA,EACZ,OAAQF,GAAcC,EAAKC,EAAgB,MAI7C,IAAK,MAAO5B,EAAGD,KAAM7P,OAAOgB,QAAQ0Q,GAClC,IAAKI,GAAmBjC,EAAGkC,GAAQN,EAAK3B,IAAK,OAAO,EAEtD,OAAO,CACT,CAGA,SAASiC,GAAQN,EAAgBO,GAC/B,MAAMX,EAAQW,EAAK1I,MAAM,KACzB,IAAI2I,EAAeR,EACnB,IAAK,IAAIvO,EAAI,EAAGA,EAAImO,EAAM3F,OAAQxI,IAAK,CACrC,IAAI+O,GAA8B,iBAAZA,KAAwBZ,EAAMnO,KAAM+O,GAGxD,OAAO,KAFPA,EAAUA,EAAQZ,EAAMnO,GAI5B,CACA,OAAO+O,CACT,CAWA,SAASH,GAAmBJ,EAA2BnP,GAErD,GAAyB,iBAAdmP,EACT,OAAOnP,EAAQ,KAAOmP,EAExB,GAAyB,iBAAdA,EACT,OAAe,EAARnP,IAAcmP,EAEvB,GAAyB,kBAAdA,EACT,QAASnP,IAAUmP,EAGrB,GAAkB,OAAdA,EACF,OAAiB,OAAVnP,EAGT,GAAIzB,MAAMoR,QAAQR,KAAeS,GAAiBT,GAChD,OAAOvT,KAAKC,UAAUmE,KAAWpE,KAAKC,UAAUsT,GAIlD,IAAK,MAAMU,KAAMV,EACf,IACGW,GACCD,EACA7P,EACAmP,EAAUU,IAGZ,OAAO,EAGX,OAAO,CACT,CAGA,SAASD,GAAiBV,GACxB,MAAM7P,EAAO5B,OAAO4B,KAAK6P,GACzB,OACE7P,EAAK8J,OAAS,GAAK9J,EAAK2H,QAAQuG,GAAe,MAATA,EAAE,KAAYpE,SAAW9J,EAAK8J,MAExE,CA2BA,SAAS4G,GAAK9C,EAAaC,GAEzB,OAAI3O,MAAMoR,QAAQ1C,GACTA,EAAOO,MAAM1I,GAAOoI,EAASlJ,SAASc,KAExCoI,EAASlJ,SAASiJ,EAC3B,CAGA,SAAS6C,GACPE,EACA/C,EACAC,GAEA,OAAQ8C,GACN,IAAK,OACH,OAAOnB,GAAoB5B,KAAY4B,GAAoB3B,GAC7D,IAAK,OACH,OAAO2B,GAAoB5B,KAAY4B,GAAoB3B,GAC7D,IAAK,OACH,OAAO2B,GAAoB5B,GAAU4B,GAAoB3B,GAC3D,IAAK,QACH,OAAO2B,GAAoB5B,IAAW4B,GAAoB3B,GAC5D,IAAK,OACH,OAAO2B,GAAoB5B,GAAU4B,GAAoB3B,GAC3D,IAAK,QACH,OAAO2B,GAAoB5B,IAAW4B,GAAoB3B,GAC5D,IAAK,MACH,OAAOD,IAAWC,EACpB,IAAK,MACH,OAAOD,IAAWC,EACpB,IAAK,MACH,OAAOD,EAASC,EAClB,IAAK,OACH,OAAOD,GAAUC,EACnB,IAAK,MACH,OAAOD,EAASC,EAClB,IAAK,OACH,OAAOD,GAAUC,EACnB,IAAK,UAEH,OAAOA,EAAqB,MAAVD,EAA2B,MAAVA,EACrC,IAAK,MACH,QAAK1O,MAAMoR,QAAQzC,IACZ6C,GAAK9C,EAAQC,GACtB,IAAK,OACH,QAAK3O,MAAMoR,QAAQzC,KACX6C,GAAK9C,EAAQC,GACvB,IAAK,OACH,OAAQqC,GAAmBrC,EAAUD,GACvC,IAAK,QACH,QAAK1O,MAAMoR,QAAQ1C,IACZsC,GAAmBrC,EAAUD,EAAO9D,QAC7C,IAAK,aACH,OAnEN,SAAmB8D,EAAaC,GAC9B,IAAK3O,MAAMoR,QAAQ1C,GAAS,OAAO,EACnC,MAAMgD,EAAQL,GAAiB1C,GAC1BI,GAAWiC,GAAmBrC,EAAUI,GACxCA,GAAW2B,GAAc3B,EAAGJ,GACjC,IAAK,IAAIvM,EAAI,EAAGA,EAAIsM,EAAO9D,OAAQxI,IACjC,GAAIsM,EAAOtM,IAAMsP,EAAMhD,EAAOtM,IAC5B,OAAO,EAGX,OAAO,CACT,CAwDauP,CAAUjD,EAAQC,GAC3B,IAAK,OACH,IAAK3O,MAAMoR,QAAQ1C,GAAS,OAAO,EACnC,IAAK,IAAItM,EAAI,EAAGA,EAAIuM,EAAS/D,OAAQxI,IAAK,CACxC,IAAIwP,GAAS,EACb,IAAK,IAAIC,EAAI,EAAGA,EAAInD,EAAO9D,OAAQiH,IACjC,GAAIb,GAAmBrC,EAASvM,GAAIsM,EAAOmD,IAAK,CAC9CD,GAAS,EACT,KACF,CAEF,IAAKA,EAAQ,OAAO,CACtB,CACA,OAAO,EACT,IAAK,SACH,IACE,OAlJUtD,EAkJMK,EAjJjB8B,GAAYnC,KACfmC,GAAYnC,GAAS,IAAIb,OAAOa,EAAMd,QAAQ,aAAc,WAEvDiD,GAAYnC,IA8Ia/B,KAAKmC,EAGjC,CAFE,MAAOnQ,GACP,OAAO,CACT,CACF,IAAK,QACH,OAnGN,SAAiBwQ,GACf,GAAU,OAANA,EAAY,MAAO,OACvB,GAAI/O,MAAMoR,QAAQrC,GAAI,MAAO,QAC7B,MAAM+C,SAAW/C,EACjB,MAAI,CAAC,SAAU,SAAU,UAAW,SAAU,aAAatJ,SAASqM,GAC3DA,EAEF,SACT,CA2FaC,CAAQrD,KAAYC,EAC7B,QAEE,OADAjB,QAAQC,MAAM,qBAAuB8D,IAC9B,EA1Jb,IAAkBnD,CA4JlB,CAGA,SAASuC,GAAOF,EAAgBG,GAC9B,IAAKA,EAAWlG,OAAQ,OAAO,EAC/B,IAAK,IAAIxI,EAAI,EAAGA,EAAI0O,EAAWlG,OAAQxI,IACrC,GAAIsO,GAAcC,EAAKG,EAAW1O,IAChC,OAAO,EAGX,OAAO,CACT,CCjLA,MAAM4P,GACc,oBAAXrU,QAA8C,oBAAbC,SAEpCqU,GFgRC,WACL,IAAIzP,EACJ,IAEEA,EAAyB,QAG3B,CAFE,MAAOjE,GACPiE,EAAU,EACZ,CACA,OAAOA,CACT,CEzRoB0P,GAEb,MAAMC,GAsCXC,YAAYC,GAqBV,GApBAA,EAAUA,GAAW,GAGrBC,KAAK9P,QAAUyP,GACfK,KAAKC,EAAOD,KAAKD,QAAUA,EAC3BC,KAAKE,EAAY,KACjBF,KAAKG,EAAsB,IAAI1T,IAC/BuT,KAAKI,EAAmB,GACxBJ,KAAKK,OAAQ,EACbL,KAAKM,EAAiB,IAAI7T,IAC1BuT,KAAKO,EAAW,GAChBP,KAAKQ,EAAW,EAChBR,KAAKS,OAAQ,EACbT,KAAKU,EAAY,IAAIvU,IACrB6T,KAAKW,EAAuB,IAAIxU,IAChC6T,KAAKY,EAAsB,GAC3BZ,KAAKa,EAAyB,IAAI1U,IAClC6T,KAAKc,EAAoB,IAAIrU,IAC7BuT,KAAKe,GAAsB,EAEvBhB,EAAQtO,WAAY,CACtB,GAAIsO,EAAQ1C,cACV,MAAM,IAAIC,MAAM,8CAElB,IAAKyC,EAAQzV,UACX,MAAM,IAAIgT,MAAM,qBAElB,IAAI0D,GAAW,EACf,IACEA,IAAa,IAAIjF,IAAIgE,EAAQhS,SAAW,IAAIkT,SAASvF,MACnD,mBAGF,CADA,MAAOzP,GACP,CAEF,GAAI+U,EACF,MAAM,IAAI1D,MAAM,4CAEpB,MACE,GAAIyC,EAAQzR,mBACV,MAAM,IAAIgP,MAAM,mDAIhByC,EAAQ5O,WACV6O,KAAKS,OAAQ,GAGXf,IAAaK,EAAQmB,gBACvB7V,OAAO8V,YAAcnB,KACrB1U,SAAS8V,cAAc,IAAIC,MAAM,cAG/BtB,EAAQ/O,cACVgP,KAAKS,OAAQ,EACbT,KAAKsB,KAGHvB,EAAQzV,YAAcyV,EAAQtO,YAChCuO,KAAKuB,EAAS,CAAA,GAAI,GAAM,EAE5B,CAEAvU,mBAA0BpC,GACpBA,GAAWA,EAAQ4W,cAErBxB,KAAKC,EAAKwB,oBAAqB,GAEjCzB,KAAKe,GAAsB,QAErBf,KAAKuB,EAAS3W,GAAS,GAAM,GAE/BoV,KAAK0B,KJXN,SAAmB5T,GACxB,MAAMY,EAAMb,EAAOC,GACb6T,EAAOzV,EAAoBqE,IAAI7B,IAAQ,IAAIjC,IACjDkV,EAAK3P,IAAIlE,GACT5B,EAAoBsE,IAAI9B,EAAKiT,EAC/B,CIOMC,CAAU5B,KAEd,CAEAhT,sBACEpC,SAEMoV,KAAKuB,EAAS3W,GAAS,GAAO,EACtC,CAEOoD,aACL,MAAO,CAACgS,KAAKzO,cAAcxD,QAASiS,KAAKxO,eAC3C,CACOD,cAML,MAAMsQ,EAAc7B,KAAKC,EAAKlS,SAAW,4BACzC,MAAO,CACLA,QAAS8T,EAAY3G,QAAQ,OAAQ,IACrC5I,eAAgB0N,KAAKC,EAAK3N,eAAiBuP,GAAa3G,QACtD,OACA,IAEF5J,kBAAmB0O,KAAKC,EAAK6B,sBAC7BvP,4BAA6ByN,KAAKC,EAAK1N,4BAE3C,CACOf,eACL,OAAOwO,KAAKC,EAAK3V,WAAa,EAChC,CAEO6D,eACL,OAAO6R,KAAKC,EAAKxO,aAAc,CACjC,CAEOlD,wBACL,OAAOyR,KAAKC,EAAK3R,kBACnB,CAEAtB,QACEpC,EACAmX,EACAC,GAGA,GADApX,EAAUA,GAAW,IAChBoV,KAAKC,EAAK3V,UACb,MAAM,IAAIgT,MAAM,2BJnFftQ,eACLc,EACAmU,EACAC,EACAH,EACAC,EACA3Y,GAEKA,IACHJ,EAAcI,gBAAiB,GAGjC,MAAM4G,QAkDRjD,eACEc,EACAiU,EACAE,EACAC,GAEA,MAAMxT,EAAMb,EAAOC,GACb1E,EAAW6E,EAAYH,GACvBuC,EAAM,IAAID,KAEV+R,EAAa,IAAI/R,KACrBC,EAAIhB,UAAYpG,EAAcE,OAASF,EAAcC,gBAiFzD8D,iBACE,IAAIZ,EAAJ,CACAA,GAAmB,EACnB,IACE,GAAI3C,EAAUuC,aAAc,CAC1B,MAAMmD,QAAc1F,EAAUuC,aAAaoW,QACzCnZ,EAAcG,UAEhB,GAAI+F,EAAO,CACT,MAAM2M,EAAiC/Q,KAAK6H,MAAMzD,GAC9C2M,GAAUpO,MAAMoR,QAAQhD,IAC1BA,EAAO1O,SAAQiV,IAAiB,IAAf3T,EAAKuB,GAAKoS,EACzBhW,EAAMmE,IAAI9B,EAAK,IACVuB,EACHb,QAAS,IAAIgB,KAAKH,EAAKb,UACvB,IAGNL,GACF,CACF,CAEA,CADA,MAAO9C,GACP,CAEF,IAAKhD,EAAcM,mBAAoB,CACrC,MAAM+Y,EAAYpY,EAAQiB,oBACtBmX,IACFpY,EAAQ6B,iBAAmBuW,EAE/B,CA5BsB,CA6BxB,CA5GQC,GACN,MAAMjS,EAAWjE,EAAMkE,IAAInH,GAC3B,OACEkH,IACC4R,IACAH,GAAczR,EAASlB,QAAUiB,IAClCC,EAASlB,QAAU+S,GAGf7R,EAASG,KAAKjE,EAAYwF,IAAItD,GAG9B4B,EAASlB,QAAUiB,EACrBgB,EAAcvD,GAIdoE,EAAiBpE,GAEZwC,EAASL,YAoCpB,SACEyB,EACAuQ,GAEA,OAAO,IAAI7P,SAASC,IAClB,IACImQ,EADAC,GAAW,EAEf,MAAMC,EAAUzS,IACVwS,IACJA,GAAW,EACXD,GAAS/W,aAAa+W,GACtBnQ,EAAQpC,GAAQ,MAAK,EAGnBgS,IACFO,EAAQ7W,YAAW,IAAM+W,KAAUT,IAGrCvQ,EAAQI,MAAM7B,GAASyS,EAAOzS,KAAOkC,OAAM,IAAMuQ,KAAS,GAE9D,CAtDiBC,CAAetR,EAAcvD,GAAWmU,EAEzD,CAvFqBW,CACjB9U,EACAiU,EACAE,EACAC,GAEFF,GAAkB/R,SAAeW,EAAgB9C,EAAUmC,EAC7D,CIkEU4S,CACJ7C,KACApV,EAAQqX,QACRrX,EAAQsX,WAAalC,KAAKC,EAAKiB,cAC/Ba,EACAC,GAC6B,IAA7BhC,KAAKC,EAAK5W,eAEd,CAEQyZ,IACF9C,KAAKE,GACPF,KAAKE,GAET,CAEOhP,YAAYC,GACjB6O,KAAKC,EAAK9O,SAAWA,EACrB6O,KAAKS,OAAQ,EACbT,KAAK8C,GACP,CAEA9V,2BACEoQ,EACAC,EACArT,GAEA,MAAM+Y,QAAqB5F,GACzBC,EACAC,GAAiB2C,KAAKC,EAAK5C,cAC3BrT,GAEFgW,KAAK9O,YACHnG,KAAK6H,MAAMmQ,GAEf,CAEOhS,eAAeC,GACpBgP,KAAKC,EAAKjP,YAAcA,EACxBgP,KAAKS,OAAQ,EACbT,KAAKsB,GACP,CAEAtU,8BACEoQ,EACAC,EACArT,GAEA,MAAMgZ,QAAwB7F,GAC5BC,EACAC,GAAiB2C,KAAKC,EAAK5C,cAC3BrT,GAEFgW,KAAKjP,eAAehG,KAAK6H,MAAMoQ,GACjC,CAEAhW,qBACEiD,EACAoN,EACArT,GAsBA,OApBIiG,EAAKgT,oBACPhT,EAAKkB,SAAWpG,KAAK6H,YACbuK,GACJlN,EAAKgT,kBACL5F,GAAiB2C,KAAKC,EAAK5C,cAC3BrT,WAGGiG,EAAKgT,mBAEVhT,EAAKiT,uBACPjT,EAAKe,YAAcjG,KAAK6H,YAChBuK,GACJlN,EAAKiT,qBACL7F,GAAiB2C,KAAKC,EAAK5C,cAC3BrT,WAGGiG,EAAKiT,sBAEPjT,CACT,CAEAjD,oBAA2BoB,GACzB4R,KAAKC,EAAK7R,WAAaA,EACnB4R,KAAKC,EAAKkD,2BACNnD,KAAKlP,uBAETkP,KAAKC,EAAKxO,iBACNuO,KAAKoD,KAGbpD,KAAK8C,IACL9C,KAAKsB,IACP,CAEAtU,4BAAmCL,GACjCqT,KAAKY,EAAsBjU,EACvBqT,KAAKC,EAAKkD,2BACNnD,KAAKlP,uBAETkP,KAAKC,EAAKxO,iBACNuO,KAAKoD,KAGbpD,KAAK8C,IACL9C,KAAKsB,IACP,CAEAtU,0BAAiCqW,GAC/BrD,KAAKC,EAAKtO,iBAAmB0R,GAAQ,CAAA,EACjCrD,KAAKC,EAAKxO,iBACNuO,KAAKoD,KAGbpD,KAAK8C,IACL9C,KAAKsB,IACP,CAGOgC,kBAAkBrU,GACvB+Q,KAAKW,EAAuB1R,EAC5B+Q,KAAK8C,GACP,CAEA9V,aAAoB6B,GAElB,GADAmR,KAAKC,EAAKpR,IAAMA,EACZmR,KAAKC,EAAKxO,WAGZ,aAFMuO,KAAKoD,SACXpD,KAAKsB,GAA0B,GAGjCtB,KAAKsB,GAA0B,EACjC,CAEOjT,gBACL,MAAO,IAAK2R,KAAKC,EAAK7R,cAAe4R,KAAKY,EAC5C,CAEOhS,sBACL,OAAOoR,KAAKC,EAAKtO,kBAAoB,EACvC,CAEOE,oBAEL,OAAOmO,KAAKW,GAAwB,IAAIxU,GAC1C,CAEOoX,gCACL,OAAOvD,KAAKC,EAAKuD,4BAA8B,EACjD,CAEO1U,SACL,OAAOkR,KAAKC,EAAKpR,KAAO,EAC1B,CAEOuC,cACL,OAAO4O,KAAKC,EAAK9O,UAAY,EAC/B,CAEOF,iBACL,OAAO+O,KAAKC,EAAKjP,aAAe,EAClC,CAEO4Q,UAAUnP,GAEf,OADAuN,KAAKM,EAAetO,IAAIS,GACjB,KACLuN,KAAKM,EAAevQ,OAAO0C,EAAG,CAElC,CAEQiP,IACN,OAAoC,IAA7B1B,KAAKC,EAAK5W,gBAA4B2W,KAAKC,EAAKwB,kBACzD,CAEAzU,UACOgT,KAAKC,EAAKxO,YACVuO,KAAKe,SACJf,KAAKuB,EAAS,CAAE,GAAE,GAAO,GAAMpP,OAAM,QAG7C,CAEOsR,gBACL,OAAO,IAAItX,IAAI6T,KAAKU,EACtB,CAEOgD,UJrPF,IAAqB5V,EIuPxBkS,KAAKM,EAAepT,QACpB8S,KAAKU,EAAUxT,QACf8S,KAAKG,EAAoBjT,QACzB8S,KAAKI,EAAmB,GACxBJ,KAAKO,EAAW,GACZP,KAAKQ,GACP/U,aAAauU,KAAKQ,GJ7PI1S,EI+PZkS,KJ9Pd9T,EAAoBkB,SAASuW,GAAMA,EAAE5T,OAAOjC,KIgQtC4R,IAAarU,OAAO8V,cAAgBnB,aAC/B3U,OAAO8V,YAIhBnB,KAAKa,EAAuBzT,SAASwW,IACnCA,EAAIC,MAAM,IAEZ7D,KAAKa,EAAuB3T,QAC5B8S,KAAKc,EAAkB5T,OACzB,CAEO4W,YAAYC,GACjB/D,KAAKE,EAAY6D,CACnB,CAEOC,eAAetV,EAAauV,GACjCjE,KAAKC,EAAKtO,iBAAmBqO,KAAKC,EAAKtO,kBAAoB,GAC3DqO,KAAKC,EAAKtO,iBAAiBjD,GAAOuV,EAC9BjE,KAAKC,EAAKxO,WACZuO,KAAKoD,KAGPpD,KAAKsB,IACLtB,KAAK8C,IACP,CAEOoB,IAAOC,GACZ,MAAMC,EAASpE,KAAKqE,EAAKF,EAAY,MAErC,OADAnE,KAAKsE,EAAmBH,EAAYC,GAC7BA,CACT,CAEOG,kBAAkB7V,GAEvB,OADAsR,KAAKc,EAAkB9O,IAAItD,GACtBsR,KAAKC,EAAKjP,YACKgP,KAAKC,EAAKjP,YAAYmF,QAAQyN,GAAQA,EAAIlV,MAAQA,IAEnEO,KAAK2U,GACCA,EAAIY,OACFxE,KAAKyE,EAAmBb,GADP,OAGzBzN,QAAQpE,GAAgB,OAARA,IAPgB,IAQrC,CAEQ0S,EAAmBN,EAA4BO,GACrD,MAAMpU,EAAW0P,KAAKa,EAAuBtQ,IAAI4T,GAGjD,GACEA,EAAWK,SACVxE,KAAKc,EAAkBpQ,IAAIyT,EAAWzV,OACtC4B,EAED,OAAO,KAGT,MAAM8T,EAASpE,KAAKkE,IAAIC,GAGlBQ,EAAY5Z,KAAKC,UAAUoZ,EAAOjV,OAGxC,IACGuV,GACDN,EAAOQ,cACPtU,GACAA,EAASqU,YAAcA,EAEvB,OAAOP,EAOT,GAHI9T,GAAU0P,KAAK6E,EAA0BV,GAGzCC,EAAOQ,aAAc,CACvB,MAAMf,EAAO7D,KAAK8E,EAAiBV,EAAOjV,OACtC0U,GACF7D,KAAKa,EAAuBrQ,IAAI2T,EAAY,CAC1CN,OACAc,aAGN,CAEA,OAAOP,CACT,CAEQS,EAA0BjB,GAChC,MAAM3T,EAAO+P,KAAKa,EAAuBtQ,IAAIqT,GACzC3T,IACFA,EAAK4T,OACL7D,KAAKa,EAAuB9Q,OAAO6T,GAEvC,CAEQtC,EAA0BoD,GAChC,MAAM1T,EAAcgP,KAAKC,EAAKjP,aAAe,GAGvCxC,EAAO,IAAI/B,IAAIuE,GACrBgP,KAAKa,EAAuBzT,SAAQ,CAACqP,EAAGC,KACjClO,EAAKkC,IAAIgM,KACZD,EAAEoH,OACF7D,KAAKa,EAAuB9Q,OAAO2M,GACrC,IAIF1L,EAAY5D,SAASwW,IACnB5D,KAAKyE,EAAmBb,EAAKc,EAAW,GAE5C,CAEQJ,EAAsBH,EAA2BC,GACvD,MAAM1V,EAAMyV,EAAWzV,IAGjBqW,EAAO/E,KAAKU,EAAUnQ,IAAI7B,GAG7BqW,GACDA,EAAKX,OAAOQ,eAAiBR,EAAOQ,cACpCG,EAAKX,OAAOY,cAAgBZ,EAAOY,cAEnChF,KAAKU,EAAUlQ,IAAI9B,EAAK,CAAEyV,aAAYC,WACtCpE,KAAKM,EAAelT,SAASqF,IAC3B,IACEA,EAAG0R,EAAYC,EAGjB,CAFE,MAAOnY,GACPmP,QAAQC,MAAMpP,EAChB,KAGN,CAEQgZ,EAAmBvW,EAAaqD,GAEtC,GAAmB,aAAfA,EAAImT,OAAuB,OAG/B,MAAMC,EAAmBpa,KAAKC,UAAU+G,EAAI5C,OAC5C,GAAI6Q,KAAKI,EAAiB1R,KAASyW,EAAnC,CAIA,GAHAnF,KAAKI,EAAiB1R,GAAOyW,EAGzBnF,KAAKC,EAAKmF,eACZ,IACEpF,KAAKC,EAAKmF,eAAe1W,EAAKqD,EAE9B,CADA,MAAO9F,GACP,CAKCyT,IAAcrU,OAAO3B,QAC1BsW,KAAKO,EAAS9G,KAAK,CACjB/K,MACA2W,GAAItT,EAAIsT,KAELrF,KAAKQ,IACRR,KAAKQ,EAAWnV,OAAOM,YAAW,KAEhCqU,KAAKQ,EAAW,EAChB,MAAM8E,EAAI,IAAItF,KAAKO,GACnBP,KAAKO,EAAW,GAGXP,KAAKC,EAAKsF,aAEfla,OACG3B,MAAK,iCAAAc,OAEFwV,KAAKC,EAAKsF,YAAW,YAAA/a,OACZgb,mBAAmBza,KAAKC,UAAUsa,KAE7C,CACEjZ,MAAO,WACPoZ,KAAM,YAGTtT,OAAM,QAEL,GACH6N,KAAKC,EAAKyF,kBAAoB,MA1CkB,CA4CvD,CAEQC,EACNjX,EACAS,EACA+V,EACAU,EACAzB,EACAC,GAEA,MAAMyB,EAAqB,CACzB1W,QACAkW,KAAMlW,EACN2W,KAAM3W,EACN+V,SACAU,OAAQA,GAAU,IAQpB,OANIzB,IAAY0B,EAAI1B,WAAaA,GAC7BC,IAAQyB,EAAIE,iBAAmB3B,GAGnCpE,KAAKiF,EAAmBvW,EAAKmX,GAEtBA,CACT,CAEOG,KAAoDtX,GACzD,OAAOsR,KAAKiG,YAAYvX,GAAK2W,EAC/B,CAEOa,MAAqDxX,GAC1D,OAAOsR,KAAKiG,YAAYvX,GAAKoX,GAC/B,CAEOK,gBAGLzX,EAAQ0X,GACR,MAAMjX,EAAQ6Q,KAAKiG,YAAmCvX,GAAKS,MAC3D,OAAiB,OAAVA,EAAkBiX,EAAsCjX,CACjE,CAOOkX,QAGLC,GACA,OAAOtG,KAAKiG,YAAYK,EAC1B,CAEOL,YAGLK,GAEA,GAAItG,KAAKW,EAAqBjQ,IAAI4V,GAMhC,OAAOtG,KAAK2F,EACVW,EACAtG,KAAKW,EAAqBpQ,IAAI+V,GAC9B,YAKJ,IAAKtG,KAAKC,EAAK9O,WAAa6O,KAAKC,EAAK9O,SAASmV,GAG7C,OAAOtG,KAAK2F,EAAkBW,EAAI,KAAM,kBAI1C,MAAMD,EAAgCrG,KAAKC,EAAK9O,SAASmV,GAGzD,GAAID,EAAQE,MACV,IAAK,MAAMC,KAAQH,EAAQE,MAAO,CAEhC,GAAIC,EAAKC,SAAWzG,KAAK0G,EAAeF,EAAKC,SAM3C,SAIF,GAAI,UAAWD,EAAM,CAEnB,GAAIA,EAAKlI,YAAc0B,KAAK2G,EAAiBH,EAAKlI,WAMhD,SAIF,IACG0B,KAAK4G,EACJJ,EAAK7L,MAAQ2L,EACbE,EAAKK,cACL7G,KAAKC,EAAKkD,sBAAwBqD,EAAKM,uBACnCN,EAAKO,uBACLld,EACJ2c,EAAK1L,MACL0L,EAAKQ,SACLR,EAAKS,aAQP,SAgBF,OANIT,EAAKU,QACPV,EAAKU,OAAO9Z,SAASoS,IACnBQ,KAAKmH,EAAO3H,EAAE2E,WAAY3E,EAAE4E,OAAO,IAIhCpE,KAAK2F,EAAkBW,EAAIE,EAAKY,MAAY,QAASZ,EAAKF,GACnE,CACA,IAAKE,EAAKa,WAOR,SAIF,MAAMzD,EAAqB,CACzByD,WAAYb,EAAKa,WACjB3Y,IAAK8X,EAAK9X,KAAO4X,GAEf,aAAcE,IAAM5C,EAAIoD,SAAWR,EAAKQ,UACxCR,EAAKc,UAAS1D,EAAI0D,QAAUd,EAAKc,SACjCd,EAAKK,gBAAejD,EAAIiD,cAAgBL,EAAKK,eAC7CL,EAAKO,oBACPnD,EAAImD,kBAAoBP,EAAKO,mBAC3BP,EAAKM,yBACPlD,EAAIkD,uBAAyBN,EAAKM,6BACTjd,IAAvB2c,EAAKe,gBACP3D,EAAI2D,cAAgBf,EAAKe,oBACG1d,IAA1B2c,EAAKgB,mBACP5D,EAAI4D,iBAAmBhB,EAAKgB,kBAC1BhB,EAAKiB,YAAW7D,EAAI6D,UAAYjB,EAAKiB,WACrCjB,EAAKkB,OAAM9D,EAAI8D,KAAOlB,EAAKkB,MAC3BlB,EAAKmB,SAAQ/D,EAAI+D,OAASnB,EAAKmB,QAC/BnB,EAAKhJ,OAAMoG,EAAIpG,KAAOgJ,EAAKhJ,MAC3BgJ,EAAKoB,QAAOhE,EAAIgE,MAAQpB,EAAKoB,OAC7BpB,EAAK7L,OAAMiJ,EAAIjJ,KAAO6L,EAAK7L,MAC3B6L,EAAKS,cAAarD,EAAIqD,YAAcT,EAAKS,aACzCT,EAAKC,UAAS7C,EAAI6C,QAAUD,EAAKC,SACjCD,EAAKlI,YAAWsF,EAAItF,UAAYkI,EAAKlI,WAGzC,MAAMvM,EAAMiO,KAAKqE,EAAKT,EAAK0C,GAE3B,GADAtG,KAAKsE,EAAmBV,EAAK7R,GACzBA,EAAI6S,eAAiB7S,EAAI8V,YAC3B,OAAO7H,KAAK2F,EACVW,EACAvU,EAAI5C,MACJ,aACAqX,EAAKF,GACL1C,EACA7R,EAGN,CAUF,OAAOiO,KAAK2F,EACVW,OACyBzc,IAAzBwc,EAAQD,aAA6B,KAAOC,EAAQD,aACpD,eAEJ,CAEQQ,EACNjM,EACAkM,EACAE,EACAjM,EACAkM,EACAC,GAEA,IAAKnM,QAAsBjR,IAAbmd,EAAwB,OAAO,EAE7C,MAAMc,UAAEA,GAAc9H,KAAK+H,EACzBlB,EACAE,GAEF,IAAKe,EACH,OAAO,EAGT,MAAMjN,EAAIH,GAAKC,EAAMmN,EAAWb,GAAe,GAC/C,OAAU,OAANpM,IAEGC,EACHF,GAAQC,EAAGC,QACEjR,IAAbmd,GACAnM,GAAKmM,EAEX,CAEQL,EAAiBrI,GACvB,OAAOF,GAAc4B,KAAK3R,gBAAiBiQ,EAC7C,CAEQoI,EAAeD,GACrB,OAAOA,EAAQ9J,MAAMxG,IACnB,MAAM2R,UAAEA,GAAc9H,KAAK+H,EAAkB5R,EAAOqD,WACpD,IAAKsO,EAAW,OAAO,EACvB,MAAMjN,EAAIH,GAAKvE,EAAOwE,KAAMmN,EAAW3R,EAAO8Q,aAAe,GAC7D,OAAU,OAANpM,IACI1E,EAAOwR,OAAOhL,MAAMqL,GAAMpN,GAAQC,EAAGmN,IAAG,GAEpD,CAEQ3D,EACNF,EACA8D,GAEA,MAAMvZ,EAAMyV,EAAWzV,IACjBwZ,EAAgB/D,EAAWkD,WAAW/O,OAG5C,GAAI4P,EAAgB,EAGlB,OAAOlI,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,IAA0B,IAAtBjI,KAAKC,EAAKmI,QAGZ,OAAOpI,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAOhD,IAHA9D,EAAanE,KAAKqI,EAAgBlE,IAIrBmE,cACVhN,GAAc0E,KAAKuI,IAAkBpE,EAAWmE,aAMjD,OAAOtI,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,MAAMO,EF/pBH,SACLlC,EACAzX,EACAqZ,GAEA,IAAKrZ,EACH,OAAO,KAGT,MAAM4Z,EAAS5Z,EAAIqH,MAAM,KAAK,GAC9B,IAAKuS,EACH,OAAO,KAGT,MAAM/M,EAAQ+M,EACXvN,QAAQ,MAAO,IACfhF,MAAM,KACNjH,KAAKyZ,GAAOA,EAAGxS,MAAM,IAAK,KAC1BC,QAAO/L,IAAA,IAAEsS,GAAEtS,EAAA,OAAKsS,IAAM4J,CAAE,IACxBrX,KAAIvE,IAAA,IAAI+R,CAAAA,GAAE/R,EAAA,OAAKie,SAASlM,EAAE,IAE7B,OAAIf,EAAMpD,OAAS,GAAKoD,EAAM,IAAM,GAAKA,EAAM,GAAKwM,EAC3CxM,EAAM,GAER,IACT,CEsoBuBkN,CACjBla,EACAsR,KAAKuI,IACLL,GAEF,GAAmB,OAAfM,EAMF,OAAOxI,KAAKmI,EAAWhE,EAAYqE,GAAY,EAAOP,GAIxD,GAAIjI,KAAKC,EAAKtO,kBAAoBjD,KAAOsR,KAAKC,EAAKtO,iBAOjD,OAAOqO,KAAKmI,EAAWhE,EANLnE,KAAKC,EAAKtO,iBAAiBjD,IAMC,EAAOuZ,GAIvD,GAA0B,UAAtB9D,EAAW0E,SAA4C,IAAtB1E,EAAW2E,OAK9C,OAAO9I,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,MAAMpB,cAAEA,EAAaiB,UAAEA,GAAc9H,KAAK+H,EACxC5D,EAAW0C,cACX7G,KAAKC,EAAKkD,sBAAwBgB,EAAW2C,uBACzC3C,EAAW4C,uBACXld,GAEN,IAAKie,EAKH,OAAO9H,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAGhD,IAAIc,GAAY,EAEZC,GAAoB,EACpBC,GAA+B,EACnC,GAAIjJ,KAAKC,EAAKkD,sBAAwBgB,EAAW2C,uBAAwB,CACvE,MAAM7C,UAAEA,EAASiF,iBAAEA,GAAqBlJ,KAAKmJ,EAC3ChF,EAAWzV,IACXyV,EAAWoD,cACXpD,EAAWqD,iBACXrD,EAAWuD,MAEbsB,EAAoB/E,GAAa,EACjC8E,EAAW9E,EACXgF,IAAiCC,CACnC,CAGA,IAAKF,EAAmB,CAEtB,GAAI7E,EAAWsC,SACb,GAAIzG,KAAK0G,EAAevC,EAAWsC,SAKjC,OAAOzG,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,QAE3C,GACL9D,EAAWsD,YF55BZ,SACLK,EACAL,GAEA,MAAM5M,EAAIH,GAAK,KAAO+M,EAAU,GAAIK,EAAW,GAC/C,OAAU,OAANjN,GACGA,GAAK4M,EAAU,IAAM5M,EAAI4M,EAAU,EAC5C,CEs5BS2B,CAAYtB,EAAW3D,EAAWsD,WAMnC,OAAOzH,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,GAAI9D,EAAWtI,UF5tBd,SAAoBA,GACzB,IACE,OAAOA,GAIT,CAHE,MAAO5P,GAEP,OADAmP,QAAQC,MAAMpP,IACP,CACT,CACF,CEqtBiCwP,CAAW0I,EAAWtI,SAK/C,OAAOmE,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,GACE9D,EAAW7F,YACV0B,KAAK2G,EAAiBxC,EAAW7F,WAMlC,OAAO0B,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,GACE9D,EAAWkF,SACVrJ,KAAKsJ,EAAiBnF,EAAWkF,QAMlC,OAAOrJ,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,EAElD,CAGA,GAAI9D,EAAWtV,MAAQmR,KAAKuJ,EAAYpF,EAAWtV,KAKjD,OAAOmR,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,MAAMpN,EAAIH,GACRyJ,EAAWxJ,MAAQjM,EACnBoZ,EACA3D,EAAW8C,aAAe,GAE5B,GAAU,OAANpM,EAKF,OAAOmF,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAehD,GAZKe,IAQHD,EF99BC,SAAyBlO,EAAW8M,GACzC,IAAK,IAAI7X,EAAI,EAAGA,EAAI6X,EAAOrP,OAAQxI,IACjC,GAAI8K,GAAQC,EAAG8M,EAAO7X,IACpB,OAAOA,EAGX,OAAQ,CACV,CEu9BiB0Z,CAAgB3O,EANzBsJ,EAAWwD,QFn2BZ,SACLO,EACAlB,EACAM,IAEAN,OAAwBnd,IAAbmd,EAAyB,EAAIA,GAGzB,EAIbA,EAAW,EACFA,EAAW,IAIpBA,EAAW,GAIb,MAAMyC,GA5JwB5O,EA4JAqN,IA3JrB,EAAU,GACZ,IAAIxa,MAAMmN,GAAG6O,KAAK,EAAI7O,GAFxB,IAAyBA,GA6J9ByM,EAAUA,GAAWmC,GACTnR,SAAW4P,IAMrBZ,EAAUmC,GAIZ,MAAME,EAAcrC,EAAQsC,QAAO,CAACC,EAAGC,IAAQA,EAAMD,GAAG,IACpDF,EAAc,KAAQA,EAAc,QAItCrC,EAAUmC,GAIZ,IAAIM,EAAa,EACjB,OAAOzC,EAAQrY,KAAK4a,IAClB,MAAMG,EAAQD,EAEd,OADAA,GAAcF,EACP,CAACG,EAAOA,EAAShD,EAAsB6C,EAAE,GAEpD,CEozBQI,CACE/B,OACwBre,IAAxBsa,EAAW6C,SAAyB,EAAI7C,EAAW6C,SACnD7C,EAAWmD,WAMb2B,EAKF,OAAOjJ,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,OAAWpe,GAAW,GAItE,GAAIkf,EAAW,EAKb,OAAO/I,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,GAAI,UAAW9D,EAMb,OAAOnE,KAAKmI,EACVhE,OACqBta,IAArBsa,EAAWiD,OAAuB,EAAIjD,EAAWiD,OACjD,EACAa,GAKJ,GAAIjI,KAAKC,EAAKiK,OAKZ,OAAOlK,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,GAA0B,YAAtB9D,EAAW0E,OAKb,OAAO7I,KAAKmI,EAAWhE,GAAa,GAAG,EAAO8D,GAIhD,MAAM7D,EAASpE,KAAKmI,EAClBhE,EACA4E,GACA,EACAd,EACApN,EACAmO,GAIF,GAAIhJ,KAAKC,EAAKkD,sBAAwBgB,EAAW2C,uBAAwB,CACvE,MAAMqD,QACJA,EACAzb,IAAK0b,EAAOC,IACZA,GACErK,KAAKsK,EACPzD,EACA/I,GAASgK,GACT,CACE,CAAC9H,KAAKuK,EACJpG,EAAWzV,IACXyV,EAAWoD,gBACTnD,EAAO1V,MAGXyb,IAEFnK,KAAKC,EAAKuD,2BACRxD,KAAKC,EAAKuD,4BAA8B,GAC1CxD,KAAKC,EAAKuD,2BAA2B4G,GAAWC,EAEhDrK,KAAKC,EAAKkD,oBAAoBqH,gBAAgBH,GAElD,CAWA,OARArK,KAAKmH,EAAOhD,EAAYC,GAQjBA,CACT,CAEAqG,IAAIC,EAAaC,GACV3K,KAAKK,QACNL,KAAKC,EAAKwK,IAAKzK,KAAKC,EAAKwK,IAAIC,EAAKC,GACjCvP,QAAQqP,IAAIC,EAAKC,GACxB,CAEQxD,EAAUhD,EAA2BC,GAC3C,IAAKpE,KAAKC,EAAK2K,iBAAkB,OAEjC,MAGMlO,EACJ0H,EAAOyC,cAAgBzC,EAAO0D,UAJpB3D,EAAWzV,IAI2B0V,EAAOY,YACzD,IAAIhF,KAAKG,EAAoBzP,IAAIgM,GAAjC,CACAsD,KAAKG,EAAoBnO,IAAI0K,GAE7B,IACEsD,KAAKC,EAAK2K,iBAAiBzG,EAAYC,EAGzC,CAFE,MAAOnY,GACPmP,QAAQC,MAAMpP,EAChB,CAPqC,CAQvC,CAEQoc,EAAmBlE,GACzB,MAAMzV,EAAMyV,EAAWzV,IACjBmc,EAAI7K,KAAKC,EAAKtT,UAWpB,OAVIke,GAAKA,EAAEnc,IAEqB,iBAD9ByV,EAAavX,OAAOC,OAAO,CAAA,EAAIsX,EAAY0G,EAAEnc,KACvBG,MACpBsV,EAAWtV,IAAMkM,GAEfoJ,EAAWtV,MAKVsV,CACT,CAEQ4D,EAAkB7T,EAAe4W,GACvC,IAAIjE,EAAgB3S,GAAQ,KAExB4T,EAAiB,GAwBrB,OAtBI9H,KAAKY,EAAoBiG,GAC3BiB,EAAY9H,KAAKY,EAAoBiG,GAC5B7G,KAAKC,EAAK7R,WACnB0Z,EAAY9H,KAAKC,EAAK7R,WAAWyY,IAAkB,GAC1C7G,KAAKC,EAAK8K,OACnBjD,EAAY9H,KAAKC,EAAK8K,KAAKlE,IAAkB,KAI1CiB,GAAagD,IACZ9K,KAAKY,EAAoBkK,GAC3BhD,EAAY9H,KAAKY,EAAoBkK,GAC5B9K,KAAKC,EAAK7R,WACnB0Z,EAAY9H,KAAKC,EAAK7R,WAAW0c,IAAa,GACrC9K,KAAKC,EAAK8K,OACnBjD,EAAY9H,KAAKC,EAAK8K,KAAKD,IAAa,IAEtChD,IACFjB,EAAgBiE,IAIb,CAAEjE,gBAAeiB,YAC1B,CAEQK,EACNhE,EACA6G,EACAC,EACAhD,EACAiD,EACAC,GAEA,IAAIvG,GAAe,GAEfoG,EAAiB,GAAKA,GAAkB7G,EAAWkD,WAAW/O,UAChE0S,EAAiB,EACjBpG,GAAe,GAGjB,MAAMiC,cAAEA,EAAaiB,UAAEA,GAAc9H,KAAK+H,EACxC5D,EAAW0C,cACX7G,KAAKC,EAAKkD,sBAAwBgB,EAAW2C,uBACzC3C,EAAW4C,uBACXld,GAGA6d,EAA+BvD,EAAWuD,KAC5CvD,EAAWuD,KAAKsD,GAChB,GAEEjZ,EAAiB,CACrBrD,IAAKgZ,EAAKhZ,KAAO,GAAKsc,EACtB/C,YACArD,eACAqG,WACAjG,YAAagG,EACb7b,MAAOgV,EAAWkD,WAAW2D,GAC7BnE,gBACAiB,YACAqD,mBAAoBA,GAOtB,OAJIzD,EAAKlK,OAAMzL,EAAIyL,KAAOkK,EAAKlK,WAChB3T,IAAXqhB,IAAsBnZ,EAAImZ,OAASA,GACnCxD,EAAKG,cAAa9V,EAAI8V,YAAcH,EAAKG,aAEtC9V,CACT,CAEQwW,IACN,OAAOvI,KAAKC,EAAKpR,MAAQ6Q,GAAYrU,OAAO+f,SAASnP,KAAO,GAC9D,CAEQsN,EAAY8B,GAClB,MAAMxc,EAAMmR,KAAKuI,IACjB,IAAK1Z,EAAK,OAAO,EAEjB,MAAMyc,EAAWzc,EAAIqM,QAAQ,eAAgB,IAAIA,QAAQ,WAAY,KAErE,QAAImQ,EAASpR,KAAKpL,MACdwc,EAASpR,KAAKqR,EAEpB,CAEQhC,EAAiBiC,GACvB,MAAMlC,EAASrJ,KAAKC,EAAKoJ,QAAU,CAAA,EACnC,IAAK,IAAIvZ,EAAI,EAAGA,EAAIyb,EAAUjT,OAAQxI,IACpC,GAAIuZ,EAAOkC,EAAUzb,IAAK,OAAO,EAEnC,OAAO,CACT,CAEQgV,EAAiB0G,GACvB,IAAK9L,GAAW,OAChB,MAAMmE,EAAuB,GAC7B,GAAI2H,EAAQC,IAAK,CACf,MAAM9H,EAAIrY,SAASwK,cAAc,SACjC6N,EAAE5N,UAAYyV,EAAQC,IACtBngB,SAASogB,KAAKC,YAAYhI,GAC1BE,EAAKpK,MAAK,IAAMkK,EAAEiI,UACpB,CACA,GAAIJ,EAAQK,GAAI,CACd,MAAMC,EAASxgB,SAASwK,cAAc,UACtCgW,EAAO/V,UAAYyV,EAAQK,GAC3BvgB,SAASogB,KAAKC,YAAYG,GAC1BjI,EAAKpK,MAAK,IAAMqS,EAAOF,UACzB,CAMA,OALIJ,EAAQO,cACVP,EAAQO,aAAa3e,SAAS+L,IAC5B0K,EAAKpK,KHr0Bb,SAAA/O,GACE6O,IAAAA,EAAAA,EAAAA,SACAyS,EAAAA,EAAAA,OACA7c,EAAAA,EAAAA,MACW+E,EAAXsF,EAAAA,UACA/C,EAAAA,EAAAA,eACAC,EAAAA,EAAAA,qBAEA,GAAa,SAATxC,EAAiB,CACnB,GAAe,WAAX8X,EACF,OAAOpW,GAAK2D,GAAU,SAAGjE,GAAA,OAAIA,SAAOnG,EAAAA,EAAS,GAApB,IACpB,GAAe,QAAX6c,EACT,OAAOpW,GAAK2D,GAAU,WAAA,OAAA,MAAMpK,EAAAA,EAAS,EAAf,GAEzB,MAAM,GAAa,UAAT+E,EAAkB,CAC3B,GAAe,WAAX8X,EACF,OAAOlU,GAAQyB,GAAU,SAAGjE,GACtBnG,GAAOmG,EAAItD,IAAI7C,EACpB,IACI,GAAe,WAAX6c,EACT,OAAOlU,GAAQyB,GAAU,SAAGjE,GACtBnG,GAAOmG,EAAG,OAAQnG,EACvB,IACI,GAAe,QAAX6c,EACT,OAAOlU,GAAQyB,GAAU,SAAGjE,GAC1BA,EAAIpI,QACAiC,GAAOmG,EAAItD,IAAI7C,EACpB,GAEJ,MAAM,GAAa,aAAT+E,GACT,GAAe,QAAX8X,GAAoBvV,EACtB,OAnFN,SACE8C,EACA5D,GAEA,OAAOiE,GAAY,CACjBR,KAAM,WACNzF,SAAU,IAAIlH,IACdkJ,OA4E4B,WAAA,MAAO,CAC/Be,qBAAAA,EACAD,eAAAA,EAFwB,EA3E5B8C,SAAAA,GAEH,CAyEY/B,CAAS+B,OAKb,CACL,GAAe,WAAXyS,EACF,OAAOxS,GAAUD,EAAUrF,GAAM,SAAGoB,GAAA,OAC1B,OAARA,EAAeA,GAAG,MAAInG,EAAAA,EAAS,IAAMA,MAAAA,EAAAA,EAAS,EADZ,IAG/B,GAAe,QAAX6c,EACT,OAAOxS,GAAUD,EAAUrF,GAAM,WAAA,OAAA,MAAM/E,EAAAA,EAAS,EAAf,IAC5B,GAAe,WAAX6c,EACT,OAAOxS,GAAUD,EAAUrF,GAAM,WAAA,OAAM,IAAN,GAEpC,CACD,OAAOT,CACR,CGqxBiBkC,CAAmBwD,GAAiCzF,OAAO,IAGlE,KACLmQ,EAAKzW,SAAS6e,GAAOA,KAAK,CAE9B,CAEQC,GAAwCjc,GAC9C,MAAM7B,EAAa,IAAI3B,IACjB0E,EAAWlB,GAAQA,EAAKkB,SAAWlB,EAAKkB,SAAW6O,KAAK5O,cACxDJ,EACJf,GAAQA,EAAKe,YAAcf,EAAKe,YAAcgP,KAAK/O,iBAoBrD,OAnBArE,OAAO4B,KAAK2C,GAAU/D,SAASkZ,IAC7B,MAAMD,EAAUlV,EAASmV,GACzB,GAAID,EAAQE,MACV,IAAK,MAAMC,KAAQH,EAAQE,MACrBC,EAAKa,aACPjZ,EAAW4D,IAAIwU,EAAKK,eAAiB,MACjCL,EAAKO,mBACP3Y,EAAW4D,IAAIwU,EAAKO,mBAI5B,IAEF/V,EAAY/B,KAAKkV,IACf/V,EAAW4D,IAAImS,EAAW0C,eAAiB,MACvC1C,EAAW4C,mBACb3Y,EAAW4D,IAAImS,EAAW4C,kBAC5B,IAEKrZ,MAAMC,KAAKS,EACpB,CAEApB,2BAAkCiD,GAChC,GAAI+P,KAAKC,EAAKkD,oBAAqB,CACjC,MAAM/U,EAAa4R,KAAKmM,GAA2Blc,GACnD+P,KAAKC,EAAKuD,iCAAmCxD,KAAKC,EAAKkD,oBAAoBiJ,kBACzEhe,EAEJ,CACF,CAEQie,KACN,MAAMC,EAAuC,CAAA,EAI7C,OAHA1f,OAAO2f,OAAOvM,KAAKC,EAAKuD,4BAA8B,IAAIpW,SAASid,IAC7DA,EAAImC,aAAa5f,OAAOC,OAAOyf,EAAmBjC,EAAImC,YAAY,IAEjEF,CACT,CAEQnD,EACNsD,EACAC,EACAC,EACAjF,GAMAiF,EAA6BA,GAA8B,EAC3DjF,EAAOA,GAAQ,GACf,MAAMpB,EAAKtG,KAAKuK,EACdkC,EAJFC,EAA0BA,GAA2B,GAO/CF,EAAcxM,KAAKqM,KAGzB,GAAIM,EAA6B,EAC/B,IAAK,IAAI7c,EAAI,EAAGA,GAAK6c,EAA4B7c,IAE/C,QAAgCjG,IAA5B2iB,EADexM,KAAKuK,EAA8BkC,EAAe3c,IAEnE,MAAO,CACLmU,WAAY,EACZiF,kBAAkB,GAK1B,MAAM0D,EAAeJ,EAAYlG,GACjC,QAAqBzc,IAAjB+iB,EAEF,MAAO,CAAE3I,WAAY,GACvB,MAAMA,EAAYyD,EAAKmF,WAAWnX,GAAMA,EAAEhH,MAAQke,IAClD,OAAI3I,EAAY,EAEP,CAAEA,WAAY,GAEhB,CAAEA,YACX,CAEQsG,EACNkC,EACAC,GAGA,OADAA,EAA0BA,GAA2B,EAC3CD,GAAAA,OAAAA,eAAkBC,EAC9B,CAEQP,GACNlc,GAEA,MAAM7B,EAAqC,CAAA,EAS3C,OARA4R,KAAKC,EAAK6M,iCAAoC9M,KAAKC,EAChD6M,iCAEC9M,KAAKC,EAAK6M,iCADV9M,KAAKkM,GAAwCjc,GAEjD+P,KAAKC,EAAK6M,iCAAiC1f,SAAS8G,IAClD,MAAM4T,UAAEA,GAAc9H,KAAK+H,EAAkB7T,GAC7C9F,EAAW8F,GAAQ4J,GAASgK,EAAU,IAEjC1Z,CACT,CAEQkc,EACNyC,EACAC,EACAR,GAMA,MAAM9d,EAAG,GAAAlE,OAAMuiB,EAAa,MAAAviB,OAAKwiB,GAC3BC,EACJjN,KAAKC,EAAKuD,4BACVxD,KAAKC,EAAKuD,2BAA2B9U,IACjCsR,KAAKC,EAAKuD,2BAA2B9U,GAAK8d,aAC1C,GACAU,EAAiB,IAAKD,KAAwBT,GAIpD,MAAO,CACL9d,MACA2b,IAAK,CACH0C,gBACAC,iBACAR,YAAaU,GAEf/C,QATApf,KAAKC,UAAUiiB,KAAyBliB,KAAKC,UAAUkiB,GAW3D,ECn3CK,MAAeC,GAapBngB,wBACEoB,GAEA,MAAMgf,EAAkD,CAAA,EAaxD,aAXQhb,QAAQib,IACZzgB,OAAOgB,QAAQQ,GAAYa,KAAI7E,IAAA,IAAE2iB,EAAeC,GAAe5iB,EAAA,OAC7D4V,KAAKsN,eAAeP,EAAeC,EAAe,MAGtD5f,SAASid,IACT,GAAIA,EAAK,CACP,MAAM3b,YAAS2b,EAAI0C,cAAkB1C,MAAAA,OAAAA,EAAI2C,gBACzCI,EAAK1e,GAAO2b,CACd,KAEK+C,CACT,EAGK,MAAMG,WAAwCJ,GAGnDrN,YAAY0N,GACVA,EAAOA,GAAQ,GACfC,QACAzN,KAAK0N,OAASF,EAAKE,QAAU,oBAC7B,IACE1N,KAAKhU,aAAewhB,EAAKxhB,cAAgBrC,WAAWqC,YAEpD,CADA,MAAOC,GACP,CAEJ,CACAe,qBAAqB+f,EAAuBC,GAC1C,MAAMte,EAAG,GAAAlE,OAAMuiB,EAAa,MAAAviB,OAAKwiB,GACjC,IAAI3C,EAAwC,KAC5C,IAAKrK,KAAKhU,aAAc,OAAOqe,EAC/B,IACE,MAAMsD,QAAa3N,KAAKhU,aAAaoW,QAAQpC,KAAK0N,OAAShf,IAAS,KAC9DuB,EAAOlF,KAAK6H,MAAM+a,GACpB1d,EAAK8c,eAAiB9c,EAAK+c,gBAAkB/c,EAAKuc,cACpDnC,EAAMpa,EAGR,CADA,MAAOhE,GACP,CAEF,OAAOoe,CACT,CACArd,sBAAsBqd,GACpB,MAAM3b,YAAS2b,EAAI0C,cAAkB1C,MAAAA,OAAAA,EAAI2C,gBACzC,GAAKhN,KAAKhU,aACV,UACQgU,KAAKhU,aAAayB,QAAQuS,KAAK0N,OAAShf,EAAK3D,KAAKC,UAAUqf,GAElE,CADA,MAAOpe,GACP,CAEJ,EAGK,MAAM2hB,WAAyCT,GAYpDrN,YAUGpV,GAAA,IAVSgjB,OACVA,EAAS,oBAAmBG,IAC5BA,EAAG9b,IACHA,EAAG+b,iBACHA,EAAmB,CAAC,GAMrBpjB,EACC+iB,QACAzN,KAAK0N,OAASA,EACd1N,KAAK6N,IAAMA,EACX7N,KAAKjO,IAAMA,EACXiO,KAAK8N,iBAAmBA,CAC1B,CACA9gB,qBAAqB+f,EAAuBC,GAC1C,MAAMte,EAAG,GAAAlE,OAAMuiB,EAAa,MAAAviB,OAAKwiB,GACjC,IAAI3C,EAAwC,KAC5C,IAAKrK,KAAK6N,IAAK,OAAOxD,EACtB,IACE,MACMpa,EAAOlF,KAAK6H,MADNoN,KAAK6N,IAAIE,QAAQ/N,KAAK0N,OAAShf,IAAQ,MAE/CuB,EAAK8c,eAAiB9c,EAAK+c,gBAAkB/c,EAAKuc,cACpDnC,EAAMpa,EAGR,CADA,MAAOhE,GACP,CAEF,OAAOoe,CACT,CACArd,sBAAsBqd,GACpB,MAAM3b,YAAS2b,EAAI0C,cAAkB1C,MAAAA,OAAAA,EAAI2C,gBACzC,IAAKhN,KAAKjO,IAAK,OACf,MAAMuI,EAAMvP,KAAKC,UAAUqf,GAC3BrK,KAAKjO,IAAIic,OACPxI,mBAAmBxF,KAAK0N,OAAShf,GACjC8W,mBAAmBlL,GACnB0F,KAAK8N,iBAET,EAGK,MAAMG,WAAyCd,GAWpDrN,YAQG5U,GAAA,IARSwiB,OACVA,EAAS,oBAAmBQ,SAC5BA,EAAQJ,iBACRA,EAAmB,CAAC,GAKrB5iB,EACCuiB,QACAzN,KAAK0N,OAASA,EACd1N,KAAKkO,SAAWA,EAChBlO,KAAK8N,iBAAmBA,CAC1B,CACA9gB,qBAAqB+f,EAAuBC,GAC1C,MAAMte,EAAG,GAAAlE,OAAMuiB,EAAa,MAAAviB,OAAKwiB,GACjC,IAAI3C,EAAwC,KAC5C,IAAKrK,KAAKkO,SAAU,OAAO7D,EAC3B,IACE,MAAMsD,EAAM3N,KAAKkO,SAAS3d,IAAIyP,KAAK0N,OAAShf,GACtCuB,EAAOlF,KAAK6H,MAAM+a,GAAO,MAC3B1d,EAAK8c,eAAiB9c,EAAK+c,gBAAkB/c,EAAKuc,cACpDnC,EAAMpa,EAGR,CADA,MAAOhE,GACP,CAEF,OAAOoe,CACT,CACArd,sBAAsBqd,GACpB,MAAM3b,YAAS2b,EAAI0C,cAAkB1C,MAAAA,OAAAA,EAAI2C,gBACzC,IAAKhN,KAAKkO,SAAU,OACpB,MAAM5T,EAAMvP,KAAKC,UAAUqf,GAC3BrK,KAAKkO,SAAS1d,IAAIwP,KAAK0N,OAAShf,EAAK4L,EAAK0F,KAAK8N,iBACjD,EAGK,MAAMK,WAAiChB,GAG5CrN,YAAiDuC,GAAA,IAArC+L,MAAEA,GAAiC/L,EAC7CoL,QACAzN,KAAKoO,MAAQA,CACf,CAEAphB,wBACEoB,GAEA,MAAMgf,EAA8D,CAAA,EAC9D5e,EAAO5B,OAAOgB,QAAQQ,GAAYa,KACtCC,IAAA,IAAE6d,EAAeC,GAAe9d,EAAA,MAAQ6d,GAAAA,OAAAA,eAAkBC,EAAc,IAE1E,OAAKhN,KAAKoO,OACVpO,KAAKoO,MAAMC,QAAQ7f,GAAMsD,MAAMya,IAC7BA,EAAOnf,SAASugB,IACd,IACE,MAAM1d,EAAOlF,KAAK6H,MAAM+a,GAAO,MAC/B,GAAI1d,EAAK8c,eAAiB9c,EAAK+c,gBAAkB/c,EAAKuc,YAAa,CACjE,MAAM9d,YAASuB,EAAK8c,cAAkB9c,MAAAA,OAAAA,EAAK+c,gBAC3CI,EAAK1e,GAAOuB,CACd,CAEA,CADA,MAAOhE,GACP,IAEF,IAEGmhB,GAdiBA,CAe1B,CAEApgB,qBAAqBshB,EAAwBC,GAE3C,OAAO,IACT,CAEAvhB,sBAAsBqd,GACpB,MAAM3b,YAAS2b,EAAI0C,cAAkB1C,MAAAA,OAAAA,EAAI2C,gBACpChN,KAAKoO,aACJpO,KAAKoO,MAAM5d,IAAI9B,EAAK3D,KAAKC,UAAUqf,GAC3C"}