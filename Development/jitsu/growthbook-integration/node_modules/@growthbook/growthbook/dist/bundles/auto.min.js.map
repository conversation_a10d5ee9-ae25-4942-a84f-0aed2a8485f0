{"version": 3, "file": "auto.min.js", "sources": ["../../src/feature-repository.ts", "../../../../node_modules/dom-mutator/dist/dom-mutator.esm.js", "../../src/util.ts", "../../src/mongrule.ts", "../../src/GrowthBook.ts", "../../src/auto-wrapper.ts"], "sourcesContent": ["import {\n  Attributes,\n  CacheSettings,\n  FeatureApiResponse,\n  Helpers,\n  Polyfills,\n} from \"./types/growthbook\";\nimport type { GrowthBook } from \".\";\n\ntype CacheEntry = {\n  data: FeatureApiResponse;\n  sse?: boolean;\n  version: string;\n  staleAt: Date;\n};\ntype ScopedChannel = {\n  src: EventSource | null;\n  cb: (event: MessageEvent<string>) => void;\n  host: string;\n  clientKey: string;\n  headers?: Record<string, string>;\n  errors: number;\n  state: \"active\" | \"idle\" | \"disabled\";\n};\n\n// Config settings\nconst cacheSettings: CacheSettings = {\n  // Consider a fetch stale after 1 minute\n  staleTTL: 1000 * 60,\n  // Max time to keep a fetch in cache (24 hours default)\n  maxAge: 1000 * 60 * 60 * 24,\n  cacheKey: \"gbFeaturesCache\",\n  backgroundSync: true,\n  maxEntries: 10,\n  disableIdleStreams: false,\n  idleStreamInterval: 20000,\n};\nconst polyfills: Polyfills = {\n  fetch: globalThis.fetch ? globalThis.fetch.bind(globalThis) : undefined,\n  SubtleCrypto: globalThis.crypto ? globalThis.crypto.subtle : undefined,\n  EventSource: globalThis.EventSource,\n};\nexport const helpers: Helpers = {\n  fetchFeaturesCall: ({ host, clientKey, headers }) => {\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/features/${clientKey}`,\n      { headers }\n    );\n  },\n  fetchRemoteEvalCall: ({ host, clientKey, payload, headers }) => {\n    const options = {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\", ...headers },\n      body: JSON.stringify(payload),\n    };\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/eval/${clientKey}`,\n      options\n    );\n  },\n  eventSourceCall: ({ host, clientKey, headers }) => {\n    if (headers) {\n      return new polyfills.EventSource(`${host}/sub/${clientKey}`, {\n        headers,\n      });\n    }\n    return new polyfills.EventSource(`${host}/sub/${clientKey}`);\n  },\n  startIdleListener: () => {\n    let idleTimeout: number | undefined;\n    const isBrowser =\n      typeof window !== \"undefined\" && typeof document !== \"undefined\";\n    if (!isBrowser) return;\n    const onVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        window.clearTimeout(idleTimeout);\n        onVisible();\n      } else if (document.visibilityState === \"hidden\") {\n        idleTimeout = window.setTimeout(\n          onHidden,\n          cacheSettings.idleStreamInterval\n        );\n      }\n    };\n    document.addEventListener(\"visibilitychange\", onVisibilityChange);\n    return () =>\n      document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n  },\n  stopIdleListener: () => {\n    // No-op, replaced by startIdleListener\n  },\n};\n\ntry {\n  if (globalThis.localStorage) {\n    polyfills.localStorage = globalThis.localStorage;\n  }\n} catch (e) {\n  // Ignore localStorage errors\n}\n\n// Global state\nconst subscribedInstances: Map<string, Set<GrowthBook>> = new Map();\nlet cacheInitialized = false;\nconst cache: Map<string, CacheEntry> = new Map();\nconst activeFetches: Map<string, Promise<FeatureApiResponse>> = new Map();\nconst streams: Map<string, ScopedChannel> = new Map();\nconst supportsSSE: Set<string> = new Set();\n\n// Public functions\nexport function setPolyfills(overrides: Partial<Polyfills>): void {\n  Object.assign(polyfills, overrides);\n}\nexport function configureCache(overrides: Partial<CacheSettings>): void {\n  Object.assign(cacheSettings, overrides);\n  if (!cacheSettings.backgroundSync) {\n    clearAutoRefresh();\n  }\n}\n\nexport async function clearCache(): Promise<void> {\n  cache.clear();\n  activeFetches.clear();\n  clearAutoRefresh();\n  cacheInitialized = false;\n  await updatePersistentCache();\n}\n\nexport async function refreshFeatures(\n  instance: GrowthBook,\n  timeout?: number,\n  skipCache?: boolean,\n  allowStale?: boolean,\n  updateInstance?: boolean,\n  backgroundSync?: boolean\n): Promise<void> {\n  if (!backgroundSync) {\n    cacheSettings.backgroundSync = false;\n  }\n\n  const data = await fetchFeaturesWithCache(\n    instance,\n    allowStale,\n    timeout,\n    skipCache\n  );\n  updateInstance && data && (await refreshInstance(instance, data));\n}\n\n// Subscribe a GrowthBook instance to feature changes\nexport function subscribe(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const subs = subscribedInstances.get(key) || new Set();\n  subs.add(instance);\n  subscribedInstances.set(key, subs);\n}\nexport function unsubscribe(instance: GrowthBook): void {\n  subscribedInstances.forEach((s) => s.delete(instance));\n}\n\nexport function onHidden() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    channel.state = \"idle\";\n    disableChannel(channel);\n  });\n}\n\nexport function onVisible() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    if (channel.state !== \"idle\") return;\n    enableChannel(channel);\n  });\n}\n\n// Private functions\n\nasync function updatePersistentCache() {\n  try {\n    if (!polyfills.localStorage) return;\n    await polyfills.localStorage.setItem(\n      cacheSettings.cacheKey,\n      JSON.stringify(Array.from(cache.entries()))\n    );\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n}\n\nasync function fetchFeaturesWithCache(\n  instance: GrowthBook,\n  allowStale?: boolean,\n  timeout?: number,\n  skipCache?: boolean\n): Promise<FeatureApiResponse | null> {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const now = new Date();\n\n  const minStaleAt = new Date(\n    now.getTime() - cacheSettings.maxAge + cacheSettings.staleTTL\n  );\n\n  await initializeCache();\n  const existing = cache.get(cacheKey);\n  if (\n    existing &&\n    !skipCache &&\n    (allowStale || existing.staleAt > now) &&\n    existing.staleAt > minStaleAt\n  ) {\n    // Restore from cache whether SSE is supported\n    if (existing.sse) supportsSSE.add(key);\n\n    // Reload features in the background if stale\n    if (existing.staleAt < now) {\n      fetchFeatures(instance);\n    }\n    // Otherwise, if we don't need to refresh now, start a background sync\n    else {\n      startAutoRefresh(instance);\n    }\n    return existing.data;\n  } else {\n    return await promiseTimeout(fetchFeatures(instance), timeout);\n  }\n}\n\nfunction getKey(instance: GrowthBook): string {\n  const [apiHost, clientKey] = instance.getApiInfo();\n  return `${apiHost}||${clientKey}`;\n}\n\nfunction getCacheKey(instance: GrowthBook): string {\n  const baseKey = getKey(instance);\n  if (!instance.isRemoteEval()) return baseKey;\n\n  const attributes = instance.getAttributes();\n  const cacheKeyAttributes =\n    instance.getCacheKeyAttributes() || Object.keys(instance.getAttributes());\n  const ca: Attributes = {};\n  cacheKeyAttributes.forEach((key) => {\n    ca[key] = attributes[key];\n  });\n\n  const fv = instance.getForcedVariations();\n  const url = instance.getUrl();\n\n  return `${baseKey}||${JSON.stringify({\n    ca,\n    fv,\n    url,\n  })}`;\n}\n\n// Guarantee the promise always resolves within {timeout} ms\n// Resolved value will be `null` when there's an error or it takes too long\n// Note: The promise will continue running in the background, even if the timeout is hit\nfunction promiseTimeout<T>(\n  promise: Promise<T>,\n  timeout?: number\n): Promise<T | null> {\n  return new Promise((resolve) => {\n    let resolved = false;\n    let timer: unknown;\n    const finish = (data?: T) => {\n      if (resolved) return;\n      resolved = true;\n      timer && clearTimeout(timer as NodeJS.Timer);\n      resolve(data || null);\n    };\n\n    if (timeout) {\n      timer = setTimeout(() => finish(), timeout);\n    }\n\n    promise.then((data) => finish(data)).catch(() => finish());\n  });\n}\n\n// Populate cache from localStorage (if available)\nasync function initializeCache(): Promise<void> {\n  if (cacheInitialized) return;\n  cacheInitialized = true;\n  try {\n    if (polyfills.localStorage) {\n      const value = await polyfills.localStorage.getItem(\n        cacheSettings.cacheKey\n      );\n      if (value) {\n        const parsed: [string, CacheEntry][] = JSON.parse(value);\n        if (parsed && Array.isArray(parsed)) {\n          parsed.forEach(([key, data]) => {\n            cache.set(key, {\n              ...data,\n              staleAt: new Date(data.staleAt),\n            });\n          });\n        }\n        cleanupCache();\n      }\n    }\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n  if (!cacheSettings.disableIdleStreams) {\n    const cleanupFn = helpers.startIdleListener();\n    if (cleanupFn) {\n      helpers.stopIdleListener = cleanupFn;\n    }\n  }\n}\n\n// Enforce the maxEntries limit\nfunction cleanupCache() {\n  const entriesWithTimestamps = Array.from(cache.entries())\n    .map(([key, value]) => ({\n      key,\n      staleAt: value.staleAt.getTime(),\n    }))\n    .sort((a, b) => a.staleAt - b.staleAt);\n\n  const entriesToRemoveCount = Math.min(\n    Math.max(0, cache.size - cacheSettings.maxEntries),\n    cache.size\n  );\n\n  for (let i = 0; i < entriesToRemoveCount; i++) {\n    cache.delete(entriesWithTimestamps[i].key);\n  }\n}\n\n// Called whenever new features are fetched from the API\nfunction onNewFeatureData(\n  key: string,\n  cacheKey: string,\n  data: FeatureApiResponse\n): void {\n  // If contents haven't changed, ignore the update, extend the stale TTL\n  const version = data.dateUpdated || \"\";\n  const staleAt = new Date(Date.now() + cacheSettings.staleTTL);\n  const existing = cache.get(cacheKey);\n  if (existing && version && existing.version === version) {\n    existing.staleAt = staleAt;\n    updatePersistentCache();\n    return;\n  }\n\n  // Update in-memory cache\n  cache.set(cacheKey, {\n    data,\n    version,\n    staleAt,\n    sse: supportsSSE.has(key),\n  });\n  cleanupCache();\n  // Update local storage (don't await this, just update asynchronously)\n  updatePersistentCache();\n\n  // Update features for all subscribed GrowthBook instances\n  const instances = subscribedInstances.get(key);\n  instances && instances.forEach((instance) => refreshInstance(instance, data));\n}\n\nasync function refreshInstance(\n  instance: GrowthBook,\n  data: FeatureApiResponse\n): Promise<void> {\n  data = await instance.decryptPayload(data, undefined, polyfills.SubtleCrypto);\n\n  await instance.refreshStickyBuckets(data);\n  instance.setExperiments(data.experiments || instance.getExperiments());\n  instance.setFeatures(data.features || instance.getFeatures());\n}\n\nasync function fetchFeatures(\n  instance: GrowthBook\n): Promise<FeatureApiResponse> {\n  const { apiHost, apiRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  const remoteEval = instance.isRemoteEval();\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n\n  let promise = activeFetches.get(cacheKey);\n  if (!promise) {\n    const fetcher: Promise<Response> = remoteEval\n      ? helpers.fetchRemoteEvalCall({\n          host: apiHost,\n          clientKey,\n          payload: {\n            attributes: instance.getAttributes(),\n            forcedVariations: instance.getForcedVariations(),\n            forcedFeatures: Array.from(instance.getForcedFeatures().entries()),\n            url: instance.getUrl(),\n          },\n          headers: apiRequestHeaders,\n        })\n      : helpers.fetchFeaturesCall({\n          host: apiHost,\n          clientKey,\n          headers: apiRequestHeaders,\n        });\n\n    // TODO: auto-retry if status code indicates a temporary error\n    promise = fetcher\n      .then((res) => {\n        if (res.headers.get(\"x-sse-support\") === \"enabled\") {\n          supportsSSE.add(key);\n        }\n        return res.json();\n      })\n      .then((data: FeatureApiResponse) => {\n        onNewFeatureData(key, cacheKey, data);\n        startAutoRefresh(instance);\n        activeFetches.delete(cacheKey);\n        return data;\n      })\n      .catch((e) => {\n        process.env.NODE_ENV !== \"production\" &&\n          instance.log(\"Error fetching features\", {\n            apiHost,\n            clientKey,\n            error: e ? e.message : null,\n          });\n        activeFetches.delete(cacheKey);\n        return Promise.resolve({});\n      });\n    activeFetches.set(cacheKey, promise);\n  }\n  return await promise;\n}\n\n// Watch a feature endpoint for changes\n// Will prefer SSE if enabled, otherwise fall back to cron\nfunction startAutoRefresh(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const { streamingHost, streamingHostRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  if (\n    cacheSettings.backgroundSync &&\n    supportsSSE.has(key) &&\n    polyfills.EventSource\n  ) {\n    if (streams.has(key)) return;\n    const channel: ScopedChannel = {\n      src: null,\n      host: streamingHost,\n      clientKey,\n      headers: streamingHostRequestHeaders,\n      cb: (event: MessageEvent<string>) => {\n        try {\n          if (event.type === \"features-updated\") {\n            const instances = subscribedInstances.get(key);\n            instances &&\n              instances.forEach((instance) => {\n                fetchFeatures(instance);\n              });\n          } else if (event.type === \"features\") {\n            const json: FeatureApiResponse = JSON.parse(event.data);\n            onNewFeatureData(key, cacheKey, json);\n          }\n          // Reset error count on success\n          channel.errors = 0;\n        } catch (e) {\n          process.env.NODE_ENV !== \"production\" &&\n            instance.log(\"SSE Error\", {\n              streamingHost,\n              clientKey,\n              error: e ? (e as Error).message : null,\n            });\n          onSSEError(channel);\n        }\n      },\n      errors: 0,\n      state: \"active\",\n    };\n    streams.set(key, channel);\n    enableChannel(channel);\n  }\n}\n\nfunction onSSEError(channel: ScopedChannel) {\n  if (channel.state === \"idle\") return;\n  channel.errors++;\n  if (channel.errors > 3 || (channel.src && channel.src.readyState === 2)) {\n    // exponential backoff after 4 errors, with jitter\n    const delay =\n      Math.pow(3, channel.errors - 3) * (1000 + Math.random() * 1000);\n    disableChannel(channel);\n    setTimeout(() => {\n      if ([\"idle\", \"active\"].includes(channel.state)) return;\n      enableChannel(channel);\n    }, Math.min(delay, 300000)); // 5 minutes max\n  }\n}\n\nfunction disableChannel(channel: ScopedChannel) {\n  if (!channel.src) return;\n  channel.src.onopen = null;\n  channel.src.onerror = null;\n  channel.src.close();\n  channel.src = null;\n  if (channel.state === \"active\") {\n    channel.state = \"disabled\";\n  }\n}\n\nfunction enableChannel(channel: ScopedChannel) {\n  channel.src = helpers.eventSourceCall({\n    host: channel.host,\n    clientKey: channel.clientKey,\n    headers: channel.headers,\n  }) as EventSource;\n  channel.state = \"active\";\n  channel.src.addEventListener(\"features\", channel.cb);\n  channel.src.addEventListener(\"features-updated\", channel.cb);\n  channel.src.onerror = () => onSSEError(channel);\n  channel.src.onopen = () => {\n    channel.errors = 0;\n  };\n}\n\nfunction destroyChannel(channel: ScopedChannel, key: string) {\n  disableChannel(channel);\n  streams.delete(key);\n}\n\nfunction clearAutoRefresh() {\n  // Clear list of which keys are auto-updated\n  supportsSSE.clear();\n\n  // Stop listening for any SSE events\n  streams.forEach(destroyChannel);\n\n  // Remove all references to GrowthBook instances\n  subscribedInstances.clear();\n\n  // Run the idle stream cleanup function\n  helpers.stopIdleListener();\n}\n", "var validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nvar nullController = {\n  revert: function revert() {}\n};\nvar elements = /*#__PURE__*/new Map();\nvar mutations = /*#__PURE__*/new Set();\n\nfunction getObserverInit(attr) {\n  return attr === 'html' ? {\n    childList: true,\n    subtree: true,\n    attributes: true,\n    characterData: true\n  } : {\n    childList: false,\n    subtree: false,\n    attributes: true,\n    attributeFilter: [attr]\n  };\n}\n\nfunction getElementRecord(element) {\n  var record = elements.get(element);\n\n  if (!record) {\n    record = {\n      element: element,\n      attributes: {}\n    };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(el, attr, getCurrentValue, setValue, mutationRunner) {\n  var currentValue = getCurrentValue(el);\n  var record = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el: el,\n    _positionTimeout: null,\n    observer: new MutationObserver(function () {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;else if (attr === 'position') record._positionTimeout = setTimeout(function () {\n        record._positionTimeout = null;\n      }, 1000);\n      var currentValue = getCurrentValue(el);\n      if (attr === 'position' && currentValue.parentNode === record.virtualValue.parentNode && currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode) return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner: mutationRunner,\n    setValue: setValue,\n    getCurrentValue: getCurrentValue\n  };\n\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n\n  return record;\n}\n\nfunction queueIfNeeded(val, record) {\n  var currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n\n  if (val && typeof val !== 'string') {\n    if (!currentVal || val.parentNode !== currentVal.parentNode || val.insertBeforeNode !== currentVal.insertBeforeNode) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(getTransformedHTML(val), record);\n}\n\nfunction classMutationRunner(record) {\n  var val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(function (m) {\n    return m.mutate(val);\n  });\n  queueIfNeeded(Array.from(val).filter(Boolean).join(' '), record);\n}\n\nfunction attrMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes(_ref) {\n  var parentSelector = _ref.parentSelector,\n      insertBeforeSelector = _ref.insertBeforeSelector;\n  var parentNode = document.querySelector(parentSelector);\n  if (!parentNode) return null;\n  var insertBeforeNode = insertBeforeSelector ? document.querySelector(insertBeforeSelector) : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode: parentNode,\n    insertBeforeNode: insertBeforeNode\n  };\n}\n\nfunction positionMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    var selectors = m.mutate();\n\n    var newNodes = _loadDOMNodes(selectors);\n\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nvar getHTMLValue = function getHTMLValue(el) {\n  return el.innerHTML;\n};\n\nvar setHTMLValue = function setHTMLValue(el, value) {\n  return el.innerHTML = value;\n};\n\nfunction getElementHTMLRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(element, 'html', getHTMLValue, setHTMLValue, htmlMutationRunner);\n  }\n\n  return elementRecord.html;\n}\n\nvar getElementPosition = function getElementPosition(el) {\n  return {\n    parentNode: el.parentElement,\n    insertBeforeNode: el.nextElementSibling\n  };\n};\n\nvar setElementPosition = function setElementPosition(el, value) {\n  if (value.insertBeforeNode && !value.parentNode.contains(value.insertBeforeNode)) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\n\nfunction getElementPositionRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(element, 'position', getElementPosition, setElementPosition, positionMutationRunner);\n  }\n\n  return elementRecord.position;\n}\n\nvar setClassValue = function setClassValue(el, val) {\n  return val ? el.className = val : el.removeAttribute('class');\n};\n\nvar getClassValue = function getClassValue(el) {\n  return el.className;\n};\n\nfunction getElementClassRecord(el) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(el, 'class', getClassValue, setClassValue, classMutationRunner);\n  }\n\n  return elementRecord.classes;\n}\n\nvar getAttrValue = function getAttrValue(attrName) {\n  return function (el) {\n    var _el$getAttribute;\n\n    return (_el$getAttribute = el.getAttribute(attrName)) != null ? _el$getAttribute : null;\n  };\n};\n\nvar setAttrValue = function setAttrValue(attrName) {\n  return function (el, val) {\n    return val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\n  };\n};\n\nfunction getElementAttributeRecord(el, attr) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(el, attr, getAttrValue(attr), setAttrValue(attr), attrMutationRunner);\n  }\n\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el, attr) {\n  var element = elements.get(el);\n  if (!element) return;\n\n  if (attr === 'html') {\n    var _element$html, _element$html$observe;\n\n    (_element$html = element.html) == null ? void 0 : (_element$html$observe = _element$html.observer) == null ? void 0 : _element$html$observe.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    var _element$classes, _element$classes$obse;\n\n    (_element$classes = element.classes) == null ? void 0 : (_element$classes$obse = _element$classes.observer) == null ? void 0 : _element$classes$obse.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    var _element$position, _element$position$obs;\n\n    (_element$position = element.position) == null ? void 0 : (_element$position$obs = _element$position.observer) == null ? void 0 : _element$position$obs.disconnect();\n    delete element.position;\n  } else {\n    var _element$attributes, _element$attributes$a, _element$attributes$a2;\n\n    (_element$attributes = element.attributes) == null ? void 0 : (_element$attributes$a = _element$attributes[attr]) == null ? void 0 : (_element$attributes$a2 = _element$attributes$a.observer) == null ? void 0 : _element$attributes$a2.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nvar transformContainer;\n\nfunction getTransformedHTML(html) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue(el, attr, m) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  var val = m.virtualValue;\n\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n\n  m.setValue(el, val);\n}\n\nfunction setValue(m, el) {\n  m.html && setPropertyValue(el, 'html', m.html);\n  m.classes && setPropertyValue(el, 'class', m.classes);\n  m.position && setPropertyValue(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(function (attr) {\n    setPropertyValue(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n} // find or create ElementPropertyRecord, add mutation to it, then run\n\n\nfunction startMutating(mutation, element) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n} // get (existing) ElementPropertyRecord, remove mutation from it, then run\n\n\nfunction stopMutating(mutation, el) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n\n  if (!record) return;\n  var index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n} // maintain list of elements associated with mutation\n\n\nfunction refreshElementsSet(mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n  var existingElements = new Set(mutation.elements);\n  var matchingElements = document.querySelectorAll(mutation.selector);\n  matchingElements.forEach(function (el) {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation) {\n  mutation.elements.forEach(function (el) {\n    return stopMutating(mutation, el);\n  });\n  mutation.elements.clear();\n  mutations[\"delete\"](mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n} // Observer for elements that don't exist in the DOM yet\n\n\nvar observer;\nfunction disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nfunction connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(function () {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false\n  });\n} // run on init\n\nconnectGlobalObserver();\n\nfunction newMutation(m) {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController; // add to global index of mutations\n\n  mutations.add(m); // run refresh on init to establish list of elements associated w/ mutation\n\n  refreshElementsSet(m);\n  return {\n    revert: function revert() {\n      revertMutation(m);\n    }\n  };\n}\n\nfunction html(selector, mutate) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction position(selector, mutate) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction classes(selector, mutate) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction attribute(selector, attribute, mutate) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, function (classnames) {\n      var mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames.split(/\\s+/g).filter(Boolean).forEach(function (c) {\n        return classnames.add(c);\n      });\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute: attribute,\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction declarative(_ref2) {\n  var selector = _ref2.selector,\n      action = _ref2.action,\n      value = _ref2.value,\n      attr = _ref2.attribute,\n      parentSelector = _ref2.parentSelector,\n      insertBeforeSelector = _ref2.insertBeforeSelector;\n\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, function (val) {\n        return val + (value != null ? value : '');\n      });\n    } else if (action === 'set') {\n      return html(selector, function () {\n        return value != null ? value : '';\n      });\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, function (val) {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, function (val) {\n        if (value) val[\"delete\"](value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, function (val) {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, function () {\n        return {\n          insertBeforeSelector: insertBeforeSelector,\n          parentSelector: parentSelector\n        };\n      });\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, function (val) {\n        return val !== null ? val + (value != null ? value : '') : value != null ? value : '';\n      });\n    } else if (action === 'set') {\n      return attribute(selector, attr, function () {\n        return value != null ? value : '';\n      });\n    } else if (action === 'remove') {\n      return attribute(selector, attr, function () {\n        return null;\n      });\n    }\n  }\n\n  return nullController;\n}\n\nvar index = {\n  html: html,\n  classes: classes,\n  attribute: attribute,\n  position: position,\n  declarative: declarative\n};\n\nexport default index;\nexport { connectGlobalObserver, disconnectGlobalObserver, validAttributeName };\n//# sourceMappingURL=dom-mutator.esm.js.map\n", "import { UrlTarget, UrlTargetType, VariationRange } from \"./types/growthbook\";\n\nfunction hashFnv32a(str: string): number {\n  let hval = 0x811c9dc5;\n  const l = str.length;\n\n  for (let i = 0; i < l; i++) {\n    hval ^= str.charCodeAt(i);\n    hval +=\n      (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);\n  }\n  return hval >>> 0;\n}\n\nexport function hash(\n  seed: string,\n  value: string,\n  version: number\n): number | null {\n  // New unbiased hashing algorithm\n  if (version === 2) {\n    return (hashFnv32a(hashFnv32a(seed + value) + \"\") % 10000) / 10000;\n  }\n  // Original biased hashing algorithm (keep for backwards compatibility)\n  if (version === 1) {\n    return (hashFnv32a(value + seed) % 1000) / 1000;\n  }\n\n  // Unknown hash version\n  return null;\n}\n\nexport function getEqualWeights(n: number): number[] {\n  if (n <= 0) return [];\n  return new Array(n).fill(1 / n);\n}\n\nexport function inRange(n: number, range: VariationRange): boolean {\n  return n >= range[0] && n < range[1];\n}\n\nexport function inNamespace(\n  hashValue: string,\n  namespace: [string, number, number]\n): boolean {\n  const n = hash(\"__\" + namespace[0], hashValue, 1);\n  if (n === null) return false;\n  return n >= namespace[1] && n < namespace[2];\n}\n\nexport function chooseVariation(n: number, ranges: VariationRange[]): number {\n  for (let i = 0; i < ranges.length; i++) {\n    if (inRange(n, ranges[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nexport function getUrlRegExp(regexString: string): RegExp | undefined {\n  try {\n    const escaped = regexString.replace(/([^\\\\])\\//g, \"$1\\\\/\");\n    return new RegExp(escaped);\n  } catch (e) {\n    console.error(e);\n    return undefined;\n  }\n}\n\nexport function isURLTargeted(url: string, targets: UrlTarget[]) {\n  if (!targets.length) return false;\n  let hasIncludeRules = false;\n  let isIncluded = false;\n\n  for (let i = 0; i < targets.length; i++) {\n    const match = _evalURLTarget(url, targets[i].type, targets[i].pattern);\n    if (targets[i].include === false) {\n      if (match) return false;\n    } else {\n      hasIncludeRules = true;\n      if (match) isIncluded = true;\n    }\n  }\n\n  return isIncluded || !hasIncludeRules;\n}\n\nfunction _evalSimpleUrlPart(\n  actual: string,\n  pattern: string,\n  isPath: boolean\n): boolean {\n  try {\n    // Escape special regex characters and change wildcard `_____` to `.*`\n    let escaped = pattern\n      .replace(/[*.+?^${}()|[\\]\\\\]/g, \"\\\\$&\")\n      .replace(/_____/g, \".*\");\n\n    if (isPath) {\n      // When matching pathname, make leading/trailing slashes optional\n      escaped = \"\\\\/?\" + escaped.replace(/(^\\/|\\/$)/g, \"\") + \"\\\\/?\";\n    }\n\n    const regex = new RegExp(\"^\" + escaped + \"$\", \"i\");\n    return regex.test(actual);\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalSimpleUrlTarget(actual: URL, pattern: string) {\n  try {\n    // If a protocol is missing, but a host is specified, add `https://` to the front\n    // Use \"_____\" as the wildcard since `*` is not a valid hostname in some browsers\n    const expected = new URL(\n      pattern.replace(/^([^:/?]*)\\./i, \"https://$1.\").replace(/\\*/g, \"_____\"),\n      \"https://_____\"\n    );\n\n    // Compare each part of the URL separately\n    const comps: Array<[string, string, boolean]> = [\n      [actual.host, expected.host, false],\n      [actual.pathname, expected.pathname, true],\n    ];\n    // We only want to compare hashes if it's explicitly being targeted\n    if (expected.hash) {\n      comps.push([actual.hash, expected.hash, false]);\n    }\n\n    expected.searchParams.forEach((v, k) => {\n      comps.push([actual.searchParams.get(k) || \"\", v, false]);\n    });\n\n    // If any comparisons fail, the whole thing fails\n    return !comps.some(\n      (data) => !_evalSimpleUrlPart(data[0], data[1], data[2])\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalURLTarget(\n  url: string,\n  type: UrlTargetType,\n  pattern: string\n): boolean {\n  try {\n    const parsed = new URL(url, \"https://_\");\n\n    if (type === \"regex\") {\n      const regex = getUrlRegExp(pattern);\n      if (!regex) return false;\n      return (\n        regex.test(parsed.href) ||\n        regex.test(parsed.href.substring(parsed.origin.length))\n      );\n    } else if (type === \"simple\") {\n      return _evalSimpleUrlTarget(parsed, pattern);\n    }\n\n    return false;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function getBucketRanges(\n  numVariations: number,\n  coverage: number | undefined,\n  weights?: number[]\n): VariationRange[] {\n  coverage = coverage === undefined ? 1 : coverage;\n\n  // Make sure coverage is within bounds\n  if (coverage < 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be greater than or equal to 0\");\n    }\n    coverage = 0;\n  } else if (coverage > 1) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be less than or equal to 1\");\n    }\n    coverage = 1;\n  }\n\n  // Default to equal weights if missing or invalid\n  const equal = getEqualWeights(numVariations);\n  weights = weights || equal;\n  if (weights.length !== numVariations) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\n        \"Experiment.weights array must be the same length as Experiment.variations\"\n      );\n    }\n    weights = equal;\n  }\n\n  // If weights don't add up to 1 (or close to it), default to equal weights\n  const totalWeight = weights.reduce((w, sum) => sum + w, 0);\n  if (totalWeight < 0.99 || totalWeight > 1.01) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.weights must add up to 1\");\n    }\n    weights = equal;\n  }\n\n  // Covert weights to ranges\n  let cumulative = 0;\n  return weights.map((w) => {\n    const start = cumulative;\n    cumulative += w;\n    return [start, start + (coverage as number) * w];\n  }) as VariationRange[];\n}\n\nexport function getQueryStringOverride(\n  id: string,\n  url: string,\n  numVariations: number\n) {\n  if (!url) {\n    return null;\n  }\n\n  const search = url.split(\"?\")[1];\n  if (!search) {\n    return null;\n  }\n\n  const match = search\n    .replace(/#.*/, \"\") // Get rid of anchor\n    .split(\"&\") // Split into key/value pairs\n    .map((kv) => kv.split(\"=\", 2))\n    .filter(([k]) => k === id) // Look for key that matches the experiment id\n    .map(([, v]) => parseInt(v)); // Parse the value into an integer\n\n  if (match.length > 0 && match[0] >= 0 && match[0] < numVariations)\n    return match[0];\n\n  return null;\n}\n\nexport function isIncluded(include: () => boolean) {\n  try {\n    return include();\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\n\nconst base64ToBuf = (b: string) =>\n  Uint8Array.from(atob(b), (c) => c.charCodeAt(0));\n\nexport async function decrypt(\n  encryptedString: string,\n  decryptionKey?: string,\n  subtle?: SubtleCrypto\n): Promise<string> {\n  decryptionKey = decryptionKey || \"\";\n  subtle = subtle || (globalThis.crypto && globalThis.crypto.subtle);\n  if (!subtle) {\n    throw new Error(\"No SubtleCrypto implementation found\");\n  }\n  try {\n    const key = await subtle.importKey(\n      \"raw\",\n      base64ToBuf(decryptionKey),\n      { name: \"AES-CBC\", length: 128 },\n      true,\n      [\"encrypt\", \"decrypt\"]\n    );\n    const [iv, cipherText] = encryptedString.split(\".\");\n    const plainTextBuffer = await subtle.decrypt(\n      { name: \"AES-CBC\", iv: base64ToBuf(iv) },\n      key,\n      base64ToBuf(cipherText)\n    );\n\n    return new TextDecoder().decode(plainTextBuffer);\n  } catch (e) {\n    throw new Error(\"Failed to decrypt\");\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function toString(input: any): string {\n  if (typeof input === \"string\") return input;\n  return JSON.stringify(input);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function paddedVersionString(input: any): string {\n  if (typeof input === \"number\") {\n    input = input + \"\";\n  }\n  if (!input || typeof input !== \"string\") {\n    input = \"0\";\n  }\n  // Remove build info and leading `v` if any\n  // Split version into parts (both core version numbers and pre-release tags)\n  // \"v1.2.3-rc.1+build123\" -> [\"1\",\"2\",\"3\",\"rc\",\"1\"]\n  const parts = (input as string).replace(/(^v|\\+.*$)/g, \"\").split(/[-.]/);\n\n  // If it's SemVer without a pre-release, add `~` to the end\n  // [\"1\",\"0\",\"0\"] -> [\"1\",\"0\",\"0\",\"~\"]\n  // \"~\" is the largest ASCII character, so this will make \"1.0.0\" greater than \"1.0.0-beta\" for example\n  if (parts.length === 3) {\n    parts.push(\"~\");\n  }\n\n  // Left pad each numeric part with spaces so string comparisons will work (\"9\">\"10\", but \" 9\"<\"10\")\n  // Then, join back together into a single string\n  return parts\n    .map((v) => (v.match(/^[0-9]+$/) ? v.padStart(5, \" \") : v))\n    .join(\"-\");\n}\n\nexport function loadSDKVersion(): string {\n  let version: string;\n  try {\n    // @ts-expect-error right-hand value to be replaced by build with string literal\n    version = __SDK_VERSION__;\n  } catch (e) {\n    version = \"\";\n  }\n  return version;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport {\n  ConditionInterface,\n  TestedObj,\n  ConditionValue,\n  Operator,\n  OperatorConditionValue,\n  VarType,\n} from \"./types/mongrule\";\nimport { paddedVersionString } from \"./util\";\n\nconst _regexCache: { [key: string]: RegExp } = {};\n\n// The top-level condition evaluation function\nexport function evalCondition(\n  obj: TestedObj,\n  condition: ConditionInterface\n): boolean {\n  // Recursive condition\n  if (\"$or\" in condition) {\n    return evalOr(obj, condition[\"$or\"] as ConditionInterface[]);\n  }\n  if (\"$nor\" in condition) {\n    return !evalOr(obj, condition[\"$nor\"] as ConditionInterface[]);\n  }\n  if (\"$and\" in condition) {\n    return evalAnd(obj, condition[\"$and\"] as ConditionInterface[]);\n  }\n  if (\"$not\" in condition) {\n    return !evalCondition(obj, condition[\"$not\"] as ConditionInterface);\n  }\n\n  // Condition is an object, keys are object paths, values are the condition for that path\n  for (const [k, v] of Object.entries(condition)) {\n    if (!evalConditionValue(v, getPath(obj, k))) return false;\n  }\n  return true;\n}\n\n// Return value at dot-separated path of an object\nfunction getPath(obj: TestedObj, path: string) {\n  const parts = path.split(\".\");\n  let current: any = obj;\n  for (let i = 0; i < parts.length; i++) {\n    if (current && typeof current === \"object\" && parts[i] in current) {\n      current = current[parts[i]];\n    } else {\n      return null;\n    }\n  }\n  return current;\n}\n\n// Transform a regex string into a real RegExp object\nfunction getRegex(regex: string): RegExp {\n  if (!_regexCache[regex]) {\n    _regexCache[regex] = new RegExp(regex.replace(/([^\\\\])\\//g, \"$1\\\\/\"));\n  }\n  return _regexCache[regex];\n}\n\n// Evaluate a single value against a condition\nfunction evalConditionValue(condition: ConditionValue, value: any) {\n  // Simple equality comparisons\n  if (typeof condition === \"string\") {\n    return value + \"\" === condition;\n  }\n  if (typeof condition === \"number\") {\n    return value * 1 === condition;\n  }\n  if (typeof condition === \"boolean\") {\n    return !!value === condition;\n  }\n\n  if (condition === null) {\n    return value === null;\n  }\n\n  if (Array.isArray(condition) || !isOperatorObject(condition)) {\n    return JSON.stringify(value) === JSON.stringify(condition);\n  }\n\n  // This is a special operator condition and we should evaluate each one separately\n  for (const op in condition) {\n    if (\n      !evalOperatorCondition(\n        op as Operator,\n        value,\n        condition[op as keyof OperatorConditionValue]\n      )\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// If the object has only keys that start with '$'\nfunction isOperatorObject(obj: any): boolean {\n  const keys = Object.keys(obj);\n  return (\n    keys.length > 0 && keys.filter((k) => k[0] === \"$\").length === keys.length\n  );\n}\n\n// Return the data type of a value\nfunction getType(v: any): VarType | \"unknown\" {\n  if (v === null) return \"null\";\n  if (Array.isArray(v)) return \"array\";\n  const t = typeof v;\n  if ([\"string\", \"number\", \"boolean\", \"object\", \"undefined\"].includes(t)) {\n    return t as VarType;\n  }\n  return \"unknown\";\n}\n\n// At least one element of actual must match the expected condition/value\nfunction elemMatch(actual: any, expected: any) {\n  if (!Array.isArray(actual)) return false;\n  const check = isOperatorObject(expected)\n    ? (v: any) => evalConditionValue(expected, v)\n    : (v: any) => evalCondition(v, expected);\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i] && check(actual[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isIn(actual: any, expected: Array<any>): boolean {\n  // Do an intersection is attribute is an array\n  if (Array.isArray(actual)) {\n    return actual.some((el) => expected.includes(el));\n  }\n  return expected.includes(actual);\n}\n\n// Evaluate a single operator condition\nfunction evalOperatorCondition(\n  operator: Operator,\n  actual: any,\n  expected: any\n): boolean {\n  switch (operator) {\n    case \"$veq\":\n      return paddedVersionString(actual) === paddedVersionString(expected);\n    case \"$vne\":\n      return paddedVersionString(actual) !== paddedVersionString(expected);\n    case \"$vgt\":\n      return paddedVersionString(actual) > paddedVersionString(expected);\n    case \"$vgte\":\n      return paddedVersionString(actual) >= paddedVersionString(expected);\n    case \"$vlt\":\n      return paddedVersionString(actual) < paddedVersionString(expected);\n    case \"$vlte\":\n      return paddedVersionString(actual) <= paddedVersionString(expected);\n    case \"$eq\":\n      return actual === expected;\n    case \"$ne\":\n      return actual !== expected;\n    case \"$lt\":\n      return actual < expected;\n    case \"$lte\":\n      return actual <= expected;\n    case \"$gt\":\n      return actual > expected;\n    case \"$gte\":\n      return actual >= expected;\n    case \"$exists\":\n      // Using `!=` and `==` instead of strict checks so it also matches for undefined\n      return expected ? actual != null : actual == null;\n    case \"$in\":\n      if (!Array.isArray(expected)) return false;\n      return isIn(actual, expected);\n    case \"$nin\":\n      if (!Array.isArray(expected)) return false;\n      return !isIn(actual, expected);\n    case \"$not\":\n      return !evalConditionValue(expected, actual);\n    case \"$size\":\n      if (!Array.isArray(actual)) return false;\n      return evalConditionValue(expected, actual.length);\n    case \"$elemMatch\":\n      return elemMatch(actual, expected);\n    case \"$all\":\n      if (!Array.isArray(actual)) return false;\n      for (let i = 0; i < expected.length; i++) {\n        let passed = false;\n        for (let j = 0; j < actual.length; j++) {\n          if (evalConditionValue(expected[i], actual[j])) {\n            passed = true;\n            break;\n          }\n        }\n        if (!passed) return false;\n      }\n      return true;\n    case \"$regex\":\n      try {\n        return getRegex(expected).test(actual);\n      } catch (e) {\n        return false;\n      }\n    case \"$type\":\n      return getType(actual) === expected;\n    default:\n      console.error(\"Unknown operator: \" + operator);\n      return false;\n  }\n}\n\n// Recursive $or rule\nfunction evalOr(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  if (!conditions.length) return true;\n  for (let i = 0; i < conditions.length; i++) {\n    if (evalCondition(obj, conditions[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// Recursive $and rule\nfunction evalAnd(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  for (let i = 0; i < conditions.length; i++) {\n    if (!evalCondition(obj, conditions[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n", "import mutate, { DeclarativeMutation } from \"dom-mutator\";\nimport type {\n  ApiHost,\n  Attributes,\n  AutoExperiment,\n  AutoExperimentVariation,\n  ClientKey,\n  Context,\n  Experiment,\n  FeatureApiResponse,\n  FeatureDefinition,\n  FeatureResult,\n  FeatureResultSource,\n  Filter,\n  LoadFeaturesOptions,\n  RealtimeUsageData,\n  RefreshFeaturesOptions,\n  Result,\n  StickyAssignments,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n  StickyExperimentKey,\n  SubscriptionFunction,\n  VariationMeta,\n  VariationRange,\n  WidenPrimitives,\n} from \"./types/growthbook\";\nimport type { ConditionInterface } from \"./types/mongrule\";\nimport {\n  chooseVariation,\n  decrypt,\n  getBucketRanges,\n  getQueryStringOverride,\n  getUrlRegExp,\n  hash,\n  inNamespace,\n  inRange,\n  isIncluded,\n  isURLTargeted,\n  loadSDKVersion,\n  toString,\n} from \"./util\";\nimport { evalCondition } from \"./mongrule\";\nimport { refreshFeatures, subscribe, unsubscribe } from \"./feature-repository\";\n\nconst isBrowser =\n  typeof window !== \"undefined\" && typeof document !== \"undefined\";\n\nconst SDK_VERSION = loadSDKVersion();\n\nexport class GrowthBook<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  AppFeatures extends Record<string, any> = Record<string, any>\n> {\n  // context is technically private, but some tools depend on it so we can't mangle the name\n  // _ctx below is a clone of this property that we use internally\n  private context: Context;\n  public debug: boolean;\n  public ready: boolean;\n  public version: string;\n\n  // Properties and methods that start with \"_\" are mangled by Terser (saves ~150 bytes)\n  private _ctx: Context;\n  private _renderer: null | (() => void);\n  private _trackedExperiments: Set<unknown>;\n  private _trackedFeatures: Record<string, string>;\n  private _subscriptions: Set<SubscriptionFunction>;\n  private _rtQueue: RealtimeUsageData[];\n  private _rtTimer: number;\n  private _assigned: Map<\n    string,\n    {\n      // eslint-disable-next-line\n      experiment: Experiment<any>;\n      // eslint-disable-next-line\n      result: Result<any>;\n    }\n  >;\n  // eslint-disable-next-line\n  private _forcedFeatureValues: Map<string, any>;\n  private _attributeOverrides: Attributes;\n  private _activeAutoExperiments: Map<\n    AutoExperiment,\n    { valueHash: string; undo: () => void }\n  >;\n  private _triggeredExpKeys: Set<string>;\n  private _loadFeaturesCalled: boolean;\n\n  constructor(context?: Context) {\n    context = context || {};\n    // These properties are all initialized in the constructor instead of above\n    // This saves ~80 bytes in the final output\n    this.version = SDK_VERSION;\n    this._ctx = this.context = context;\n    this._renderer = null;\n    this._trackedExperiments = new Set();\n    this._trackedFeatures = {};\n    this.debug = false;\n    this._subscriptions = new Set();\n    this._rtQueue = [];\n    this._rtTimer = 0;\n    this.ready = false;\n    this._assigned = new Map();\n    this._forcedFeatureValues = new Map();\n    this._attributeOverrides = {};\n    this._activeAutoExperiments = new Map();\n    this._triggeredExpKeys = new Set();\n    this._loadFeaturesCalled = false;\n\n    if (context.remoteEval) {\n      if (context.decryptionKey) {\n        throw new Error(\"Encryption is not available for remoteEval\");\n      }\n      if (!context.clientKey) {\n        throw new Error(\"Missing clientKey\");\n      }\n      let isGbHost = false;\n      try {\n        isGbHost = !!new URL(context.apiHost || \"\").hostname.match(\n          /growthbook\\.io$/i\n        );\n      } catch (e) {\n        // ignore invalid URLs\n      }\n      if (isGbHost) {\n        throw new Error(\"Cannot use remoteEval on GrowthBook Cloud\");\n      }\n    } else {\n      if (context.cacheKeyAttributes) {\n        throw new Error(\"cacheKeyAttributes are only used for remoteEval\");\n      }\n    }\n\n    if (context.features) {\n      this.ready = true;\n    }\n\n    if (isBrowser && context.enableDevMode) {\n      window._growthbook = this;\n      document.dispatchEvent(new Event(\"gbloaded\"));\n    }\n\n    if (context.experiments) {\n      this.ready = true;\n      this._updateAllAutoExperiments();\n    }\n\n    if (context.clientKey && !context.remoteEval) {\n      this._refresh({}, true, false);\n    }\n  }\n\n  public async loadFeatures(options?: LoadFeaturesOptions): Promise<void> {\n    if (options && options.autoRefresh) {\n      // interpret deprecated autoRefresh option as subscribeToChanges\n      this._ctx.subscribeToChanges = true;\n    }\n    this._loadFeaturesCalled = true;\n\n    await this._refresh(options, true, true);\n\n    if (this._canSubscribe()) {\n      subscribe(this);\n    }\n  }\n\n  public async refreshFeatures(\n    options?: RefreshFeaturesOptions\n  ): Promise<void> {\n    await this._refresh(options, false, true);\n  }\n\n  public getApiInfo(): [ApiHost, ClientKey] {\n    return [this.getApiHosts().apiHost, this.getClientKey()];\n  }\n  public getApiHosts(): {\n    apiHost: string;\n    streamingHost: string;\n    apiRequestHeaders?: Record<string, string>;\n    streamingHostRequestHeaders?: Record<string, string>;\n  } {\n    const defaultHost = this._ctx.apiHost || \"https://cdn.growthbook.io\";\n    return {\n      apiHost: defaultHost.replace(/\\/*$/, \"\"),\n      streamingHost: (this._ctx.streamingHost || defaultHost).replace(\n        /\\/*$/,\n        \"\"\n      ),\n      apiRequestHeaders: this._ctx.apiHostRequestHeaders,\n      streamingHostRequestHeaders: this._ctx.streamingHostRequestHeaders,\n    };\n  }\n  public getClientKey(): string {\n    return this._ctx.clientKey || \"\";\n  }\n\n  public isRemoteEval(): boolean {\n    return this._ctx.remoteEval || false;\n  }\n\n  public getCacheKeyAttributes(): (keyof Attributes)[] | undefined {\n    return this._ctx.cacheKeyAttributes;\n  }\n\n  private async _refresh(\n    options?: RefreshFeaturesOptions,\n    allowStale?: boolean,\n    updateInstance?: boolean\n  ) {\n    options = options || {};\n    if (!this._ctx.clientKey) {\n      throw new Error(\"Missing clientKey\");\n    }\n    await refreshFeatures(\n      this,\n      options.timeout,\n      options.skipCache || this._ctx.enableDevMode,\n      allowStale,\n      updateInstance,\n      this._ctx.backgroundSync !== false\n    );\n  }\n\n  private _render() {\n    if (this._renderer) {\n      this._renderer();\n    }\n  }\n\n  public setFeatures(features: Record<string, FeatureDefinition>) {\n    this._ctx.features = features;\n    this.ready = true;\n    this._render();\n  }\n\n  public async setEncryptedFeatures(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const featuresJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setFeatures(\n      JSON.parse(featuresJSON) as Record<string, FeatureDefinition>\n    );\n  }\n\n  public setExperiments(experiments: AutoExperiment[]): void {\n    this._ctx.experiments = experiments;\n    this.ready = true;\n    this._updateAllAutoExperiments();\n  }\n\n  public async setEncryptedExperiments(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const experimentsJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setExperiments(JSON.parse(experimentsJSON) as AutoExperiment[]);\n  }\n\n  public async decryptPayload(\n    data: FeatureApiResponse,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<FeatureApiResponse> {\n    if (data.encryptedFeatures) {\n      data.features = JSON.parse(\n        await decrypt(\n          data.encryptedFeatures,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedFeatures;\n    }\n    if (data.encryptedExperiments) {\n      data.experiments = JSON.parse(\n        await decrypt(\n          data.encryptedExperiments,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedExperiments;\n    }\n    return data;\n  }\n\n  public async setAttributes(attributes: Attributes) {\n    this._ctx.attributes = attributes;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setAttributeOverrides(overrides: Attributes) {\n    this._attributeOverrides = overrides;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setForcedVariations(vars: Record<string, number>) {\n    this._ctx.forcedVariations = vars || {};\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  // eslint-disable-next-line\n  public setForcedFeatures(map: Map<string, any>) {\n    this._forcedFeatureValues = map;\n    this._render();\n  }\n\n  public async setURL(url: string) {\n    this._ctx.url = url;\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      this._updateAllAutoExperiments(true);\n      return;\n    }\n    this._updateAllAutoExperiments(true);\n  }\n\n  public getAttributes() {\n    return { ...this._ctx.attributes, ...this._attributeOverrides };\n  }\n\n  public getForcedVariations() {\n    return this._ctx.forcedVariations || {};\n  }\n\n  public getForcedFeatures() {\n    // eslint-disable-next-line\n    return this._forcedFeatureValues || new Map<string, any>();\n  }\n\n  public getStickyBucketAssignmentDocs() {\n    return this._ctx.stickyBucketAssignmentDocs || {};\n  }\n\n  public getUrl() {\n    return this._ctx.url || \"\";\n  }\n\n  public getFeatures() {\n    return this._ctx.features || {};\n  }\n\n  public getExperiments() {\n    return this._ctx.experiments || [];\n  }\n\n  public subscribe(cb: SubscriptionFunction): () => void {\n    this._subscriptions.add(cb);\n    return () => {\n      this._subscriptions.delete(cb);\n    };\n  }\n\n  private _canSubscribe() {\n    return this._ctx.backgroundSync !== false && this._ctx.subscribeToChanges;\n  }\n\n  private async _refreshForRemoteEval() {\n    if (!this._ctx.remoteEval) return;\n    if (!this._loadFeaturesCalled) return;\n    await this._refresh({}, false, true).catch(() => {\n      // Ignore errors\n    });\n  }\n\n  public getAllResults() {\n    return new Map(this._assigned);\n  }\n\n  public destroy() {\n    // Release references to save memory\n    this._subscriptions.clear();\n    this._assigned.clear();\n    this._trackedExperiments.clear();\n    this._trackedFeatures = {};\n    this._rtQueue = [];\n    if (this._rtTimer) {\n      clearTimeout(this._rtTimer);\n    }\n    unsubscribe(this);\n\n    if (isBrowser && window._growthbook === this) {\n      delete window._growthbook;\n    }\n\n    // Undo any active auto experiments\n    this._activeAutoExperiments.forEach((exp) => {\n      exp.undo();\n    });\n    this._activeAutoExperiments.clear();\n    this._triggeredExpKeys.clear();\n  }\n\n  public setRenderer(renderer: () => void) {\n    this._renderer = renderer;\n  }\n\n  public forceVariation(key: string, variation: number) {\n    this._ctx.forcedVariations = this._ctx.forcedVariations || {};\n    this._ctx.forcedVariations[key] = variation;\n    if (this._ctx.remoteEval) {\n      this._refreshForRemoteEval();\n      return;\n    }\n    this._updateAllAutoExperiments();\n    this._render();\n  }\n\n  public run<T>(experiment: Experiment<T>): Result<T> {\n    const result = this._run(experiment, null);\n    this._fireSubscriptions(experiment, result);\n    return result;\n  }\n\n  public triggerExperiment(key: string) {\n    this._triggeredExpKeys.add(key);\n    if (!this._ctx.experiments) return null;\n    const experiments = this._ctx.experiments.filter((exp) => exp.key === key);\n    return experiments\n      .map((exp) => {\n        if (!exp.manual) return null;\n        return this._runAutoExperiment(exp);\n      })\n      .filter((res) => res !== null);\n  }\n\n  private _runAutoExperiment(experiment: AutoExperiment, forceRerun?: boolean) {\n    const existing = this._activeAutoExperiments.get(experiment);\n\n    // If this is a manual experiment and it's not already running, skip\n    if (\n      experiment.manual &&\n      !this._triggeredExpKeys.has(experiment.key) &&\n      !existing\n    )\n      return null;\n\n    // Run the experiment\n    const result = this.run(experiment);\n\n    // A hash to quickly tell if the assigned value changed\n    const valueHash = JSON.stringify(result.value);\n\n    // If the changes are already active, no need to re-apply them\n    if (\n      !forceRerun &&\n      result.inExperiment &&\n      existing &&\n      existing.valueHash === valueHash\n    ) {\n      return result;\n    }\n\n    // Undo any existing changes\n    if (existing) this._undoActiveAutoExperiment(experiment);\n\n    // Apply new changes\n    if (result.inExperiment) {\n      const undo = this._applyDOMChanges(result.value);\n      if (undo) {\n        this._activeAutoExperiments.set(experiment, {\n          undo,\n          valueHash,\n        });\n      }\n    }\n\n    return result;\n  }\n\n  private _undoActiveAutoExperiment(exp: AutoExperiment) {\n    const data = this._activeAutoExperiments.get(exp);\n    if (data) {\n      data.undo();\n      this._activeAutoExperiments.delete(exp);\n    }\n  }\n\n  private _updateAllAutoExperiments(forceRerun?: boolean) {\n    const experiments = this._ctx.experiments || [];\n\n    // Stop any experiments that are no longer defined\n    const keys = new Set(experiments);\n    this._activeAutoExperiments.forEach((v, k) => {\n      if (!keys.has(k)) {\n        v.undo();\n        this._activeAutoExperiments.delete(k);\n      }\n    });\n\n    // Re-run all new/updated experiments\n    experiments.forEach((exp) => {\n      this._runAutoExperiment(exp, forceRerun);\n    });\n  }\n\n  private _fireSubscriptions<T>(experiment: Experiment<T>, result: Result<T>) {\n    const key = experiment.key;\n\n    // If assigned variation has changed, fire subscriptions\n    const prev = this._assigned.get(key);\n    // TODO: what if the experiment definition has changed?\n    if (\n      !prev ||\n      prev.result.inExperiment !== result.inExperiment ||\n      prev.result.variationId !== result.variationId\n    ) {\n      this._assigned.set(key, { experiment, result });\n      this._subscriptions.forEach((cb) => {\n        try {\n          cb(experiment, result);\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }\n\n  private _trackFeatureUsage(key: string, res: FeatureResult): void {\n    // Don't track feature usage that was forced via an override\n    if (res.source === \"override\") return;\n\n    // Only track a feature once, unless the assigned value changed\n    const stringifiedValue = JSON.stringify(res.value);\n    if (this._trackedFeatures[key] === stringifiedValue) return;\n    this._trackedFeatures[key] = stringifiedValue;\n\n    // Fire user-supplied callback\n    if (this._ctx.onFeatureUsage) {\n      try {\n        this._ctx.onFeatureUsage(key, res);\n      } catch (e) {\n        // Ignore feature usage callback errors\n      }\n    }\n\n    // In browser environments, queue up feature usage to be tracked in batches\n    if (!isBrowser || !window.fetch) return;\n    this._rtQueue.push({\n      key,\n      on: res.on,\n    });\n    if (!this._rtTimer) {\n      this._rtTimer = window.setTimeout(() => {\n        // Reset the queue\n        this._rtTimer = 0;\n        const q = [...this._rtQueue];\n        this._rtQueue = [];\n\n        // Skip logging if a real-time usage key is not configured\n        if (!this._ctx.realtimeKey) return;\n\n        window\n          .fetch(\n            `https://rt.growthbook.io/?key=${\n              this._ctx.realtimeKey\n            }&events=${encodeURIComponent(JSON.stringify(q))}`,\n\n            {\n              cache: \"no-cache\",\n              mode: \"no-cors\",\n            }\n          )\n          .catch(() => {\n            // TODO: retry in case of network errors?\n          });\n      }, this._ctx.realtimeInterval || 2000);\n    }\n  }\n\n  private _getFeatureResult<T>(\n    key: string,\n    value: T,\n    source: FeatureResultSource,\n    ruleId?: string,\n    experiment?: Experiment<T>,\n    result?: Result<T>\n  ): FeatureResult<T> {\n    const ret: FeatureResult = {\n      value,\n      on: !!value,\n      off: !value,\n      source,\n      ruleId: ruleId || \"\",\n    };\n    if (experiment) ret.experiment = experiment;\n    if (result) ret.experimentResult = result;\n\n    // Track the usage of this feature in real-time\n    this._trackFeatureUsage(key, ret);\n\n    return ret;\n  }\n\n  public isOn<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).on;\n  }\n\n  public isOff<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).off;\n  }\n\n  public getFeatureValue<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(key: K, defaultValue: V): WidenPrimitives<V> {\n    const value = this.evalFeature<WidenPrimitives<V>, K>(key).value;\n    return value === null ? (defaultValue as WidenPrimitives<V>) : value;\n  }\n\n  /**\n   * @deprecated Use {@link evalFeature}\n   * @param id\n   */\n  // eslint-disable-next-line\n  public feature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    return this.evalFeature(id);\n  }\n\n  public evalFeature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    // Global override\n    if (this._forcedFeatureValues.has(id)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Global override\", {\n          id,\n          value: this._forcedFeatureValues.get(id),\n        });\n      return this._getFeatureResult(\n        id,\n        this._forcedFeatureValues.get(id),\n        \"override\"\n      );\n    }\n\n    // Unknown feature id\n    if (!this._ctx.features || !this._ctx.features[id]) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Unknown feature\", { id });\n      return this._getFeatureResult(id, null, \"unknownFeature\");\n    }\n\n    // Get the feature\n    const feature: FeatureDefinition<V> = this._ctx.features[id];\n\n    // Loop through the rules\n    if (feature.rules) {\n      for (const rule of feature.rules) {\n        // If there are filters for who is included (e.g. namespaces)\n        if (rule.filters && this._isFilteredOut(rule.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip rule because of filters\", {\n              id,\n              rule,\n            });\n          continue;\n        }\n\n        // Feature value is being forced\n        if (\"force\" in rule) {\n          // If it's a conditional rule, skip if the condition doesn't pass\n          if (rule.condition && !this._conditionPasses(rule.condition)) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because of condition ff\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          // If this is a percentage rollout, skip if not included\n          if (\n            !this._isIncludedInRollout(\n              rule.seed || id,\n              rule.hashAttribute,\n              this._ctx.stickyBucketService && !rule.disableStickyBucketing\n                ? rule.fallbackAttribute\n                : undefined,\n              rule.range,\n              rule.coverage,\n              rule.hashVersion\n            )\n          ) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because user not included in rollout\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Force value from rule\", {\n              id,\n              rule,\n            });\n\n          // If this was a remotely evaluated experiment, fire the tracking callbacks\n          if (rule.tracks) {\n            rule.tracks.forEach((t) => {\n              this._track(t.experiment, t.result);\n            });\n          }\n\n          return this._getFeatureResult(id, rule.force as V, \"force\", rule.id);\n        }\n        if (!rule.variations) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip invalid rule\", {\n              id,\n              rule,\n            });\n\n          continue;\n        }\n\n        // For experiment rules, run an experiment\n        const exp: Experiment<V> = {\n          variations: rule.variations as [V, V, ...V[]],\n          key: rule.key || id,\n        };\n        if (\"coverage\" in rule) exp.coverage = rule.coverage;\n        if (rule.weights) exp.weights = rule.weights;\n        if (rule.hashAttribute) exp.hashAttribute = rule.hashAttribute;\n        if (rule.fallbackAttribute)\n          exp.fallbackAttribute = rule.fallbackAttribute;\n        if (rule.disableStickyBucketing)\n          exp.disableStickyBucketing = rule.disableStickyBucketing;\n        if (rule.bucketVersion !== undefined)\n          exp.bucketVersion = rule.bucketVersion;\n        if (rule.minBucketVersion !== undefined)\n          exp.minBucketVersion = rule.minBucketVersion;\n        if (rule.namespace) exp.namespace = rule.namespace;\n        if (rule.meta) exp.meta = rule.meta;\n        if (rule.ranges) exp.ranges = rule.ranges;\n        if (rule.name) exp.name = rule.name;\n        if (rule.phase) exp.phase = rule.phase;\n        if (rule.seed) exp.seed = rule.seed;\n        if (rule.hashVersion) exp.hashVersion = rule.hashVersion;\n        if (rule.filters) exp.filters = rule.filters;\n        if (rule.condition) exp.condition = rule.condition;\n\n        // Only return a value if the user is part of the experiment\n        const res = this._run(exp, id);\n        this._fireSubscriptions(exp, res);\n        if (res.inExperiment && !res.passthrough) {\n          return this._getFeatureResult(\n            id,\n            res.value,\n            \"experiment\",\n            rule.id,\n            exp,\n            res\n          );\n        }\n      }\n    }\n\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"Use default value\", {\n        id,\n        value: feature.defaultValue,\n      });\n\n    // Fall back to using the default value\n    return this._getFeatureResult(\n      id,\n      feature.defaultValue === undefined ? null : feature.defaultValue,\n      \"defaultValue\"\n    );\n  }\n\n  private _isIncludedInRollout(\n    seed: string,\n    hashAttribute: string | undefined,\n    fallbackAttribute: string | undefined,\n    range: VariationRange | undefined,\n    coverage: number | undefined,\n    hashVersion: number | undefined\n  ): boolean {\n    if (!range && coverage === undefined) return true;\n\n    const { hashValue } = this._getHashAttribute(\n      hashAttribute,\n      fallbackAttribute\n    );\n    if (!hashValue) {\n      return false;\n    }\n\n    const n = hash(seed, hashValue, hashVersion || 1);\n    if (n === null) return false;\n\n    return range\n      ? inRange(n, range)\n      : coverage !== undefined\n      ? n <= coverage\n      : true;\n  }\n\n  private _conditionPasses(condition: ConditionInterface): boolean {\n    return evalCondition(this.getAttributes(), condition);\n  }\n\n  private _isFilteredOut(filters: Filter[]): boolean {\n    return filters.some((filter) => {\n      const { hashValue } = this._getHashAttribute(filter.attribute);\n      if (!hashValue) return true;\n      const n = hash(filter.seed, hashValue, filter.hashVersion || 2);\n      if (n === null) return true;\n      return !filter.ranges.some((r) => inRange(n, r));\n    });\n  }\n\n  private _run<T>(\n    experiment: Experiment<T>,\n    featureId: string | null\n  ): Result<T> {\n    const key = experiment.key;\n    const numVariations = experiment.variations.length;\n\n    // 1. If experiment has less than 2 variations, return immediately\n    if (numVariations < 2) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Invalid experiment\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2. If the context is disabled, return immediately\n    if (this._ctx.enabled === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Context disabled\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2.5. Merge in experiment overrides from the context\n    experiment = this._mergeOverrides(experiment);\n\n    // 2.6 New, more powerful URL targeting\n    if (\n      experiment.urlPatterns &&\n      !isURLTargeted(this._getContextUrl(), experiment.urlPatterns)\n    ) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url targeting\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 3. If a variation is forced from a querystring, return the forced variation\n    const qsOverride = getQueryStringOverride(\n      key,\n      this._getContextUrl(),\n      numVariations\n    );\n    if (qsOverride !== null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via querystring\", {\n          id: key,\n          variation: qsOverride,\n        });\n      return this._getResult(experiment, qsOverride, false, featureId);\n    }\n\n    // 4. If a variation is forced in the context, return the forced variation\n    if (this._ctx.forcedVariations && key in this._ctx.forcedVariations) {\n      const variation = this._ctx.forcedVariations[key];\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via dev tools\", {\n          id: key,\n          variation,\n        });\n      return this._getResult(experiment, variation, false, featureId);\n    }\n\n    // 5. Exclude if a draft experiment or not active\n    if (experiment.status === \"draft\" || experiment.active === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because inactive\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 6. Get the hash attribute and return if empty\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n    if (!hashValue) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because missing hashAttribute\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    let assigned = -1;\n\n    let foundStickyBucket = false;\n    let stickyBucketVersionIsBlocked = false;\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const { variation, versionIsBlocked } = this._getStickyBucketVariation(\n        experiment.key,\n        experiment.bucketVersion,\n        experiment.minBucketVersion,\n        experiment.meta\n      );\n      foundStickyBucket = variation >= 0;\n      assigned = variation;\n      stickyBucketVersionIsBlocked = !!versionIsBlocked;\n    }\n\n    // Some checks are not needed if we already have a sticky bucket\n    if (!foundStickyBucket) {\n      // 7. Exclude if user is filtered out (used to be called \"namespace\")\n      if (experiment.filters) {\n        if (this._isFilteredOut(experiment.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip because of filters\", {\n              id: key,\n            });\n          return this._getResult(experiment, -1, false, featureId);\n        }\n      } else if (\n        experiment.namespace &&\n        !inNamespace(hashValue, experiment.namespace)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of namespace\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 7.5. Exclude if experiment.include returns false or throws\n      if (experiment.include && !isIncluded(experiment.include)) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of include function\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8. Exclude if condition is false\n      if (\n        experiment.condition &&\n        !this._conditionPasses(experiment.condition)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of condition exp\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8.1. Exclude if user is not in a required group\n      if (\n        experiment.groups &&\n        !this._hasGroupOverlap(experiment.groups as string[])\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of groups\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n    }\n\n    // 8.2. Old style URL targeting\n    if (experiment.url && !this._urlIsValid(experiment.url as RegExp)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 9. Get the variation from the sticky bucket or get bucket ranges and choose variation\n    const n = hash(\n      experiment.seed || key,\n      hashValue,\n      experiment.hashVersion || 1\n    );\n    if (n === null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of invalid hash version\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    if (!foundStickyBucket) {\n      const ranges =\n        experiment.ranges ||\n        getBucketRanges(\n          numVariations,\n          experiment.coverage === undefined ? 1 : experiment.coverage,\n          experiment.weights\n        );\n      assigned = chooseVariation(n, ranges);\n    }\n\n    // 9.5 Unenroll if any prior sticky buckets are blocked by version\n    if (stickyBucketVersionIsBlocked) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because sticky bucket version is blocked\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId, undefined, true);\n    }\n\n    // 10. Return if not in experiment\n    if (assigned < 0) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of coverage\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 11. Experiment has a forced variation\n    if (\"force\" in experiment) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force variation\", {\n          id: key,\n          variation: experiment.force,\n        });\n      return this._getResult(\n        experiment,\n        experiment.force === undefined ? -1 : experiment.force,\n        false,\n        featureId\n      );\n    }\n\n    // 12. Exclude if in QA mode\n    if (this._ctx.qaMode) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because QA mode\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 12.5. Exclude if experiment is stopped\n    if (experiment.status === \"stopped\") {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because stopped\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 13. Build the result object\n    const result = this._getResult(\n      experiment,\n      assigned,\n      true,\n      featureId,\n      n,\n      foundStickyBucket\n    );\n\n    // 13.5. Persist sticky bucket\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const {\n        changed,\n        key: attrKey,\n        doc,\n      } = this._generateStickyBucketAssignmentDoc(\n        hashAttribute,\n        toString(hashValue),\n        {\n          [this._getStickyBucketExperimentKey(\n            experiment.key,\n            experiment.bucketVersion\n          )]: result.key,\n        }\n      );\n      if (changed) {\n        // update local docs\n        this._ctx.stickyBucketAssignmentDocs =\n          this._ctx.stickyBucketAssignmentDocs || {};\n        this._ctx.stickyBucketAssignmentDocs[attrKey] = doc;\n        // save doc\n        this._ctx.stickyBucketService.saveAssignments(doc);\n      }\n    }\n\n    // 14. Fire the tracking callback\n    this._track(experiment, result);\n\n    // 15. Return the result\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"In experiment\", {\n        id: key,\n        variation: result.variationId,\n      });\n    return result;\n  }\n\n  log(msg: string, ctx: Record<string, unknown>) {\n    if (!this.debug) return;\n    if (this._ctx.log) this._ctx.log(msg, ctx);\n    else console.log(msg, ctx);\n  }\n\n  private _track<T>(experiment: Experiment<T>, result: Result<T>) {\n    if (!this._ctx.trackingCallback) return;\n\n    const key = experiment.key;\n\n    // Make sure a tracking callback is only fired once per unique experiment\n    const k =\n      result.hashAttribute + result.hashValue + key + result.variationId;\n    if (this._trackedExperiments.has(k)) return;\n    this._trackedExperiments.add(k);\n\n    try {\n      this._ctx.trackingCallback(experiment, result);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  private _mergeOverrides<T>(experiment: Experiment<T>): Experiment<T> {\n    const key = experiment.key;\n    const o = this._ctx.overrides;\n    if (o && o[key]) {\n      experiment = Object.assign({}, experiment, o[key]);\n      if (typeof experiment.url === \"string\") {\n        experiment.url = getUrlRegExp(\n          // eslint-disable-next-line\n          experiment.url as any\n        );\n      }\n    }\n\n    return experiment;\n  }\n\n  private _getHashAttribute(attr?: string, fallback?: string) {\n    let hashAttribute = attr || \"id\";\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let hashValue: any = \"\";\n\n    if (this._attributeOverrides[hashAttribute]) {\n      hashValue = this._attributeOverrides[hashAttribute];\n    } else if (this._ctx.attributes) {\n      hashValue = this._ctx.attributes[hashAttribute] || \"\";\n    } else if (this._ctx.user) {\n      hashValue = this._ctx.user[hashAttribute] || \"\";\n    }\n\n    // if no match, try fallback\n    if (!hashValue && fallback) {\n      if (this._attributeOverrides[fallback]) {\n        hashValue = this._attributeOverrides[fallback];\n      } else if (this._ctx.attributes) {\n        hashValue = this._ctx.attributes[fallback] || \"\";\n      } else if (this._ctx.user) {\n        hashValue = this._ctx.user[fallback] || \"\";\n      }\n      if (hashValue) {\n        hashAttribute = fallback;\n      }\n    }\n\n    return { hashAttribute, hashValue };\n  }\n\n  private _getResult<T>(\n    experiment: Experiment<T>,\n    variationIndex: number,\n    hashUsed: boolean,\n    featureId: string | null,\n    bucket?: number,\n    stickyBucketUsed?: boolean\n  ): Result<T> {\n    let inExperiment = true;\n    // If assigned variation is not valid, use the baseline and mark the user as not in the experiment\n    if (variationIndex < 0 || variationIndex >= experiment.variations.length) {\n      variationIndex = 0;\n      inExperiment = false;\n    }\n\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n\n    const meta: Partial<VariationMeta> = experiment.meta\n      ? experiment.meta[variationIndex]\n      : {};\n\n    const res: Result<T> = {\n      key: meta.key || \"\" + variationIndex,\n      featureId,\n      inExperiment,\n      hashUsed,\n      variationId: variationIndex,\n      value: experiment.variations[variationIndex],\n      hashAttribute,\n      hashValue,\n      stickyBucketUsed: !!stickyBucketUsed,\n    };\n\n    if (meta.name) res.name = meta.name;\n    if (bucket !== undefined) res.bucket = bucket;\n    if (meta.passthrough) res.passthrough = meta.passthrough;\n\n    return res;\n  }\n\n  private _getContextUrl() {\n    return this._ctx.url || (isBrowser ? window.location.href : \"\");\n  }\n\n  private _urlIsValid(urlRegex: RegExp): boolean {\n    const url = this._getContextUrl();\n    if (!url) return false;\n\n    const pathOnly = url.replace(/^https?:\\/\\//, \"\").replace(/^[^/]*\\//, \"/\");\n\n    if (urlRegex.test(url)) return true;\n    if (urlRegex.test(pathOnly)) return true;\n    return false;\n  }\n\n  private _hasGroupOverlap(expGroups: string[]): boolean {\n    const groups = this._ctx.groups || {};\n    for (let i = 0; i < expGroups.length; i++) {\n      if (groups[expGroups[i]]) return true;\n    }\n    return false;\n  }\n\n  private _applyDOMChanges(changes: AutoExperimentVariation) {\n    if (!isBrowser) return;\n    const undo: (() => void)[] = [];\n    if (changes.css) {\n      const s = document.createElement(\"style\");\n      s.innerHTML = changes.css;\n      document.head.appendChild(s);\n      undo.push(() => s.remove());\n    }\n    if (changes.js) {\n      const script = document.createElement(\"script\");\n      script.innerHTML = changes.js;\n      document.head.appendChild(script);\n      undo.push(() => script.remove());\n    }\n    if (changes.domMutations) {\n      changes.domMutations.forEach((mutation) => {\n        undo.push(mutate.declarative(mutation as DeclarativeMutation).revert);\n      });\n    }\n    return () => {\n      undo.forEach((fn) => fn());\n    };\n  }\n\n  private _deriveStickyBucketIdentifierAttributes(data?: FeatureApiResponse) {\n    const attributes = new Set<string>();\n    const features = data && data.features ? data.features : this.getFeatures();\n    const experiments =\n      data && data.experiments ? data.experiments : this.getExperiments();\n    Object.keys(features).forEach((id) => {\n      const feature = features[id];\n      if (feature.rules) {\n        for (const rule of feature.rules) {\n          if (rule.variations) {\n            attributes.add(rule.hashAttribute || \"id\");\n            if (rule.fallbackAttribute) {\n              attributes.add(rule.fallbackAttribute);\n            }\n          }\n        }\n      }\n    });\n    experiments.map((experiment) => {\n      attributes.add(experiment.hashAttribute || \"id\");\n      if (experiment.fallbackAttribute) {\n        attributes.add(experiment.fallbackAttribute);\n      }\n    });\n    return Array.from(attributes);\n  }\n\n  public async refreshStickyBuckets(data?: FeatureApiResponse) {\n    if (this._ctx.stickyBucketService) {\n      const attributes = this._getStickyBucketAttributes(data);\n      this._ctx.stickyBucketAssignmentDocs = await this._ctx.stickyBucketService.getAllAssignments(\n        attributes\n      );\n    }\n  }\n\n  private _getStickyBucketAssignments(): StickyAssignments {\n    const mergedAssignments: StickyAssignments = {};\n    Object.values(this._ctx.stickyBucketAssignmentDocs || {}).forEach((doc) => {\n      if (doc.assignments) Object.assign(mergedAssignments, doc.assignments);\n    });\n    return mergedAssignments;\n  }\n\n  private _getStickyBucketVariation(\n    experimentKey: string,\n    experimentBucketVersion?: number,\n    minExperimentBucketVersion?: number,\n    meta?: VariationMeta[]\n  ): {\n    variation: number;\n    versionIsBlocked?: boolean;\n  } {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    minExperimentBucketVersion = minExperimentBucketVersion || 0;\n    meta = meta || [];\n    const id = this._getStickyBucketExperimentKey(\n      experimentKey,\n      experimentBucketVersion\n    );\n    const assignments = this._getStickyBucketAssignments();\n\n    // users with any blocked bucket version (0 to minExperimentBucketVersion) are excluded from the test\n    if (minExperimentBucketVersion > 0) {\n      for (let i = 0; i <= minExperimentBucketVersion; i++) {\n        const blockedKey = this._getStickyBucketExperimentKey(experimentKey, i);\n        if (assignments[blockedKey] !== undefined) {\n          return {\n            variation: -1,\n            versionIsBlocked: true,\n          };\n        }\n      }\n    }\n    const variationKey = assignments[id];\n    if (variationKey === undefined)\n      // no assignment found\n      return { variation: -1 };\n    const variation = meta.findIndex((m) => m.key === variationKey);\n    if (variation < 0)\n      // invalid assignment, treat as \"no assignment found\"\n      return { variation: -1 };\n\n    return { variation };\n  }\n\n  private _getStickyBucketExperimentKey(\n    experimentKey: string,\n    experimentBucketVersion?: number\n  ): StickyExperimentKey {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    return `${experimentKey}__${experimentBucketVersion}`;\n  }\n\n  private _getStickyBucketAttributes(\n    data?: FeatureApiResponse\n  ): Record<string, string> {\n    const attributes: Record<string, string> = {};\n    this._ctx.stickyBucketIdentifierAttributes = !this._ctx\n      .stickyBucketIdentifierAttributes\n      ? this._deriveStickyBucketIdentifierAttributes(data)\n      : this._ctx.stickyBucketIdentifierAttributes;\n    this._ctx.stickyBucketIdentifierAttributes.forEach((attr) => {\n      const { hashValue } = this._getHashAttribute(attr);\n      attributes[attr] = toString(hashValue);\n    });\n    return attributes;\n  }\n\n  private _generateStickyBucketAssignmentDoc(\n    attributeName: string,\n    attributeValue: string,\n    assignments: StickyAssignments\n  ): {\n    key: StickyAttributeKey;\n    doc: StickyAssignmentsDocument;\n    changed: boolean;\n  } {\n    const key = `${attributeName}||${attributeValue}`;\n    const existingAssignments =\n      this._ctx.stickyBucketAssignmentDocs &&\n      this._ctx.stickyBucketAssignmentDocs[key]\n        ? this._ctx.stickyBucketAssignmentDocs[key].assignments || {}\n        : {};\n    const newAssignments = { ...existingAssignments, ...assignments };\n    const changed =\n      JSON.stringify(existingAssignments) !== JSON.stringify(newAssignments);\n\n    return {\n      key,\n      doc: {\n        attributeName,\n        attributeValue,\n        assignments: newAssignments,\n      },\n      changed,\n    };\n  }\n}\n", "import { Context, GrowthBook } from \"./index\";\n\ndeclare global {\n  interface Window {\n    _growthbook?: GrowthBook;\n    growthbook_config?: Context;\n    // eslint-disable-next-line\n    dataLayer: any[];\n    analytics?: {\n      track?: (name: string, props?: Record<string, unknown>) => void;\n    };\n  }\n}\n\nconst getUUID = () => {\n  const COOKIE_NAME = \"gbuuid\";\n  const COOKIE_DAYS = 400; // 400 days is the max cookie duration for chrome\n\n  // use the browsers crypto.randomUUID if set\n  const genUUID = () => {\n    if (window.crypto && crypto.randomUUID) return crypto.randomUUID();\n    return (\"\" + 1e7 + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, (c) =>\n      (\n        ((c as unknown) as number) ^\n        (crypto.getRandomValues(new Uint8Array(1))[0] &\n          (15 >> (((c as unknown) as number) / 4)))\n      ).toString(16)\n    );\n  };\n  const getCookie = (name: string): string => {\n    const value = \"; \" + document.cookie;\n    const parts = value.split(`; ${name}=`);\n    return parts.length === 2 ? parts[1].split(\";\")[0] : \"\";\n  };\n  const setCookie = (name: string, value: string) => {\n    const d = new Date();\n    d.setTime(d.getTime() + 24 * 60 * 60 * 1000 * COOKIE_DAYS);\n    document.cookie = name + \"=\" + value + \";path=/;expires=\" + d.toUTCString();\n  };\n\n  // get the existing UUID from cookie if set, otherwise create one and store it in the cookie\n  if (getCookie(COOKIE_NAME)) return getCookie(COOKIE_NAME);\n\n  const uuid = genUUID();\n  setCookie(COOKIE_NAME, uuid);\n  return uuid;\n};\n\nfunction getUtmAttributes() {\n  // Store utm- params in sessionStorage for future page loads\n  let utms: Record<string, string> = {};\n  try {\n    const existing = sessionStorage.getItem(\"utm_params\");\n    if (existing) {\n      utms = JSON.parse(existing);\n    }\n\n    // Add utm params from querystring\n    if (location.search) {\n      const params = new URLSearchParams(location.search);\n      let hasChanges = false;\n      [\"source\", \"medium\", \"campaign\", \"term\", \"content\"].forEach((k) => {\n        // Querystring is in snake_case\n        const param = `utm_${k}`;\n        // Attribute keys are camelCase\n        const attr = `utm` + k[0].toUpperCase() + k.slice(1);\n\n        if (params.has(param)) {\n          utms[attr] = params.get(param) || \"\";\n          hasChanges = true;\n        }\n      });\n\n      // Write back to sessionStorage\n      if (hasChanges) {\n        sessionStorage.setItem(\"utm_params\", JSON.stringify(utms));\n      }\n    }\n  } catch (e) {\n    // Do nothing if sessionStorage is disabled (e.g. incognito window)\n  }\n\n  return utms;\n}\n\nfunction getAutoAttributes() {\n  const ua = navigator.userAgent;\n\n  const browser = ua.match(/Edg/)\n    ? \"edge\"\n    : ua.match(/Chrome/)\n    ? \"chrome\"\n    : ua.match(/Firefox/)\n    ? \"firefox\"\n    : ua.match(/Safari/)\n    ? \"safari\"\n    : \"unknown\";\n\n  return {\n    id: getUUID(),\n    url: location.href,\n    path: location.pathname,\n    host: location.host,\n    query: location.search,\n    deviceType: ua.match(/Mobi/) ? \"mobile\" : \"desktop\",\n    browser,\n    ...getUtmAttributes(),\n  };\n}\n\n// Initialize the data layer if it doesn't exist yet (GA4, GTM)\nwindow.dataLayer = window.dataLayer || [];\n\nconst currentScript = document.currentScript;\nconst dataContext = currentScript ? currentScript.dataset : {};\nconst windowContext = window.growthbook_config || {};\n\nfunction getAttributes() {\n  // Merge auto attributes and user-supplied attributes\n  const attributes = dataContext[\"noAutoAttributes\"] ? {} : getAutoAttributes();\n  if (windowContext.attributes) {\n    Object.assign(attributes, windowContext.attributes);\n  }\n  return attributes;\n}\n\n// Create GrowthBook instance\nconst gb = new GrowthBook({\n  ...dataContext,\n  remoteEval: !!dataContext.remoteEval,\n  subscribeToChanges: true,\n  trackingCallback: (e, r) => {\n    const p = { experiment_id: e.key, variation_id: r.key };\n    window.dataLayer.push([\"event\", \"experiment_viewed\", p]);\n    window.analytics &&\n      window.analytics.track &&\n      window.analytics.track(\"Experiment Viewed\", p);\n  },\n  ...windowContext,\n  attributes: getAttributes(),\n});\n\n// Load features/experiments\ngb.loadFeatures();\n\n// Poll for URL changes and update GrowthBook\nlet currentUrl = location.href;\nsetInterval(() => {\n  if (location.href !== currentUrl) {\n    currentUrl = location.href;\n    gb.setURL(currentUrl);\n    gb.setAttributes({\n      ...gb.getAttributes(),\n      ...getAttributes(),\n    });\n  }\n}, 500);\n\n// Store a reference in window to enable more advanced use cases\nexport default gb;\n"], "names": ["cacheSettings", "staleTTL", "maxAge", "cache<PERSON>ey", "backgroundSync", "maxEntries", "disableIdleStreams", "idleStreamInterval", "polyfills", "fetch", "globalThis", "bind", "undefined", "SubtleCrypto", "crypto", "subtle", "EventSource", "helpers", "fetchFeaturesCall", "_ref", "host", "client<PERSON>ey", "headers", "concat", "fetchRemoteEvalCall", "_ref2", "payload", "options", "method", "body", "JSON", "stringify", "eventSourceCall", "_ref3", "startIdleListener", "idleTimeout", "window", "document", "onVisibilityChange", "visibilityState", "clearTimeout", "streams", "for<PERSON>ach", "channel", "state", "enableChannel", "setTimeout", "onHidden", "addEventListener", "removeEventListener", "stopIdleListener", "localStorage", "e", "subscribedInstances", "Map", "cacheInitialized", "cache", "activeFetches", "supportsSSE", "Set", "disableChannel", "async", "updatePersistentCache", "setItem", "Array", "from", "entries", "<PERSON><PERSON><PERSON>", "instance", "apiHost", "getApiInfo", "get<PERSON><PERSON><PERSON><PERSON>", "baseKey", "isRemoteEval", "attributes", "getAttributes", "cacheKeyAttributes", "getCacheKeyAttributes", "Object", "keys", "ca", "key", "fv", "getForcedVariations", "url", "getUrl", "cleanupCache", "entriesWithTimestamps", "map", "_ref5", "value", "staleAt", "getTime", "sort", "a", "b", "entriesToRemoveCount", "Math", "min", "max", "size", "i", "delete", "onNewFeatureData", "data", "version", "dateUpdated", "Date", "now", "existing", "get", "set", "sse", "has", "instances", "refreshInstance", "decryptPayload", "refreshStickyBuckets", "setExperiments", "experiments", "getExperiments", "setFeatures", "features", "getFeatures", "fetchFeatures", "apiRequestHeaders", "getApiHosts", "getClientKey", "remoteEval", "promise", "forcedVariations", "forcedFeatures", "getForcedFeatures", "then", "res", "add", "json", "startAutoRefresh", "catch", "Promise", "resolve", "streamingHost", "streamingHostRequestHeaders", "src", "cb", "event", "type", "parse", "errors", "onSSEError", "readyState", "delay", "pow", "random", "includes", "onopen", "onerror", "close", "validAttributeName", "nullController", "revert", "elements", "mutations", "getElementRecord", "element", "record", "createElementPropertyRecord", "el", "attr", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "parentNode", "insertBeforeNode", "observe", "childList", "subtree", "characterData", "attributeFilter", "getObserverInit", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "m", "mutate", "html", "transformContainer", "createElement", "innerHTML", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "join", "attrMutationRunner", "positionMutationRunner", "newNodes", "parentSelector", "insertBeforeSelector", "querySelector", "_loadDOMNodes", "getHTMLValue", "setHTMLValue", "getElementHTMLRecord", "elementRecord", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getElementAttributeRecord", "attrName", "_el$getAttribute", "getAttribute", "setAttribute", "setAttrValue", "setPropertyV<PERSON>ue", "length", "_element$html", "_element$html$observe", "disconnect", "_element$classes", "_element$classes$obse", "_element$position", "_element$position$obs", "_element$attributes", "_element$attributes$a", "_element$attributes$a2", "deleteElementPropertyRecord", "refreshElementsSet", "mutation", "kind", "existingElements", "querySelectorAll", "selector", "attribute", "push", "startMutating", "refreshAllElementSets", "newMutation", "index", "indexOf", "splice", "stopMutating", "clear", "test", "classnames", "mutatedClassnames", "c", "hashFnv32a", "str", "hval", "l", "charCodeAt", "hash", "seed", "inRange", "n", "range", "getUrlRegExp", "regexString", "escaped", "replace", "RegExp", "console", "error", "_evalURLTarget", "pattern", "parsed", "URL", "regex", "href", "substring", "origin", "actual", "expected", "comps", "pathname", "searchParams", "v", "k", "some", "isPath", "_evalSimpleUrlPart", "_evalSimpleUrlTarget", "documentElement", "base64ToBuf", "Uint8Array", "atob", "decrypt", "encryptedString", "decryptionKey", "Error", "importKey", "name", "iv", "cipherText", "plainTextBuffer", "TextDecoder", "decode", "toString", "input", "paddedVersionString", "parts", "match", "padStart", "_regexCache", "evalCondition", "obj", "condition", "evalOr", "conditions", "evalAnd", "evalConditionValue", "<PERSON><PERSON><PERSON>", "path", "current", "isArray", "isOperatorObject", "op", "evalOperatorCondition", "isIn", "operator", "check", "elemMatch", "passed", "j", "t", "getType", "<PERSON><PERSON><PERSON><PERSON>", "SDK_VERSION", "loadSDKVersion", "getUUID", "COOKIE_NAME", "<PERSON><PERSON><PERSON><PERSON>", "cookie", "uuid", "randomUUID", "getRandomValues", "d", "setTime", "toUTCString", "<PERSON><PERSON><PERSON><PERSON>", "getUtmAttributes", "utms", "sessionStorage", "getItem", "location", "search", "params", "URLSearchParams", "has<PERSON><PERSON><PERSON>", "param", "toUpperCase", "slice", "dataLayer", "currentScript", "dataContext", "dataset", "windowContext", "growthbook_config", "ua", "navigator", "userAgent", "browser", "id", "query", "deviceType", "getAutoAttributes", "assign", "gb", "constructor", "context", "this", "_ctx", "_renderer", "_trackedExperiments", "_trackedFeatures", "debug", "_subscriptions", "_rtQueue", "_rtTimer", "ready", "_assigned", "_forcedFeatureV<PERSON>ues", "_attributeOverrides", "_activeAutoExperiments", "_triggeredExpKeys", "_loadFeaturesCalled", "isGbHost", "hostname", "enableDevMode", "_growthbook", "dispatchEvent", "Event", "_updateAllAutoExperiments", "_refresh", "autoRefresh", "subscribeToChanges", "_canSubscribe", "subs", "subscribe", "defaultHost", "apiHostRequestHeaders", "allowStale", "updateInstance", "timeout", "<PERSON><PERSON><PERSON>", "minStaleAt", "_ref4", "cleanupFn", "initializeCache", "timer", "resolved", "finish", "promiseTimeout", "fetchFeaturesWithCache", "refreshFeatures", "_render", "featuresJSON", "experimentsJSON", "encryptedFeatures", "encryptedExperiments", "stickyBucketService", "_refreshForRemoteEval", "overrides", "vars", "setForcedFeatures", "getStickyBucketAssignmentDocs", "stickyBucketAssignmentDocs", "getAllResults", "destroy", "s", "exp", "undo", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "forceVariation", "variation", "run", "experiment", "result", "_run", "_fireSubscriptions", "triggerExperiment", "manual", "_runAutoExperiment", "forceRerun", "valueHash", "inExperiment", "_undoActiveAutoExperiment", "_applyDOMChanges", "prev", "variationId", "_trackFeatureUsage", "source", "stringifiedValue", "onFeatureUsage", "on", "q", "realtimeKey", "encodeURIComponent", "mode", "realtimeInterval", "_getFeatureResult", "ruleId", "ret", "off", "experimentResult", "isOn", "evalFeature", "isOff", "getFeatureValue", "defaultValue", "feature", "rules", "rule", "filters", "_isFilteredOut", "_conditionPasses", "_isIncludedInRollout", "hashAttribute", "disableStickyBucketing", "fallbackAttribute", "coverage", "hashVersion", "tracks", "_track", "force", "variations", "weights", "bucketVersion", "minBucketVersion", "namespace", "meta", "ranges", "phase", "passthrough", "hashValue", "_getHashAttribute", "r", "featureId", "numVariations", "_getResult", "enabled", "_mergeOverrides", "urlPatterns", "targets", "hasIncludeRules", "isIncluded", "include", "isURLTargeted", "_getContextUrl", "qsOverride", "kv", "parseInt", "getQueryStringOverride", "status", "active", "assigned", "foundStickyBucket", "stickyBucketVersionIsBlocked", "versionIsBlocked", "_getStickyBucketVariation", "inNamespace", "groups", "_hasGroupOverlap", "_urlIsValid", "chooseVariation", "equal", "fill", "totalWeight", "reduce", "w", "sum", "cumulative", "start", "getBucketRanges", "qaMode", "changed", "attrKey", "doc", "_generateStickyBucketAssignmentDoc", "_getStickyBucketExperimentKey", "saveAssignments", "log", "msg", "ctx", "trackingCallback", "o", "fallback", "user", "variationIndex", "hashUsed", "bucket", "stickyBucketUsed", "urlRegex", "pathOnly", "expGroups", "changes", "css", "head", "append<PERSON><PERSON><PERSON>", "remove", "js", "script", "domMutations", "action", "fn", "_deriveStickyBucketIdentifierAttributes", "_getStickyBucketAttributes", "getAllAssignments", "_getStickyBucketAssignments", "mergedAssignments", "values", "assignments", "<PERSON><PERSON><PERSON>", "experimentBucketVersion", "minExperimentBucketVersion", "<PERSON><PERSON><PERSON>", "findIndex", "stickyBucketIdentifierAttributes", "attributeName", "attributeValue", "existingAssignments", "newAssignments", "p", "experiment_id", "variation_id", "analytics", "track", "loadFeatures", "currentUrl", "setInterval", "setURL", "setAttributes"], "mappings": "wCA0BA,MAAMA,EAA+B,CAEnCC,SAAU,IAEVC,OAAQ,MACRC,SAAU,kBACVC,gBAAgB,EAChBC,WAAY,GACZC,oBAAoB,EACpBC,mBAAoB,KAEhBC,EAAuB,CAC3BC,MAAOC,WAAWD,MAAQC,WAAWD,MAAME,KAAKD,iBAAcE,EAC9DC,aAAcH,WAAWI,OAASJ,WAAWI,OAAOC,YAASH,EAC7DI,YAAaN,WAAWM,aAEbC,EAAmB,CAC9BC,kBAAmBC,IAAkC,IAAjCC,KAAEA,EAAIC,UAAEA,EAASC,QAAEA,GAASH,EAC9C,OAAQX,EAAUC,gBACbW,EAAI,kBAAAG,OAAiBF,GACxB,CAAEC,WACH,EAEHE,oBAAqBC,IAA2C,IAA1CL,KAAEA,EAAIC,UAAEA,EAASK,QAAEA,EAAOJ,QAAEA,GAASG,EACzD,MAAME,EAAU,CACdC,OAAQ,OACRN,QAAS,CAAE,eAAgB,sBAAuBA,GAClDO,KAAMC,KAAKC,UAAUL,IAEvB,OAAQlB,EAAUC,MAAK,GAAAc,OAClBH,EAAiBC,cAAAA,OAAAA,GACpBM,EACD,EAEHK,gBAAiBC,IAAkC,IAAjCb,KAAEA,EAAIC,UAAEA,EAASC,QAAEA,GAASW,EAC5C,OAAIX,EACK,IAAId,EAAUQ,sBAAeI,EAAI,SAAAG,OAAQF,GAAa,CAC3DC,YAGG,IAAId,EAAUQ,sBAAeI,EAAI,SAAAG,OAAQF,GAAY,EAE9Da,kBAAmB,KACjB,IAAIC,EAGJ,GADoB,oBAAXC,QAA8C,oBAAbC,SAC1B,OAChB,MAAMC,EAAqB,KACQ,YAA7BD,SAASE,iBACXH,OAAOI,aAAaL,GA8F1BM,EAAQC,SAASC,IACVA,GACiB,SAAlBA,EAAQC,OACZC,EAAcF,EAAQ,KA/FoB,WAA7BN,SAASE,kBAClBJ,EAAcC,OAAOU,WACnBC,EACA/C,EAAcO,oBAElB,EAGF,OADA8B,SAASW,iBAAiB,mBAAoBV,GACvC,IACLD,SAASY,oBAAoB,mBAAoBX,EAAmB,EAExEY,iBAAkB,QAKpB,IACMxC,WAAWyC,eACb3C,EAAU2C,aAAezC,WAAWyC,aAGtC,CADA,MAAOC,GACP,CAIF,MAAMC,EAAoD,IAAIC,IAC9D,IAAIC,GAAmB,EACvB,MAAMC,EAAiC,IAAIF,IACrCG,EAA0D,IAAIH,IAC9Db,EAAsC,IAAIa,IAC1CI,EAA2B,IAAIC,IAqD9B,SAASZ,IACdN,EAAQC,SAASC,IACVA,IACLA,EAAQC,MAAQ,OAChBgB,EAAejB,GAAQ,GAE3B,CAYAkB,eAAeC,IACb,IACE,IAAKtD,EAAU2C,aAAc,aACvB3C,EAAU2C,aAAaY,QAC3B/D,EAAcG,SACd2B,KAAKC,UAAUiC,MAAMC,KAAKT,EAAMU,YAGlC,CADA,MAAOd,GACP,CAEJ,CAyCA,SAASe,EAAOC,GACd,MAAOC,EAAShD,GAAa+C,EAASE,aACtC,MAAUD,GAAAA,OAAAA,eAAYhD,EACxB,CAEA,SAASkD,EAAYH,GACnB,MAAMI,EAAUL,EAAOC,GACvB,IAAKA,EAASK,eAAgB,OAAOD,EAErC,MAAME,EAAaN,EAASO,gBACtBC,EACJR,EAASS,yBAA2BC,OAAOC,KAAKX,EAASO,iBACrDK,EAAiB,CAAA,EACvBJ,EAAmBlC,SAASuC,IAC1BD,EAAGC,GAAOP,EAAWO,EAAI,IAG3B,MAAMC,EAAKd,EAASe,sBACdC,EAAMhB,EAASiB,SAErB,MAAA,GAAA9D,OAAUiD,EAAO,MAAAjD,OAAKO,KAAKC,UAAU,CACnCiD,KACAE,KACAE,QAEJ,CA6DA,SAASE,IACP,MAAMC,EAAwBvB,MAAMC,KAAKT,EAAMU,WAC5CsB,KAAIC,IAAA,IAAER,EAAKS,GAAMD,EAAA,MAAM,CACtBR,MACAU,QAASD,EAAMC,QAAQC,UACxB,IACAC,MAAK,CAACC,EAAGC,IAAMD,EAAEH,QAAUI,EAAEJ,UAE1BK,EAAuBC,KAAKC,IAChCD,KAAKE,IAAI,EAAG3C,EAAM4C,KAAOpG,EAAcK,YACvCmD,EAAM4C,MAGR,IAAK,IAAIC,EAAI,EAAGA,EAAIL,EAAsBK,IACxC7C,EAAM8C,OAAOf,EAAsBc,GAAGpB,IAE1C,CAGA,SAASsB,EACPtB,EACA9E,EACAqG,GAGA,MAAMC,EAAUD,EAAKE,aAAe,GAC9Bf,EAAU,IAAIgB,KAAKA,KAAKC,MAAQ5G,EAAcC,UAC9C4G,EAAWrD,EAAMsD,IAAI3G,GAC3B,GAAI0G,GAAYJ,GAAWI,EAASJ,UAAYA,EAG9C,OAFAI,EAASlB,QAAUA,OACnB7B,IAKFN,EAAMuD,IAAI5G,EAAU,CAClBqG,OACAC,UACAd,UACAqB,IAAKtD,EAAYuD,IAAIhC,KAEvBK,IAEAxB,IAGA,MAAMoD,EAAY7D,EAAoByD,IAAI7B,GAC1CiC,GAAaA,EAAUxE,SAAS0B,GAAa+C,EAAgB/C,EAAUoC,IACzE,CAEA3C,eAAesD,EACb/C,EACAoC,GAEAA,QAAapC,EAASgD,eAAeZ,OAAM5F,EAAWJ,EAAUK,oBAE1DuD,EAASiD,qBAAqBb,GACpCpC,EAASkD,eAAed,EAAKe,aAAenD,EAASoD,kBACrDpD,EAASqD,YAAYjB,EAAKkB,UAAYtD,EAASuD,cACjD,CAEA9D,eAAe+D,EACbxD,GAEA,MAAMC,QAAEA,EAAOwD,kBAAEA,GAAsBzD,EAAS0D,cAC1CzG,EAAY+C,EAAS2D,eACrBC,EAAa5D,EAASK,eACtBQ,EAAMd,EAAOC,GACbjE,EAAWoE,EAAYH,GAE7B,IAAI6D,EAAUxE,EAAcqD,IAAI3G,GA8ChC,OA7CK8H,IAoBHA,GAnBmCD,EAC/B/G,EAAQO,oBAAoB,CAC1BJ,KAAMiD,EACNhD,YACAK,QAAS,CACPgD,WAAYN,EAASO,gBACrBuD,iBAAkB9D,EAASe,sBAC3BgD,eAAgBnE,MAAMC,KAAKG,EAASgE,oBAAoBlE,WACxDkB,IAAKhB,EAASiB,UAEhB/D,QAASuG,IAEX5G,EAAQC,kBAAkB,CACxBE,KAAMiD,EACNhD,YACAC,QAASuG,KAKZQ,MAAMC,IACoC,YAArCA,EAAIhH,QAAQwF,IAAI,kBAClBpD,EAAY6E,IAAItD,GAEXqD,EAAIE,UAEZH,MAAM7B,IACLD,EAAiBtB,EAAK9E,EAAUqG,GAChCiC,EAAiBrE,GACjBX,EAAc6C,OAAOnG,GACdqG,KAERkC,OAAOtF,IAONK,EAAc6C,OAAOnG,GACdwI,QAAQC,QAAQ,CAAA,MAE3BnF,EAAcsD,IAAI5G,EAAU8H,UAEjBA,CACf,CAIA,SAASQ,EAAiBrE,GACxB,MAAMa,EAAMd,EAAOC,GACbjE,EAAWoE,EAAYH,IACvByE,cAAEA,EAAaC,4BAAEA,GAAgC1E,EAAS0D,cAC1DzG,EAAY+C,EAAS2D,eAC3B,GACE/H,EAAcI,gBACdsD,EAAYuD,IAAIhC,IAChBzE,EAAUQ,YACV,CACA,GAAIyB,EAAQwE,IAAIhC,GAAM,OACtB,MAAMtC,EAAyB,CAC7BoG,IAAK,KACL3H,KAAMyH,EACNxH,YACAC,QAASwH,EACTE,GAAKC,IACH,IACE,GAAmB,qBAAfA,EAAMC,KAA6B,CACrC,MAAMhC,EAAY7D,EAAoByD,IAAI7B,GAC1CiC,GACEA,EAAUxE,SAAS0B,IACjBwD,EAAcxD,EAAS,GAE7B,MAAO,GAAmB,aAAf6E,EAAMC,KAAqB,CACpC,MAAMV,EAA2B1G,KAAKqH,MAAMF,EAAMzC,MAClDD,EAAiBtB,EAAK9E,EAAUqI,EAClC,CAEA7F,EAAQyG,OAAS,CASnB,CARE,MAAOhG,GAOPiG,EAAW1G,EACb,GAEFyG,OAAQ,EACRxG,MAAO,UAETH,EAAQsE,IAAI9B,EAAKtC,GACjBE,EAAcF,EAChB,CACF,CAEA,SAAS0G,EAAW1G,GAClB,GAAsB,SAAlBA,EAAQC,QACZD,EAAQyG,SACJzG,EAAQyG,OAAS,GAAMzG,EAAQoG,KAAkC,IAA3BpG,EAAQoG,IAAIO,YAAmB,CAEvE,MAAMC,EACJtD,KAAKuD,IAAI,EAAG7G,EAAQyG,OAAS,IAAM,IAAuB,IAAhBnD,KAAKwD,UACjD7F,EAAejB,GACfG,YAAW,KACL,CAAC,OAAQ,UAAU4G,SAAS/G,EAAQC,QACxCC,EAAcF,EAAQ,GACrBsD,KAAKC,IAAIqD,EAAO,KACrB,CACF,CAEA,SAAS3F,EAAejB,GACjBA,EAAQoG,MACbpG,EAAQoG,IAAIY,OAAS,KACrBhH,EAAQoG,IAAIa,QAAU,KACtBjH,EAAQoG,IAAIc,QACZlH,EAAQoG,IAAM,KACQ,WAAlBpG,EAAQC,QACVD,EAAQC,MAAQ,YAEpB,CAEA,SAASC,EAAcF,GACrBA,EAAQoG,IAAM9H,EAAQe,gBAAgB,CACpCZ,KAAMuB,EAAQvB,KACdC,UAAWsB,EAAQtB,UACnBC,QAASqB,EAAQrB,UAEnBqB,EAAQC,MAAQ,SAChBD,EAAQoG,IAAI/F,iBAAiB,WAAYL,EAAQqG,IACjDrG,EAAQoG,IAAI/F,iBAAiB,mBAAoBL,EAAQqG,IACzDrG,EAAQoG,IAAIa,QAAU,IAAMP,EAAW1G,GACvCA,EAAQoG,IAAIY,OAAS,KACnBhH,EAAQyG,OAAS,CAAC,CAEtB,CC3gBaU,IAAAA,EAAqB,+BAC5BC,EAAqC,CACzCC,OAAQ,WAAA,GAGJC,EAAwC,IAAI3G,IAC5C4G,EAA2B,IAAIvG,IAkBrC,SAASwG,EAAiBC,GACxB,IAAIC,EAASJ,EAASnD,IAAIsD,GAO1B,OALKC,GAEHJ,EAASlD,IAAIqD,EADbC,EAAS,CAAED,QAAAA,EAAS1F,WAAY,CAAA,IAI3B2F,CACR,CAED,SAASC,EACPC,EACAC,EACAC,EACAC,EACAC,GAEA,IAAMC,EAAeH,EAAgBF,GAC/BF,EAA0C,CAC9CQ,SAAS,EACTC,cAAeF,EACfG,aAAcH,EACdV,UAAW,GACXK,GAAAA,EACAS,EAAkB,KAClBC,SAAU,IAAIC,kBAAiB,WAK7B,GAAa,aAATV,IAAuBH,EAAOW,EAAlC,CACkB,aAATR,IACPH,EAAOW,EAAmBlI,YAAW,WACnCuH,EAAOW,EAAmB,IADQ,GAEjC,MAEL,IAAMJ,EAAeH,EAAgBF,GAE1B,aAATC,GACAI,EAAaO,aAAed,EAAOU,aAAaI,YAChDP,EAAaQ,mBAAqBf,EAAOU,aAAaK,kBAGpDR,IAAiBP,EAAOU,eAC5BV,EAAOS,cAAgBF,EACvBD,EAAeN,GAbb,CAcH,IACDM,eAAAA,EACAD,SAAAA,EACAD,gBAAAA,GAYF,MAVa,aAATD,GAAuBD,EAAGY,WAC5Bd,EAAOY,SAASI,QAAQd,EAAGY,WAAY,CACrCG,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ8G,eAAe,IAGjBnB,EAAOY,SAASI,QAAQd,EA5E5B,SAAyBC,GACvB,MAAgB,SAATA,EACH,CACEc,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ8G,eAAe,GAEjB,CACEF,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ+G,gBAAiB,CAACjB,GAEzB,CA8D+BkB,CAAgBlB,IAEvCH,CACR,CAED,SAASsB,EACPC,EACAvB,GAEA,IAAMwB,EAAaxB,EAAOI,gBAAgBJ,EAAOE,IACjDF,EAAOU,aAAea,EAClBA,GAAsB,iBAARA,EAEbC,GACDD,EAAIT,aAAeU,EAAWV,YAC9BS,EAAIR,mBAAqBS,EAAWT,mBAEpCf,EAAOQ,SAAU,EACjBiB,KAEOF,IAAQC,IACjBxB,EAAOQ,SAAU,EACjBiB,IAEH,CAED,SAASC,EAAmB1B,GAC1B,IAAIuB,EAAMvB,EAAOS,cACjBT,EAAOH,UAAUxH,SAAQ,SAACsJ,GAAA,OAAKJ,EAAMI,EAAEC,OAAOL,MAC9CD,EAkJF,SAA4BO,GAK1B,OAJKC,IACHA,EAAqB9J,SAAS+J,cAAc,QAE9CD,EAAmBE,UAAYH,EACxBC,EAAmBE,SAC3B,CAxJeC,CAAmBV,GAAMvB,EACxC,CACD,SAASkC,EAAoBlC,GAC3B,IAAMuB,EAAM,IAAIjI,IAAI0G,EAAOS,cAAc0B,MAAM,OAAOC,OAAOC,UAC7DrC,EAAOH,UAAUxH,SAAQ,SAACsJ,GAAA,OAAIA,EAAEC,OAAOL,MACvCD,EACE3H,MAAMC,KAAK2H,GACRa,OAAOC,SACPC,KAAK,KACRtC,EAEH,CAED,SAASuC,EAAmBvC,GAC1B,IAAIuB,EAAqBvB,EAAOS,cAChCT,EAAOH,UAAUxH,SAAQ,SAACsJ,GAAA,OAAKJ,EAAMI,EAAEC,OAAOL,MAC9CD,EAAcC,EAAKvB,EACpB,CAkBD,SAASwC,EAAuBxC,GAC9B,IAAIuB,EAAMvB,EAAOS,cACjBT,EAAOH,UAAUxH,SAAQ,SAACsJ,GACxB,IACMc,EApBV,SAAA3L,GACE4L,IACAC,EAAAA,EAAAA,qBAEM7B,EAAa9I,SAAS4K,cAH5BF,EAAAA,gBAIA,IAAK5B,EAAY,OAAO,KACxB,IAAMC,EAAmB4B,EACrB3K,SAAS4K,cAA2BD,GACpC,KACJ,OAAIA,IAAyB5B,EAAyB,KAC/C,CACLD,WAAAA,EACAC,iBAAAA,EAEH,CAMoB8B,CADClB,EAAEC,UAEpBL,EAAMkB,GAAYlB,KAEpBD,EAAcC,EAAKvB,EACpB,CAED,IAAM8C,EAAe,SAAC5C,GAAD,OAAiBA,EAAG8B,SAApB,EACfe,EAAe,SAAC7C,EAAa7E,GAAd,OAAiC6E,EAAG8B,UAAY3G,CAAhD,EACrB,SAAS2H,EAAqBjD,GAC5B,IAAMkD,EAAgBnD,EAAiBC,GAUvC,OATKkD,EAAcpB,OACjBoB,EAAcpB,KAAO5B,EACnBF,EACA,OACA+C,EACAC,EACArB,IAGGuB,EAAcpB,IACtB,CAED,IAAMqB,EAAqB,SAAChD,GAC1B,MAAO,CACLY,WAAYZ,EAAGiD,cACfpC,iBAAkBb,EAAGkD,mBAExB,EACKC,EAAqB,SAACnD,EAAa7E,GAErCA,EAAM0F,mBACL1F,EAAMyF,WAAWwC,SAASjI,EAAM0F,mBAMnC1F,EAAMyF,WAAWyC,aAAarD,EAAI7E,EAAM0F,iBACzC,EACD,SAASyC,EAAyBzD,GAChC,IAAMkD,EAAgBnD,EAAiBC,GAUvC,OATKkD,EAAcQ,WACjBR,EAAcQ,SAAWxD,EACvBF,EACA,WACAmD,EACAG,EACAb,IAGGS,EAAcQ,QACtB,CAED,IAqDI3B,EAmGAlB,EAxJE8C,EAAgB,SAACxD,EAAaqB,GAAd,OACpBA,EAAOrB,EAAGyD,UAAYpC,EAAOrB,EAAG0D,gBAAgB,QAD5B,EAEhBC,EAAgB,SAAC3D,GAAD,OAAiBA,EAAGyD,SAApB,EACtB,SAASG,EAAsB5D,GAC7B,IAAM+C,EAAgBnD,EAAiBI,GAUvC,OATK+C,EAAcc,UACjBd,EAAcc,QAAU9D,EACtBC,EACA,QACA2D,EACAH,EACAxB,IAGGe,EAAcc,OACtB,CAMD,SAASC,EAA0B9D,EAAaC,GAC9C,IALoB8D,EAKdhB,EAAgBnD,EAAiBI,GAUvC,OATK+C,EAAc5I,WAAW8F,KAC5B8C,EAAc5I,WAAW8F,GAAQF,EAC/BC,EACAC,GATgB8D,EAUH9D,EAVwB,SAACD,GAAD,IAAAgE,EAAA,cAAAA,EACzChE,EAAGiE,aAAaF,MAAa,OACV,SAACA,GAAD,OAAsB,SAAC/D,EAAaqB,GAAd,OACjC,OAARA,EAAerB,EAAGkE,aAAaH,EAAU1C,GAAOrB,EAAG0D,gBAAgBK,GADhD,CASfI,CAAalE,GACboC,IAGGU,EAAc5I,WAAW8F,EACjC,CA6BD,SAASmE,EACPpE,EACAC,EACAwB,GAEA,GAAKA,EAAEnB,QAAP,CACAmB,EAAEnB,SAAU,EACZ,IAAMe,EAAMI,EAAEjB,aACTiB,EAAE9B,UAAU0E,QAnCnB,SAAqCrE,EAAaC,GAChD,IAEqBqE,EAAAC,EAFf1E,EAAUH,EAASnD,IAAIyD,GAC7B,GAAKH,EACL,GAAa,SAATI,EACYS,OAAd4D,EAAAzE,EAAQ8B,cAAMjB,EAAAA,EAAAA,aAAU8D,oBACjB3E,EAAQ8B,UACV,GAAa,UAAT1B,EAAkB,CAAA,IAAAwE,EAAAC,EACVhE,OAAjB+D,EAAA5E,EAAQgE,iBAASnD,EAAAA,EAAAA,aAAU8D,oBACpB3E,EAAQgE,OAChB,MAAM,GAAa,aAAT5D,EAAqB,CAAA,IAAA0E,EAAAC,EACZlE,OAAlBiE,EAAA9E,EAAQ0D,kBAAU7C,EAAAA,EAAAA,aAAU8D,oBACrB3E,EAAQ0D,QAChB,KAAM,CAAA,IAAAsB,EAAAC,EAAAC,EACL,OAAAF,EAAAhF,EAAQ1F,aAAoBuG,OAA5BoE,EAAAD,EAAqB5E,YAAOS,EAAAA,EAAAA,aAAU8D,oBAC/B3E,EAAQ1F,WAAW8F,EAC3B,CACF,CAoBG+E,CAA4BhF,EAAIC,GAElCwB,EAAEtB,SAASH,EAAIqB,EANC,CAOjB,CAED,SAASlB,EAASsB,EAAkBzB,GAClCyB,EAAEE,MAAQyC,EAA6BpE,EAAI,OAAQyB,EAAEE,MACrDF,EAAEoC,SAAWO,EAAkCpE,EAAI,QAASyB,EAAEoC,SAC9DpC,EAAE8B,UAAYa,EAAiCpE,EAAI,WAAYyB,EAAE8B,UACjEhJ,OAAOC,KAAKiH,EAAEtH,YAAYhC,SAAQ,SAAI8H,GACpCmE,EAAkCpE,EAAIC,EAAMwB,EAAEtH,WAAW8F,MAE5D,CAED,SAASsB,IACP7B,EAASvH,QAAQgI,EAClB,CAsCD,SAAS8E,EAAmBC,GAG1B,GAAsB,aAAlBA,EAASC,MAAkD,IAA3BD,EAASxF,SAAS7D,KAAtD,CAEA,IAAMuJ,EAAmB,IAAIhM,IAAI8L,EAASxF,UACjB5H,SAASuN,iBAAiBH,EAASI,UAE3CnN,SAAQ,SAAE6H,GACpBoF,EAAiB1I,IAAIsD,KACxBkF,EAASxF,SAAS1B,IAAIgC,GA7C5B,SAAuBkF,EAAoBrF,GACzC,IAAIC,EAAiD,KAC/B,SAAlBoF,EAASC,KACXrF,EAASgD,EAAqBjD,GACH,UAAlBqF,EAASC,KAClBrF,EAAS8D,EAAsB/D,GACJ,cAAlBqF,EAASC,KAClBrF,EAASgE,EAA0BjE,EAASqF,EAASK,WAC1B,aAAlBL,EAASC,OAClBrF,EAASwD,EAAyBzD,IAE/BC,IACLA,EAAOH,UAAU6F,KAAKN,GACtBpF,EAAOM,eAAeN,GACvB,CAgCK2F,CAAcP,EAAUlF,MARsC,CAWnE,CAQD,SAAS0F,IACP/F,EAAUxH,QAAQ8M,EACnB,CA4BD,SAASU,EAAYlE,GAEnB,MAAwB,oBAAb3J,SAAiC0H,GAE5CG,EAAU3B,IAAIyD,GAEdwD,EAAmBxD,GACZ,CACLhC,OAAQ,WA5CZ,IAAwByF,KA6CHzD,GA5CV/B,SAASvH,SAAQ,SAAE6H,GAAA,OAnC9B,SAAsBkF,EAAoBlF,GACxC,IAAIF,EAAiD,KAUrD,GATsB,SAAlBoF,EAASC,KACXrF,EAASgD,EAAqB9C,GACH,UAAlBkF,EAASC,KAClBrF,EAAS8D,EAAsB5D,GACJ,cAAlBkF,EAASC,KAClBrF,EAASgE,EAA0B9D,EAAIkF,EAASK,WACrB,aAAlBL,EAASC,OAClBrF,EAASwD,EAAyBtD,IAE/BF,EAAL,CACA,IAAM8F,EAAQ9F,EAAOH,UAAUkG,QAAQX,IACxB,IAAXU,GAAc9F,EAAOH,UAAUmG,OAAOF,EAAO,GACjD9F,EAAOM,eAAeN,EAHT,CAId,CAoBiCiG,CAAab,EAAUlF,MACvDkF,EAASxF,SAASsG,QAClBrG,EAAS,OAAQuF,EA2Cd,GAEJ,CAED,SAASvD,EACP2D,EACA5D,GAEA,OAAOiE,EAAY,CACjBR,KAAM,OACNzF,SAAU,IAAItG,IACdsI,OAAAA,EACA4D,SAAAA,GAEH,CAcD,SAASzB,EACPyB,EACA5D,GAEA,OAAOiE,EAAY,CACjBR,KAAM,QACNzF,SAAU,IAAItG,IACdsI,OAAAA,EACA4D,SAAAA,GAEH,CAED,SAASC,EACPD,EACAC,EACA7D,GAEA,OAAKnC,EAAmB0G,KAAKV,GAEX,UAAdA,GAAuC,cAAdA,EACpB1B,EAAQyB,GAAU,SAAUY,GACjC,IAAMC,EAAoBzE,EAAOjI,MAAMC,KAAKwM,GAAY9D,KAAK,MAC7D8D,EAAWF,QACNG,GACLA,EACGlE,MAAM,QACNC,OAAOC,SACPhK,SAAQ,SAACiO,GAAA,OAAIF,EAAWlI,IAAIoI,KAChC,IAGIT,EAAY,CACjBR,KAAM,YACNI,UAAAA,EACA7F,SAAU,IAAItG,IACdsI,OAAAA,EACA4D,SAAAA,IAnB8C9F,CAqBjD,CCxcD,SAAS6G,EAAWC,GAClB,IAAIC,EAAO,WACX,MAAMC,EAAIF,EAAIjC,OAEd,IAAK,IAAIvI,EAAI,EAAGA,EAAI0K,EAAG1K,IACrByK,GAAQD,EAAIG,WAAW3K,GACvByK,IACGA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAErE,OAAOA,IAAS,CAClB,CAEO,SAASG,GACdC,EACAxL,EACAe,GAGA,OAAgB,IAAZA,EACMmK,EAAWA,EAAWM,EAAOxL,GAAS,IAAM,IAAS,IAG/C,IAAZe,EACMmK,EAAWlL,EAAQwL,GAAQ,IAAQ,IAItC,IACT,CAOO,SAASC,GAAQC,EAAWC,GACjC,OAAOD,GAAKC,EAAM,IAAMD,EAAIC,EAAM,EACpC,CAoBO,SAASC,GAAaC,GAC3B,IACE,MAAMC,EAAUD,EAAYE,QAAQ,aAAc,SAClD,OAAO,IAAIC,OAAOF,EAIpB,CAHE,MAAOpO,GAEP,YADAuO,QAAQC,MAAMxO,EAEhB,CACF,CA2EA,SAASyO,GACPzM,EACA8D,EACA4I,GAEA,IACE,MAAMC,EAAS,IAAIC,IAAI5M,EAAK,aAE5B,GAAa,UAAT8D,EAAkB,CACpB,MAAM+I,EAAQX,GAAaQ,GAC3B,QAAKG,IAEHA,EAAMzB,KAAKuB,EAAOG,OAClBD,EAAMzB,KAAKuB,EAAOG,KAAKC,UAAUJ,EAAOK,OAAOxD,SAEnD,CAAO,MAAa,WAAT1F,GA/Cf,SAA8BmJ,EAAaP,GACzC,IAGE,MAAMQ,EAAW,IAAIN,IACnBF,EAAQL,QAAQ,gBAAiB,eAAeA,QAAQ,MAAO,SAC/D,iBAIIc,EAA0C,CAC9C,CAACF,EAAOjR,KAAMkR,EAASlR,MAAM,GAC7B,CAACiR,EAAOG,SAAUF,EAASE,UAAU,IAYvC,OATIF,EAASrB,MACXsB,EAAMxC,KAAK,CAACsC,EAAOpB,KAAMqB,EAASrB,MAAM,IAG1CqB,EAASG,aAAa/P,SAAQ,CAACgQ,EAAGC,KAChCJ,EAAMxC,KAAK,CAACsC,EAAOI,aAAa3L,IAAI6L,IAAM,GAAID,GAAG,GAAO,KAIlDH,EAAMK,MACXpM,IAhDP,SACE6L,EACAP,EACAe,GAEA,IAEE,IAAIrB,EAAUM,EACXL,QAAQ,sBAAuB,QAC/BA,QAAQ,SAAU,MAQrB,OANIoB,IAEFrB,EAAU,OAASA,EAAQC,QAAQ,aAAc,IAAM,QAG3C,IAAIC,OAAO,IAAMF,EAAU,IAAK,KACjChB,KAAK6B,EAGpB,CAFE,MAAOjP,GACP,OAAO,CACT,CACF,CA2BiB0P,CAAmBtM,EAAK,GAAIA,EAAK,GAAIA,EAAK,KAIzD,CAFE,MAAOpD,GACP,OAAO,CACT,CACF,CAkBa2P,CAAqBhB,EAAQD,EAMxC,CAFE,MAAO1O,GACP,OAAO,CACT,CACF,CDqM0B,oBAAbf,WAEN4I,IACHA,EAAW,IAAIC,kBAAiB,WAC9B+E,GACD,KAGHA,IACAhF,EAASI,QAAQhJ,SAAS2Q,gBAAiB,CACzC1H,WAAW,EACXC,SAAS,EACT7G,YAAY,EACZ8G,eAAe,KC1HnB,MAAMyH,GAAelN,GACnBmN,WAAWjP,KAAKkP,KAAKpN,IAAK4K,GAAMA,EAAEK,WAAW,KAExCnN,eAAeuP,GACpBC,EACAC,EACAvS,GAIA,GAFAuS,EAAgBA,GAAiB,KACjCvS,EAASA,GAAWL,WAAWI,QAAUJ,WAAWI,OAAOC,QAEzD,MAAM,IAAIwS,MAAM,wCAElB,IACE,MAAMtO,QAAYlE,EAAOyS,UACvB,MACAP,GAAYK,GACZ,CAAEG,KAAM,UAAW7E,OAAQ,MAC3B,EACA,CAAC,UAAW,aAEP8E,EAAIC,GAAcN,EAAgB7G,MAAM,KACzCoH,QAAwB7S,EAAOqS,QACnC,CAAEK,KAAM,UAAWC,GAAIT,GAAYS,IACnCzO,EACAgO,GAAYU,IAGd,OAAO,IAAIE,aAAcC,OAAOF,EAGlC,CAFE,MAAOxQ,GACP,MAAM,IAAImQ,MAAM,oBAClB,CACF,CAGO,SAASQ,GAASC,GACvB,MAAqB,iBAAVA,EAA2BA,EAC/BlS,KAAKC,UAAUiS,EACxB,CAGO,SAASC,GAAoBD,GACb,iBAAVA,IACTA,GAAgB,IAEbA,GAA0B,iBAAVA,IACnBA,EAAQ,KAKV,MAAME,EAASF,EAAiBvC,QAAQ,cAAe,IAAIjF,MAAM,QAWjE,OANqB,IAAjB0H,EAAMtF,QACRsF,EAAMnE,KAAK,KAKNmE,EACJ1O,KAAKkN,GAAOA,EAAEyB,MAAM,YAAczB,EAAE0B,SAAS,EAAG,KAAO1B,IACvD/F,KAAK,IACV,CClTA,MAAM0H,GAAyC,CAAA,EAGxC,SAASC,GACdC,EACAC,GAGA,GAAI,QAASA,EACX,OAAOC,GAAOF,EAAKC,EAAe,KAEpC,GAAI,SAAUA,EACZ,OAAQC,GAAOF,EAAKC,EAAgB,MAEtC,GAAI,SAAUA,EACZ,OAsMJ,SAAiBD,EAAgBG,GAC/B,IAAK,IAAIrO,EAAI,EAAGA,EAAIqO,EAAW9F,OAAQvI,IACrC,IAAKiO,GAAcC,EAAKG,EAAWrO,IACjC,OAAO,EAGX,OAAO,CACT,CA7MWsO,CAAQJ,EAAKC,EAAgB,MAEtC,GAAI,SAAUA,EACZ,OAAQF,GAAcC,EAAKC,EAAgB,MAI7C,IAAK,MAAO7B,EAAGD,KAAM5N,OAAOZ,QAAQsQ,GAClC,IAAKI,GAAmBlC,EAAGmC,GAAQN,EAAK5B,IAAK,OAAO,EAEtD,OAAO,CACT,CAGA,SAASkC,GAAQN,EAAgBO,GAC/B,MAAMZ,EAAQY,EAAKtI,MAAM,KACzB,IAAIuI,EAAeR,EACnB,IAAK,IAAIlO,EAAI,EAAGA,EAAI6N,EAAMtF,OAAQvI,IAAK,CACrC,IAAI0O,GAA8B,iBAAZA,KAAwBb,EAAM7N,KAAM0O,GAGxD,OAAO,KAFPA,EAAUA,EAAQb,EAAM7N,GAI5B,CACA,OAAO0O,CACT,CAWA,SAASH,GAAmBJ,EAA2B9O,GAErD,GAAyB,iBAAd8O,EACT,OAAO9O,EAAQ,KAAO8O,EAExB,GAAyB,iBAAdA,EACT,OAAe,EAAR9O,IAAc8O,EAEvB,GAAyB,kBAAdA,EACT,QAAS9O,IAAU8O,EAGrB,GAAkB,OAAdA,EACF,OAAiB,OAAV9O,EAGT,GAAI1B,MAAMgR,QAAQR,KAAeS,GAAiBT,GAChD,OAAO1S,KAAKC,UAAU2D,KAAW5D,KAAKC,UAAUyS,GAIlD,IAAK,MAAMU,KAAMV,EACf,IACGW,GACCD,EACAxP,EACA8O,EAAUU,IAGZ,OAAO,EAGX,OAAO,CACT,CAGA,SAASD,GAAiBV,GACxB,MAAMxP,EAAOD,OAAOC,KAAKwP,GACzB,OACExP,EAAK6J,OAAS,GAAK7J,EAAK0H,QAAQkG,GAAe,MAATA,EAAE,KAAY/D,SAAW7J,EAAK6J,MAExE,CA2BA,SAASwG,GAAK/C,EAAaC,GAEzB,OAAItO,MAAMgR,QAAQ3C,GACTA,EAAOO,MAAMrI,GAAO+H,EAAS5I,SAASa,KAExC+H,EAAS5I,SAAS2I,EAC3B,CAGA,SAAS8C,GACPE,EACAhD,EACAC,GAEA,OAAQ+C,GACN,IAAK,OACH,OAAOpB,GAAoB5B,KAAY4B,GAAoB3B,GAC7D,IAAK,OACH,OAAO2B,GAAoB5B,KAAY4B,GAAoB3B,GAC7D,IAAK,OACH,OAAO2B,GAAoB5B,GAAU4B,GAAoB3B,GAC3D,IAAK,QACH,OAAO2B,GAAoB5B,IAAW4B,GAAoB3B,GAC5D,IAAK,OACH,OAAO2B,GAAoB5B,GAAU4B,GAAoB3B,GAC3D,IAAK,QACH,OAAO2B,GAAoB5B,IAAW4B,GAAoB3B,GAC5D,IAAK,MACH,OAAOD,IAAWC,EACpB,IAAK,MACH,OAAOD,IAAWC,EACpB,IAAK,MACH,OAAOD,EAASC,EAClB,IAAK,OACH,OAAOD,GAAUC,EACnB,IAAK,MACH,OAAOD,EAASC,EAClB,IAAK,OACH,OAAOD,GAAUC,EACnB,IAAK,UAEH,OAAOA,EAAqB,MAAVD,EAA2B,MAAVA,EACrC,IAAK,MACH,QAAKrO,MAAMgR,QAAQ1C,IACZ8C,GAAK/C,EAAQC,GACtB,IAAK,OACH,QAAKtO,MAAMgR,QAAQ1C,KACX8C,GAAK/C,EAAQC,GACvB,IAAK,OACH,OAAQsC,GAAmBtC,EAAUD,GACvC,IAAK,QACH,QAAKrO,MAAMgR,QAAQ3C,IACZuC,GAAmBtC,EAAUD,EAAOzD,QAC7C,IAAK,aACH,OAnEN,SAAmByD,EAAaC,GAC9B,IAAKtO,MAAMgR,QAAQ3C,GAAS,OAAO,EACnC,MAAMiD,EAAQL,GAAiB3C,GAC1BI,GAAWkC,GAAmBtC,EAAUI,GACxCA,GAAW4B,GAAc5B,EAAGJ,GACjC,IAAK,IAAIjM,EAAI,EAAGA,EAAIgM,EAAOzD,OAAQvI,IACjC,GAAIgM,EAAOhM,IAAMiP,EAAMjD,EAAOhM,IAC5B,OAAO,EAGX,OAAO,CACT,CAwDakP,CAAUlD,EAAQC,GAC3B,IAAK,OACH,IAAKtO,MAAMgR,QAAQ3C,GAAS,OAAO,EACnC,IAAK,IAAIhM,EAAI,EAAGA,EAAIiM,EAAS1D,OAAQvI,IAAK,CACxC,IAAImP,GAAS,EACb,IAAK,IAAIC,EAAI,EAAGA,EAAIpD,EAAOzD,OAAQ6G,IACjC,GAAIb,GAAmBtC,EAASjM,GAAIgM,EAAOoD,IAAK,CAC9CD,GAAS,EACT,KACF,CAEF,IAAKA,EAAQ,OAAO,CACtB,CACA,OAAO,EACT,IAAK,SACH,IACE,OAlJUvD,EAkJMK,EAjJjB+B,GAAYpC,KACfoC,GAAYpC,GAAS,IAAIP,OAAOO,EAAMR,QAAQ,aAAc,WAEvD4C,GAAYpC,IA8IazB,KAAK6B,EAGjC,CAFE,MAAOjP,GACP,OAAO,CACT,CACF,IAAK,QACH,OAnGN,SAAiBsP,GACf,GAAU,OAANA,EAAY,MAAO,OACvB,GAAI1O,MAAMgR,QAAQtC,GAAI,MAAO,QAC7B,MAAMgD,SAAWhD,EACjB,MAAI,CAAC,SAAU,SAAU,UAAW,SAAU,aAAahJ,SAASgM,GAC3DA,EAEF,SACT,CA2FaC,CAAQtD,KAAYC,EAC7B,QAEE,OADAX,QAAQC,MAAM,qBAAuByD,IAC9B,EA1Jb,IAAkBpD,CA4JlB,CAGA,SAASwC,GAAOF,EAAgBG,GAC9B,IAAKA,EAAW9F,OAAQ,OAAO,EAC/B,IAAK,IAAIvI,EAAI,EAAGA,EAAIqO,EAAW9F,OAAQvI,IACrC,GAAIiO,GAAcC,EAAKG,EAAWrO,IAChC,OAAO,EAGX,OAAO,CACT,CCjLA,MAAMuP,GACc,oBAAXxT,QAA8C,oBAAbC,SAEpCwT,GFgRC,WACL,IAAIpP,EACJ,IAEEA,EAAU,QAGZ,CAFE,MAAOrD,GACPqD,EAAU,EACZ,CACA,OAAOA,CACT,CEzRoBqP,GClCdC,GAAU,KACd,MAAMC,EAAc,SAcdC,EAAaxC,IACjB,MACMS,GADQ,KAAO7R,SAAS6T,QACV1J,MAAK,KAAAjL,OAAMkS,EAAQ,MACvC,OAAwB,IAAjBS,EAAMtF,OAAesF,EAAM,GAAG1H,MAAM,KAAK,GAAK,EAAE,EASzD,GAAIyJ,EAAUD,GAAc,OAAOC,EAAUD,GAE7C,MAAMG,EAvBA/T,OAAOtB,QAAUA,OAAOsV,WAAmBtV,OAAOsV,aAC/C,uCAAwC3E,QAAQ,UAAWd,IAE5DA,EACD7P,OAAOuV,gBAAgB,IAAInD,WAAW,IAAI,GACxC,IAASvC,EAA2B,GACvCoD,SAAS,MAmBf,MAXkB,EAACN,EAAc/N,KAC/B,MAAM4Q,EAAI,IAAI3P,KACd2P,EAAEC,QAAQD,EAAE1Q,UAAY,QACxBvD,SAAS6T,OAASzC,UAAa/N,EAAQ,mBAAqB4Q,EAAEE,aAAa,EAO7EC,CAAUT,EAAaG,GAChBA,CAAI,EAGb,SAASO,KAEP,IAAIC,EAA+B,CAAA,EACnC,IACE,MAAM9P,EAAW+P,eAAeC,QAAQ,cAMxC,GALIhQ,IACF8P,EAAO7U,KAAKqH,MAAMtC,IAIhBiQ,SAASC,OAAQ,CACnB,MAAMC,EAAS,IAAIC,gBAAgBH,SAASC,QAC5C,IAAIG,GAAa,EACjB,CAAC,SAAU,SAAU,WAAY,OAAQ,WAAWxU,SAASiQ,IAE3D,MAAMwE,EAAexE,OAAAA,OAAAA,GAEfnI,EAAO,MAAQmI,EAAE,GAAGyE,cAAgBzE,EAAE0E,MAAM,GAE9CL,EAAO/P,IAAIkQ,KACbR,EAAKnM,GAAQwM,EAAOlQ,IAAIqQ,IAAU,GAClCD,GAAa,EACf,IAIEA,GACFN,eAAe7S,QAAQ,aAAcjC,KAAKC,UAAU4U,GAExD,CAEA,CADA,MAAOvT,GACP,CAGF,OAAOuT,CACT,CA4BAvU,OAAOkV,UAAYlV,OAAOkV,WAAa,GAEvC,MAAMC,GAAgBlV,SAASkV,cACzBC,GAAcD,GAAgBA,GAAcE,QAAU,CAAA,EACtDC,GAAgBtV,OAAOuV,mBAAqB,GAElD,SAAShT,KAEP,MAAMD,EAAa8S,GAA8B,iBAAI,CAAE,EAlCzD,WACE,MAAMI,EAAKC,UAAUC,UAEfC,EAAUH,EAAGzD,MAAM,OACrB,OACAyD,EAAGzD,MAAM,UACT,SACAyD,EAAGzD,MAAM,WACT,UACAyD,EAAGzD,MAAM,UACT,SACA,UAEJ,MAAO,CACL6D,GAAIjC,KACJ3Q,IAAK0R,SAAS5E,KACd4C,KAAMgC,SAAStE,SACfpR,KAAM0V,SAAS1V,KACf6W,MAAOnB,SAASC,OAChBmB,WAAYN,EAAGzD,MAAM,QAAU,SAAW,UAC1C4D,aACGrB,KAEP,CAW4DyB,GAI1D,OAHIT,GAAchT,YAChBI,OAAOsT,OAAO1T,EAAYgT,GAAchT,YAEnCA,CACT,CAGA,MAAM2T,GAAK,ID7EJ,MAsCLC,YAAYC,GAqBV,GApBAA,EAAUA,GAAW,GAGrBC,KAAK/R,QAAUoP,GACf2C,KAAKC,EAAOD,KAAKD,QAAUA,EAC3BC,KAAKE,EAAY,KACjBF,KAAKG,EAAsB,IAAIhV,IAC/B6U,KAAKI,EAAmB,GACxBJ,KAAKK,OAAQ,EACbL,KAAKM,EAAiB,IAAInV,IAC1B6U,KAAKO,EAAW,GAChBP,KAAKQ,EAAW,EAChBR,KAAKS,OAAQ,EACbT,KAAKU,EAAY,IAAI5V,IACrBkV,KAAKW,EAAuB,IAAI7V,IAChCkV,KAAKY,EAAsB,GAC3BZ,KAAKa,EAAyB,IAAI/V,IAClCkV,KAAKc,EAAoB,IAAI3V,IAC7B6U,KAAKe,GAAsB,EAEvBhB,EAAQvQ,WAAY,CACtB,GAAIuQ,EAAQjF,cACV,MAAM,IAAIC,MAAM,8CAElB,IAAKgF,EAAQlX,UACX,MAAM,IAAIkS,MAAM,qBAElB,IAAIiG,GAAW,EACf,IACEA,IAAa,IAAIxH,IAAIuG,EAAQlU,SAAW,IAAIoV,SAAStF,MACnD,mBAGF,CADA,MAAO/Q,GACP,CAEF,GAAIoW,EACF,MAAM,IAAIjG,MAAM,4CAEpB,MACE,GAAIgF,EAAQ3T,mBACV,MAAM,IAAI2O,MAAM,mDAIhBgF,EAAQ7Q,WACV8Q,KAAKS,OAAQ,GAGXrD,IAAa2C,EAAQmB,gBACvBtX,OAAOuX,YAAcnB,KACrBnW,SAASuX,cAAc,IAAIC,MAAM,cAG/BtB,EAAQhR,cACViR,KAAKS,OAAQ,EACbT,KAAKsB,KAGHvB,EAAQlX,YAAckX,EAAQvQ,YAChCwQ,KAAKuB,EAAS,CAAA,GAAI,GAAM,EAE5B,CAEAlW,mBAA0BlC,GACpBA,GAAWA,EAAQqY,cAErBxB,KAAKC,EAAKwB,oBAAqB,GAEjCzB,KAAKe,GAAsB,QAErBf,KAAKuB,EAASpY,GAAS,GAAM,GAE/B6W,KAAK0B,KJXN,SAAmB9V,GACxB,MAAMa,EAAMd,EAAOC,GACb+V,EAAO9W,EAAoByD,IAAI7B,IAAQ,IAAItB,IACjDwW,EAAK5R,IAAInE,GACTf,EAAoB0D,IAAI9B,EAAKkV,EAC/B,CIOMC,CAAU5B,KAEd,CAEA3U,sBACElC,SAEM6W,KAAKuB,EAASpY,GAAS,GAAO,EACtC,CAEO2C,aACL,MAAO,CAACkU,KAAK1Q,cAAczD,QAASmU,KAAKzQ,eAC3C,CACOD,cAML,MAAMuS,EAAc7B,KAAKC,EAAKpU,SAAW,4BACzC,MAAO,CACLA,QAASgW,EAAY5I,QAAQ,OAAQ,IACrC5I,eAAgB2P,KAAKC,EAAK5P,eAAiBwR,GAAa5I,QACtD,OACA,IAEF5J,kBAAmB2Q,KAAKC,EAAK6B,sBAC7BxR,4BAA6B0P,KAAKC,EAAK3P,4BAE3C,CACOf,eACL,OAAOyQ,KAAKC,EAAKpX,WAAa,EAChC,CAEOoD,eACL,OAAO+T,KAAKC,EAAKzQ,aAAc,CACjC,CAEOnD,wBACL,OAAO2T,KAAKC,EAAK7T,kBACnB,CAEAf,QACElC,EACA4Y,EACAC,GAGA,GADA7Y,EAAUA,GAAW,IAChB6W,KAAKC,EAAKpX,UACb,MAAM,IAAIkS,MAAM,2BJnFf1P,eACLO,EACAqW,EACAC,EACAH,EACAC,EACApa,GAEKA,IACHJ,EAAcI,gBAAiB,GAGjC,MAAMoG,QAkDR3C,eACEO,EACAmW,EACAE,EACAC,GAEA,MAAMzV,EAAMd,EAAOC,GACbjE,EAAWoE,EAAYH,GACvBwC,EAAM,IAAID,KAEVgU,EAAa,IAAIhU,KACrBC,EAAIhB,UAAY5F,EAAcE,OAASF,EAAcC,gBAiFzD4D,iBACE,IAAIN,EAAJ,CACAA,GAAmB,EACnB,IACE,GAAI/C,EAAU2C,aAAc,CAC1B,MAAMuC,QAAclF,EAAU2C,aAAa0T,QACzC7W,EAAcG,UAEhB,GAAIuF,EAAO,CACT,MAAMqM,EAAiCjQ,KAAKqH,MAAMzD,GAC9CqM,GAAU/N,MAAMgR,QAAQjD,IAC1BA,EAAOrP,SAAQkY,IAAiB,IAAf3V,EAAKuB,GAAKoU,EACzBpX,EAAMuD,IAAI9B,EAAK,IACVuB,EACHb,QAAS,IAAIgB,KAAKH,EAAKb,UACvB,IAGNL,GACF,CACF,CAEA,CADA,MAAOlC,GACP,CAEqC,CACrC,MAAMyX,EAAY5Z,EAAQiB,oBACtB2Y,IACF5Z,EAAQiC,iBAAmB2X,EAE/B,CA5BsB,CA6BxB,CA5GQC,GACN,MAAMjU,EAAWrD,EAAMsD,IAAI3G,GAC3B,OACE0G,IACC6T,IACAH,GAAc1T,EAASlB,QAAUiB,IAClCC,EAASlB,QAAUgV,GAGf9T,EAASG,KAAKtD,EAAY6E,IAAItD,GAG9B4B,EAASlB,QAAUiB,EACrBgB,EAAcxD,GAIdqE,EAAiBrE,GAEZyC,EAASL,YAoCpB,SACEyB,EACAwS,GAEA,OAAO,IAAI9R,SAASC,IAClB,IACImS,EADAC,GAAW,EAEf,MAAMC,EAAUzU,IACVwU,IACJA,GAAW,EACXD,GAASvY,aAAauY,GACtBnS,EAAQpC,GAAQ,MAAK,EAGnBiU,IACFM,EAAQjY,YAAW,IAAMmY,KAAUR,IAGrCxS,EAAQI,MAAM7B,GAASyU,EAAOzU,KAAOkC,OAAM,IAAMuS,KAAS,GAE9D,CAtDiBC,CAAetT,EAAcxD,GAAWqW,EAEzD,CAvFqBU,CACjB/W,EACAmW,EACAE,EACAC,GAEFF,GAAkBhU,SAAeW,EAAgB/C,EAAUoC,EAC7D,CIkEU4U,CACJ5C,KACA7W,EAAQ8Y,QACR9Y,EAAQ+Y,WAAalC,KAAKC,EAAKiB,cAC/Ba,EACAC,GAC6B,IAA7BhC,KAAKC,EAAKrY,eAEd,CAEQib,IACF7C,KAAKE,GACPF,KAAKE,GAET,CAEOjR,YAAYC,GACjB8Q,KAAKC,EAAK/Q,SAAWA,EACrB8Q,KAAKS,OAAQ,EACbT,KAAK6C,GACP,CAEAxX,2BACEwP,EACAC,EACAvS,GAEA,MAAMua,QAAqBlI,GACzBC,EACAC,GAAiBkF,KAAKC,EAAKnF,cAC3BvS,GAEFyX,KAAK/Q,YACH3F,KAAKqH,MAAMmS,GAEf,CAEOhU,eAAeC,GACpBiR,KAAKC,EAAKlR,YAAcA,EACxBiR,KAAKS,OAAQ,EACbT,KAAKsB,GACP,CAEAjW,8BACEwP,EACAC,EACAvS,GAEA,MAAMwa,QAAwBnI,GAC5BC,EACAC,GAAiBkF,KAAKC,EAAKnF,cAC3BvS,GAEFyX,KAAKlR,eAAexF,KAAKqH,MAAMoS,GACjC,CAEA1X,qBACE2C,EACA8M,EACAvS,GAsBA,OApBIyF,EAAKgV,oBACPhV,EAAKkB,SAAW5F,KAAKqH,YACbiK,GACJ5M,EAAKgV,kBACLlI,GAAiBkF,KAAKC,EAAKnF,cAC3BvS,WAGGyF,EAAKgV,mBAEVhV,EAAKiV,uBACPjV,EAAKe,YAAczF,KAAKqH,YAChBiK,GACJ5M,EAAKiV,qBACLnI,GAAiBkF,KAAKC,EAAKnF,cAC3BvS,WAGGyF,EAAKiV,sBAEPjV,CACT,CAEA3C,oBAA2Ba,GACzB8T,KAAKC,EAAK/T,WAAaA,EACnB8T,KAAKC,EAAKiD,2BACNlD,KAAKnR,uBAETmR,KAAKC,EAAKzQ,iBACNwQ,KAAKmD,KAGbnD,KAAK6C,IACL7C,KAAKsB,IACP,CAEAjW,4BAAmC+X,GACjCpD,KAAKY,EAAsBwC,EACvBpD,KAAKC,EAAKiD,2BACNlD,KAAKnR,uBAETmR,KAAKC,EAAKzQ,iBACNwQ,KAAKmD,KAGbnD,KAAK6C,IACL7C,KAAKsB,IACP,CAEAjW,0BAAiCgY,GAC/BrD,KAAKC,EAAKvQ,iBAAmB2T,GAAQ,CAAA,EACjCrD,KAAKC,EAAKzQ,iBACNwQ,KAAKmD,KAGbnD,KAAK6C,IACL7C,KAAKsB,IACP,CAGOgC,kBAAkBtW,GACvBgT,KAAKW,EAAuB3T,EAC5BgT,KAAK6C,GACP,CAEAxX,aAAoBuB,GAElB,GADAoT,KAAKC,EAAKrT,IAAMA,EACZoT,KAAKC,EAAKzQ,WAGZ,aAFMwQ,KAAKmD,SACXnD,KAAKsB,GAA0B,GAGjCtB,KAAKsB,GAA0B,EACjC,CAEOnV,gBACL,MAAO,IAAK6T,KAAKC,EAAK/T,cAAe8T,KAAKY,EAC5C,CAEOjU,sBACL,OAAOqT,KAAKC,EAAKvQ,kBAAoB,EACvC,CAEOE,oBAEL,OAAOoQ,KAAKW,GAAwB,IAAI7V,GAC1C,CAEOyY,gCACL,OAAOvD,KAAKC,EAAKuD,4BAA8B,EACjD,CAEO3W,SACL,OAAOmT,KAAKC,EAAKrT,KAAO,EAC1B,CAEOuC,cACL,OAAO6Q,KAAKC,EAAK/Q,UAAY,EAC/B,CAEOF,iBACL,OAAOgR,KAAKC,EAAKlR,aAAe,EAClC,CAEO6S,UAAUpR,GAEf,OADAwP,KAAKM,EAAevQ,IAAIS,GACjB,KACLwP,KAAKM,EAAexS,OAAO0C,EAAG,CAElC,CAEQkR,IACN,OAAoC,IAA7B1B,KAAKC,EAAKrY,gBAA4BoY,KAAKC,EAAKwB,kBACzD,CAEApW,UACO2U,KAAKC,EAAKzQ,YACVwQ,KAAKe,SACJf,KAAKuB,EAAS,CAAE,GAAE,GAAO,GAAMrR,OAAM,QAG7C,CAEOuT,gBACL,OAAO,IAAI3Y,IAAIkV,KAAKU,EACtB,CAEOgD,UJrPF,IAAqB9X,EIuPxBoU,KAAKM,EAAevI,QACpBiI,KAAKU,EAAU3I,QACfiI,KAAKG,EAAoBpI,QACzBiI,KAAKI,EAAmB,GACxBJ,KAAKO,EAAW,GACZP,KAAKQ,GACPxW,aAAagW,KAAKQ,GJ7PI5U,EI+PZoU,KJ9PdnV,EAAoBX,SAASyZ,GAAMA,EAAE7V,OAAOlC,KIgQtCwR,IAAaxT,OAAOuX,cAAgBnB,aAC/BpW,OAAOuX,YAIhBnB,KAAKa,EAAuB3W,SAAS0Z,IACnCA,EAAIC,MAAM,IAEZ7D,KAAKa,EAAuB9I,QAC5BiI,KAAKc,EAAkB/I,OACzB,CAEO+L,YAAYC,GACjB/D,KAAKE,EAAY6D,CACnB,CAEOC,eAAevX,EAAawX,GACjCjE,KAAKC,EAAKvQ,iBAAmBsQ,KAAKC,EAAKvQ,kBAAoB,GAC3DsQ,KAAKC,EAAKvQ,iBAAiBjD,GAAOwX,EAC9BjE,KAAKC,EAAKzQ,WACZwQ,KAAKmD,KAGPnD,KAAKsB,IACLtB,KAAK6C,IACP,CAEOqB,IAAOC,GACZ,MAAMC,EAASpE,KAAKqE,EAAKF,EAAY,MAErC,OADAnE,KAAKsE,EAAmBH,EAAYC,GAC7BA,CACT,CAEOG,kBAAkB9X,GAEvB,OADAuT,KAAKc,EAAkB/Q,IAAItD,GACtBuT,KAAKC,EAAKlR,YACKiR,KAAKC,EAAKlR,YAAYkF,QAAQ2P,GAAQA,EAAInX,MAAQA,IAEnEO,KAAK4W,GACCA,EAAIY,OACFxE,KAAKyE,EAAmBb,GADP,OAGzB3P,QAAQnE,GAAgB,OAARA,IAPgB,IAQrC,CAEQ2U,EAAmBN,EAA4BO,GACrD,MAAMrW,EAAW2R,KAAKa,EAAuBvS,IAAI6V,GAGjD,GACEA,EAAWK,SACVxE,KAAKc,EAAkBrS,IAAI0V,EAAW1X,OACtC4B,EAED,OAAO,KAGT,MAAM+V,EAASpE,KAAKkE,IAAIC,GAGlBQ,EAAYrb,KAAKC,UAAU6a,EAAOlX,OAGxC,IACGwX,GACDN,EAAOQ,cACPvW,GACAA,EAASsW,YAAcA,EAEvB,OAAOP,EAOT,GAHI/V,GAAU2R,KAAK6E,EAA0BV,GAGzCC,EAAOQ,aAAc,CACvB,MAAMf,EAAO7D,KAAK8E,EAAiBV,EAAOlX,OACtC2W,GACF7D,KAAKa,EAAuBtS,IAAI4V,EAAY,CAC1CN,OACAc,aAGN,CAEA,OAAOP,CACT,CAEQS,EAA0BjB,GAChC,MAAM5V,EAAOgS,KAAKa,EAAuBvS,IAAIsV,GACzC5V,IACFA,EAAK6V,OACL7D,KAAKa,EAAuB/S,OAAO8V,GAEvC,CAEQtC,EAA0BoD,GAChC,MAAM3V,EAAciR,KAAKC,EAAKlR,aAAe,GAGvCxC,EAAO,IAAIpB,IAAI4D,GACrBiR,KAAKa,EAAuB3W,SAAQ,CAACgQ,EAAGC,KACjC5N,EAAKkC,IAAI0L,KACZD,EAAE2J,OACF7D,KAAKa,EAAuB/S,OAAOqM,GACrC,IAIFpL,EAAY7E,SAAS0Z,IACnB5D,KAAKyE,EAAmBb,EAAKc,EAAW,GAE5C,CAEQJ,EAAsBH,EAA2BC,GACvD,MAAM3X,EAAM0X,EAAW1X,IAGjBsY,EAAO/E,KAAKU,EAAUpS,IAAI7B,GAG7BsY,GACDA,EAAKX,OAAOQ,eAAiBR,EAAOQ,cACpCG,EAAKX,OAAOY,cAAgBZ,EAAOY,cAEnChF,KAAKU,EAAUnS,IAAI9B,EAAK,CAAE0X,aAAYC,WACtCpE,KAAKM,EAAepW,SAASsG,IAC3B,IACEA,EAAG2T,EAAYC,EAGjB,CAFE,MAAOxZ,GACPuO,QAAQC,MAAMxO,EAChB,KAGN,CAEQqa,EAAmBxY,EAAaqD,GAEtC,GAAmB,aAAfA,EAAIoV,OAAuB,OAG/B,MAAMC,EAAmB7b,KAAKC,UAAUuG,EAAI5C,OAC5C,GAAI8S,KAAKI,EAAiB3T,KAAS0Y,EAAnC,CAIA,GAHAnF,KAAKI,EAAiB3T,GAAO0Y,EAGzBnF,KAAKC,EAAKmF,eACZ,IACEpF,KAAKC,EAAKmF,eAAe3Y,EAAKqD,EAE9B,CADA,MAAOlF,GACP,CAKCwS,IAAcxT,OAAO3B,QAC1B+X,KAAKO,EAAShJ,KAAK,CACjB9K,MACA4Y,GAAIvV,EAAIuV,KAELrF,KAAKQ,IACRR,KAAKQ,EAAW5W,OAAOU,YAAW,KAEhC0V,KAAKQ,EAAW,EAChB,MAAM8E,EAAI,IAAItF,KAAKO,GACnBP,KAAKO,EAAW,GAGXP,KAAKC,EAAKsF,aAEf3b,OACG3B,MAAK,iCAAAc,OAEFiX,KAAKC,EAAKsF,YAAW,YAAAxc,OACZyc,mBAAmBlc,KAAKC,UAAU+b,KAE7C,CACEta,MAAO,WACPya,KAAM,YAGTvV,OAAM,QAEL,GACH8P,KAAKC,EAAKyF,kBAAoB,MA1CkB,CA4CvD,CAEQC,EACNlZ,EACAS,EACAgY,EACAU,EACAzB,EACAC,GAEA,MAAMyB,EAAqB,CACzB3Y,QACAmY,KAAMnY,EACN4Y,KAAM5Y,EACNgY,SACAU,OAAQA,GAAU,IAQpB,OANIzB,IAAY0B,EAAI1B,WAAaA,GAC7BC,IAAQyB,EAAIE,iBAAmB3B,GAGnCpE,KAAKiF,EAAmBxY,EAAKoZ,GAEtBA,CACT,CAEOG,KAAoDvZ,GACzD,OAAOuT,KAAKiG,YAAYxZ,GAAK4Y,EAC/B,CAEOa,MAAqDzZ,GAC1D,OAAOuT,KAAKiG,YAAYxZ,GAAKqZ,GAC/B,CAEOK,gBAGL1Z,EAAQ2Z,GACR,MAAMlZ,EAAQ8S,KAAKiG,YAAmCxZ,GAAKS,MAC3D,OAAiB,OAAVA,EAAkBkZ,EAAsClZ,CACjE,CAOOmZ,QAGL7G,GACA,OAAOQ,KAAKiG,YAAYzG,EAC1B,CAEOyG,YAGLzG,GAEA,GAAIQ,KAAKW,EAAqBlS,IAAI+Q,GAMhC,OAAOQ,KAAK2F,EACVnG,EACAQ,KAAKW,EAAqBrS,IAAIkR,GAC9B,YAKJ,IAAKQ,KAAKC,EAAK/Q,WAAa8Q,KAAKC,EAAK/Q,SAASsQ,GAG7C,OAAOQ,KAAK2F,EAAkBnG,EAAI,KAAM,kBAI1C,MAAM6G,EAAgCrG,KAAKC,EAAK/Q,SAASsQ,GAGzD,GAAI6G,EAAQC,MACV,IAAK,MAAMC,KAAQF,EAAQC,MAAO,CAEhC,GAAIC,EAAKC,SAAWxG,KAAKyG,EAAeF,EAAKC,SAM3C,SAIF,GAAI,UAAWD,EAAM,CAEnB,GAAIA,EAAKvK,YAAcgE,KAAK0G,EAAiBH,EAAKvK,WAMhD,SAIF,IACGgE,KAAK2G,EACJJ,EAAK7N,MAAQ8G,EACb+G,EAAKK,cACL5G,KAAKC,EAAKiD,sBAAwBqD,EAAKM,uBACnCN,EAAKO,uBACL1e,EACJme,EAAK1N,MACL0N,EAAKQ,SACLR,EAAKS,aAQP,SAgBF,OANIT,EAAKU,QACPV,EAAKU,OAAO/c,SAASgT,IACnB8C,KAAKkH,EAAOhK,EAAEiH,WAAYjH,EAAEkH,OAAO,IAIhCpE,KAAK2F,EAAkBnG,EAAI+G,EAAKY,MAAY,QAASZ,EAAK/G,GACnE,CACA,IAAK+G,EAAKa,WAOR,SAIF,MAAMxD,EAAqB,CACzBwD,WAAYb,EAAKa,WACjB3a,IAAK8Z,EAAK9Z,KAAO+S,GAEf,aAAc+G,IAAM3C,EAAImD,SAAWR,EAAKQ,UACxCR,EAAKc,UAASzD,EAAIyD,QAAUd,EAAKc,SACjCd,EAAKK,gBAAehD,EAAIgD,cAAgBL,EAAKK,eAC7CL,EAAKO,oBACPlD,EAAIkD,kBAAoBP,EAAKO,mBAC3BP,EAAKM,yBACPjD,EAAIiD,uBAAyBN,EAAKM,6BACTze,IAAvBme,EAAKe,gBACP1D,EAAI0D,cAAgBf,EAAKe,oBACGlf,IAA1Bme,EAAKgB,mBACP3D,EAAI2D,iBAAmBhB,EAAKgB,kBAC1BhB,EAAKiB,YAAW5D,EAAI4D,UAAYjB,EAAKiB,WACrCjB,EAAKkB,OAAM7D,EAAI6D,KAAOlB,EAAKkB,MAC3BlB,EAAKmB,SAAQ9D,EAAI8D,OAASnB,EAAKmB,QAC/BnB,EAAKtL,OAAM2I,EAAI3I,KAAOsL,EAAKtL,MAC3BsL,EAAKoB,QAAO/D,EAAI+D,MAAQpB,EAAKoB,OAC7BpB,EAAK7N,OAAMkL,EAAIlL,KAAO6N,EAAK7N,MAC3B6N,EAAKS,cAAapD,EAAIoD,YAAcT,EAAKS,aACzCT,EAAKC,UAAS5C,EAAI4C,QAAUD,EAAKC,SACjCD,EAAKvK,YAAW4H,EAAI5H,UAAYuK,EAAKvK,WAGzC,MAAMlM,EAAMkQ,KAAKqE,EAAKT,EAAKpE,GAE3B,GADAQ,KAAKsE,EAAmBV,EAAK9T,GACzBA,EAAI8U,eAAiB9U,EAAI8X,YAC3B,OAAO5H,KAAK2F,EACVnG,EACA1P,EAAI5C,MACJ,aACAqZ,EAAK/G,GACLoE,EACA9T,EAGN,CAUF,OAAOkQ,KAAK2F,EACVnG,OACyBpX,IAAzBie,EAAQD,aAA6B,KAAOC,EAAQD,aACpD,eAEJ,CAEQO,EACNjO,EACAkO,EACAE,EACAjO,EACAkO,EACAC,GAEA,IAAKnO,QAAsBzQ,IAAb2e,EAAwB,OAAO,EAE7C,MAAMc,UAAEA,GAAc7H,KAAK8H,EACzBlB,EACAE,GAEF,IAAKe,EACH,OAAO,EAGT,MAAMjP,EAAIH,GAAKC,EAAMmP,EAAWb,GAAe,GAC/C,OAAU,OAANpO,IAEGC,EACHF,GAAQC,EAAGC,QACEzQ,IAAb2e,GACAnO,GAAKmO,EAEX,CAEQL,EAAiB1K,GACvB,OAAOF,GAAckE,KAAK7T,gBAAiB6P,EAC7C,CAEQyK,EAAeD,GACrB,OAAOA,EAAQpM,MAAMnG,IACnB,MAAM4T,UAAEA,GAAc7H,KAAK8H,EAAkB7T,EAAOqD,WACpD,IAAKuQ,EAAW,OAAO,EACvB,MAAMjP,EAAIH,GAAKxE,EAAOyE,KAAMmP,EAAW5T,EAAO+S,aAAe,GAC7D,OAAU,OAANpO,IACI3E,EAAOyT,OAAOtN,MAAM2N,GAAMpP,GAAQC,EAAGmP,IAAG,GAEpD,CAEQ1D,EACNF,EACA6D,GAEA,MAAMvb,EAAM0X,EAAW1X,IACjBwb,EAAgB9D,EAAWiD,WAAWhR,OAG5C,GAAI6R,EAAgB,EAGlB,OAAOjI,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,IAA0B,IAAtBhI,KAAKC,EAAKkI,QAGZ,OAAOnI,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAOhD,IAHA7D,EAAanE,KAAKoI,EAAgBjE,IAIrBkE,cFxyBV,SAAuBzb,EAAa0b,GACzC,IAAKA,EAAQlS,OAAQ,OAAO,EAC5B,IAAImS,GAAkB,EAClBC,GAAa,EAEjB,IAAK,IAAI3a,EAAI,EAAGA,EAAIya,EAAQlS,OAAQvI,IAAK,CACvC,MAAM8N,EAAQtC,GAAezM,EAAK0b,EAAQza,GAAG6C,KAAM4X,EAAQza,GAAGyL,SAC9D,IAA2B,IAAvBgP,EAAQza,GAAG4a,SACb,GAAI9M,EAAO,OAAO,OAElB4M,GAAkB,EACd5M,IAAO6M,GAAa,EAE5B,CAEA,OAAOA,IAAeD,CACxB,CEyxBOG,CAAc1I,KAAK2I,IAAkBxE,EAAWkE,aAMjD,OAAOrI,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAMY,EF/pBH,SACLpJ,EACA5S,EACAqb,GAEA,IAAKrb,EACH,OAAO,KAGT,MAAM2R,EAAS3R,EAAIoH,MAAM,KAAK,GAC9B,IAAKuK,EACH,OAAO,KAGT,MAAM5C,EAAQ4C,EACXtF,QAAQ,MAAO,IACfjF,MAAM,KACNhH,KAAK6b,GAAOA,EAAG7U,MAAM,IAAK,KAC1BC,QAAOtL,IAAA,IAAEwR,GAAExR,EAAA,OAAKwR,IAAMqF,CAAE,IACxBxS,KAAI/D,IAAA,IAAIiR,CAAAA,GAAEjR,EAAA,OAAK6f,SAAS5O,EAAE,IAE7B,OAAIyB,EAAMvF,OAAS,GAAKuF,EAAM,IAAM,GAAKA,EAAM,GAAKsM,EAC3CtM,EAAM,GAER,IACT,CEsoBuBoN,CACjBtc,EACAuT,KAAK2I,IACLV,GAEF,GAAmB,OAAfW,EAMF,OAAO5I,KAAKkI,EAAW/D,EAAYyE,GAAY,EAAOZ,GAIxD,GAAIhI,KAAKC,EAAKvQ,kBAAoBjD,KAAOuT,KAAKC,EAAKvQ,iBAOjD,OAAOsQ,KAAKkI,EAAW/D,EANLnE,KAAKC,EAAKvQ,iBAAiBjD,IAMC,EAAOub,GAIvD,GAA0B,UAAtB7D,EAAW6E,SAA4C,IAAtB7E,EAAW8E,OAK9C,OAAOjJ,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAMpB,cAAEA,EAAaiB,UAAEA,GAAc7H,KAAK8H,EACxC3D,EAAWyC,cACX5G,KAAKC,EAAKiD,sBAAwBiB,EAAW0C,uBACzC1C,EAAW2C,uBACX1e,GAEN,IAAKyf,EAKH,OAAO7H,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAGhD,IAAIkB,GAAY,EAEZC,GAAoB,EACpBC,GAA+B,EACnC,GAAIpJ,KAAKC,EAAKiD,sBAAwBiB,EAAW0C,uBAAwB,CACvE,MAAM5C,UAAEA,EAASoF,iBAAEA,GAAqBrJ,KAAKsJ,EAC3CnF,EAAW1X,IACX0X,EAAWmD,cACXnD,EAAWoD,iBACXpD,EAAWsD,MAEb0B,EAAoBlF,GAAa,EACjCiF,EAAWjF,EACXmF,IAAiCC,CACnC,CAGA,IAAKF,EAAmB,CAEtB,GAAIhF,EAAWqC,SACb,GAAIxG,KAAKyG,EAAetC,EAAWqC,SAKjC,OAAOxG,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,QAE3C,GACL7D,EAAWqD,YF55BZ,SACLK,EACAL,GAEA,MAAM5O,EAAIH,GAAK,KAAO+O,EAAU,GAAIK,EAAW,GAC/C,OAAU,OAANjP,GACGA,GAAK4O,EAAU,IAAM5O,EAAI4O,EAAU,EAC5C,CEs5BS+B,CAAY1B,EAAW1D,EAAWqD,WAMnC,OAAOxH,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GAAI7D,EAAWsE,UF5tBd,SAAoBA,GACzB,IACE,OAAOA,GAIT,CAHE,MAAO7d,GAEP,OADAuO,QAAQC,MAAMxO,IACP,CACT,CACF,CEqtBiC4d,CAAWrE,EAAWsE,SAK/C,OAAOzI,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GACE7D,EAAWnI,YACVgE,KAAK0G,EAAiBvC,EAAWnI,WAMlC,OAAOgE,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GACE7D,EAAWqF,SACVxJ,KAAKyJ,EAAiBtF,EAAWqF,QAMlC,OAAOxJ,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,EAElD,CAGA,GAAI7D,EAAWvX,MAAQoT,KAAK0J,EAAYvF,EAAWvX,KAKjD,OAAOoT,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAMpP,EAAIH,GACR0L,EAAWzL,MAAQjM,EACnBob,EACA1D,EAAW6C,aAAe,GAE5B,GAAU,OAANpO,EAKF,OAAOoH,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAehD,GAZKmB,IAQHD,EF99BC,SAAyBtQ,EAAW8O,GACzC,IAAK,IAAI7Z,EAAI,EAAGA,EAAI6Z,EAAOtR,OAAQvI,IACjC,GAAI8K,GAAQC,EAAG8O,EAAO7Z,IACpB,OAAOA,EAGX,OAAQ,CACV,CEu9BiB8b,CAAgB/Q,EANzBuL,EAAWuD,QFn2BZ,SACLO,EACAlB,EACAM,IAEAN,OAAwB3e,IAAb2e,EAAyB,EAAIA,GAGzB,EAIbA,EAAW,EACFA,EAAW,IAIpBA,EAAW,GAIb,MAAM6C,GA5JwBhR,EA4JAqP,IA3JrB,EAAU,GACZ,IAAIzc,MAAMoN,GAAGiR,KAAK,EAAIjR,GAFxB,IAAyBA,GA6J9ByO,EAAUA,GAAWuC,GACTxT,SAAW6R,IAMrBZ,EAAUuC,GAIZ,MAAME,EAAczC,EAAQ0C,QAAO,CAACC,EAAGC,IAAQA,EAAMD,GAAG,IACpDF,EAAc,KAAQA,EAAc,QAItCzC,EAAUuC,GAIZ,IAAIM,EAAa,EACjB,OAAO7C,EAAQra,KAAKgd,IAClB,MAAMG,EAAQD,EAEd,OADAA,GAAcF,EACP,CAACG,EAAOA,EAASpD,EAAsBiD,EAAE,GAEpD,CEozBQI,CACEnC,OACwB7f,IAAxB+b,EAAW4C,SAAyB,EAAI5C,EAAW4C,SACnD5C,EAAWkD,WAMb+B,EAKF,OAAOpJ,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,OAAW5f,GAAW,GAItE,GAAI8gB,EAAW,EAKb,OAAOlJ,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GAAI,UAAW7D,EAMb,OAAOnE,KAAKkI,EACV/D,OACqB/b,IAArB+b,EAAWgD,OAAuB,EAAIhD,EAAWgD,OACjD,EACAa,GAKJ,GAAIhI,KAAKC,EAAKoK,OAKZ,OAAOrK,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GAA0B,YAAtB7D,EAAW6E,OAKb,OAAOhJ,KAAKkI,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAM5D,EAASpE,KAAKkI,EAClB/D,EACA+E,GACA,EACAlB,EACApP,EACAuQ,GAIF,GAAInJ,KAAKC,EAAKiD,sBAAwBiB,EAAW0C,uBAAwB,CACvE,MAAMyD,QACJA,EACA7d,IAAK8d,EAAOC,IACZA,GACExK,KAAKyK,EACP7D,EACArL,GAASsM,GACT,CACE,CAAC7H,KAAK0K,EACJvG,EAAW1X,IACX0X,EAAWmD,gBACTlD,EAAO3X,MAGX6d,IAEFtK,KAAKC,EAAKuD,2BACRxD,KAAKC,EAAKuD,4BAA8B,GAC1CxD,KAAKC,EAAKuD,2BAA2B+G,GAAWC,EAEhDxK,KAAKC,EAAKiD,oBAAoByH,gBAAgBH,GAElD,CAWA,OARAxK,KAAKkH,EAAO/C,EAAYC,GAQjBA,CACT,CAEAwG,IAAIC,EAAaC,GACV9K,KAAKK,QACNL,KAAKC,EAAK2K,IAAK5K,KAAKC,EAAK2K,IAAIC,EAAKC,GACjC3R,QAAQyR,IAAIC,EAAKC,GACxB,CAEQ5D,EAAU/C,EAA2BC,GAC3C,IAAKpE,KAAKC,EAAK8K,iBAAkB,OAEjC,MAGM5Q,EACJiK,EAAOwC,cAAgBxC,EAAOyD,UAJpB1D,EAAW1X,IAI2B2X,EAAOY,YACzD,IAAIhF,KAAKG,EAAoB1R,IAAI0L,GAAjC,CACA6F,KAAKG,EAAoBpQ,IAAIoK,GAE7B,IACE6F,KAAKC,EAAK8K,iBAAiB5G,EAAYC,EAGzC,CAFE,MAAOxZ,GACPuO,QAAQC,MAAMxO,EAChB,CAPqC,CAQvC,CAEQwd,EAAmBjE,GACzB,MAAM1X,EAAM0X,EAAW1X,IACjBue,EAAIhL,KAAKC,EAAKmD,UAWpB,OAVI4H,GAAKA,EAAEve,IAEqB,iBAD9B0X,EAAa7X,OAAOsT,OAAO,CAAA,EAAIuE,EAAY6G,EAAEve,KACvBG,MACpBuX,EAAWvX,IAAMkM,GAEfqL,EAAWvX,MAKVuX,CACT,CAEQ2D,EAAkB9V,EAAeiZ,GACvC,IAAIrE,EAAgB5U,GAAQ,KAExB6V,EAAiB,GAwBrB,OAtBI7H,KAAKY,EAAoBgG,GAC3BiB,EAAY7H,KAAKY,EAAoBgG,GAC5B5G,KAAKC,EAAK/T,WACnB2b,EAAY7H,KAAKC,EAAK/T,WAAW0a,IAAkB,GAC1C5G,KAAKC,EAAKiL,OACnBrD,EAAY7H,KAAKC,EAAKiL,KAAKtE,IAAkB,KAI1CiB,GAAaoD,IACZjL,KAAKY,EAAoBqK,GAC3BpD,EAAY7H,KAAKY,EAAoBqK,GAC5BjL,KAAKC,EAAK/T,WACnB2b,EAAY7H,KAAKC,EAAK/T,WAAW+e,IAAa,GACrCjL,KAAKC,EAAKiL,OACnBrD,EAAY7H,KAAKC,EAAKiL,KAAKD,IAAa,IAEtCpD,IACFjB,EAAgBqE,IAIb,CAAErE,gBAAeiB,YAC1B,CAEQK,EACN/D,EACAgH,EACAC,EACApD,EACAqD,EACAC,GAEA,IAAI1G,GAAe,GAEfuG,EAAiB,GAAKA,GAAkBhH,EAAWiD,WAAWhR,UAChE+U,EAAiB,EACjBvG,GAAe,GAGjB,MAAMgC,cAAEA,EAAaiB,UAAEA,GAAc7H,KAAK8H,EACxC3D,EAAWyC,cACX5G,KAAKC,EAAKiD,sBAAwBiB,EAAW0C,uBACzC1C,EAAW2C,uBACX1e,GAGAqf,EAA+BtD,EAAWsD,KAC5CtD,EAAWsD,KAAK0D,GAChB,GAEErb,EAAiB,CACrBrD,IAAKgb,EAAKhb,KAAO,GAAK0e,EACtBnD,YACApD,eACAwG,WACApG,YAAamG,EACbje,MAAOiX,EAAWiD,WAAW+D,GAC7BvE,gBACAiB,YACAyD,mBAAoBA,GAOtB,OAJI7D,EAAKxM,OAAMnL,EAAImL,KAAOwM,EAAKxM,WAChB7S,IAAXijB,IAAsBvb,EAAIub,OAASA,GACnC5D,EAAKG,cAAa9X,EAAI8X,YAAcH,EAAKG,aAEtC9X,CACT,CAEQ6Y,IACN,OAAO3I,KAAKC,EAAKrT,MAAQwQ,GAAYxT,OAAO0U,SAAS5E,KAAO,GAC9D,CAEQgQ,EAAY6B,GAClB,MAAM3e,EAAMoT,KAAK2I,IACjB,IAAK/b,EAAK,OAAO,EAEjB,MAAM4e,EAAW5e,EAAIqM,QAAQ,eAAgB,IAAIA,QAAQ,WAAY,KAErE,QAAIsS,EAASvT,KAAKpL,MACd2e,EAASvT,KAAKwT,EAEpB,CAEQ/B,EAAiBgC,GACvB,MAAMjC,EAASxJ,KAAKC,EAAKuJ,QAAU,CAAA,EACnC,IAAK,IAAI3b,EAAI,EAAGA,EAAI4d,EAAUrV,OAAQvI,IACpC,GAAI2b,EAAOiC,EAAU5d,IAAK,OAAO,EAEnC,OAAO,CACT,CAEQiX,EAAiB4G,GACvB,IAAKtO,GAAW,OAChB,MAAMyG,EAAuB,GAC7B,GAAI6H,EAAQC,IAAK,CACf,MAAMhI,EAAI9Z,SAAS+J,cAAc,SACjC+P,EAAE9P,UAAY6X,EAAQC,IACtB9hB,SAAS+hB,KAAKC,YAAYlI,GAC1BE,EAAKtM,MAAK,IAAMoM,EAAEmI,UACpB,CACA,GAAIJ,EAAQK,GAAI,CACd,MAAMC,EAASniB,SAAS+J,cAAc,UACtCoY,EAAOnY,UAAY6X,EAAQK,GAC3BliB,SAAS+hB,KAAKC,YAAYG,GAC1BnI,EAAKtM,MAAK,IAAMyU,EAAOF,UACzB,CAMA,OALIJ,EAAQO,cACVP,EAAQO,aAAa/hB,SAAS+M,IAC5B4M,EAAKtM,KHr0Bb,SAAAtO,GACEoO,IAAAA,EAAAA,EAAAA,SACA6U,EAAAA,EAAAA,OACAhf,EAAAA,EAAAA,MACW8E,EAAXsF,EAAAA,UACA/C,EAAAA,EAAAA,eACAC,EAAAA,EAAAA,qBAEA,GAAa,SAATxC,EAAiB,CACnB,GAAe,WAAXka,EACF,OAAOxY,EAAK2D,GAAU,SAAGjE,GAAA,OAAIA,SAAOlG,EAAAA,EAAS,GAApB,IACpB,GAAe,QAAXgf,EACT,OAAOxY,EAAK2D,GAAU,WAAA,OAAA,MAAMnK,EAAAA,EAAS,EAAf,GAEzB,MAAM,GAAa,UAAT8E,EAAkB,CAC3B,GAAe,WAAXka,EACF,OAAOtW,EAAQyB,GAAU,SAAGjE,GACtBlG,GAAOkG,EAAIrD,IAAI7C,EACpB,IACI,GAAe,WAAXgf,EACT,OAAOtW,EAAQyB,GAAU,SAAGjE,GACtBlG,GAAOkG,EAAG,OAAQlG,EACvB,IACI,GAAe,QAAXgf,EACT,OAAOtW,EAAQyB,GAAU,SAAGjE,GAC1BA,EAAI2E,QACA7K,GAAOkG,EAAIrD,IAAI7C,EACpB,GAEJ,MAAM,GAAa,aAAT8E,GACT,GAAe,QAAXka,GAAoB3X,EACtB,OAnFN,SACE8C,EACA5D,GAEA,OAAOiE,EAAY,CACjBR,KAAM,WACNzF,SAAU,IAAItG,IACdsI,OA4E4B,WAAA,MAAO,CAC/Be,qBAAAA,EACAD,eAAAA,EAFwB,EA3E5B8C,SAAAA,GAEH,CAyEY/B,CAAS+B,OAKb,CACL,GAAe,WAAX6U,EACF,OAAO5U,EAAUD,EAAUrF,GAAM,SAAGoB,GAAA,OAC1B,OAARA,EAAeA,GAAG,MAAIlG,EAAAA,EAAS,IAAMA,MAAAA,EAAAA,EAAS,EADZ,IAG/B,GAAe,QAAXgf,EACT,OAAO5U,EAAUD,EAAUrF,GAAM,WAAA,OAAA,MAAM9E,EAAAA,EAAS,EAAf,IAC5B,GAAe,WAAXgf,EACT,OAAO5U,EAAUD,EAAUrF,GAAM,WAAA,OAAM,IAAN,GAEpC,CACD,OAAOT,CACR,CGqxBiBkC,CAAmBwD,GAAiCzF,OAAO,IAGlE,KACLqS,EAAK3Z,SAASiiB,GAAOA,KAAK,CAE9B,CAEQC,GAAwCpe,GAC9C,MAAM9B,EAAa,IAAIf,IACjB+D,EAAWlB,GAAQA,EAAKkB,SAAWlB,EAAKkB,SAAW8Q,KAAK7Q,cACxDJ,EACJf,GAAQA,EAAKe,YAAcf,EAAKe,YAAciR,KAAKhR,iBAoBrD,OAnBA1C,OAAOC,KAAK2C,GAAUhF,SAASsV,IAC7B,MAAM6G,EAAUnX,EAASsQ,GACzB,GAAI6G,EAAQC,MACV,IAAK,MAAMC,KAAQF,EAAQC,MACrBC,EAAKa,aACPlb,EAAW6D,IAAIwW,EAAKK,eAAiB,MACjCL,EAAKO,mBACP5a,EAAW6D,IAAIwW,EAAKO,mBAI5B,IAEF/X,EAAY/B,KAAKmX,IACfjY,EAAW6D,IAAIoU,EAAWyC,eAAiB,MACvCzC,EAAW2C,mBACb5a,EAAW6D,IAAIoU,EAAW2C,kBAC5B,IAEKtb,MAAMC,KAAKS,EACpB,CAEAb,2BAAkC2C,GAChC,GAAIgS,KAAKC,EAAKiD,oBAAqB,CACjC,MAAMhX,EAAa8T,KAAKqM,GAA2Bre,GACnDgS,KAAKC,EAAKuD,iCAAmCxD,KAAKC,EAAKiD,oBAAoBoJ,kBACzEpgB,EAEJ,CACF,CAEQqgB,KACN,MAAMC,EAAuC,CAAA,EAI7C,OAHAlgB,OAAOmgB,OAAOzM,KAAKC,EAAKuD,4BAA8B,IAAItZ,SAASsgB,IAC7DA,EAAIkC,aAAapgB,OAAOsT,OAAO4M,EAAmBhC,EAAIkC,YAAY,IAEjEF,CACT,CAEQlD,EACNqD,EACAC,EACAC,EACApF,GAMAoF,EAA6BA,GAA8B,EAC3DpF,EAAOA,GAAQ,GACf,MAAMjI,EAAKQ,KAAK0K,EACdiC,EAJFC,EAA0BA,GAA2B,GAO/CF,EAAc1M,KAAKuM,KAGzB,GAAIM,EAA6B,EAC/B,IAAK,IAAIhf,EAAI,EAAGA,GAAKgf,EAA4Bhf,IAE/C,QAAgCzF,IAA5BskB,EADe1M,KAAK0K,EAA8BiC,EAAe9e,IAEnE,MAAO,CACLoW,WAAY,EACZoF,kBAAkB,GAK1B,MAAMyD,EAAeJ,EAAYlN,GACjC,QAAqBpX,IAAjB0kB,EAEF,MAAO,CAAE7I,WAAY,GACvB,MAAMA,EAAYwD,EAAKsF,WAAWvZ,GAAMA,EAAE/G,MAAQqgB,IAClD,OAAI7I,EAAY,EAEP,CAAEA,WAAY,GAEhB,CAAEA,YACX,CAEQyG,EACNiC,EACAC,GAGA,OADAA,EAA0BA,GAA2B,EAC3CD,GAAAA,OAAAA,eAAkBC,EAC9B,CAEQP,GACNre,GAEA,MAAM9B,EAAqC,CAAA,EAS3C,OARA8T,KAAKC,EAAK+M,iCAAoChN,KAAKC,EAChD+M,iCAEChN,KAAKC,EAAK+M,iCADVhN,KAAKoM,GAAwCpe,GAEjDgS,KAAKC,EAAK+M,iCAAiC9iB,SAAS8H,IAClD,MAAM6V,UAAEA,GAAc7H,KAAK8H,EAAkB9V,GAC7C9F,EAAW8F,GAAQuJ,GAASsM,EAAU,IAEjC3b,CACT,CAEQue,EACNwC,EACAC,EACAR,GAMA,MAAMjgB,EAAG,GAAA1D,OAAMkkB,EAAa,MAAAlkB,OAAKmkB,GAC3BC,EACJnN,KAAKC,EAAKuD,4BACVxD,KAAKC,EAAKuD,2BAA2B/W,IACjCuT,KAAKC,EAAKuD,2BAA2B/W,GAAKigB,aAC1C,GACAU,EAAiB,IAAKD,KAAwBT,GAIpD,MAAO,CACLjgB,MACA+d,IAAK,CACHyC,gBACAC,iBACAR,YAAaU,GAEf9C,QATAhhB,KAAKC,UAAU4jB,KAAyB7jB,KAAKC,UAAU6jB,GAW3D,GCnyCwB,IACrBpO,GACHxP,aAAcwP,GAAYxP,WAC1BiS,oBAAoB,EACpBsJ,iBAAkB,CAACngB,EAAGmd,KACpB,MAAMsF,EAAI,CAAEC,cAAe1iB,EAAE6B,IAAK8gB,aAAcxF,EAAEtb,KAClD7C,OAAOkV,UAAUvH,KAAK,CAAC,QAAS,oBAAqB8V,IACrDzjB,OAAO4jB,WACL5jB,OAAO4jB,UAAUC,OACjB7jB,OAAO4jB,UAAUC,MAAM,oBAAqBJ,EAAE,KAE/CnO,GACHhT,WAAYC,OAId0T,GAAG6N,eAGH,IAAIC,GAAarP,SAAS5E,YAC1BkU,aAAY,KACNtP,SAAS5E,OAASiU,KACpBA,GAAarP,SAAS5E,KACtBmG,GAAGgO,OAAOF,IACV9N,GAAGiO,cAAc,IACZjO,GAAG1T,mBACHA,OAEP,GACC"}