{"version": 3, "file": "index.min.js", "sources": ["../../src/feature-repository.ts", "../../../../node_modules/dom-mutator/dist/dom-mutator.esm.js", "../../src/util.ts", "../../src/mongrule.ts", "../../src/GrowthBook.ts", "../../src/sticky-bucket-service.ts"], "sourcesContent": ["import {\n  Attributes,\n  CacheSettings,\n  FeatureApiResponse,\n  Helpers,\n  Polyfills,\n} from \"./types/growthbook\";\nimport type { GrowthBook } from \".\";\n\ntype CacheEntry = {\n  data: FeatureApiResponse;\n  sse?: boolean;\n  version: string;\n  staleAt: Date;\n};\ntype ScopedChannel = {\n  src: EventSource | null;\n  cb: (event: MessageEvent<string>) => void;\n  host: string;\n  clientKey: string;\n  headers?: Record<string, string>;\n  errors: number;\n  state: \"active\" | \"idle\" | \"disabled\";\n};\n\n// Config settings\nconst cacheSettings: CacheSettings = {\n  // Consider a fetch stale after 1 minute\n  staleTTL: 1000 * 60,\n  // Max time to keep a fetch in cache (24 hours default)\n  maxAge: 1000 * 60 * 60 * 24,\n  cacheKey: \"gbFeaturesCache\",\n  backgroundSync: true,\n  maxEntries: 10,\n  disableIdleStreams: false,\n  idleStreamInterval: 20000,\n};\nconst polyfills: Polyfills = {\n  fetch: globalThis.fetch ? globalThis.fetch.bind(globalThis) : undefined,\n  SubtleCrypto: globalThis.crypto ? globalThis.crypto.subtle : undefined,\n  EventSource: globalThis.EventSource,\n};\nexport const helpers: Helpers = {\n  fetchFeaturesCall: ({ host, clientKey, headers }) => {\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/features/${clientKey}`,\n      { headers }\n    );\n  },\n  fetchRemoteEvalCall: ({ host, clientKey, payload, headers }) => {\n    const options = {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\", ...headers },\n      body: JSON.stringify(payload),\n    };\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/eval/${clientKey}`,\n      options\n    );\n  },\n  eventSourceCall: ({ host, clientKey, headers }) => {\n    if (headers) {\n      return new polyfills.EventSource(`${host}/sub/${clientKey}`, {\n        headers,\n      });\n    }\n    return new polyfills.EventSource(`${host}/sub/${clientKey}`);\n  },\n  startIdleListener: () => {\n    let idleTimeout: number | undefined;\n    const isBrowser =\n      typeof window !== \"undefined\" && typeof document !== \"undefined\";\n    if (!isBrowser) return;\n    const onVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        window.clearTimeout(idleTimeout);\n        onVisible();\n      } else if (document.visibilityState === \"hidden\") {\n        idleTimeout = window.setTimeout(\n          onHidden,\n          cacheSettings.idleStreamInterval\n        );\n      }\n    };\n    document.addEventListener(\"visibilitychange\", onVisibilityChange);\n    return () =>\n      document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n  },\n  stopIdleListener: () => {\n    // No-op, replaced by startIdleListener\n  },\n};\n\ntry {\n  if (globalThis.localStorage) {\n    polyfills.localStorage = globalThis.localStorage;\n  }\n} catch (e) {\n  // Ignore localStorage errors\n}\n\n// Global state\nconst subscribedInstances: Map<string, Set<GrowthBook>> = new Map();\nlet cacheInitialized = false;\nconst cache: Map<string, CacheEntry> = new Map();\nconst activeFetches: Map<string, Promise<FeatureApiResponse>> = new Map();\nconst streams: Map<string, ScopedChannel> = new Map();\nconst supportsSSE: Set<string> = new Set();\n\n// Public functions\nexport function setPolyfills(overrides: Partial<Polyfills>): void {\n  Object.assign(polyfills, overrides);\n}\nexport function configureCache(overrides: Partial<CacheSettings>): void {\n  Object.assign(cacheSettings, overrides);\n  if (!cacheSettings.backgroundSync) {\n    clearAutoRefresh();\n  }\n}\n\nexport async function clearCache(): Promise<void> {\n  cache.clear();\n  activeFetches.clear();\n  clearAutoRefresh();\n  cacheInitialized = false;\n  await updatePersistentCache();\n}\n\nexport async function refreshFeatures(\n  instance: GrowthBook,\n  timeout?: number,\n  skipCache?: boolean,\n  allowStale?: boolean,\n  updateInstance?: boolean,\n  backgroundSync?: boolean\n): Promise<void> {\n  if (!backgroundSync) {\n    cacheSettings.backgroundSync = false;\n  }\n\n  const data = await fetchFeaturesWithCache(\n    instance,\n    allowStale,\n    timeout,\n    skipCache\n  );\n  updateInstance && data && (await refreshInstance(instance, data));\n}\n\n// Subscribe a GrowthBook instance to feature changes\nexport function subscribe(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const subs = subscribedInstances.get(key) || new Set();\n  subs.add(instance);\n  subscribedInstances.set(key, subs);\n}\nexport function unsubscribe(instance: GrowthBook): void {\n  subscribedInstances.forEach((s) => s.delete(instance));\n}\n\nexport function onHidden() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    channel.state = \"idle\";\n    disableChannel(channel);\n  });\n}\n\nexport function onVisible() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    if (channel.state !== \"idle\") return;\n    enableChannel(channel);\n  });\n}\n\n// Private functions\n\nasync function updatePersistentCache() {\n  try {\n    if (!polyfills.localStorage) return;\n    await polyfills.localStorage.setItem(\n      cacheSettings.cacheKey,\n      JSON.stringify(Array.from(cache.entries()))\n    );\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n}\n\nasync function fetchFeaturesWithCache(\n  instance: GrowthBook,\n  allowStale?: boolean,\n  timeout?: number,\n  skipCache?: boolean\n): Promise<FeatureApiResponse | null> {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const now = new Date();\n\n  const minStaleAt = new Date(\n    now.getTime() - cacheSettings.maxAge + cacheSettings.staleTTL\n  );\n\n  await initializeCache();\n  const existing = cache.get(cacheKey);\n  if (\n    existing &&\n    !skipCache &&\n    (allowStale || existing.staleAt > now) &&\n    existing.staleAt > minStaleAt\n  ) {\n    // Restore from cache whether SSE is supported\n    if (existing.sse) supportsSSE.add(key);\n\n    // Reload features in the background if stale\n    if (existing.staleAt < now) {\n      fetchFeatures(instance);\n    }\n    // Otherwise, if we don't need to refresh now, start a background sync\n    else {\n      startAutoRefresh(instance);\n    }\n    return existing.data;\n  } else {\n    return await promiseTimeout(fetchFeatures(instance), timeout);\n  }\n}\n\nfunction getKey(instance: GrowthBook): string {\n  const [apiHost, clientKey] = instance.getApiInfo();\n  return `${apiHost}||${clientKey}`;\n}\n\nfunction getCacheKey(instance: GrowthBook): string {\n  const baseKey = getKey(instance);\n  if (!instance.isRemoteEval()) return baseKey;\n\n  const attributes = instance.getAttributes();\n  const cacheKeyAttributes =\n    instance.getCacheKeyAttributes() || Object.keys(instance.getAttributes());\n  const ca: Attributes = {};\n  cacheKeyAttributes.forEach((key) => {\n    ca[key] = attributes[key];\n  });\n\n  const fv = instance.getForcedVariations();\n  const url = instance.getUrl();\n\n  return `${baseKey}||${JSON.stringify({\n    ca,\n    fv,\n    url,\n  })}`;\n}\n\n// Guarantee the promise always resolves within {timeout} ms\n// Resolved value will be `null` when there's an error or it takes too long\n// Note: The promise will continue running in the background, even if the timeout is hit\nfunction promiseTimeout<T>(\n  promise: Promise<T>,\n  timeout?: number\n): Promise<T | null> {\n  return new Promise((resolve) => {\n    let resolved = false;\n    let timer: unknown;\n    const finish = (data?: T) => {\n      if (resolved) return;\n      resolved = true;\n      timer && clearTimeout(timer as NodeJS.Timer);\n      resolve(data || null);\n    };\n\n    if (timeout) {\n      timer = setTimeout(() => finish(), timeout);\n    }\n\n    promise.then((data) => finish(data)).catch(() => finish());\n  });\n}\n\n// Populate cache from localStorage (if available)\nasync function initializeCache(): Promise<void> {\n  if (cacheInitialized) return;\n  cacheInitialized = true;\n  try {\n    if (polyfills.localStorage) {\n      const value = await polyfills.localStorage.getItem(\n        cacheSettings.cacheKey\n      );\n      if (value) {\n        const parsed: [string, CacheEntry][] = JSON.parse(value);\n        if (parsed && Array.isArray(parsed)) {\n          parsed.forEach(([key, data]) => {\n            cache.set(key, {\n              ...data,\n              staleAt: new Date(data.staleAt),\n            });\n          });\n        }\n        cleanupCache();\n      }\n    }\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n  if (!cacheSettings.disableIdleStreams) {\n    const cleanupFn = helpers.startIdleListener();\n    if (cleanupFn) {\n      helpers.stopIdleListener = cleanupFn;\n    }\n  }\n}\n\n// Enforce the maxEntries limit\nfunction cleanupCache() {\n  const entriesWithTimestamps = Array.from(cache.entries())\n    .map(([key, value]) => ({\n      key,\n      staleAt: value.staleAt.getTime(),\n    }))\n    .sort((a, b) => a.staleAt - b.staleAt);\n\n  const entriesToRemoveCount = Math.min(\n    Math.max(0, cache.size - cacheSettings.maxEntries),\n    cache.size\n  );\n\n  for (let i = 0; i < entriesToRemoveCount; i++) {\n    cache.delete(entriesWithTimestamps[i].key);\n  }\n}\n\n// Called whenever new features are fetched from the API\nfunction onNewFeatureData(\n  key: string,\n  cacheKey: string,\n  data: FeatureApiResponse\n): void {\n  // If contents haven't changed, ignore the update, extend the stale TTL\n  const version = data.dateUpdated || \"\";\n  const staleAt = new Date(Date.now() + cacheSettings.staleTTL);\n  const existing = cache.get(cacheKey);\n  if (existing && version && existing.version === version) {\n    existing.staleAt = staleAt;\n    updatePersistentCache();\n    return;\n  }\n\n  // Update in-memory cache\n  cache.set(cacheKey, {\n    data,\n    version,\n    staleAt,\n    sse: supportsSSE.has(key),\n  });\n  cleanupCache();\n  // Update local storage (don't await this, just update asynchronously)\n  updatePersistentCache();\n\n  // Update features for all subscribed GrowthBook instances\n  const instances = subscribedInstances.get(key);\n  instances && instances.forEach((instance) => refreshInstance(instance, data));\n}\n\nasync function refreshInstance(\n  instance: GrowthBook,\n  data: FeatureApiResponse\n): Promise<void> {\n  data = await instance.decryptPayload(data, undefined, polyfills.SubtleCrypto);\n\n  await instance.refreshStickyBuckets(data);\n  instance.setExperiments(data.experiments || instance.getExperiments());\n  instance.setFeatures(data.features || instance.getFeatures());\n}\n\nasync function fetchFeatures(\n  instance: GrowthBook\n): Promise<FeatureApiResponse> {\n  const { apiHost, apiRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  const remoteEval = instance.isRemoteEval();\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n\n  let promise = activeFetches.get(cacheKey);\n  if (!promise) {\n    const fetcher: Promise<Response> = remoteEval\n      ? helpers.fetchRemoteEvalCall({\n          host: apiHost,\n          clientKey,\n          payload: {\n            attributes: instance.getAttributes(),\n            forcedVariations: instance.getForcedVariations(),\n            forcedFeatures: Array.from(instance.getForcedFeatures().entries()),\n            url: instance.getUrl(),\n          },\n          headers: apiRequestHeaders,\n        })\n      : helpers.fetchFeaturesCall({\n          host: apiHost,\n          clientKey,\n          headers: apiRequestHeaders,\n        });\n\n    // TODO: auto-retry if status code indicates a temporary error\n    promise = fetcher\n      .then((res) => {\n        if (res.headers.get(\"x-sse-support\") === \"enabled\") {\n          supportsSSE.add(key);\n        }\n        return res.json();\n      })\n      .then((data: FeatureApiResponse) => {\n        onNewFeatureData(key, cacheKey, data);\n        startAutoRefresh(instance);\n        activeFetches.delete(cacheKey);\n        return data;\n      })\n      .catch((e) => {\n        process.env.NODE_ENV !== \"production\" &&\n          instance.log(\"Error fetching features\", {\n            apiHost,\n            clientKey,\n            error: e ? e.message : null,\n          });\n        activeFetches.delete(cacheKey);\n        return Promise.resolve({});\n      });\n    activeFetches.set(cacheKey, promise);\n  }\n  return await promise;\n}\n\n// Watch a feature endpoint for changes\n// Will prefer SSE if enabled, otherwise fall back to cron\nfunction startAutoRefresh(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const { streamingHost, streamingHostRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  if (\n    cacheSettings.backgroundSync &&\n    supportsSSE.has(key) &&\n    polyfills.EventSource\n  ) {\n    if (streams.has(key)) return;\n    const channel: ScopedChannel = {\n      src: null,\n      host: streamingHost,\n      clientKey,\n      headers: streamingHostRequestHeaders,\n      cb: (event: MessageEvent<string>) => {\n        try {\n          if (event.type === \"features-updated\") {\n            const instances = subscribedInstances.get(key);\n            instances &&\n              instances.forEach((instance) => {\n                fetchFeatures(instance);\n              });\n          } else if (event.type === \"features\") {\n            const json: FeatureApiResponse = JSON.parse(event.data);\n            onNewFeatureData(key, cacheKey, json);\n          }\n          // Reset error count on success\n          channel.errors = 0;\n        } catch (e) {\n          process.env.NODE_ENV !== \"production\" &&\n            instance.log(\"SSE Error\", {\n              streamingHost,\n              clientKey,\n              error: e ? (e as Error).message : null,\n            });\n          onSSEError(channel);\n        }\n      },\n      errors: 0,\n      state: \"active\",\n    };\n    streams.set(key, channel);\n    enableChannel(channel);\n  }\n}\n\nfunction onSSEError(channel: ScopedChannel) {\n  if (channel.state === \"idle\") return;\n  channel.errors++;\n  if (channel.errors > 3 || (channel.src && channel.src.readyState === 2)) {\n    // exponential backoff after 4 errors, with jitter\n    const delay =\n      Math.pow(3, channel.errors - 3) * (1000 + Math.random() * 1000);\n    disableChannel(channel);\n    setTimeout(() => {\n      if ([\"idle\", \"active\"].includes(channel.state)) return;\n      enableChannel(channel);\n    }, Math.min(delay, 300000)); // 5 minutes max\n  }\n}\n\nfunction disableChannel(channel: ScopedChannel) {\n  if (!channel.src) return;\n  channel.src.onopen = null;\n  channel.src.onerror = null;\n  channel.src.close();\n  channel.src = null;\n  if (channel.state === \"active\") {\n    channel.state = \"disabled\";\n  }\n}\n\nfunction enableChannel(channel: ScopedChannel) {\n  channel.src = helpers.eventSourceCall({\n    host: channel.host,\n    clientKey: channel.clientKey,\n    headers: channel.headers,\n  }) as EventSource;\n  channel.state = \"active\";\n  channel.src.addEventListener(\"features\", channel.cb);\n  channel.src.addEventListener(\"features-updated\", channel.cb);\n  channel.src.onerror = () => onSSEError(channel);\n  channel.src.onopen = () => {\n    channel.errors = 0;\n  };\n}\n\nfunction destroyChannel(channel: ScopedChannel, key: string) {\n  disableChannel(channel);\n  streams.delete(key);\n}\n\nfunction clearAutoRefresh() {\n  // Clear list of which keys are auto-updated\n  supportsSSE.clear();\n\n  // Stop listening for any SSE events\n  streams.forEach(destroyChannel);\n\n  // Remove all references to GrowthBook instances\n  subscribedInstances.clear();\n\n  // Run the idle stream cleanup function\n  helpers.stopIdleListener();\n}\n", "var validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nvar nullController = {\n  revert: function revert() {}\n};\nvar elements = /*#__PURE__*/new Map();\nvar mutations = /*#__PURE__*/new Set();\n\nfunction getObserverInit(attr) {\n  return attr === 'html' ? {\n    childList: true,\n    subtree: true,\n    attributes: true,\n    characterData: true\n  } : {\n    childList: false,\n    subtree: false,\n    attributes: true,\n    attributeFilter: [attr]\n  };\n}\n\nfunction getElementRecord(element) {\n  var record = elements.get(element);\n\n  if (!record) {\n    record = {\n      element: element,\n      attributes: {}\n    };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(el, attr, getCurrentValue, setValue, mutationRunner) {\n  var currentValue = getCurrentValue(el);\n  var record = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el: el,\n    _positionTimeout: null,\n    observer: new MutationObserver(function () {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;else if (attr === 'position') record._positionTimeout = setTimeout(function () {\n        record._positionTimeout = null;\n      }, 1000);\n      var currentValue = getCurrentValue(el);\n      if (attr === 'position' && currentValue.parentNode === record.virtualValue.parentNode && currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode) return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner: mutationRunner,\n    setValue: setValue,\n    getCurrentValue: getCurrentValue\n  };\n\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n\n  return record;\n}\n\nfunction queueIfNeeded(val, record) {\n  var currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n\n  if (val && typeof val !== 'string') {\n    if (!currentVal || val.parentNode !== currentVal.parentNode || val.insertBeforeNode !== currentVal.insertBeforeNode) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(getTransformedHTML(val), record);\n}\n\nfunction classMutationRunner(record) {\n  var val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(function (m) {\n    return m.mutate(val);\n  });\n  queueIfNeeded(Array.from(val).filter(Boolean).join(' '), record);\n}\n\nfunction attrMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    return val = m.mutate(val);\n  });\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes(_ref) {\n  var parentSelector = _ref.parentSelector,\n      insertBeforeSelector = _ref.insertBeforeSelector;\n  var parentNode = document.querySelector(parentSelector);\n  if (!parentNode) return null;\n  var insertBeforeNode = insertBeforeSelector ? document.querySelector(insertBeforeSelector) : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode: parentNode,\n    insertBeforeNode: insertBeforeNode\n  };\n}\n\nfunction positionMutationRunner(record) {\n  var val = record.originalValue;\n  record.mutations.forEach(function (m) {\n    var selectors = m.mutate();\n\n    var newNodes = _loadDOMNodes(selectors);\n\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nvar getHTMLValue = function getHTMLValue(el) {\n  return el.innerHTML;\n};\n\nvar setHTMLValue = function setHTMLValue(el, value) {\n  return el.innerHTML = value;\n};\n\nfunction getElementHTMLRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(element, 'html', getHTMLValue, setHTMLValue, htmlMutationRunner);\n  }\n\n  return elementRecord.html;\n}\n\nvar getElementPosition = function getElementPosition(el) {\n  return {\n    parentNode: el.parentElement,\n    insertBeforeNode: el.nextElementSibling\n  };\n};\n\nvar setElementPosition = function setElementPosition(el, value) {\n  if (value.insertBeforeNode && !value.parentNode.contains(value.insertBeforeNode)) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\n\nfunction getElementPositionRecord(element) {\n  var elementRecord = getElementRecord(element);\n\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(element, 'position', getElementPosition, setElementPosition, positionMutationRunner);\n  }\n\n  return elementRecord.position;\n}\n\nvar setClassValue = function setClassValue(el, val) {\n  return val ? el.className = val : el.removeAttribute('class');\n};\n\nvar getClassValue = function getClassValue(el) {\n  return el.className;\n};\n\nfunction getElementClassRecord(el) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(el, 'class', getClassValue, setClassValue, classMutationRunner);\n  }\n\n  return elementRecord.classes;\n}\n\nvar getAttrValue = function getAttrValue(attrName) {\n  return function (el) {\n    var _el$getAttribute;\n\n    return (_el$getAttribute = el.getAttribute(attrName)) != null ? _el$getAttribute : null;\n  };\n};\n\nvar setAttrValue = function setAttrValue(attrName) {\n  return function (el, val) {\n    return val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\n  };\n};\n\nfunction getElementAttributeRecord(el, attr) {\n  var elementRecord = getElementRecord(el);\n\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(el, attr, getAttrValue(attr), setAttrValue(attr), attrMutationRunner);\n  }\n\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el, attr) {\n  var element = elements.get(el);\n  if (!element) return;\n\n  if (attr === 'html') {\n    var _element$html, _element$html$observe;\n\n    (_element$html = element.html) == null ? void 0 : (_element$html$observe = _element$html.observer) == null ? void 0 : _element$html$observe.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    var _element$classes, _element$classes$obse;\n\n    (_element$classes = element.classes) == null ? void 0 : (_element$classes$obse = _element$classes.observer) == null ? void 0 : _element$classes$obse.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    var _element$position, _element$position$obs;\n\n    (_element$position = element.position) == null ? void 0 : (_element$position$obs = _element$position.observer) == null ? void 0 : _element$position$obs.disconnect();\n    delete element.position;\n  } else {\n    var _element$attributes, _element$attributes$a, _element$attributes$a2;\n\n    (_element$attributes = element.attributes) == null ? void 0 : (_element$attributes$a = _element$attributes[attr]) == null ? void 0 : (_element$attributes$a2 = _element$attributes$a.observer) == null ? void 0 : _element$attributes$a2.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nvar transformContainer;\n\nfunction getTransformedHTML(html) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue(el, attr, m) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  var val = m.virtualValue;\n\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n\n  m.setValue(el, val);\n}\n\nfunction setValue(m, el) {\n  m.html && setPropertyValue(el, 'html', m.html);\n  m.classes && setPropertyValue(el, 'class', m.classes);\n  m.position && setPropertyValue(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(function (attr) {\n    setPropertyValue(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n} // find or create ElementPropertyRecord, add mutation to it, then run\n\n\nfunction startMutating(mutation, element) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n} // get (existing) ElementPropertyRecord, remove mutation from it, then run\n\n\nfunction stopMutating(mutation, el) {\n  var record = null;\n\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n\n  if (!record) return;\n  var index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n} // maintain list of elements associated with mutation\n\n\nfunction refreshElementsSet(mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n  var existingElements = new Set(mutation.elements);\n  var matchingElements = document.querySelectorAll(mutation.selector);\n  matchingElements.forEach(function (el) {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation) {\n  mutation.elements.forEach(function (el) {\n    return stopMutating(mutation, el);\n  });\n  mutation.elements.clear();\n  mutations[\"delete\"](mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n} // Observer for elements that don't exist in the DOM yet\n\n\nvar observer;\nfunction disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nfunction connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(function () {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false\n  });\n} // run on init\n\nconnectGlobalObserver();\n\nfunction newMutation(m) {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController; // add to global index of mutations\n\n  mutations.add(m); // run refresh on init to establish list of elements associated w/ mutation\n\n  refreshElementsSet(m);\n  return {\n    revert: function revert() {\n      revertMutation(m);\n    }\n  };\n}\n\nfunction html(selector, mutate) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction position(selector, mutate) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction classes(selector, mutate) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction attribute(selector, attribute, mutate) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, function (classnames) {\n      var mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames.split(/\\s+/g).filter(Boolean).forEach(function (c) {\n        return classnames.add(c);\n      });\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute: attribute,\n    elements: new Set(),\n    mutate: mutate,\n    selector: selector\n  });\n}\n\nfunction declarative(_ref2) {\n  var selector = _ref2.selector,\n      action = _ref2.action,\n      value = _ref2.value,\n      attr = _ref2.attribute,\n      parentSelector = _ref2.parentSelector,\n      insertBeforeSelector = _ref2.insertBeforeSelector;\n\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, function (val) {\n        return val + (value != null ? value : '');\n      });\n    } else if (action === 'set') {\n      return html(selector, function () {\n        return value != null ? value : '';\n      });\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, function (val) {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, function (val) {\n        if (value) val[\"delete\"](value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, function (val) {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, function () {\n        return {\n          insertBeforeSelector: insertBeforeSelector,\n          parentSelector: parentSelector\n        };\n      });\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, function (val) {\n        return val !== null ? val + (value != null ? value : '') : value != null ? value : '';\n      });\n    } else if (action === 'set') {\n      return attribute(selector, attr, function () {\n        return value != null ? value : '';\n      });\n    } else if (action === 'remove') {\n      return attribute(selector, attr, function () {\n        return null;\n      });\n    }\n  }\n\n  return nullController;\n}\n\nvar index = {\n  html: html,\n  classes: classes,\n  attribute: attribute,\n  position: position,\n  declarative: declarative\n};\n\nexport default index;\nexport { connectGlobalObserver, disconnectGlobalObserver, validAttributeName };\n//# sourceMappingURL=dom-mutator.esm.js.map\n", "import { UrlTarget, UrlTargetType, VariationRange } from \"./types/growthbook\";\n\nfunction hashFnv32a(str: string): number {\n  let hval = 0x811c9dc5;\n  const l = str.length;\n\n  for (let i = 0; i < l; i++) {\n    hval ^= str.charCodeAt(i);\n    hval +=\n      (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);\n  }\n  return hval >>> 0;\n}\n\nexport function hash(\n  seed: string,\n  value: string,\n  version: number\n): number | null {\n  // New unbiased hashing algorithm\n  if (version === 2) {\n    return (hashFnv32a(hashFnv32a(seed + value) + \"\") % 10000) / 10000;\n  }\n  // Original biased hashing algorithm (keep for backwards compatibility)\n  if (version === 1) {\n    return (hashFnv32a(value + seed) % 1000) / 1000;\n  }\n\n  // Unknown hash version\n  return null;\n}\n\nexport function getEqualWeights(n: number): number[] {\n  if (n <= 0) return [];\n  return new Array(n).fill(1 / n);\n}\n\nexport function inRange(n: number, range: VariationRange): boolean {\n  return n >= range[0] && n < range[1];\n}\n\nexport function inNamespace(\n  hashValue: string,\n  namespace: [string, number, number]\n): boolean {\n  const n = hash(\"__\" + namespace[0], hashValue, 1);\n  if (n === null) return false;\n  return n >= namespace[1] && n < namespace[2];\n}\n\nexport function chooseVariation(n: number, ranges: VariationRange[]): number {\n  for (let i = 0; i < ranges.length; i++) {\n    if (inRange(n, ranges[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nexport function getUrlRegExp(regexString: string): RegExp | undefined {\n  try {\n    const escaped = regexString.replace(/([^\\\\])\\//g, \"$1\\\\/\");\n    return new RegExp(escaped);\n  } catch (e) {\n    console.error(e);\n    return undefined;\n  }\n}\n\nexport function isURLTargeted(url: string, targets: UrlTarget[]) {\n  if (!targets.length) return false;\n  let hasIncludeRules = false;\n  let isIncluded = false;\n\n  for (let i = 0; i < targets.length; i++) {\n    const match = _evalURLTarget(url, targets[i].type, targets[i].pattern);\n    if (targets[i].include === false) {\n      if (match) return false;\n    } else {\n      hasIncludeRules = true;\n      if (match) isIncluded = true;\n    }\n  }\n\n  return isIncluded || !hasIncludeRules;\n}\n\nfunction _evalSimpleUrlPart(\n  actual: string,\n  pattern: string,\n  isPath: boolean\n): boolean {\n  try {\n    // Escape special regex characters and change wildcard `_____` to `.*`\n    let escaped = pattern\n      .replace(/[*.+?^${}()|[\\]\\\\]/g, \"\\\\$&\")\n      .replace(/_____/g, \".*\");\n\n    if (isPath) {\n      // When matching pathname, make leading/trailing slashes optional\n      escaped = \"\\\\/?\" + escaped.replace(/(^\\/|\\/$)/g, \"\") + \"\\\\/?\";\n    }\n\n    const regex = new RegExp(\"^\" + escaped + \"$\", \"i\");\n    return regex.test(actual);\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalSimpleUrlTarget(actual: URL, pattern: string) {\n  try {\n    // If a protocol is missing, but a host is specified, add `https://` to the front\n    // Use \"_____\" as the wildcard since `*` is not a valid hostname in some browsers\n    const expected = new URL(\n      pattern.replace(/^([^:/?]*)\\./i, \"https://$1.\").replace(/\\*/g, \"_____\"),\n      \"https://_____\"\n    );\n\n    // Compare each part of the URL separately\n    const comps: Array<[string, string, boolean]> = [\n      [actual.host, expected.host, false],\n      [actual.pathname, expected.pathname, true],\n    ];\n    // We only want to compare hashes if it's explicitly being targeted\n    if (expected.hash) {\n      comps.push([actual.hash, expected.hash, false]);\n    }\n\n    expected.searchParams.forEach((v, k) => {\n      comps.push([actual.searchParams.get(k) || \"\", v, false]);\n    });\n\n    // If any comparisons fail, the whole thing fails\n    return !comps.some(\n      (data) => !_evalSimpleUrlPart(data[0], data[1], data[2])\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalURLTarget(\n  url: string,\n  type: UrlTargetType,\n  pattern: string\n): boolean {\n  try {\n    const parsed = new URL(url, \"https://_\");\n\n    if (type === \"regex\") {\n      const regex = getUrlRegExp(pattern);\n      if (!regex) return false;\n      return (\n        regex.test(parsed.href) ||\n        regex.test(parsed.href.substring(parsed.origin.length))\n      );\n    } else if (type === \"simple\") {\n      return _evalSimpleUrlTarget(parsed, pattern);\n    }\n\n    return false;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function getBucketRanges(\n  numVariations: number,\n  coverage: number | undefined,\n  weights?: number[]\n): VariationRange[] {\n  coverage = coverage === undefined ? 1 : coverage;\n\n  // Make sure coverage is within bounds\n  if (coverage < 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be greater than or equal to 0\");\n    }\n    coverage = 0;\n  } else if (coverage > 1) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be less than or equal to 1\");\n    }\n    coverage = 1;\n  }\n\n  // Default to equal weights if missing or invalid\n  const equal = getEqualWeights(numVariations);\n  weights = weights || equal;\n  if (weights.length !== numVariations) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\n        \"Experiment.weights array must be the same length as Experiment.variations\"\n      );\n    }\n    weights = equal;\n  }\n\n  // If weights don't add up to 1 (or close to it), default to equal weights\n  const totalWeight = weights.reduce((w, sum) => sum + w, 0);\n  if (totalWeight < 0.99 || totalWeight > 1.01) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.weights must add up to 1\");\n    }\n    weights = equal;\n  }\n\n  // Covert weights to ranges\n  let cumulative = 0;\n  return weights.map((w) => {\n    const start = cumulative;\n    cumulative += w;\n    return [start, start + (coverage as number) * w];\n  }) as VariationRange[];\n}\n\nexport function getQueryStringOverride(\n  id: string,\n  url: string,\n  numVariations: number\n) {\n  if (!url) {\n    return null;\n  }\n\n  const search = url.split(\"?\")[1];\n  if (!search) {\n    return null;\n  }\n\n  const match = search\n    .replace(/#.*/, \"\") // Get rid of anchor\n    .split(\"&\") // Split into key/value pairs\n    .map((kv) => kv.split(\"=\", 2))\n    .filter(([k]) => k === id) // Look for key that matches the experiment id\n    .map(([, v]) => parseInt(v)); // Parse the value into an integer\n\n  if (match.length > 0 && match[0] >= 0 && match[0] < numVariations)\n    return match[0];\n\n  return null;\n}\n\nexport function isIncluded(include: () => boolean) {\n  try {\n    return include();\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\n\nconst base64ToBuf = (b: string) =>\n  Uint8Array.from(atob(b), (c) => c.charCodeAt(0));\n\nexport async function decrypt(\n  encryptedString: string,\n  decryptionKey?: string,\n  subtle?: SubtleCrypto\n): Promise<string> {\n  decryptionKey = decryptionKey || \"\";\n  subtle = subtle || (globalThis.crypto && globalThis.crypto.subtle);\n  if (!subtle) {\n    throw new Error(\"No SubtleCrypto implementation found\");\n  }\n  try {\n    const key = await subtle.importKey(\n      \"raw\",\n      base64ToBuf(decryptionKey),\n      { name: \"AES-CBC\", length: 128 },\n      true,\n      [\"encrypt\", \"decrypt\"]\n    );\n    const [iv, cipherText] = encryptedString.split(\".\");\n    const plainTextBuffer = await subtle.decrypt(\n      { name: \"AES-CBC\", iv: base64ToBuf(iv) },\n      key,\n      base64ToBuf(cipherText)\n    );\n\n    return new TextDecoder().decode(plainTextBuffer);\n  } catch (e) {\n    throw new Error(\"Failed to decrypt\");\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function toString(input: any): string {\n  if (typeof input === \"string\") return input;\n  return JSON.stringify(input);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function paddedVersionString(input: any): string {\n  if (typeof input === \"number\") {\n    input = input + \"\";\n  }\n  if (!input || typeof input !== \"string\") {\n    input = \"0\";\n  }\n  // Remove build info and leading `v` if any\n  // Split version into parts (both core version numbers and pre-release tags)\n  // \"v1.2.3-rc.1+build123\" -> [\"1\",\"2\",\"3\",\"rc\",\"1\"]\n  const parts = (input as string).replace(/(^v|\\+.*$)/g, \"\").split(/[-.]/);\n\n  // If it's SemVer without a pre-release, add `~` to the end\n  // [\"1\",\"0\",\"0\"] -> [\"1\",\"0\",\"0\",\"~\"]\n  // \"~\" is the largest ASCII character, so this will make \"1.0.0\" greater than \"1.0.0-beta\" for example\n  if (parts.length === 3) {\n    parts.push(\"~\");\n  }\n\n  // Left pad each numeric part with spaces so string comparisons will work (\"9\">\"10\", but \" 9\"<\"10\")\n  // Then, join back together into a single string\n  return parts\n    .map((v) => (v.match(/^[0-9]+$/) ? v.padStart(5, \" \") : v))\n    .join(\"-\");\n}\n\nexport function loadSDKVersion(): string {\n  let version: string;\n  try {\n    // @ts-expect-error right-hand value to be replaced by build with string literal\n    version = __SDK_VERSION__;\n  } catch (e) {\n    version = \"\";\n  }\n  return version;\n}\n", "/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport {\n  ConditionInterface,\n  TestedObj,\n  ConditionValue,\n  Operator,\n  OperatorConditionValue,\n  VarType,\n} from \"./types/mongrule\";\nimport { paddedVersionString } from \"./util\";\n\nconst _regexCache: { [key: string]: RegExp } = {};\n\n// The top-level condition evaluation function\nexport function evalCondition(\n  obj: TestedObj,\n  condition: ConditionInterface\n): boolean {\n  // Recursive condition\n  if (\"$or\" in condition) {\n    return evalOr(obj, condition[\"$or\"] as ConditionInterface[]);\n  }\n  if (\"$nor\" in condition) {\n    return !evalOr(obj, condition[\"$nor\"] as ConditionInterface[]);\n  }\n  if (\"$and\" in condition) {\n    return evalAnd(obj, condition[\"$and\"] as ConditionInterface[]);\n  }\n  if (\"$not\" in condition) {\n    return !evalCondition(obj, condition[\"$not\"] as ConditionInterface);\n  }\n\n  // Condition is an object, keys are object paths, values are the condition for that path\n  for (const [k, v] of Object.entries(condition)) {\n    if (!evalConditionValue(v, getPath(obj, k))) return false;\n  }\n  return true;\n}\n\n// Return value at dot-separated path of an object\nfunction getPath(obj: TestedObj, path: string) {\n  const parts = path.split(\".\");\n  let current: any = obj;\n  for (let i = 0; i < parts.length; i++) {\n    if (current && typeof current === \"object\" && parts[i] in current) {\n      current = current[parts[i]];\n    } else {\n      return null;\n    }\n  }\n  return current;\n}\n\n// Transform a regex string into a real RegExp object\nfunction getRegex(regex: string): RegExp {\n  if (!_regexCache[regex]) {\n    _regexCache[regex] = new RegExp(regex.replace(/([^\\\\])\\//g, \"$1\\\\/\"));\n  }\n  return _regexCache[regex];\n}\n\n// Evaluate a single value against a condition\nfunction evalConditionValue(condition: ConditionValue, value: any) {\n  // Simple equality comparisons\n  if (typeof condition === \"string\") {\n    return value + \"\" === condition;\n  }\n  if (typeof condition === \"number\") {\n    return value * 1 === condition;\n  }\n  if (typeof condition === \"boolean\") {\n    return !!value === condition;\n  }\n\n  if (condition === null) {\n    return value === null;\n  }\n\n  if (Array.isArray(condition) || !isOperatorObject(condition)) {\n    return JSON.stringify(value) === JSON.stringify(condition);\n  }\n\n  // This is a special operator condition and we should evaluate each one separately\n  for (const op in condition) {\n    if (\n      !evalOperatorCondition(\n        op as Operator,\n        value,\n        condition[op as keyof OperatorConditionValue]\n      )\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// If the object has only keys that start with '$'\nfunction isOperatorObject(obj: any): boolean {\n  const keys = Object.keys(obj);\n  return (\n    keys.length > 0 && keys.filter((k) => k[0] === \"$\").length === keys.length\n  );\n}\n\n// Return the data type of a value\nfunction getType(v: any): VarType | \"unknown\" {\n  if (v === null) return \"null\";\n  if (Array.isArray(v)) return \"array\";\n  const t = typeof v;\n  if ([\"string\", \"number\", \"boolean\", \"object\", \"undefined\"].includes(t)) {\n    return t as VarType;\n  }\n  return \"unknown\";\n}\n\n// At least one element of actual must match the expected condition/value\nfunction elemMatch(actual: any, expected: any) {\n  if (!Array.isArray(actual)) return false;\n  const check = isOperatorObject(expected)\n    ? (v: any) => evalConditionValue(expected, v)\n    : (v: any) => evalCondition(v, expected);\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i] && check(actual[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isIn(actual: any, expected: Array<any>): boolean {\n  // Do an intersection is attribute is an array\n  if (Array.isArray(actual)) {\n    return actual.some((el) => expected.includes(el));\n  }\n  return expected.includes(actual);\n}\n\n// Evaluate a single operator condition\nfunction evalOperatorCondition(\n  operator: Operator,\n  actual: any,\n  expected: any\n): boolean {\n  switch (operator) {\n    case \"$veq\":\n      return paddedVersionString(actual) === paddedVersionString(expected);\n    case \"$vne\":\n      return paddedVersionString(actual) !== paddedVersionString(expected);\n    case \"$vgt\":\n      return paddedVersionString(actual) > paddedVersionString(expected);\n    case \"$vgte\":\n      return paddedVersionString(actual) >= paddedVersionString(expected);\n    case \"$vlt\":\n      return paddedVersionString(actual) < paddedVersionString(expected);\n    case \"$vlte\":\n      return paddedVersionString(actual) <= paddedVersionString(expected);\n    case \"$eq\":\n      return actual === expected;\n    case \"$ne\":\n      return actual !== expected;\n    case \"$lt\":\n      return actual < expected;\n    case \"$lte\":\n      return actual <= expected;\n    case \"$gt\":\n      return actual > expected;\n    case \"$gte\":\n      return actual >= expected;\n    case \"$exists\":\n      // Using `!=` and `==` instead of strict checks so it also matches for undefined\n      return expected ? actual != null : actual == null;\n    case \"$in\":\n      if (!Array.isArray(expected)) return false;\n      return isIn(actual, expected);\n    case \"$nin\":\n      if (!Array.isArray(expected)) return false;\n      return !isIn(actual, expected);\n    case \"$not\":\n      return !evalConditionValue(expected, actual);\n    case \"$size\":\n      if (!Array.isArray(actual)) return false;\n      return evalConditionValue(expected, actual.length);\n    case \"$elemMatch\":\n      return elemMatch(actual, expected);\n    case \"$all\":\n      if (!Array.isArray(actual)) return false;\n      for (let i = 0; i < expected.length; i++) {\n        let passed = false;\n        for (let j = 0; j < actual.length; j++) {\n          if (evalConditionValue(expected[i], actual[j])) {\n            passed = true;\n            break;\n          }\n        }\n        if (!passed) return false;\n      }\n      return true;\n    case \"$regex\":\n      try {\n        return getRegex(expected).test(actual);\n      } catch (e) {\n        return false;\n      }\n    case \"$type\":\n      return getType(actual) === expected;\n    default:\n      console.error(\"Unknown operator: \" + operator);\n      return false;\n  }\n}\n\n// Recursive $or rule\nfunction evalOr(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  if (!conditions.length) return true;\n  for (let i = 0; i < conditions.length; i++) {\n    if (evalCondition(obj, conditions[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// Recursive $and rule\nfunction evalAnd(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  for (let i = 0; i < conditions.length; i++) {\n    if (!evalCondition(obj, conditions[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n", "import mutate, { DeclarativeMutation } from \"dom-mutator\";\nimport type {\n  ApiHost,\n  Attributes,\n  AutoExperiment,\n  AutoExperimentVariation,\n  ClientKey,\n  Context,\n  Experiment,\n  FeatureApiResponse,\n  FeatureDefinition,\n  FeatureResult,\n  FeatureResultSource,\n  Filter,\n  LoadFeaturesOptions,\n  RealtimeUsageData,\n  RefreshFeaturesOptions,\n  Result,\n  StickyAssignments,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n  StickyExperimentKey,\n  SubscriptionFunction,\n  VariationMeta,\n  VariationRange,\n  WidenPrimitives,\n} from \"./types/growthbook\";\nimport type { ConditionInterface } from \"./types/mongrule\";\nimport {\n  chooseVariation,\n  decrypt,\n  getBucketRanges,\n  getQueryStringOverride,\n  getUrlRegExp,\n  hash,\n  inNamespace,\n  inRange,\n  isIncluded,\n  isURLTargeted,\n  loadSDKVersion,\n  toString,\n} from \"./util\";\nimport { evalCondition } from \"./mongrule\";\nimport { refreshFeatures, subscribe, unsubscribe } from \"./feature-repository\";\n\nconst isBrowser =\n  typeof window !== \"undefined\" && typeof document !== \"undefined\";\n\nconst SDK_VERSION = loadSDKVersion();\n\nexport class GrowthBook<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  AppFeatures extends Record<string, any> = Record<string, any>\n> {\n  // context is technically private, but some tools depend on it so we can't mangle the name\n  // _ctx below is a clone of this property that we use internally\n  private context: Context;\n  public debug: boolean;\n  public ready: boolean;\n  public version: string;\n\n  // Properties and methods that start with \"_\" are mangled by Terser (saves ~150 bytes)\n  private _ctx: Context;\n  private _renderer: null | (() => void);\n  private _trackedExperiments: Set<unknown>;\n  private _trackedFeatures: Record<string, string>;\n  private _subscriptions: Set<SubscriptionFunction>;\n  private _rtQueue: RealtimeUsageData[];\n  private _rtTimer: number;\n  private _assigned: Map<\n    string,\n    {\n      // eslint-disable-next-line\n      experiment: Experiment<any>;\n      // eslint-disable-next-line\n      result: Result<any>;\n    }\n  >;\n  // eslint-disable-next-line\n  private _forcedFeatureValues: Map<string, any>;\n  private _attributeOverrides: Attributes;\n  private _activeAutoExperiments: Map<\n    AutoExperiment,\n    { valueHash: string; undo: () => void }\n  >;\n  private _triggeredExpKeys: Set<string>;\n  private _loadFeaturesCalled: boolean;\n\n  constructor(context?: Context) {\n    context = context || {};\n    // These properties are all initialized in the constructor instead of above\n    // This saves ~80 bytes in the final output\n    this.version = SDK_VERSION;\n    this._ctx = this.context = context;\n    this._renderer = null;\n    this._trackedExperiments = new Set();\n    this._trackedFeatures = {};\n    this.debug = false;\n    this._subscriptions = new Set();\n    this._rtQueue = [];\n    this._rtTimer = 0;\n    this.ready = false;\n    this._assigned = new Map();\n    this._forcedFeatureValues = new Map();\n    this._attributeOverrides = {};\n    this._activeAutoExperiments = new Map();\n    this._triggeredExpKeys = new Set();\n    this._loadFeaturesCalled = false;\n\n    if (context.remoteEval) {\n      if (context.decryptionKey) {\n        throw new Error(\"Encryption is not available for remoteEval\");\n      }\n      if (!context.clientKey) {\n        throw new Error(\"Missing clientKey\");\n      }\n      let isGbHost = false;\n      try {\n        isGbHost = !!new URL(context.apiHost || \"\").hostname.match(\n          /growthbook\\.io$/i\n        );\n      } catch (e) {\n        // ignore invalid URLs\n      }\n      if (isGbHost) {\n        throw new Error(\"Cannot use remoteEval on GrowthBook Cloud\");\n      }\n    } else {\n      if (context.cacheKeyAttributes) {\n        throw new Error(\"cacheKeyAttributes are only used for remoteEval\");\n      }\n    }\n\n    if (context.features) {\n      this.ready = true;\n    }\n\n    if (isBrowser && context.enableDevMode) {\n      window._growthbook = this;\n      document.dispatchEvent(new Event(\"gbloaded\"));\n    }\n\n    if (context.experiments) {\n      this.ready = true;\n      this._updateAllAutoExperiments();\n    }\n\n    if (context.clientKey && !context.remoteEval) {\n      this._refresh({}, true, false);\n    }\n  }\n\n  public async loadFeatures(options?: LoadFeaturesOptions): Promise<void> {\n    if (options && options.autoRefresh) {\n      // interpret deprecated autoRefresh option as subscribeToChanges\n      this._ctx.subscribeToChanges = true;\n    }\n    this._loadFeaturesCalled = true;\n\n    await this._refresh(options, true, true);\n\n    if (this._canSubscribe()) {\n      subscribe(this);\n    }\n  }\n\n  public async refreshFeatures(\n    options?: RefreshFeaturesOptions\n  ): Promise<void> {\n    await this._refresh(options, false, true);\n  }\n\n  public getApiInfo(): [ApiHost, ClientKey] {\n    return [this.getApiHosts().apiHost, this.getClientKey()];\n  }\n  public getApiHosts(): {\n    apiHost: string;\n    streamingHost: string;\n    apiRequestHeaders?: Record<string, string>;\n    streamingHostRequestHeaders?: Record<string, string>;\n  } {\n    const defaultHost = this._ctx.apiHost || \"https://cdn.growthbook.io\";\n    return {\n      apiHost: defaultHost.replace(/\\/*$/, \"\"),\n      streamingHost: (this._ctx.streamingHost || defaultHost).replace(\n        /\\/*$/,\n        \"\"\n      ),\n      apiRequestHeaders: this._ctx.apiHostRequestHeaders,\n      streamingHostRequestHeaders: this._ctx.streamingHostRequestHeaders,\n    };\n  }\n  public getClientKey(): string {\n    return this._ctx.clientKey || \"\";\n  }\n\n  public isRemoteEval(): boolean {\n    return this._ctx.remoteEval || false;\n  }\n\n  public getCacheKeyAttributes(): (keyof Attributes)[] | undefined {\n    return this._ctx.cacheKeyAttributes;\n  }\n\n  private async _refresh(\n    options?: RefreshFeaturesOptions,\n    allowStale?: boolean,\n    updateInstance?: boolean\n  ) {\n    options = options || {};\n    if (!this._ctx.clientKey) {\n      throw new Error(\"Missing clientKey\");\n    }\n    await refreshFeatures(\n      this,\n      options.timeout,\n      options.skipCache || this._ctx.enableDevMode,\n      allowStale,\n      updateInstance,\n      this._ctx.backgroundSync !== false\n    );\n  }\n\n  private _render() {\n    if (this._renderer) {\n      this._renderer();\n    }\n  }\n\n  public setFeatures(features: Record<string, FeatureDefinition>) {\n    this._ctx.features = features;\n    this.ready = true;\n    this._render();\n  }\n\n  public async setEncryptedFeatures(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const featuresJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setFeatures(\n      JSON.parse(featuresJSON) as Record<string, FeatureDefinition>\n    );\n  }\n\n  public setExperiments(experiments: AutoExperiment[]): void {\n    this._ctx.experiments = experiments;\n    this.ready = true;\n    this._updateAllAutoExperiments();\n  }\n\n  public async setEncryptedExperiments(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const experimentsJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setExperiments(JSON.parse(experimentsJSON) as AutoExperiment[]);\n  }\n\n  public async decryptPayload(\n    data: FeatureApiResponse,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<FeatureApiResponse> {\n    if (data.encryptedFeatures) {\n      data.features = JSON.parse(\n        await decrypt(\n          data.encryptedFeatures,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedFeatures;\n    }\n    if (data.encryptedExperiments) {\n      data.experiments = JSON.parse(\n        await decrypt(\n          data.encryptedExperiments,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedExperiments;\n    }\n    return data;\n  }\n\n  public async setAttributes(attributes: Attributes) {\n    this._ctx.attributes = attributes;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setAttributeOverrides(overrides: Attributes) {\n    this._attributeOverrides = overrides;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setForcedVariations(vars: Record<string, number>) {\n    this._ctx.forcedVariations = vars || {};\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  // eslint-disable-next-line\n  public setForcedFeatures(map: Map<string, any>) {\n    this._forcedFeatureValues = map;\n    this._render();\n  }\n\n  public async setURL(url: string) {\n    this._ctx.url = url;\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      this._updateAllAutoExperiments(true);\n      return;\n    }\n    this._updateAllAutoExperiments(true);\n  }\n\n  public getAttributes() {\n    return { ...this._ctx.attributes, ...this._attributeOverrides };\n  }\n\n  public getForcedVariations() {\n    return this._ctx.forcedVariations || {};\n  }\n\n  public getForcedFeatures() {\n    // eslint-disable-next-line\n    return this._forcedFeatureValues || new Map<string, any>();\n  }\n\n  public getStickyBucketAssignmentDocs() {\n    return this._ctx.stickyBucketAssignmentDocs || {};\n  }\n\n  public getUrl() {\n    return this._ctx.url || \"\";\n  }\n\n  public getFeatures() {\n    return this._ctx.features || {};\n  }\n\n  public getExperiments() {\n    return this._ctx.experiments || [];\n  }\n\n  public subscribe(cb: SubscriptionFunction): () => void {\n    this._subscriptions.add(cb);\n    return () => {\n      this._subscriptions.delete(cb);\n    };\n  }\n\n  private _canSubscribe() {\n    return this._ctx.backgroundSync !== false && this._ctx.subscribeToChanges;\n  }\n\n  private async _refreshForRemoteEval() {\n    if (!this._ctx.remoteEval) return;\n    if (!this._loadFeaturesCalled) return;\n    await this._refresh({}, false, true).catch(() => {\n      // Ignore errors\n    });\n  }\n\n  public getAllResults() {\n    return new Map(this._assigned);\n  }\n\n  public destroy() {\n    // Release references to save memory\n    this._subscriptions.clear();\n    this._assigned.clear();\n    this._trackedExperiments.clear();\n    this._trackedFeatures = {};\n    this._rtQueue = [];\n    if (this._rtTimer) {\n      clearTimeout(this._rtTimer);\n    }\n    unsubscribe(this);\n\n    if (isBrowser && window._growthbook === this) {\n      delete window._growthbook;\n    }\n\n    // Undo any active auto experiments\n    this._activeAutoExperiments.forEach((exp) => {\n      exp.undo();\n    });\n    this._activeAutoExperiments.clear();\n    this._triggeredExpKeys.clear();\n  }\n\n  public setRenderer(renderer: () => void) {\n    this._renderer = renderer;\n  }\n\n  public forceVariation(key: string, variation: number) {\n    this._ctx.forcedVariations = this._ctx.forcedVariations || {};\n    this._ctx.forcedVariations[key] = variation;\n    if (this._ctx.remoteEval) {\n      this._refreshForRemoteEval();\n      return;\n    }\n    this._updateAllAutoExperiments();\n    this._render();\n  }\n\n  public run<T>(experiment: Experiment<T>): Result<T> {\n    const result = this._run(experiment, null);\n    this._fireSubscriptions(experiment, result);\n    return result;\n  }\n\n  public triggerExperiment(key: string) {\n    this._triggeredExpKeys.add(key);\n    if (!this._ctx.experiments) return null;\n    const experiments = this._ctx.experiments.filter((exp) => exp.key === key);\n    return experiments\n      .map((exp) => {\n        if (!exp.manual) return null;\n        return this._runAutoExperiment(exp);\n      })\n      .filter((res) => res !== null);\n  }\n\n  private _runAutoExperiment(experiment: AutoExperiment, forceRerun?: boolean) {\n    const existing = this._activeAutoExperiments.get(experiment);\n\n    // If this is a manual experiment and it's not already running, skip\n    if (\n      experiment.manual &&\n      !this._triggeredExpKeys.has(experiment.key) &&\n      !existing\n    )\n      return null;\n\n    // Run the experiment\n    const result = this.run(experiment);\n\n    // A hash to quickly tell if the assigned value changed\n    const valueHash = JSON.stringify(result.value);\n\n    // If the changes are already active, no need to re-apply them\n    if (\n      !forceRerun &&\n      result.inExperiment &&\n      existing &&\n      existing.valueHash === valueHash\n    ) {\n      return result;\n    }\n\n    // Undo any existing changes\n    if (existing) this._undoActiveAutoExperiment(experiment);\n\n    // Apply new changes\n    if (result.inExperiment) {\n      const undo = this._applyDOMChanges(result.value);\n      if (undo) {\n        this._activeAutoExperiments.set(experiment, {\n          undo,\n          valueHash,\n        });\n      }\n    }\n\n    return result;\n  }\n\n  private _undoActiveAutoExperiment(exp: AutoExperiment) {\n    const data = this._activeAutoExperiments.get(exp);\n    if (data) {\n      data.undo();\n      this._activeAutoExperiments.delete(exp);\n    }\n  }\n\n  private _updateAllAutoExperiments(forceRerun?: boolean) {\n    const experiments = this._ctx.experiments || [];\n\n    // Stop any experiments that are no longer defined\n    const keys = new Set(experiments);\n    this._activeAutoExperiments.forEach((v, k) => {\n      if (!keys.has(k)) {\n        v.undo();\n        this._activeAutoExperiments.delete(k);\n      }\n    });\n\n    // Re-run all new/updated experiments\n    experiments.forEach((exp) => {\n      this._runAutoExperiment(exp, forceRerun);\n    });\n  }\n\n  private _fireSubscriptions<T>(experiment: Experiment<T>, result: Result<T>) {\n    const key = experiment.key;\n\n    // If assigned variation has changed, fire subscriptions\n    const prev = this._assigned.get(key);\n    // TODO: what if the experiment definition has changed?\n    if (\n      !prev ||\n      prev.result.inExperiment !== result.inExperiment ||\n      prev.result.variationId !== result.variationId\n    ) {\n      this._assigned.set(key, { experiment, result });\n      this._subscriptions.forEach((cb) => {\n        try {\n          cb(experiment, result);\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }\n\n  private _trackFeatureUsage(key: string, res: FeatureResult): void {\n    // Don't track feature usage that was forced via an override\n    if (res.source === \"override\") return;\n\n    // Only track a feature once, unless the assigned value changed\n    const stringifiedValue = JSON.stringify(res.value);\n    if (this._trackedFeatures[key] === stringifiedValue) return;\n    this._trackedFeatures[key] = stringifiedValue;\n\n    // Fire user-supplied callback\n    if (this._ctx.onFeatureUsage) {\n      try {\n        this._ctx.onFeatureUsage(key, res);\n      } catch (e) {\n        // Ignore feature usage callback errors\n      }\n    }\n\n    // In browser environments, queue up feature usage to be tracked in batches\n    if (!isBrowser || !window.fetch) return;\n    this._rtQueue.push({\n      key,\n      on: res.on,\n    });\n    if (!this._rtTimer) {\n      this._rtTimer = window.setTimeout(() => {\n        // Reset the queue\n        this._rtTimer = 0;\n        const q = [...this._rtQueue];\n        this._rtQueue = [];\n\n        // Skip logging if a real-time usage key is not configured\n        if (!this._ctx.realtimeKey) return;\n\n        window\n          .fetch(\n            `https://rt.growthbook.io/?key=${\n              this._ctx.realtimeKey\n            }&events=${encodeURIComponent(JSON.stringify(q))}`,\n\n            {\n              cache: \"no-cache\",\n              mode: \"no-cors\",\n            }\n          )\n          .catch(() => {\n            // TODO: retry in case of network errors?\n          });\n      }, this._ctx.realtimeInterval || 2000);\n    }\n  }\n\n  private _getFeatureResult<T>(\n    key: string,\n    value: T,\n    source: FeatureResultSource,\n    ruleId?: string,\n    experiment?: Experiment<T>,\n    result?: Result<T>\n  ): FeatureResult<T> {\n    const ret: FeatureResult = {\n      value,\n      on: !!value,\n      off: !value,\n      source,\n      ruleId: ruleId || \"\",\n    };\n    if (experiment) ret.experiment = experiment;\n    if (result) ret.experimentResult = result;\n\n    // Track the usage of this feature in real-time\n    this._trackFeatureUsage(key, ret);\n\n    return ret;\n  }\n\n  public isOn<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).on;\n  }\n\n  public isOff<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).off;\n  }\n\n  public getFeatureValue<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(key: K, defaultValue: V): WidenPrimitives<V> {\n    const value = this.evalFeature<WidenPrimitives<V>, K>(key).value;\n    return value === null ? (defaultValue as WidenPrimitives<V>) : value;\n  }\n\n  /**\n   * @deprecated Use {@link evalFeature}\n   * @param id\n   */\n  // eslint-disable-next-line\n  public feature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    return this.evalFeature(id);\n  }\n\n  public evalFeature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    // Global override\n    if (this._forcedFeatureValues.has(id)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Global override\", {\n          id,\n          value: this._forcedFeatureValues.get(id),\n        });\n      return this._getFeatureResult(\n        id,\n        this._forcedFeatureValues.get(id),\n        \"override\"\n      );\n    }\n\n    // Unknown feature id\n    if (!this._ctx.features || !this._ctx.features[id]) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Unknown feature\", { id });\n      return this._getFeatureResult(id, null, \"unknownFeature\");\n    }\n\n    // Get the feature\n    const feature: FeatureDefinition<V> = this._ctx.features[id];\n\n    // Loop through the rules\n    if (feature.rules) {\n      for (const rule of feature.rules) {\n        // If there are filters for who is included (e.g. namespaces)\n        if (rule.filters && this._isFilteredOut(rule.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip rule because of filters\", {\n              id,\n              rule,\n            });\n          continue;\n        }\n\n        // Feature value is being forced\n        if (\"force\" in rule) {\n          // If it's a conditional rule, skip if the condition doesn't pass\n          if (rule.condition && !this._conditionPasses(rule.condition)) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because of condition ff\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          // If this is a percentage rollout, skip if not included\n          if (\n            !this._isIncludedInRollout(\n              rule.seed || id,\n              rule.hashAttribute,\n              this._ctx.stickyBucketService && !rule.disableStickyBucketing\n                ? rule.fallbackAttribute\n                : undefined,\n              rule.range,\n              rule.coverage,\n              rule.hashVersion\n            )\n          ) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because user not included in rollout\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Force value from rule\", {\n              id,\n              rule,\n            });\n\n          // If this was a remotely evaluated experiment, fire the tracking callbacks\n          if (rule.tracks) {\n            rule.tracks.forEach((t) => {\n              this._track(t.experiment, t.result);\n            });\n          }\n\n          return this._getFeatureResult(id, rule.force as V, \"force\", rule.id);\n        }\n        if (!rule.variations) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip invalid rule\", {\n              id,\n              rule,\n            });\n\n          continue;\n        }\n\n        // For experiment rules, run an experiment\n        const exp: Experiment<V> = {\n          variations: rule.variations as [V, V, ...V[]],\n          key: rule.key || id,\n        };\n        if (\"coverage\" in rule) exp.coverage = rule.coverage;\n        if (rule.weights) exp.weights = rule.weights;\n        if (rule.hashAttribute) exp.hashAttribute = rule.hashAttribute;\n        if (rule.fallbackAttribute)\n          exp.fallbackAttribute = rule.fallbackAttribute;\n        if (rule.disableStickyBucketing)\n          exp.disableStickyBucketing = rule.disableStickyBucketing;\n        if (rule.bucketVersion !== undefined)\n          exp.bucketVersion = rule.bucketVersion;\n        if (rule.minBucketVersion !== undefined)\n          exp.minBucketVersion = rule.minBucketVersion;\n        if (rule.namespace) exp.namespace = rule.namespace;\n        if (rule.meta) exp.meta = rule.meta;\n        if (rule.ranges) exp.ranges = rule.ranges;\n        if (rule.name) exp.name = rule.name;\n        if (rule.phase) exp.phase = rule.phase;\n        if (rule.seed) exp.seed = rule.seed;\n        if (rule.hashVersion) exp.hashVersion = rule.hashVersion;\n        if (rule.filters) exp.filters = rule.filters;\n        if (rule.condition) exp.condition = rule.condition;\n\n        // Only return a value if the user is part of the experiment\n        const res = this._run(exp, id);\n        this._fireSubscriptions(exp, res);\n        if (res.inExperiment && !res.passthrough) {\n          return this._getFeatureResult(\n            id,\n            res.value,\n            \"experiment\",\n            rule.id,\n            exp,\n            res\n          );\n        }\n      }\n    }\n\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"Use default value\", {\n        id,\n        value: feature.defaultValue,\n      });\n\n    // Fall back to using the default value\n    return this._getFeatureResult(\n      id,\n      feature.defaultValue === undefined ? null : feature.defaultValue,\n      \"defaultValue\"\n    );\n  }\n\n  private _isIncludedInRollout(\n    seed: string,\n    hashAttribute: string | undefined,\n    fallbackAttribute: string | undefined,\n    range: VariationRange | undefined,\n    coverage: number | undefined,\n    hashVersion: number | undefined\n  ): boolean {\n    if (!range && coverage === undefined) return true;\n\n    const { hashValue } = this._getHashAttribute(\n      hashAttribute,\n      fallbackAttribute\n    );\n    if (!hashValue) {\n      return false;\n    }\n\n    const n = hash(seed, hashValue, hashVersion || 1);\n    if (n === null) return false;\n\n    return range\n      ? inRange(n, range)\n      : coverage !== undefined\n      ? n <= coverage\n      : true;\n  }\n\n  private _conditionPasses(condition: ConditionInterface): boolean {\n    return evalCondition(this.getAttributes(), condition);\n  }\n\n  private _isFilteredOut(filters: Filter[]): boolean {\n    return filters.some((filter) => {\n      const { hashValue } = this._getHashAttribute(filter.attribute);\n      if (!hashValue) return true;\n      const n = hash(filter.seed, hashValue, filter.hashVersion || 2);\n      if (n === null) return true;\n      return !filter.ranges.some((r) => inRange(n, r));\n    });\n  }\n\n  private _run<T>(\n    experiment: Experiment<T>,\n    featureId: string | null\n  ): Result<T> {\n    const key = experiment.key;\n    const numVariations = experiment.variations.length;\n\n    // 1. If experiment has less than 2 variations, return immediately\n    if (numVariations < 2) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Invalid experiment\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2. If the context is disabled, return immediately\n    if (this._ctx.enabled === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Context disabled\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2.5. Merge in experiment overrides from the context\n    experiment = this._mergeOverrides(experiment);\n\n    // 2.6 New, more powerful URL targeting\n    if (\n      experiment.urlPatterns &&\n      !isURLTargeted(this._getContextUrl(), experiment.urlPatterns)\n    ) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url targeting\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 3. If a variation is forced from a querystring, return the forced variation\n    const qsOverride = getQueryStringOverride(\n      key,\n      this._getContextUrl(),\n      numVariations\n    );\n    if (qsOverride !== null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via querystring\", {\n          id: key,\n          variation: qsOverride,\n        });\n      return this._getResult(experiment, qsOverride, false, featureId);\n    }\n\n    // 4. If a variation is forced in the context, return the forced variation\n    if (this._ctx.forcedVariations && key in this._ctx.forcedVariations) {\n      const variation = this._ctx.forcedVariations[key];\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via dev tools\", {\n          id: key,\n          variation,\n        });\n      return this._getResult(experiment, variation, false, featureId);\n    }\n\n    // 5. Exclude if a draft experiment or not active\n    if (experiment.status === \"draft\" || experiment.active === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because inactive\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 6. Get the hash attribute and return if empty\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n    if (!hashValue) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because missing hashAttribute\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    let assigned = -1;\n\n    let foundStickyBucket = false;\n    let stickyBucketVersionIsBlocked = false;\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const { variation, versionIsBlocked } = this._getStickyBucketVariation(\n        experiment.key,\n        experiment.bucketVersion,\n        experiment.minBucketVersion,\n        experiment.meta\n      );\n      foundStickyBucket = variation >= 0;\n      assigned = variation;\n      stickyBucketVersionIsBlocked = !!versionIsBlocked;\n    }\n\n    // Some checks are not needed if we already have a sticky bucket\n    if (!foundStickyBucket) {\n      // 7. Exclude if user is filtered out (used to be called \"namespace\")\n      if (experiment.filters) {\n        if (this._isFilteredOut(experiment.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip because of filters\", {\n              id: key,\n            });\n          return this._getResult(experiment, -1, false, featureId);\n        }\n      } else if (\n        experiment.namespace &&\n        !inNamespace(hashValue, experiment.namespace)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of namespace\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 7.5. Exclude if experiment.include returns false or throws\n      if (experiment.include && !isIncluded(experiment.include)) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of include function\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8. Exclude if condition is false\n      if (\n        experiment.condition &&\n        !this._conditionPasses(experiment.condition)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of condition exp\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8.1. Exclude if user is not in a required group\n      if (\n        experiment.groups &&\n        !this._hasGroupOverlap(experiment.groups as string[])\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of groups\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n    }\n\n    // 8.2. Old style URL targeting\n    if (experiment.url && !this._urlIsValid(experiment.url as RegExp)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 9. Get the variation from the sticky bucket or get bucket ranges and choose variation\n    const n = hash(\n      experiment.seed || key,\n      hashValue,\n      experiment.hashVersion || 1\n    );\n    if (n === null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of invalid hash version\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    if (!foundStickyBucket) {\n      const ranges =\n        experiment.ranges ||\n        getBucketRanges(\n          numVariations,\n          experiment.coverage === undefined ? 1 : experiment.coverage,\n          experiment.weights\n        );\n      assigned = chooseVariation(n, ranges);\n    }\n\n    // 9.5 Unenroll if any prior sticky buckets are blocked by version\n    if (stickyBucketVersionIsBlocked) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because sticky bucket version is blocked\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId, undefined, true);\n    }\n\n    // 10. Return if not in experiment\n    if (assigned < 0) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of coverage\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 11. Experiment has a forced variation\n    if (\"force\" in experiment) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force variation\", {\n          id: key,\n          variation: experiment.force,\n        });\n      return this._getResult(\n        experiment,\n        experiment.force === undefined ? -1 : experiment.force,\n        false,\n        featureId\n      );\n    }\n\n    // 12. Exclude if in QA mode\n    if (this._ctx.qaMode) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because QA mode\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 12.5. Exclude if experiment is stopped\n    if (experiment.status === \"stopped\") {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because stopped\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 13. Build the result object\n    const result = this._getResult(\n      experiment,\n      assigned,\n      true,\n      featureId,\n      n,\n      foundStickyBucket\n    );\n\n    // 13.5. Persist sticky bucket\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const {\n        changed,\n        key: attrKey,\n        doc,\n      } = this._generateStickyBucketAssignmentDoc(\n        hashAttribute,\n        toString(hashValue),\n        {\n          [this._getStickyBucketExperimentKey(\n            experiment.key,\n            experiment.bucketVersion\n          )]: result.key,\n        }\n      );\n      if (changed) {\n        // update local docs\n        this._ctx.stickyBucketAssignmentDocs =\n          this._ctx.stickyBucketAssignmentDocs || {};\n        this._ctx.stickyBucketAssignmentDocs[attrKey] = doc;\n        // save doc\n        this._ctx.stickyBucketService.saveAssignments(doc);\n      }\n    }\n\n    // 14. Fire the tracking callback\n    this._track(experiment, result);\n\n    // 15. Return the result\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"In experiment\", {\n        id: key,\n        variation: result.variationId,\n      });\n    return result;\n  }\n\n  log(msg: string, ctx: Record<string, unknown>) {\n    if (!this.debug) return;\n    if (this._ctx.log) this._ctx.log(msg, ctx);\n    else console.log(msg, ctx);\n  }\n\n  private _track<T>(experiment: Experiment<T>, result: Result<T>) {\n    if (!this._ctx.trackingCallback) return;\n\n    const key = experiment.key;\n\n    // Make sure a tracking callback is only fired once per unique experiment\n    const k =\n      result.hashAttribute + result.hashValue + key + result.variationId;\n    if (this._trackedExperiments.has(k)) return;\n    this._trackedExperiments.add(k);\n\n    try {\n      this._ctx.trackingCallback(experiment, result);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  private _mergeOverrides<T>(experiment: Experiment<T>): Experiment<T> {\n    const key = experiment.key;\n    const o = this._ctx.overrides;\n    if (o && o[key]) {\n      experiment = Object.assign({}, experiment, o[key]);\n      if (typeof experiment.url === \"string\") {\n        experiment.url = getUrlRegExp(\n          // eslint-disable-next-line\n          experiment.url as any\n        );\n      }\n    }\n\n    return experiment;\n  }\n\n  private _getHashAttribute(attr?: string, fallback?: string) {\n    let hashAttribute = attr || \"id\";\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let hashValue: any = \"\";\n\n    if (this._attributeOverrides[hashAttribute]) {\n      hashValue = this._attributeOverrides[hashAttribute];\n    } else if (this._ctx.attributes) {\n      hashValue = this._ctx.attributes[hashAttribute] || \"\";\n    } else if (this._ctx.user) {\n      hashValue = this._ctx.user[hashAttribute] || \"\";\n    }\n\n    // if no match, try fallback\n    if (!hashValue && fallback) {\n      if (this._attributeOverrides[fallback]) {\n        hashValue = this._attributeOverrides[fallback];\n      } else if (this._ctx.attributes) {\n        hashValue = this._ctx.attributes[fallback] || \"\";\n      } else if (this._ctx.user) {\n        hashValue = this._ctx.user[fallback] || \"\";\n      }\n      if (hashValue) {\n        hashAttribute = fallback;\n      }\n    }\n\n    return { hashAttribute, hashValue };\n  }\n\n  private _getResult<T>(\n    experiment: Experiment<T>,\n    variationIndex: number,\n    hashUsed: boolean,\n    featureId: string | null,\n    bucket?: number,\n    stickyBucketUsed?: boolean\n  ): Result<T> {\n    let inExperiment = true;\n    // If assigned variation is not valid, use the baseline and mark the user as not in the experiment\n    if (variationIndex < 0 || variationIndex >= experiment.variations.length) {\n      variationIndex = 0;\n      inExperiment = false;\n    }\n\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n\n    const meta: Partial<VariationMeta> = experiment.meta\n      ? experiment.meta[variationIndex]\n      : {};\n\n    const res: Result<T> = {\n      key: meta.key || \"\" + variationIndex,\n      featureId,\n      inExperiment,\n      hashUsed,\n      variationId: variationIndex,\n      value: experiment.variations[variationIndex],\n      hashAttribute,\n      hashValue,\n      stickyBucketUsed: !!stickyBucketUsed,\n    };\n\n    if (meta.name) res.name = meta.name;\n    if (bucket !== undefined) res.bucket = bucket;\n    if (meta.passthrough) res.passthrough = meta.passthrough;\n\n    return res;\n  }\n\n  private _getContextUrl() {\n    return this._ctx.url || (isBrowser ? window.location.href : \"\");\n  }\n\n  private _urlIsValid(urlRegex: RegExp): boolean {\n    const url = this._getContextUrl();\n    if (!url) return false;\n\n    const pathOnly = url.replace(/^https?:\\/\\//, \"\").replace(/^[^/]*\\//, \"/\");\n\n    if (urlRegex.test(url)) return true;\n    if (urlRegex.test(pathOnly)) return true;\n    return false;\n  }\n\n  private _hasGroupOverlap(expGroups: string[]): boolean {\n    const groups = this._ctx.groups || {};\n    for (let i = 0; i < expGroups.length; i++) {\n      if (groups[expGroups[i]]) return true;\n    }\n    return false;\n  }\n\n  private _applyDOMChanges(changes: AutoExperimentVariation) {\n    if (!isBrowser) return;\n    const undo: (() => void)[] = [];\n    if (changes.css) {\n      const s = document.createElement(\"style\");\n      s.innerHTML = changes.css;\n      document.head.appendChild(s);\n      undo.push(() => s.remove());\n    }\n    if (changes.js) {\n      const script = document.createElement(\"script\");\n      script.innerHTML = changes.js;\n      document.head.appendChild(script);\n      undo.push(() => script.remove());\n    }\n    if (changes.domMutations) {\n      changes.domMutations.forEach((mutation) => {\n        undo.push(mutate.declarative(mutation as DeclarativeMutation).revert);\n      });\n    }\n    return () => {\n      undo.forEach((fn) => fn());\n    };\n  }\n\n  private _deriveStickyBucketIdentifierAttributes(data?: FeatureApiResponse) {\n    const attributes = new Set<string>();\n    const features = data && data.features ? data.features : this.getFeatures();\n    const experiments =\n      data && data.experiments ? data.experiments : this.getExperiments();\n    Object.keys(features).forEach((id) => {\n      const feature = features[id];\n      if (feature.rules) {\n        for (const rule of feature.rules) {\n          if (rule.variations) {\n            attributes.add(rule.hashAttribute || \"id\");\n            if (rule.fallbackAttribute) {\n              attributes.add(rule.fallbackAttribute);\n            }\n          }\n        }\n      }\n    });\n    experiments.map((experiment) => {\n      attributes.add(experiment.hashAttribute || \"id\");\n      if (experiment.fallbackAttribute) {\n        attributes.add(experiment.fallbackAttribute);\n      }\n    });\n    return Array.from(attributes);\n  }\n\n  public async refreshStickyBuckets(data?: FeatureApiResponse) {\n    if (this._ctx.stickyBucketService) {\n      const attributes = this._getStickyBucketAttributes(data);\n      this._ctx.stickyBucketAssignmentDocs = await this._ctx.stickyBucketService.getAllAssignments(\n        attributes\n      );\n    }\n  }\n\n  private _getStickyBucketAssignments(): StickyAssignments {\n    const mergedAssignments: StickyAssignments = {};\n    Object.values(this._ctx.stickyBucketAssignmentDocs || {}).forEach((doc) => {\n      if (doc.assignments) Object.assign(mergedAssignments, doc.assignments);\n    });\n    return mergedAssignments;\n  }\n\n  private _getStickyBucketVariation(\n    experimentKey: string,\n    experimentBucketVersion?: number,\n    minExperimentBucketVersion?: number,\n    meta?: VariationMeta[]\n  ): {\n    variation: number;\n    versionIsBlocked?: boolean;\n  } {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    minExperimentBucketVersion = minExperimentBucketVersion || 0;\n    meta = meta || [];\n    const id = this._getStickyBucketExperimentKey(\n      experimentKey,\n      experimentBucketVersion\n    );\n    const assignments = this._getStickyBucketAssignments();\n\n    // users with any blocked bucket version (0 to minExperimentBucketVersion) are excluded from the test\n    if (minExperimentBucketVersion > 0) {\n      for (let i = 0; i <= minExperimentBucketVersion; i++) {\n        const blockedKey = this._getStickyBucketExperimentKey(experimentKey, i);\n        if (assignments[blockedKey] !== undefined) {\n          return {\n            variation: -1,\n            versionIsBlocked: true,\n          };\n        }\n      }\n    }\n    const variationKey = assignments[id];\n    if (variationKey === undefined)\n      // no assignment found\n      return { variation: -1 };\n    const variation = meta.findIndex((m) => m.key === variationKey);\n    if (variation < 0)\n      // invalid assignment, treat as \"no assignment found\"\n      return { variation: -1 };\n\n    return { variation };\n  }\n\n  private _getStickyBucketExperimentKey(\n    experimentKey: string,\n    experimentBucketVersion?: number\n  ): StickyExperimentKey {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    return `${experimentKey}__${experimentBucketVersion}`;\n  }\n\n  private _getStickyBucketAttributes(\n    data?: FeatureApiResponse\n  ): Record<string, string> {\n    const attributes: Record<string, string> = {};\n    this._ctx.stickyBucketIdentifierAttributes = !this._ctx\n      .stickyBucketIdentifierAttributes\n      ? this._deriveStickyBucketIdentifierAttributes(data)\n      : this._ctx.stickyBucketIdentifierAttributes;\n    this._ctx.stickyBucketIdentifierAttributes.forEach((attr) => {\n      const { hashValue } = this._getHashAttribute(attr);\n      attributes[attr] = toString(hashValue);\n    });\n    return attributes;\n  }\n\n  private _generateStickyBucketAssignmentDoc(\n    attributeName: string,\n    attributeValue: string,\n    assignments: StickyAssignments\n  ): {\n    key: StickyAttributeKey;\n    doc: StickyAssignmentsDocument;\n    changed: boolean;\n  } {\n    const key = `${attributeName}||${attributeValue}`;\n    const existingAssignments =\n      this._ctx.stickyBucketAssignmentDocs &&\n      this._ctx.stickyBucketAssignmentDocs[key]\n        ? this._ctx.stickyBucketAssignmentDocs[key].assignments || {}\n        : {};\n    const newAssignments = { ...existingAssignments, ...assignments };\n    const changed =\n      JSON.stringify(existingAssignments) !== JSON.stringify(newAssignments);\n\n    return {\n      key,\n      doc: {\n        attributeName,\n        attributeValue,\n        assignments: newAssignments,\n      },\n      changed,\n    };\n  }\n}\n", "import {\n  LocalStorageCompat,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n} from \"./types/growthbook\";\n\nexport interface CookieAttributes {\n  expires?: number | Date | undefined;\n  path?: string | undefined;\n  domain?: string | undefined;\n  secure?: boolean | undefined;\n  sameSite?: \"strict\" | \"Strict\" | \"lax\" | \"Lax\" | \"none\" | \"None\" | undefined;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [property: string]: any;\n}\nexport interface JsCookiesCompat<T = string> {\n  set(\n    name: string,\n    value: string | T,\n    options?: CookieAttributes\n  ): string | undefined;\n  get(name: string): string | T | undefined;\n  get(): { [key: string]: string };\n  remove(name: string, options?: CookieAttributes): void;\n}\n\nexport interface IORedisCompat {\n  mget(...keys: string[]): Promise<string[]>;\n  set(key: string, value: string): Promise<string>;\n}\n\nexport interface RequestCompat {\n  cookies: Record<string, string>;\n  [key: string]: unknown;\n}\nexport interface ResponseCompat {\n  cookie(\n    name: string,\n    value: string,\n    options?: CookieAttributes\n  ): ResponseCompat;\n  [key: string]: unknown;\n}\n\n/**\n * Responsible for reading and writing documents which describe sticky bucket assignments.\n */\nexport abstract class StickyBucketService {\n  abstract getAssignments(\n    attributeName: string,\n    attributeValue: string\n  ): Promise<StickyAssignmentsDocument | null>;\n\n  abstract saveAssignments(doc: StickyAssignmentsDocument): Promise<unknown>;\n\n  /**\n   * The SDK calls getAllAssignments to populate sticky buckets. This in turn will\n   * typically loop through individual getAssignments calls. However, some StickyBucketService\n   * instances (i.e. Redis) will instead perform a multi-query inside getAllAssignments instead.\n   */\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<string, StickyAssignmentsDocument> = {};\n    (\n      await Promise.all(\n        Object.entries(attributes).map(([attributeName, attributeValue]) =>\n          this.getAssignments(attributeName, attributeValue)\n        )\n      )\n    ).forEach((doc) => {\n      if (doc) {\n        const key = `${doc.attributeName}||${doc.attributeValue}`;\n        docs[key] = doc;\n      }\n    });\n    return docs;\n  }\n}\n\nexport class LocalStorageStickyBucketService extends StickyBucketService {\n  private prefix: string;\n  private localStorage: LocalStorageCompat | undefined;\n  constructor(opts?: { prefix?: string; localStorage?: LocalStorageCompat }) {\n    opts = opts || {};\n    super();\n    this.prefix = opts.prefix || \"gbStickyBuckets__\";\n    try {\n      this.localStorage = opts.localStorage || globalThis.localStorage;\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.localStorage) return doc;\n    try {\n      const raw = (await this.localStorage.getItem(this.prefix + key)) || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.localStorage) return;\n    try {\n      await this.localStorage.setItem(this.prefix + key, JSON.stringify(doc));\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n}\n\nexport class ExpressCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with cookieParser() middleware from npm: 'cookie-parser'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value must be manually encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private req: RequestCompat;\n  private res: ResponseCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    req,\n    res,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    req: RequestCompat;\n    res: ResponseCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.req = req;\n    this.res = res;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.req) return doc;\n    try {\n      const raw = this.req.cookies[this.prefix + key] || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.res) return;\n    const str = JSON.stringify(doc);\n    this.res.cookie(\n      encodeURIComponent(this.prefix + key),\n      encodeURIComponent(str),\n      this.cookieAttributes\n    );\n  }\n}\n\nexport class BrowserCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with npm: 'js-cookie'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value is automatically encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private jsCookie: JsCookiesCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    jsCookie,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    jsCookie: JsCookiesCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.jsCookie = jsCookie;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.jsCookie) return doc;\n    try {\n      const raw = this.jsCookie.get(this.prefix + key);\n      const data = JSON.parse(raw || \"{}\");\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.jsCookie) return;\n    const str = JSON.stringify(doc);\n    this.jsCookie.set(this.prefix + key, str, this.cookieAttributes);\n  }\n}\n\nexport class RedisStickyBucketService extends StickyBucketService {\n  /** Intended to be used with npm: 'ioredis'. **/\n  private redis: IORedisCompat | undefined;\n  constructor({ redis }: { redis: IORedisCompat }) {\n    super();\n    this.redis = redis;\n  }\n\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<StickyAttributeKey, StickyAssignmentsDocument> = {};\n    const keys = Object.entries(attributes).map(\n      ([attributeName, attributeValue]) => `${attributeName}||${attributeValue}`\n    );\n    if (!this.redis) return docs;\n    this.redis.mget(...keys).then((values) => {\n      values.forEach((raw) => {\n        try {\n          const data = JSON.parse(raw || \"{}\");\n          if (data.attributeName && data.attributeValue && data.assignments) {\n            const key = `${data.attributeName}||${data.attributeValue}`;\n            docs[key] = data;\n          }\n        } catch (e) {\n          // ignore redis doc parse errors\n        }\n      });\n    });\n    return docs;\n  }\n\n  async getAssignments(_attributeName: string, _attributeValue: string) {\n    // not implemented\n    return null;\n  }\n\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.redis) return;\n    await this.redis.set(key, JSON.stringify(doc));\n  }\n}\n"], "names": ["cacheSettings", "staleTTL", "maxAge", "cache<PERSON>ey", "backgroundSync", "maxEntries", "disableIdleStreams", "idleStreamInterval", "polyfills", "fetch", "globalThis", "bind", "undefined", "SubtleCrypto", "crypto", "subtle", "EventSource", "helpers", "fetchFeaturesCall", "_ref", "host", "client<PERSON>ey", "headers", "concat", "fetchRemoteEvalCall", "_ref2", "payload", "options", "method", "body", "JSON", "stringify", "eventSourceCall", "_ref3", "startIdleListener", "idleTimeout", "window", "document", "onVisibilityChange", "visibilityState", "clearTimeout", "onVisible", "setTimeout", "onHidden", "addEventListener", "removeEventListener", "stopIdleListener", "localStorage", "e", "subscribedInstances", "Map", "cacheInitialized", "cache", "activeFetches", "streams", "supportsSSE", "Set", "for<PERSON>ach", "channel", "state", "disableChannel", "enableChannel", "async", "updatePersistentCache", "setItem", "Array", "from", "entries", "<PERSON><PERSON><PERSON>", "instance", "apiHost", "getApiInfo", "get<PERSON><PERSON><PERSON><PERSON>", "baseKey", "isRemoteEval", "attributes", "getAttributes", "cacheKeyAttributes", "getCacheKeyAttributes", "Object", "keys", "ca", "key", "fv", "getForcedVariations", "url", "getUrl", "cleanupCache", "entriesWithTimestamps", "map", "_ref5", "value", "staleAt", "getTime", "sort", "a", "b", "entriesToRemoveCount", "Math", "min", "max", "size", "i", "delete", "onNewFeatureData", "data", "version", "dateUpdated", "Date", "now", "existing", "get", "set", "sse", "has", "instances", "refreshInstance", "decryptPayload", "refreshStickyBuckets", "setExperiments", "experiments", "getExperiments", "setFeatures", "features", "getFeatures", "fetchFeatures", "apiRequestHeaders", "getApiHosts", "getClientKey", "remoteEval", "promise", "forcedVariations", "forcedFeatures", "getForcedFeatures", "then", "res", "add", "json", "startAutoRefresh", "catch", "Promise", "resolve", "streamingHost", "streamingHostRequestHeaders", "src", "cb", "event", "type", "parse", "errors", "onSSEError", "readyState", "delay", "pow", "random", "includes", "onopen", "onerror", "close", "destroyChannel", "clearAutoRefresh", "clear", "validAttributeName", "nullController", "revert", "elements", "mutations", "getElementRecord", "element", "record", "createElementPropertyRecord", "el", "attr", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "parentNode", "insertBeforeNode", "observe", "childList", "subtree", "characterData", "attributeFilter", "getObserverInit", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "m", "mutate", "html", "transformContainer", "createElement", "innerHTML", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "join", "attrMutationRunner", "positionMutationRunner", "newNodes", "parentSelector", "insertBeforeSelector", "querySelector", "_loadDOMNodes", "getHTMLValue", "setHTMLValue", "getElementHTMLRecord", "elementRecord", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getElementAttributeRecord", "attrName", "_el$getAttribute", "getAttribute", "setAttribute", "setAttrValue", "setPropertyV<PERSON>ue", "length", "_element$html", "_element$html$observe", "disconnect", "_element$classes", "_element$classes$obse", "_element$position", "_element$position$obs", "_element$attributes", "_element$attributes$a", "_element$attributes$a2", "deleteElementPropertyRecord", "refreshElementsSet", "mutation", "kind", "existingElements", "querySelectorAll", "selector", "attribute", "push", "startMutating", "refreshAllElementSets", "newMutation", "index", "indexOf", "splice", "stopMutating", "test", "classnames", "mutatedClassnames", "c", "hashFnv32a", "str", "hval", "l", "charCodeAt", "hash", "seed", "inRange", "n", "range", "getUrlRegExp", "regexString", "escaped", "replace", "RegExp", "console", "error", "isURLTargeted", "targets", "hasIncludeRules", "isIncluded", "match", "_evalURLTarget", "pattern", "include", "parsed", "URL", "regex", "href", "substring", "origin", "actual", "expected", "comps", "pathname", "searchParams", "v", "k", "some", "isPath", "_evalSimpleUrlPart", "_evalSimpleUrlTarget", "documentElement", "base64ToBuf", "Uint8Array", "atob", "decrypt", "encryptedString", "decryptionKey", "Error", "importKey", "name", "iv", "cipherText", "plainTextBuffer", "TextDecoder", "decode", "toString", "input", "paddedVersionString", "parts", "padStart", "_regexCache", "evalCondition", "obj", "condition", "evalOr", "conditions", "evalAnd", "evalConditionValue", "<PERSON><PERSON><PERSON>", "path", "current", "isArray", "isOperatorObject", "op", "evalOperatorCondition", "isIn", "operator", "check", "elemMatch", "passed", "j", "t", "getType", "<PERSON><PERSON><PERSON><PERSON>", "SDK_VERSION", "loadSDKVersion", "StickyBucketService", "docs", "all", "attributeName", "attributeValue", "this", "getAssignments", "doc", "constructor", "prefix", "js<PERSON><PERSON><PERSON>", "cookieAttributes", "super", "raw", "assignments", "req", "cookies", "cookie", "encodeURIComponent", "context", "_ctx", "_renderer", "_trackedExperiments", "_trackedFeatures", "debug", "_subscriptions", "_rtQueue", "_rtTimer", "ready", "_assigned", "_forcedFeatureV<PERSON>ues", "_attributeOverrides", "_activeAutoExperiments", "_triggeredExpKeys", "_loadFeaturesCalled", "isGbHost", "hostname", "enableDevMode", "_growthbook", "dispatchEvent", "Event", "_updateAllAutoExperiments", "_refresh", "autoRefresh", "subscribeToChanges", "_canSubscribe", "subs", "subscribe", "defaultHost", "apiHostRequestHeaders", "allowStale", "updateInstance", "timeout", "<PERSON><PERSON><PERSON>", "minStaleAt", "getItem", "_ref4", "cleanupFn", "initializeCache", "timer", "resolved", "finish", "promiseTimeout", "fetchFeaturesWithCache", "refreshFeatures", "_render", "featuresJSON", "experimentsJSON", "encryptedFeatures", "encryptedExperiments", "stickyBucketService", "_refreshForRemoteEval", "overrides", "vars", "setForcedFeatures", "getStickyBucketAssignmentDocs", "stickyBucketAssignmentDocs", "getAllResults", "destroy", "s", "exp", "undo", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "forceVariation", "variation", "run", "experiment", "result", "_run", "_fireSubscriptions", "triggerExperiment", "manual", "_runAutoExperiment", "forceRerun", "valueHash", "inExperiment", "_undoActiveAutoExperiment", "_applyDOMChanges", "prev", "variationId", "_trackFeatureUsage", "source", "stringifiedValue", "onFeatureUsage", "on", "q", "realtimeKey", "mode", "realtimeInterval", "_getFeatureResult", "ruleId", "ret", "off", "experimentResult", "isOn", "evalFeature", "isOff", "getFeatureValue", "defaultValue", "feature", "id", "rules", "rule", "filters", "_isFilteredOut", "_conditionPasses", "_isIncludedInRollout", "hashAttribute", "disableStickyBucketing", "fallbackAttribute", "coverage", "hashVersion", "tracks", "_track", "force", "variations", "weights", "bucketVersion", "minBucketVersion", "namespace", "meta", "ranges", "phase", "passthrough", "hashValue", "_getHashAttribute", "r", "featureId", "numVariations", "_getResult", "enabled", "_mergeOverrides", "urlPatterns", "_getContextUrl", "qsOverride", "search", "kv", "parseInt", "getQueryStringOverride", "status", "active", "assigned", "foundStickyBucket", "stickyBucketVersionIsBlocked", "versionIsBlocked", "_getStickyBucketVariation", "inNamespace", "groups", "_hasGroupOverlap", "_urlIsValid", "chooseVariation", "equal", "fill", "totalWeight", "reduce", "w", "sum", "cumulative", "start", "getBucketRanges", "qaMode", "changed", "attrKey", "_generateStickyBucketAssignmentDoc", "_getStickyBucketExperimentKey", "saveAssignments", "log", "msg", "ctx", "trackingCallback", "o", "assign", "fallback", "user", "variationIndex", "hashUsed", "bucket", "stickyBucketUsed", "location", "urlRegex", "pathOnly", "expGroups", "changes", "css", "head", "append<PERSON><PERSON><PERSON>", "remove", "js", "script", "domMutations", "action", "fn", "_deriveStickyBucketIdentifierAttributes", "_getStickyBucketAttributes", "getAllAssignments", "_getStickyBucketAssignments", "mergedAssignments", "values", "<PERSON><PERSON><PERSON>", "experimentBucketVersion", "minExperimentBucketVersion", "<PERSON><PERSON><PERSON>", "findIndex", "stickyBucketIdentifierAttributes", "existingAssignments", "newAssignments", "opts", "redis", "mget", "_attributeName", "_attributeValue"], "mappings": "wCA0BA,MAAMA,EAA+B,CAEnCC,SAAU,IAEVC,OAAQ,MACRC,SAAU,kBACVC,gBAAgB,EAChBC,WAAY,GACZC,oBAAoB,EACpBC,mBAAoB,KAEhBC,EAAuB,CAC3BC,MAAOC,WAAWD,MAAQC,WAAWD,MAAME,KAAKD,iBAAcE,EAC9DC,aAAcH,WAAWI,OAASJ,WAAWI,OAAOC,YAASH,EAC7DI,YAAaN,WAAWM,aAEbC,EAAmB,CAC9BC,kBAAmBC,IAAkC,IAAjCC,KAAEA,EAAIC,UAAEA,EAASC,QAAEA,GAASH,EAC9C,OAAQX,EAAUC,gBACbW,EAAI,kBAAAG,OAAiBF,GACxB,CAAEC,WACH,EAEHE,oBAAqBC,IAA2C,IAA1CL,KAAEA,EAAIC,UAAEA,EAASK,QAAEA,EAAOJ,QAAEA,GAASG,EACzD,MAAME,EAAU,CACdC,OAAQ,OACRN,QAAS,CAAE,eAAgB,sBAAuBA,GAClDO,KAAMC,KAAKC,UAAUL,IAEvB,OAAQlB,EAAUC,MAAK,GAAAc,OAClBH,EAAiBC,cAAAA,OAAAA,GACpBM,EACD,EAEHK,gBAAiBC,IAAkC,IAAjCb,KAAEA,EAAIC,UAAEA,EAASC,QAAEA,GAASW,EAC5C,OAAIX,EACK,IAAId,EAAUQ,sBAAeI,EAAI,SAAAG,OAAQF,GAAa,CAC3DC,YAGG,IAAId,EAAUQ,sBAAeI,EAAI,SAAAG,OAAQF,GAAY,EAE9Da,kBAAmB,KACjB,IAAIC,EAGJ,GADoB,oBAAXC,QAA8C,oBAAbC,SAC1B,OAChB,MAAMC,EAAqB,KACQ,YAA7BD,SAASE,iBACXH,OAAOI,aAAaL,GACpBM,KACsC,WAA7BJ,SAASE,kBAClBJ,EAAcC,OAAOM,WACnBC,EACA3C,EAAcO,oBAElB,EAGF,OADA8B,SAASO,iBAAiB,mBAAoBN,GACvC,IACLD,SAASQ,oBAAoB,mBAAoBP,EAAmB,EAExEQ,iBAAkB,QAKpB,IACMpC,WAAWqC,eACbvC,EAAUuC,aAAerC,WAAWqC,aAGtC,CADA,MAAOC,GACP,CAIF,MAAMC,EAAoD,IAAIC,IAC9D,IAAIC,GAAmB,EACvB,MAAMC,EAAiC,IAAIF,IACrCG,EAA0D,IAAIH,IAC9DI,EAAsC,IAAIJ,IAC1CK,EAA2B,IAAIC,IAqD9B,SAASb,IACdW,EAAQG,SAASC,IACVA,IACLA,EAAQC,MAAQ,OAChBC,EAAeF,GAAQ,GAE3B,CAEO,SAASjB,IACda,EAAQG,SAASC,IACVA,GACiB,SAAlBA,EAAQC,OACZE,EAAcH,EAAQ,GAE1B,CAIAI,eAAeC,IACb,IACE,IAAKvD,EAAUuC,aAAc,aACvBvC,EAAUuC,aAAaiB,QAC3BhE,EAAcG,SACd2B,KAAKC,UAAUkC,MAAMC,KAAKd,EAAMe,YAGlC,CADA,MAAOnB,GACP,CAEJ,CAyCA,SAASoB,EAAOC,GACd,MAAOC,EAASjD,GAAagD,EAASE,aACtC,MAAUD,GAAAA,OAAAA,eAAYjD,EACxB,CAEA,SAASmD,EAAYH,GACnB,MAAMI,EAAUL,EAAOC,GACvB,IAAKA,EAASK,eAAgB,OAAOD,EAErC,MAAME,EAAaN,EAASO,gBACtBC,EACJR,EAASS,yBAA2BC,OAAOC,KAAKX,EAASO,iBACrDK,EAAiB,CAAA,EACvBJ,EAAmBpB,SAASyB,IAC1BD,EAAGC,GAAOP,EAAWO,EAAI,IAG3B,MAAMC,EAAKd,EAASe,sBACdC,EAAMhB,EAASiB,SAErB,MAAA,GAAA/D,OAAUkD,EAAO,MAAAlD,OAAKO,KAAKC,UAAU,CACnCkD,KACAE,KACAE,QAEJ,CA6DA,SAASE,IACP,MAAMC,EAAwBvB,MAAMC,KAAKd,EAAMe,WAC5CsB,KAAIC,IAAA,IAAER,EAAKS,GAAMD,EAAA,MAAM,CACtBR,MACAU,QAASD,EAAMC,QAAQC,UACxB,IACAC,MAAK,CAACC,EAAGC,IAAMD,EAAEH,QAAUI,EAAEJ,UAE1BK,EAAuBC,KAAKC,IAChCD,KAAKE,IAAI,EAAGhD,EAAMiD,KAAOrG,EAAcK,YACvC+C,EAAMiD,MAGR,IAAK,IAAIC,EAAI,EAAGA,EAAIL,EAAsBK,IACxClD,EAAMmD,OAAOf,EAAsBc,GAAGpB,IAE1C,CAGA,SAASsB,EACPtB,EACA/E,EACAsG,GAGA,MAAMC,EAAUD,EAAKE,aAAe,GAC9Bf,EAAU,IAAIgB,KAAKA,KAAKC,MAAQ7G,EAAcC,UAC9C6G,EAAW1D,EAAM2D,IAAI5G,GAC3B,GAAI2G,GAAYJ,GAAWI,EAASJ,UAAYA,EAG9C,OAFAI,EAASlB,QAAUA,OACnB7B,IAKFX,EAAM4D,IAAI7G,EAAU,CAClBsG,OACAC,UACAd,UACAqB,IAAK1D,EAAY2D,IAAIhC,KAEvBK,IAEAxB,IAGA,MAAMoD,EAAYlE,EAAoB8D,IAAI7B,GAC1CiC,GAAaA,EAAU1D,SAASY,GAAa+C,EAAgB/C,EAAUoC,IACzE,CAEA3C,eAAesD,EACb/C,EACAoC,GAEAA,QAAapC,EAASgD,eAAeZ,OAAM7F,EAAWJ,EAAUK,oBAE1DwD,EAASiD,qBAAqBb,GACpCpC,EAASkD,eAAed,EAAKe,aAAenD,EAASoD,kBACrDpD,EAASqD,YAAYjB,EAAKkB,UAAYtD,EAASuD,cACjD,CAEA9D,eAAe+D,EACbxD,GAEA,MAAMC,QAAEA,EAAOwD,kBAAEA,GAAsBzD,EAAS0D,cAC1C1G,EAAYgD,EAAS2D,eACrBC,EAAa5D,EAASK,eACtBQ,EAAMd,EAAOC,GACblE,EAAWqE,EAAYH,GAE7B,IAAI6D,EAAU7E,EAAc0D,IAAI5G,GA8ChC,OA7CK+H,IAoBHA,GAnBmCD,EAC/BhH,EAAQO,oBAAoB,CAC1BJ,KAAMkD,EACNjD,YACAK,QAAS,CACPiD,WAAYN,EAASO,gBACrBuD,iBAAkB9D,EAASe,sBAC3BgD,eAAgBnE,MAAMC,KAAKG,EAASgE,oBAAoBlE,WACxDkB,IAAKhB,EAASiB,UAEhBhE,QAASwG,IAEX7G,EAAQC,kBAAkB,CACxBE,KAAMkD,EACNjD,YACAC,QAASwG,KAKZQ,MAAMC,IACoC,YAArCA,EAAIjH,QAAQyF,IAAI,kBAClBxD,EAAYiF,IAAItD,GAEXqD,EAAIE,UAEZH,MAAM7B,IACLD,EAAiBtB,EAAK/E,EAAUsG,GAChCiC,EAAiBrE,GACjBhB,EAAckD,OAAOpG,GACdsG,KAERkC,OAAO3F,IAONK,EAAckD,OAAOpG,GACdyI,QAAQC,QAAQ,CAAA,MAE3BxF,EAAc2D,IAAI7G,EAAU+H,UAEjBA,CACf,CAIA,SAASQ,EAAiBrE,GACxB,MAAMa,EAAMd,EAAOC,GACblE,EAAWqE,EAAYH,IACvByE,cAAEA,EAAaC,4BAAEA,GAAgC1E,EAAS0D,cAC1D1G,EAAYgD,EAAS2D,eAC3B,GACEhI,EAAcI,gBACdmD,EAAY2D,IAAIhC,IAChB1E,EAAUQ,YACV,CACA,GAAIsC,EAAQ4D,IAAIhC,GAAM,OACtB,MAAMxB,EAAyB,CAC7BsF,IAAK,KACL5H,KAAM0H,EACNzH,YACAC,QAASyH,EACTE,GAAKC,IACH,IACE,GAAmB,qBAAfA,EAAMC,KAA6B,CACrC,MAAMhC,EAAYlE,EAAoB8D,IAAI7B,GAC1CiC,GACEA,EAAU1D,SAASY,IACjBwD,EAAcxD,EAAS,GAE7B,MAAO,GAAmB,aAAf6E,EAAMC,KAAqB,CACpC,MAAMV,EAA2B3G,KAAKsH,MAAMF,EAAMzC,MAClDD,EAAiBtB,EAAK/E,EAAUsI,EAClC,CAEA/E,EAAQ2F,OAAS,CASnB,CARE,MAAOrG,GAOPsG,EAAW5F,EACb,GAEF2F,OAAQ,EACR1F,MAAO,UAETL,EAAQ0D,IAAI9B,EAAKxB,GACjBG,EAAcH,EAChB,CACF,CAEA,SAAS4F,EAAW5F,GAClB,GAAsB,SAAlBA,EAAQC,QACZD,EAAQ2F,SACJ3F,EAAQ2F,OAAS,GAAM3F,EAAQsF,KAAkC,IAA3BtF,EAAQsF,IAAIO,YAAmB,CAEvE,MAAMC,EACJtD,KAAKuD,IAAI,EAAG/F,EAAQ2F,OAAS,IAAM,IAAuB,IAAhBnD,KAAKwD,UACjD9F,EAAeF,GACfhB,YAAW,KACL,CAAC,OAAQ,UAAUiH,SAASjG,EAAQC,QACxCE,EAAcH,EAAQ,GACrBwC,KAAKC,IAAIqD,EAAO,KACrB,CACF,CAEA,SAAS5F,EAAeF,GACjBA,EAAQsF,MACbtF,EAAQsF,IAAIY,OAAS,KACrBlG,EAAQsF,IAAIa,QAAU,KACtBnG,EAAQsF,IAAIc,QACZpG,EAAQsF,IAAM,KACQ,WAAlBtF,EAAQC,QACVD,EAAQC,MAAQ,YAEpB,CAEA,SAASE,EAAcH,GACrBA,EAAQsF,IAAM/H,EAAQe,gBAAgB,CACpCZ,KAAMsC,EAAQtC,KACdC,UAAWqC,EAAQrC,UACnBC,QAASoC,EAAQpC,UAEnBoC,EAAQC,MAAQ,SAChBD,EAAQsF,IAAIpG,iBAAiB,WAAYc,EAAQuF,IACjDvF,EAAQsF,IAAIpG,iBAAiB,mBAAoBc,EAAQuF,IACzDvF,EAAQsF,IAAIa,QAAU,IAAMP,EAAW5F,GACvCA,EAAQsF,IAAIY,OAAS,KACnBlG,EAAQ2F,OAAS,CAAC,CAEtB,CAEA,SAASU,EAAerG,EAAwBwB,GAC9CtB,EAAeF,GACfJ,EAAQiD,OAAOrB,EACjB,CAEA,SAAS8E,IAEPzG,EAAY0G,QAGZ3G,EAAQG,QAAQsG,GAGhB9G,EAAoBgH,QAGpBhJ,EAAQ6B,kBACV,CC9hBaoH,IAAAA,EAAqB,+BAC5BC,EAAqC,CACzCC,OAAQ,WAAA,GAGJC,EAAwC,IAAInH,IAC5CoH,EAA2B,IAAI9G,IAkBrC,SAAS+G,EAAiBC,GACxB,IAAIC,EAASJ,EAAStD,IAAIyD,GAO1B,OALKC,GAEHJ,EAASrD,IAAIwD,EADbC,EAAS,CAAED,QAAAA,EAAS7F,WAAY,CAAA,IAI3B8F,CACR,CAED,SAASC,EACPC,EACAC,EACAC,EACAC,EACAC,GAEA,IAAMC,EAAeH,EAAgBF,GAC/BF,EAA0C,CAC9CQ,SAAS,EACTC,cAAeF,EACfG,aAAcH,EACdV,UAAW,GACXK,GAAAA,EACAS,EAAkB,KAClBC,SAAU,IAAIC,kBAAiB,WAK7B,GAAa,aAATV,IAAuBH,EAAOW,EAAlC,CACkB,aAATR,IACPH,EAAOW,EAAmB1I,YAAW,WACnC+H,EAAOW,EAAmB,IADQ,GAEjC,MAEL,IAAMJ,EAAeH,EAAgBF,GAE1B,aAATC,GACAI,EAAaO,aAAed,EAAOU,aAAaI,YAChDP,EAAaQ,mBAAqBf,EAAOU,aAAaK,kBAGpDR,IAAiBP,EAAOU,eAC5BV,EAAOS,cAAgBF,EACvBD,EAAeN,GAbb,CAcH,IACDM,eAAAA,EACAD,SAAAA,EACAD,gBAAAA,GAYF,MAVa,aAATD,GAAuBD,EAAGY,WAC5Bd,EAAOY,SAASI,QAAQd,EAAGY,WAAY,CACrCG,WAAW,EACXC,SAAS,EACThH,YAAY,EACZiH,eAAe,IAGjBnB,EAAOY,SAASI,QAAQd,EA5E5B,SAAyBC,GACvB,MAAgB,SAATA,EACH,CACEc,WAAW,EACXC,SAAS,EACThH,YAAY,EACZiH,eAAe,GAEjB,CACEF,WAAW,EACXC,SAAS,EACThH,YAAY,EACZkH,gBAAiB,CAACjB,GAEzB,CA8D+BkB,CAAgBlB,IAEvCH,CACR,CAED,SAASsB,EACPC,EACAvB,GAEA,IAAMwB,EAAaxB,EAAOI,gBAAgBJ,EAAOE,IACjDF,EAAOU,aAAea,EAClBA,GAAsB,iBAARA,EAEbC,GACDD,EAAIT,aAAeU,EAAWV,YAC9BS,EAAIR,mBAAqBS,EAAWT,mBAEpCf,EAAOQ,SAAU,EACjBiB,KAEOF,IAAQC,IACjBxB,EAAOQ,SAAU,EACjBiB,IAEH,CAED,SAASC,EAAmB1B,GAC1B,IAAIuB,EAAMvB,EAAOS,cACjBT,EAAOH,UAAU7G,SAAQ,SAAC2I,GAAA,OAAKJ,EAAMI,EAAEC,OAAOL,MAC9CD,EAkJF,SAA4BO,GAK1B,OAJKC,IACHA,EAAqBlK,SAASmK,cAAc,QAE9CD,EAAmBE,UAAYH,EACxBC,EAAmBE,SAC3B,CAxJeC,CAAmBV,GAAMvB,EACxC,CACD,SAASkC,EAAoBlC,GAC3B,IAAMuB,EAAM,IAAIxI,IAAIiH,EAAOS,cAAc0B,MAAM,OAAOC,OAAOC,UAC7DrC,EAAOH,UAAU7G,SAAQ,SAAC2I,GAAA,OAAIA,EAAEC,OAAOL,MACvCD,EACE9H,MAAMC,KAAK8H,GACRa,OAAOC,SACPC,KAAK,KACRtC,EAEH,CAED,SAASuC,EAAmBvC,GAC1B,IAAIuB,EAAqBvB,EAAOS,cAChCT,EAAOH,UAAU7G,SAAQ,SAAC2I,GAAA,OAAKJ,EAAMI,EAAEC,OAAOL,MAC9CD,EAAcC,EAAKvB,EACpB,CAkBD,SAASwC,EAAuBxC,GAC9B,IAAIuB,EAAMvB,EAAOS,cACjBT,EAAOH,UAAU7G,SAAQ,SAAC2I,GACxB,IACMc,EApBV,SAAA/L,GACEgM,IACAC,EAAAA,EAAAA,qBAEM7B,EAAalJ,SAASgL,cAH5BF,EAAAA,gBAIA,IAAK5B,EAAY,OAAO,KACxB,IAAMC,EAAmB4B,EACrB/K,SAASgL,cAA2BD,GACpC,KACJ,OAAIA,IAAyB5B,EAAyB,KAC/C,CACLD,WAAAA,EACAC,iBAAAA,EAEH,CAMoB8B,CADClB,EAAEC,UAEpBL,EAAMkB,GAAYlB,KAEpBD,EAAcC,EAAKvB,EACpB,CAED,IAAM8C,EAAe,SAAC5C,GAAD,OAAiBA,EAAG8B,SAApB,EACfe,EAAe,SAAC7C,EAAahF,GAAd,OAAiCgF,EAAG8B,UAAY9G,CAAhD,EACrB,SAAS8H,EAAqBjD,GAC5B,IAAMkD,EAAgBnD,EAAiBC,GAUvC,OATKkD,EAAcpB,OACjBoB,EAAcpB,KAAO5B,EACnBF,EACA,OACA+C,EACAC,EACArB,IAGGuB,EAAcpB,IACtB,CAED,IAAMqB,EAAqB,SAAChD,GAC1B,MAAO,CACLY,WAAYZ,EAAGiD,cACfpC,iBAAkBb,EAAGkD,mBAExB,EACKC,EAAqB,SAACnD,EAAahF,GAErCA,EAAM6F,mBACL7F,EAAM4F,WAAWwC,SAASpI,EAAM6F,mBAMnC7F,EAAM4F,WAAWyC,aAAarD,EAAIhF,EAAM6F,iBACzC,EACD,SAASyC,EAAyBzD,GAChC,IAAMkD,EAAgBnD,EAAiBC,GAUvC,OATKkD,EAAcQ,WACjBR,EAAcQ,SAAWxD,EACvBF,EACA,WACAmD,EACAG,EACAb,IAGGS,EAAcQ,QACtB,CAED,IAqDI3B,EAmGAlB,EAxJE8C,EAAgB,SAACxD,EAAaqB,GAAd,OACpBA,EAAOrB,EAAGyD,UAAYpC,EAAOrB,EAAG0D,gBAAgB,QAD5B,EAEhBC,EAAgB,SAAC3D,GAAD,OAAiBA,EAAGyD,SAApB,EACtB,SAASG,EAAsB5D,GAC7B,IAAM+C,EAAgBnD,EAAiBI,GAUvC,OATK+C,EAAcc,UACjBd,EAAcc,QAAU9D,EACtBC,EACA,QACA2D,EACAH,EACAxB,IAGGe,EAAcc,OACtB,CAMD,SAASC,EAA0B9D,EAAaC,GAC9C,IALoB8D,EAKdhB,EAAgBnD,EAAiBI,GAUvC,OATK+C,EAAc/I,WAAWiG,KAC5B8C,EAAc/I,WAAWiG,GAAQF,EAC/BC,EACAC,GATgB8D,EAUH9D,EAVwB,SAACD,GAAD,IAAAgE,EAAA,cAAAA,EACzChE,EAAGiE,aAAaF,MAAa,OACV,SAACA,GAAD,OAAsB,SAAC/D,EAAaqB,GAAd,OACjC,OAARA,EAAerB,EAAGkE,aAAaH,EAAU1C,GAAOrB,EAAG0D,gBAAgBK,GADhD,CASfI,CAAalE,GACboC,IAGGU,EAAc/I,WAAWiG,EACjC,CA6BD,SAASmE,EACPpE,EACAC,EACAwB,GAEA,GAAKA,EAAEnB,QAAP,CACAmB,EAAEnB,SAAU,EACZ,IAAMe,EAAMI,EAAEjB,aACTiB,EAAE9B,UAAU0E,QAnCnB,SAAqCrE,EAAaC,GAChD,IAEqBqE,EAAAC,EAFf1E,EAAUH,EAAStD,IAAI4D,GAC7B,GAAKH,EACL,GAAa,SAATI,EACYS,OAAd4D,EAAAzE,EAAQ8B,cAAMjB,EAAAA,EAAAA,aAAU8D,oBACjB3E,EAAQ8B,UACV,GAAa,UAAT1B,EAAkB,CAAA,IAAAwE,EAAAC,EACVhE,OAAjB+D,EAAA5E,EAAQgE,iBAASnD,EAAAA,EAAAA,aAAU8D,oBACpB3E,EAAQgE,OAChB,MAAM,GAAa,aAAT5D,EAAqB,CAAA,IAAA0E,EAAAC,EACZlE,OAAlBiE,EAAA9E,EAAQ0D,kBAAU7C,EAAAA,EAAAA,aAAU8D,oBACrB3E,EAAQ0D,QAChB,KAAM,CAAA,IAAAsB,EAAAC,EAAAC,EACL,OAAAF,EAAAhF,EAAQ7F,aAAoB0G,OAA5BoE,EAAAD,EAAqB5E,YAAOS,EAAAA,EAAAA,aAAU8D,oBAC/B3E,EAAQ7F,WAAWiG,EAC3B,CACF,CAoBG+E,CAA4BhF,EAAIC,GAElCwB,EAAEtB,SAASH,EAAIqB,EANC,CAOjB,CAED,SAASlB,EAASsB,EAAkBzB,GAClCyB,EAAEE,MAAQyC,EAA6BpE,EAAI,OAAQyB,EAAEE,MACrDF,EAAEoC,SAAWO,EAAkCpE,EAAI,QAASyB,EAAEoC,SAC9DpC,EAAE8B,UAAYa,EAAiCpE,EAAI,WAAYyB,EAAE8B,UACjEnJ,OAAOC,KAAKoH,EAAEzH,YAAYlB,SAAQ,SAAImH,GACpCmE,EAAkCpE,EAAIC,EAAMwB,EAAEzH,WAAWiG,MAE5D,CAED,SAASsB,IACP7B,EAAS5G,QAAQqH,EAClB,CAsCD,SAAS8E,EAAmBC,GAG1B,GAAsB,aAAlBA,EAASC,MAAkD,IAA3BD,EAASxF,SAAShE,KAAtD,CAEA,IAAM0J,EAAmB,IAAIvM,IAAIqM,EAASxF,UACjBhI,SAAS2N,iBAAiBH,EAASI,UAE3CxM,SAAQ,SAAEkH,GACpBoF,EAAiB7I,IAAIyD,KACxBkF,EAASxF,SAAS7B,IAAImC,GA7C5B,SAAuBkF,EAAoBrF,GACzC,IAAIC,EAAiD,KAC/B,SAAlBoF,EAASC,KACXrF,EAASgD,EAAqBjD,GACH,UAAlBqF,EAASC,KAClBrF,EAAS8D,EAAsB/D,GACJ,cAAlBqF,EAASC,KAClBrF,EAASgE,EAA0BjE,EAASqF,EAASK,WAC1B,aAAlBL,EAASC,OAClBrF,EAASwD,EAAyBzD,IAE/BC,IACLA,EAAOH,UAAU6F,KAAKN,GACtBpF,EAAOM,eAAeN,GACvB,CAgCK2F,CAAcP,EAAUlF,MARsC,CAWnE,CAQD,SAAS0F,IACP/F,EAAU7G,QAAQmM,EACnB,CA4BD,SAASU,EAAYlE,GAEnB,MAAwB,oBAAb/J,SAAiC8H,GAE5CG,EAAU9B,IAAI4D,GAEdwD,EAAmBxD,GACZ,CACLhC,OAAQ,WA5CZ,IAAwByF,KA6CHzD,GA5CV/B,SAAS5G,SAAQ,SAAEkH,GAAA,OAnC9B,SAAsBkF,EAAoBlF,GACxC,IAAIF,EAAiD,KAUrD,GATsB,SAAlBoF,EAASC,KACXrF,EAASgD,EAAqB9C,GACH,UAAlBkF,EAASC,KAClBrF,EAAS8D,EAAsB5D,GACJ,cAAlBkF,EAASC,KAClBrF,EAASgE,EAA0B9D,EAAIkF,EAASK,WACrB,aAAlBL,EAASC,OAClBrF,EAASwD,EAAyBtD,IAE/BF,EAAL,CACA,IAAM8F,EAAQ9F,EAAOH,UAAUkG,QAAQX,IACxB,IAAXU,GAAc9F,EAAOH,UAAUmG,OAAOF,EAAO,GACjD9F,EAAOM,eAAeN,EAHT,CAId,CAoBiCiG,CAAab,EAAUlF,MACvDkF,EAASxF,SAASJ,QAClBK,EAAS,OAAQuF,EA2Cd,GAEJ,CAED,SAASvD,GACP2D,EACA5D,GAEA,OAAOiE,EAAY,CACjBR,KAAM,OACNzF,SAAU,IAAI7G,IACd6I,OAAAA,EACA4D,SAAAA,GAEH,CAcD,SAASzB,GACPyB,EACA5D,GAEA,OAAOiE,EAAY,CACjBR,KAAM,QACNzF,SAAU,IAAI7G,IACd6I,OAAAA,EACA4D,SAAAA,GAEH,CAED,SAASC,GACPD,EACAC,EACA7D,GAEA,OAAKnC,EAAmByG,KAAKT,GAEX,UAAdA,GAAuC,cAAdA,EACpB1B,GAAQyB,GAAU,SAAUW,GACjC,IAAMC,EAAoBxE,EAAOpI,MAAMC,KAAK0M,GAAY7D,KAAK,MAC7D6D,EAAW3G,QACN4G,GACLA,EACGjE,MAAM,QACNC,OAAOC,SACPrJ,SAAQ,SAACqN,GAAA,OAAIF,EAAWpI,IAAIsI,KAChC,IAGIR,EAAY,CACjBR,KAAM,YACNI,UAAAA,EACA7F,SAAU,IAAI7G,IACd6I,OAAAA,EACA4D,SAAAA,IAnB8C9F,CAqBjD,CCxcD,SAAS4G,GAAWC,GAClB,IAAIC,EAAO,WACX,MAAMC,EAAIF,EAAIhC,OAEd,IAAK,IAAI1I,EAAI,EAAGA,EAAI4K,EAAG5K,IACrB2K,GAAQD,EAAIG,WAAW7K,GACvB2K,IACGA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAAMA,GAAQ,IAErE,OAAOA,IAAS,CAClB,CAEO,SAASG,GACdC,EACA1L,EACAe,GAGA,OAAgB,IAAZA,EACMqK,GAAWA,GAAWM,EAAO1L,GAAS,IAAM,IAAS,IAG/C,IAAZe,EACMqK,GAAWpL,EAAQ0L,GAAQ,IAAQ,IAItC,IACT,CAOO,SAASC,GAAQC,EAAWC,GACjC,OAAOD,GAAKC,EAAM,IAAMD,EAAIC,EAAM,EACpC,CAoBO,SAASC,GAAaC,GAC3B,IACE,MAAMC,EAAUD,EAAYE,QAAQ,aAAc,SAClD,OAAO,IAAIC,OAAOF,EAIpB,CAHE,MAAO3O,GAEP,YADA8O,QAAQC,MAAM/O,EAEhB,CACF,CAEO,SAASgP,GAAc3M,EAAa4M,GACzC,IAAKA,EAAQjD,OAAQ,OAAO,EAC5B,IAAIkD,GAAkB,EAClBC,GAAa,EAEjB,IAAK,IAAI7L,EAAI,EAAGA,EAAI2L,EAAQjD,OAAQ1I,IAAK,CACvC,MAAM8L,EAAQC,GAAehN,EAAK4M,EAAQ3L,GAAG6C,KAAM8I,EAAQ3L,GAAGgM,SAC9D,IAA2B,IAAvBL,EAAQ3L,GAAGiM,SACb,GAAIH,EAAO,OAAO,OAElBF,GAAkB,EACdE,IAAOD,GAAa,EAE5B,CAEA,OAAOA,IAAeD,CACxB,CAyDA,SAASG,GACPhN,EACA8D,EACAmJ,GAEA,IACE,MAAME,EAAS,IAAIC,IAAIpN,EAAK,aAE5B,GAAa,UAAT8D,EAAkB,CACpB,MAAMuJ,EAAQjB,GAAaa,GAC3B,QAAKI,IAEHA,EAAM/B,KAAK6B,EAAOG,OAClBD,EAAM/B,KAAK6B,EAAOG,KAAKC,UAAUJ,EAAOK,OAAO7D,SAEnD,CAAO,MAAa,WAAT7F,GA/Cf,SAA8B2J,EAAaR,GACzC,IAGE,MAAMS,EAAW,IAAIN,IACnBH,EAAQV,QAAQ,gBAAiB,eAAeA,QAAQ,MAAO,SAC/D,iBAIIoB,EAA0C,CAC9C,CAACF,EAAO1R,KAAM2R,EAAS3R,MAAM,GAC7B,CAAC0R,EAAOG,SAAUF,EAASE,UAAU,IAYvC,OATIF,EAAS3B,MACX4B,EAAM7C,KAAK,CAAC2C,EAAO1B,KAAM2B,EAAS3B,MAAM,IAG1C2B,EAASG,aAAazP,SAAQ,CAAC0P,EAAGC,KAChCJ,EAAM7C,KAAK,CAAC2C,EAAOI,aAAanM,IAAIqM,IAAM,GAAID,GAAG,GAAO,KAIlDH,EAAMK,MACX5M,IAhDP,SACEqM,EACAR,EACAgB,GAEA,IAEE,IAAI3B,EAAUW,EACXV,QAAQ,sBAAuB,QAC/BA,QAAQ,SAAU,MAQrB,OANI0B,IAEF3B,EAAU,OAASA,EAAQC,QAAQ,aAAc,IAAM,QAG3C,IAAIC,OAAO,IAAMF,EAAU,IAAK,KACjChB,KAAKmC,EAGpB,CAFE,MAAO9P,GACP,OAAO,CACT,CACF,CA2BiBuQ,CAAmB9M,EAAK,GAAIA,EAAK,GAAIA,EAAK,KAIzD,CAFE,MAAOzD,GACP,OAAO,CACT,CACF,CAkBawQ,CAAqBhB,EAAQF,EAMxC,CAFE,MAAOtP,GACP,OAAO,CACT,CACF,CDqM0B,oBAAbX,WAENgJ,IACHA,EAAW,IAAIC,kBAAiB,WAC9B+E,GACD,KAGHA,IACAhF,EAASI,QAAQpJ,SAASoR,gBAAiB,CACzC/H,WAAW,EACXC,SAAS,EACThH,YAAY,EACZiH,eAAe,KC1HnB,MAAM8H,GAAe1N,GACnB2N,WAAWzP,KAAK0P,KAAK5N,IAAK8K,GAAMA,EAAEK,WAAW,KAExCrN,eAAe+P,GACpBC,EACAC,EACAhT,GAIA,GAFAgT,EAAgBA,GAAiB,KACjChT,EAASA,GAAWL,WAAWI,QAAUJ,WAAWI,OAAOC,QAEzD,MAAM,IAAIiT,MAAM,wCAElB,IACE,MAAM9O,QAAYnE,EAAOkT,UACvB,MACAP,GAAYK,GACZ,CAAEG,KAAM,UAAWlF,OAAQ,MAC3B,EACA,CAAC,UAAW,aAEPmF,EAAIC,GAAcN,EAAgBlH,MAAM,KACzCyH,QAAwBtT,EAAO8S,QACnC,CAAEK,KAAM,UAAWC,GAAIT,GAAYS,IACnCjP,EACAwO,GAAYU,IAGd,OAAO,IAAIE,aAAcC,OAAOF,EAGlC,CAFE,MAAOrR,GACP,MAAM,IAAIgR,MAAM,oBAClB,CACF,CAGO,SAASQ,GAASC,GACvB,MAAqB,iBAAVA,EAA2BA,EAC/B3S,KAAKC,UAAU0S,EACxB,CAGO,SAASC,GAAoBD,GACb,iBAAVA,IACTA,GAAgB,IAEbA,GAA0B,iBAAVA,IACnBA,EAAQ,KAKV,MAAME,EAASF,EAAiB7C,QAAQ,cAAe,IAAIhF,MAAM,QAWjE,OANqB,IAAjB+H,EAAM3F,QACR2F,EAAMxE,KAAK,KAKNwE,EACJlP,KAAK0N,GAAOA,EAAEf,MAAM,YAAce,EAAEyB,SAAS,EAAG,KAAOzB,IACvDpG,KAAK,IACV,CClTA,MAAM8H,GAAyC,CAAA,EAGxC,SAASC,GACdC,EACAC,GAGA,GAAI,QAASA,EACX,OAAOC,GAAOF,EAAKC,EAAe,KAEpC,GAAI,SAAUA,EACZ,OAAQC,GAAOF,EAAKC,EAAgB,MAEtC,GAAI,SAAUA,EACZ,OAsMJ,SAAiBD,EAAgBG,GAC/B,IAAK,IAAI5O,EAAI,EAAGA,EAAI4O,EAAWlG,OAAQ1I,IACrC,IAAKwO,GAAcC,EAAKG,EAAW5O,IACjC,OAAO,EAGX,OAAO,CACT,CA7MW6O,CAAQJ,EAAKC,EAAgB,MAEtC,GAAI,SAAUA,EACZ,OAAQF,GAAcC,EAAKC,EAAgB,MAI7C,IAAK,MAAO5B,EAAGD,KAAMpO,OAAOZ,QAAQ6Q,GAClC,IAAKI,GAAmBjC,EAAGkC,GAAQN,EAAK3B,IAAK,OAAO,EAEtD,OAAO,CACT,CAGA,SAASiC,GAAQN,EAAgBO,GAC/B,MAAMX,EAAQW,EAAK1I,MAAM,KACzB,IAAI2I,EAAeR,EACnB,IAAK,IAAIzO,EAAI,EAAGA,EAAIqO,EAAM3F,OAAQ1I,IAAK,CACrC,IAAIiP,GAA8B,iBAAZA,KAAwBZ,EAAMrO,KAAMiP,GAGxD,OAAO,KAFPA,EAAUA,EAAQZ,EAAMrO,GAI5B,CACA,OAAOiP,CACT,CAWA,SAASH,GAAmBJ,EAA2BrP,GAErD,GAAyB,iBAAdqP,EACT,OAAOrP,EAAQ,KAAOqP,EAExB,GAAyB,iBAAdA,EACT,OAAe,EAARrP,IAAcqP,EAEvB,GAAyB,kBAAdA,EACT,QAASrP,IAAUqP,EAGrB,GAAkB,OAAdA,EACF,OAAiB,OAAVrP,EAGT,GAAI1B,MAAMuR,QAAQR,KAAeS,GAAiBT,GAChD,OAAOlT,KAAKC,UAAU4D,KAAW7D,KAAKC,UAAUiT,GAIlD,IAAK,MAAMU,KAAMV,EACf,IACGW,GACCD,EACA/P,EACAqP,EAAUU,IAGZ,OAAO,EAGX,OAAO,CACT,CAGA,SAASD,GAAiBV,GACxB,MAAM/P,EAAOD,OAAOC,KAAK+P,GACzB,OACE/P,EAAKgK,OAAS,GAAKhK,EAAK6H,QAAQuG,GAAe,MAATA,EAAE,KAAYpE,SAAWhK,EAAKgK,MAExE,CA2BA,SAAS4G,GAAK9C,EAAaC,GAEzB,OAAI9O,MAAMuR,QAAQ1C,GACTA,EAAOO,MAAM1I,GAAOoI,EAASpJ,SAASgB,KAExCoI,EAASpJ,SAASmJ,EAC3B,CAGA,SAAS6C,GACPE,EACA/C,EACAC,GAEA,OAAQ8C,GACN,IAAK,OACH,OAAOnB,GAAoB5B,KAAY4B,GAAoB3B,GAC7D,IAAK,OACH,OAAO2B,GAAoB5B,KAAY4B,GAAoB3B,GAC7D,IAAK,OACH,OAAO2B,GAAoB5B,GAAU4B,GAAoB3B,GAC3D,IAAK,QACH,OAAO2B,GAAoB5B,IAAW4B,GAAoB3B,GAC5D,IAAK,OACH,OAAO2B,GAAoB5B,GAAU4B,GAAoB3B,GAC3D,IAAK,QACH,OAAO2B,GAAoB5B,IAAW4B,GAAoB3B,GAC5D,IAAK,MACH,OAAOD,IAAWC,EACpB,IAAK,MACH,OAAOD,IAAWC,EACpB,IAAK,MACH,OAAOD,EAASC,EAClB,IAAK,OACH,OAAOD,GAAUC,EACnB,IAAK,MACH,OAAOD,EAASC,EAClB,IAAK,OACH,OAAOD,GAAUC,EACnB,IAAK,UAEH,OAAOA,EAAqB,MAAVD,EAA2B,MAAVA,EACrC,IAAK,MACH,QAAK7O,MAAMuR,QAAQzC,IACZ6C,GAAK9C,EAAQC,GACtB,IAAK,OACH,QAAK9O,MAAMuR,QAAQzC,KACX6C,GAAK9C,EAAQC,GACvB,IAAK,OACH,OAAQqC,GAAmBrC,EAAUD,GACvC,IAAK,QACH,QAAK7O,MAAMuR,QAAQ1C,IACZsC,GAAmBrC,EAAUD,EAAO9D,QAC7C,IAAK,aACH,OAnEN,SAAmB8D,EAAaC,GAC9B,IAAK9O,MAAMuR,QAAQ1C,GAAS,OAAO,EACnC,MAAMgD,EAAQL,GAAiB1C,GAC1BI,GAAWiC,GAAmBrC,EAAUI,GACxCA,GAAW2B,GAAc3B,EAAGJ,GACjC,IAAK,IAAIzM,EAAI,EAAGA,EAAIwM,EAAO9D,OAAQ1I,IACjC,GAAIwM,EAAOxM,IAAMwP,EAAMhD,EAAOxM,IAC5B,OAAO,EAGX,OAAO,CACT,CAwDayP,CAAUjD,EAAQC,GAC3B,IAAK,OACH,IAAK9O,MAAMuR,QAAQ1C,GAAS,OAAO,EACnC,IAAK,IAAIxM,EAAI,EAAGA,EAAIyM,EAAS/D,OAAQ1I,IAAK,CACxC,IAAI0P,GAAS,EACb,IAAK,IAAIC,EAAI,EAAGA,EAAInD,EAAO9D,OAAQiH,IACjC,GAAIb,GAAmBrC,EAASzM,GAAIwM,EAAOmD,IAAK,CAC9CD,GAAS,EACT,KACF,CAEF,IAAKA,EAAQ,OAAO,CACtB,CACA,OAAO,EACT,IAAK,SACH,IACE,OAlJUtD,EAkJMK,EAjJjB8B,GAAYnC,KACfmC,GAAYnC,GAAS,IAAIb,OAAOa,EAAMd,QAAQ,aAAc,WAEvDiD,GAAYnC,IA8Ia/B,KAAKmC,EAGjC,CAFE,MAAO9P,GACP,OAAO,CACT,CACF,IAAK,QACH,OAnGN,SAAiBmQ,GACf,GAAU,OAANA,EAAY,MAAO,OACvB,GAAIlP,MAAMuR,QAAQrC,GAAI,MAAO,QAC7B,MAAM+C,SAAW/C,EACjB,MAAI,CAAC,SAAU,SAAU,UAAW,SAAU,aAAaxJ,SAASuM,GAC3DA,EAEF,SACT,CA2FaC,CAAQrD,KAAYC,EAC7B,QAEE,OADAjB,QAAQC,MAAM,qBAAuB8D,IAC9B,EA1Jb,IAAkBnD,CA4JlB,CAGA,SAASuC,GAAOF,EAAgBG,GAC9B,IAAKA,EAAWlG,OAAQ,OAAO,EAC/B,IAAK,IAAI1I,EAAI,EAAGA,EAAI4O,EAAWlG,OAAQ1I,IACrC,GAAIwO,GAAcC,EAAKG,EAAW5O,IAChC,OAAO,EAGX,OAAO,CACT,CCjLA,MAAM8P,GACc,oBAAXhU,QAA8C,oBAAbC,SAEpCgU,GFgRC,WACL,IAAI3P,EACJ,IAEEA,EAAyB,QAG3B,CAFE,MAAO1D,GACP0D,EAAU,EACZ,CACA,OAAOA,CACT,CEzRoB4P,GCDb,MAAeC,GAapBzS,wBACEa,GAEA,MAAM6R,EAAkD,CAAA,EAaxD,aAXQ5N,QAAQ6N,IACZ1R,OAAOZ,QAAQQ,GAAYc,KAAItE,IAAA,IAAEuV,EAAeC,GAAexV,EAAA,OAC7DyV,KAAKC,eAAeH,EAAeC,EAAe,MAGtDlT,SAASqT,IACT,GAAIA,EAAK,CACP,MAAM5R,YAAS4R,EAAIJ,cAAkBI,MAAAA,OAAAA,EAAIH,gBACzCH,EAAKtR,GAAO4R,CACd,KAEKN,CACT,4CAkGK,cAA+CD,GAWpDQ,YAQG9U,GAAA,IARS+U,OACVA,EAAS,oBAAmBC,SAC5BA,EAAQC,iBACRA,EAAmB,CAAC,GAKrBjV,EACCkV,QACAP,KAAKI,OAASA,EACdJ,KAAKK,SAAWA,EAChBL,KAAKM,iBAAmBA,CAC1B,CACApT,qBAAqB4S,EAAuBC,GAC1C,MAAMzR,EAAG,GAAA3D,OAAMmV,EAAa,MAAAnV,OAAKoV,GACjC,IAAIG,EAAwC,KAC5C,IAAKF,KAAKK,SAAU,OAAOH,EAC3B,IACE,MAAMM,EAAMR,KAAKK,SAASlQ,IAAI6P,KAAKI,OAAS9R,GACtCuB,EAAO3E,KAAKsH,MAAMgO,GAAO,MAC3B3Q,EAAKiQ,eAAiBjQ,EAAKkQ,gBAAkBlQ,EAAK4Q,cACpDP,EAAMrQ,EAGR,CADA,MAAOzD,GACP,CAEF,OAAO8T,CACT,CACAhT,sBAAsBgT,GACpB,MAAM5R,YAAS4R,EAAIJ,cAAkBI,MAAAA,OAAAA,EAAIH,gBACzC,IAAKC,KAAKK,SAAU,OACpB,MAAMjG,EAAMlP,KAAKC,UAAU+U,GAC3BF,KAAKK,SAASjQ,IAAI4P,KAAKI,OAAS9R,EAAK8L,EAAK4F,KAAKM,iBACjD,sCArGK,cAA+CX,GAYpDQ,YAUGtV,GAAA,IAVSuV,OACVA,EAAS,oBAAmBM,IAC5BA,EAAG/O,IACHA,EAAG2O,iBACHA,EAAmB,CAAC,GAMrBzV,EACC0V,QACAP,KAAKI,OAASA,EACdJ,KAAKU,IAAMA,EACXV,KAAKrO,IAAMA,EACXqO,KAAKM,iBAAmBA,CAC1B,CACApT,qBAAqB4S,EAAuBC,GAC1C,MAAMzR,EAAG,GAAA3D,OAAMmV,EAAa,MAAAnV,OAAKoV,GACjC,IAAIG,EAAwC,KAC5C,IAAKF,KAAKU,IAAK,OAAOR,EACtB,IACE,MACMrQ,EAAO3E,KAAKsH,MADNwN,KAAKU,IAAIC,QAAQX,KAAKI,OAAS9R,IAAQ,MAE/CuB,EAAKiQ,eAAiBjQ,EAAKkQ,gBAAkBlQ,EAAK4Q,cACpDP,EAAMrQ,EAGR,CADA,MAAOzD,GACP,CAEF,OAAO8T,CACT,CACAhT,sBAAsBgT,GACpB,MAAM5R,YAAS4R,EAAIJ,cAAkBI,MAAAA,OAAAA,EAAIH,gBACzC,IAAKC,KAAKrO,IAAK,OACf,MAAMyI,EAAMlP,KAAKC,UAAU+U,GAC3BF,KAAKrO,IAAIiP,OACPC,mBAAmBb,KAAKI,OAAS9R,GACjCuS,mBAAmBzG,GACnB4F,KAAKM,iBAET,gBD1HK,MAsCLH,YAAYW,GAqBV,GApBAA,EAAUA,GAAW,GAGrBd,KAAKlQ,QAAU2P,GACfO,KAAKe,EAAOf,KAAKc,QAAUA,EAC3Bd,KAAKgB,EAAY,KACjBhB,KAAKiB,EAAsB,IAAIrU,IAC/BoT,KAAKkB,EAAmB,GACxBlB,KAAKmB,OAAQ,EACbnB,KAAKoB,EAAiB,IAAIxU,IAC1BoT,KAAKqB,EAAW,GAChBrB,KAAKsB,EAAW,EAChBtB,KAAKuB,OAAQ,EACbvB,KAAKwB,EAAY,IAAIlV,IACrB0T,KAAKyB,EAAuB,IAAInV,IAChC0T,KAAK0B,EAAsB,GAC3B1B,KAAK2B,EAAyB,IAAIrV,IAClC0T,KAAK4B,EAAoB,IAAIhV,IAC7BoT,KAAK6B,GAAsB,EAEvBf,EAAQzP,WAAY,CACtB,GAAIyP,EAAQ3D,cACV,MAAM,IAAIC,MAAM,8CAElB,IAAK0D,EAAQrW,UACX,MAAM,IAAI2S,MAAM,qBAElB,IAAI0E,GAAW,EACf,IACEA,IAAa,IAAIjG,IAAIiF,EAAQpT,SAAW,IAAIqU,SAASvG,MACnD,mBAGF,CADA,MAAOpP,GACP,CAEF,GAAI0V,EACF,MAAM,IAAI1E,MAAM,4CAEpB,MACE,GAAI0D,EAAQ7S,mBACV,MAAM,IAAImP,MAAM,mDAIhB0D,EAAQ/P,WACViP,KAAKuB,OAAQ,GAGX/B,IAAasB,EAAQkB,gBACvBxW,OAAOyW,YAAcjC,KACrBvU,SAASyW,cAAc,IAAIC,MAAM,cAG/BrB,EAAQlQ,cACVoP,KAAKuB,OAAQ,EACbvB,KAAKoC,KAGHtB,EAAQrW,YAAcqW,EAAQzP,YAChC2O,KAAKqC,EAAS,CAAA,GAAI,GAAM,EAE5B,CAEAnV,mBAA0BnC,GACpBA,GAAWA,EAAQuX,cAErBtC,KAAKe,EAAKwB,oBAAqB,GAEjCvC,KAAK6B,GAAsB,QAErB7B,KAAKqC,EAAStX,GAAS,GAAM,GAE/BiV,KAAKwC,KJXN,SAAmB/U,GACxB,MAAMa,EAAMd,EAAOC,GACbgV,EAAOpW,EAAoB8D,IAAI7B,IAAQ,IAAI1B,IACjD6V,EAAK7Q,IAAInE,GACTpB,EAAoB+D,IAAI9B,EAAKmU,EAC/B,CIOMC,CAAU1C,KAEd,CAEA9S,sBACEnC,SAEMiV,KAAKqC,EAAStX,GAAS,GAAO,EACtC,CAEO4C,aACL,MAAO,CAACqS,KAAK7O,cAAczD,QAASsS,KAAK5O,eAC3C,CACOD,cAML,MAAMwR,EAAc3C,KAAKe,EAAKrT,SAAW,4BACzC,MAAO,CACLA,QAASiV,EAAY3H,QAAQ,OAAQ,IACrC9I,eAAgB8N,KAAKe,EAAK7O,eAAiByQ,GAAa3H,QACtD,OACA,IAEF9J,kBAAmB8O,KAAKe,EAAK6B,sBAC7BzQ,4BAA6B6N,KAAKe,EAAK5O,4BAE3C,CACOf,eACL,OAAO4O,KAAKe,EAAKtW,WAAa,EAChC,CAEOqD,eACL,OAAOkS,KAAKe,EAAK1P,aAAc,CACjC,CAEOnD,wBACL,OAAO8R,KAAKe,EAAK9S,kBACnB,CAEAf,QACEnC,EACA8X,EACAC,GAGA,GADA/X,EAAUA,GAAW,IAChBiV,KAAKe,EAAKtW,UACb,MAAM,IAAI2S,MAAM,2BJnFflQ,eACLO,EACAsV,EACAC,EACAH,EACAC,EACAtZ,GAEKA,IACHJ,EAAcI,gBAAiB,GAGjC,MAAMqG,QAkDR3C,eACEO,EACAoV,EACAE,EACAC,GAEA,MAAM1U,EAAMd,EAAOC,GACblE,EAAWqE,EAAYH,GACvBwC,EAAM,IAAID,KAEViT,EAAa,IAAIjT,KACrBC,EAAIhB,UAAY7F,EAAcE,OAASF,EAAcC,gBAiFzD6D,iBACE,IAAIX,EAAJ,CACAA,GAAmB,EACnB,IACE,GAAI3C,EAAUuC,aAAc,CAC1B,MAAM4C,QAAcnF,EAAUuC,aAAa+W,QACzC9Z,EAAcG,UAEhB,GAAIwF,EAAO,CACT,MAAM6M,EAAiC1Q,KAAKsH,MAAMzD,GAC9C6M,GAAUvO,MAAMuR,QAAQhD,IAC1BA,EAAO/O,SAAQsW,IAAiB,IAAf7U,EAAKuB,GAAKsT,EACzB3W,EAAM4D,IAAI9B,EAAK,IACVuB,EACHb,QAAS,IAAIgB,KAAKH,EAAKb,UACvB,IAGNL,GACF,CACF,CAEA,CADA,MAAOvC,GACP,CAEF,IAAKhD,EAAcM,mBAAoB,CACrC,MAAM0Z,EAAY/Y,EAAQiB,oBACtB8X,IACF/Y,EAAQ6B,iBAAmBkX,EAE/B,CA5BsB,CA6BxB,CA5GQC,GACN,MAAMnT,EAAW1D,EAAM2D,IAAI5G,GAC3B,OACE2G,IACC8S,IACAH,GAAc3S,EAASlB,QAAUiB,IAClCC,EAASlB,QAAUiU,GAGf/S,EAASG,KAAK1D,EAAYiF,IAAItD,GAG9B4B,EAASlB,QAAUiB,EACrBgB,EAAcxD,GAIdqE,EAAiBrE,GAEZyC,EAASL,YAoCpB,SACEyB,EACAyR,GAEA,OAAO,IAAI/Q,SAASC,IAClB,IACIqR,EADAC,GAAW,EAEf,MAAMC,EAAU3T,IACV0T,IACJA,GAAW,EACXD,GAAS1X,aAAa0X,GACtBrR,EAAQpC,GAAQ,MAAK,EAGnBkT,IACFO,EAAQxX,YAAW,IAAM0X,KAAUT,IAGrCzR,EAAQI,MAAM7B,GAAS2T,EAAO3T,KAAOkC,OAAM,IAAMyR,KAAS,GAE9D,CAtDiBC,CAAexS,EAAcxD,GAAWsV,EAEzD,CAvFqBW,CACjBjW,EACAoV,EACAE,EACAC,GAEFF,GAAkBjT,SAAeW,EAAgB/C,EAAUoC,EAC7D,CIkEU8T,CACJ3D,KACAjV,EAAQgY,QACRhY,EAAQiY,WAAahD,KAAKe,EAAKiB,cAC/Ba,EACAC,GAC6B,IAA7B9C,KAAKe,EAAKvX,eAEd,CAEQoa,IACF5D,KAAKgB,GACPhB,KAAKgB,GAET,CAEOlQ,YAAYC,GACjBiP,KAAKe,EAAKhQ,SAAWA,EACrBiP,KAAKuB,OAAQ,EACbvB,KAAK4D,GACP,CAEA1W,2BACEgQ,EACAC,EACAhT,GAEA,MAAM0Z,QAAqB5G,GACzBC,EACAC,GAAiB6C,KAAKe,EAAK5D,cAC3BhT,GAEF6V,KAAKlP,YACH5F,KAAKsH,MAAMqR,GAEf,CAEOlT,eAAeC,GACpBoP,KAAKe,EAAKnQ,YAAcA,EACxBoP,KAAKuB,OAAQ,EACbvB,KAAKoC,GACP,CAEAlV,8BACEgQ,EACAC,EACAhT,GAEA,MAAM2Z,QAAwB7G,GAC5BC,EACAC,GAAiB6C,KAAKe,EAAK5D,cAC3BhT,GAEF6V,KAAKrP,eAAezF,KAAKsH,MAAMsR,GACjC,CAEA5W,qBACE2C,EACAsN,EACAhT,GAsBA,OApBI0F,EAAKkU,oBACPlU,EAAKkB,SAAW7F,KAAKsH,YACbyK,GACJpN,EAAKkU,kBACL5G,GAAiB6C,KAAKe,EAAK5D,cAC3BhT,WAGG0F,EAAKkU,mBAEVlU,EAAKmU,uBACPnU,EAAKe,YAAc1F,KAAKsH,YAChByK,GACJpN,EAAKmU,qBACL7G,GAAiB6C,KAAKe,EAAK5D,cAC3BhT,WAGG0F,EAAKmU,sBAEPnU,CACT,CAEA3C,oBAA2Ba,GACzBiS,KAAKe,EAAKhT,WAAaA,EACnBiS,KAAKe,EAAKkD,2BACNjE,KAAKtP,uBAETsP,KAAKe,EAAK1P,iBACN2O,KAAKkE,KAGblE,KAAK4D,IACL5D,KAAKoC,IACP,CAEAlV,4BAAmCiX,GACjCnE,KAAK0B,EAAsByC,EACvBnE,KAAKe,EAAKkD,2BACNjE,KAAKtP,uBAETsP,KAAKe,EAAK1P,iBACN2O,KAAKkE,KAGblE,KAAK4D,IACL5D,KAAKoC,IACP,CAEAlV,0BAAiCkX,GAC/BpE,KAAKe,EAAKxP,iBAAmB6S,GAAQ,CAAA,EACjCpE,KAAKe,EAAK1P,iBACN2O,KAAKkE,KAGblE,KAAK4D,IACL5D,KAAKoC,IACP,CAGOiC,kBAAkBxV,GACvBmR,KAAKyB,EAAuB5S,EAC5BmR,KAAK4D,GACP,CAEA1W,aAAoBuB,GAElB,GADAuR,KAAKe,EAAKtS,IAAMA,EACZuR,KAAKe,EAAK1P,WAGZ,aAFM2O,KAAKkE,SACXlE,KAAKoC,GAA0B,GAGjCpC,KAAKoC,GAA0B,EACjC,CAEOpU,gBACL,MAAO,IAAKgS,KAAKe,EAAKhT,cAAeiS,KAAK0B,EAC5C,CAEOlT,sBACL,OAAOwR,KAAKe,EAAKxP,kBAAoB,EACvC,CAEOE,oBAEL,OAAOuO,KAAKyB,GAAwB,IAAInV,GAC1C,CAEOgY,gCACL,OAAOtE,KAAKe,EAAKwD,4BAA8B,EACjD,CAEO7V,SACL,OAAOsR,KAAKe,EAAKtS,KAAO,EAC1B,CAEOuC,cACL,OAAOgP,KAAKe,EAAKhQ,UAAY,EAC/B,CAEOF,iBACL,OAAOmP,KAAKe,EAAKnQ,aAAe,EAClC,CAEO8R,UAAUrQ,GAEf,OADA2N,KAAKoB,EAAexP,IAAIS,GACjB,KACL2N,KAAKoB,EAAezR,OAAO0C,EAAG,CAElC,CAEQmQ,IACN,OAAoC,IAA7BxC,KAAKe,EAAKvX,gBAA4BwW,KAAKe,EAAKwB,kBACzD,CAEArV,UACO8S,KAAKe,EAAK1P,YACV2O,KAAK6B,SACJ7B,KAAKqC,EAAS,CAAE,GAAE,GAAO,GAAMtQ,OAAM,QAG7C,CAEOyS,gBACL,OAAO,IAAIlY,IAAI0T,KAAKwB,EACtB,CAEOiD,UJrPF,IAAqBhX,EIuPxBuS,KAAKoB,EAAe/N,QACpB2M,KAAKwB,EAAUnO,QACf2M,KAAKiB,EAAoB5N,QACzB2M,KAAKkB,EAAmB,GACxBlB,KAAKqB,EAAW,GACZrB,KAAKsB,GACP1V,aAAaoU,KAAKsB,GJ7PI7T,EI+PZuS,KJ9Pd3T,EAAoBQ,SAAS6X,GAAMA,EAAE/U,OAAOlC,KIgQtC+R,IAAahU,OAAOyW,cAAgBjC,aAC/BxU,OAAOyW,YAIhBjC,KAAK2B,EAAuB9U,SAAS8X,IACnCA,EAAIC,MAAM,IAEZ5E,KAAK2B,EAAuBtO,QAC5B2M,KAAK4B,EAAkBvO,OACzB,CAEOwR,YAAYC,GACjB9E,KAAKgB,EAAY8D,CACnB,CAEOC,eAAezW,EAAa0W,GACjChF,KAAKe,EAAKxP,iBAAmByO,KAAKe,EAAKxP,kBAAoB,GAC3DyO,KAAKe,EAAKxP,iBAAiBjD,GAAO0W,EAC9BhF,KAAKe,EAAK1P,WACZ2O,KAAKkE,KAGPlE,KAAKoC,IACLpC,KAAK4D,IACP,CAEOqB,IAAOC,GACZ,MAAMC,EAASnF,KAAKoF,EAAKF,EAAY,MAErC,OADAlF,KAAKqF,EAAmBH,EAAYC,GAC7BA,CACT,CAEOG,kBAAkBhX,GAEvB,OADA0R,KAAK4B,EAAkBhQ,IAAItD,GACtB0R,KAAKe,EAAKnQ,YACKoP,KAAKe,EAAKnQ,YAAYqF,QAAQ0O,GAAQA,EAAIrW,MAAQA,IAEnEO,KAAK8V,GACCA,EAAIY,OACFvF,KAAKwF,EAAmBb,GADP,OAGzB1O,QAAQtE,GAAgB,OAARA,IAPgB,IAQrC,CAEQ6T,EAAmBN,EAA4BO,GACrD,MAAMvV,EAAW8P,KAAK2B,EAAuBxR,IAAI+U,GAGjD,GACEA,EAAWK,SACVvF,KAAK4B,EAAkBtR,IAAI4U,EAAW5W,OACtC4B,EAED,OAAO,KAGT,MAAMiV,EAASnF,KAAKiF,IAAIC,GAGlBQ,EAAYxa,KAAKC,UAAUga,EAAOpW,OAGxC,IACG0W,GACDN,EAAOQ,cACPzV,GACAA,EAASwV,YAAcA,EAEvB,OAAOP,EAOT,GAHIjV,GAAU8P,KAAK4F,EAA0BV,GAGzCC,EAAOQ,aAAc,CACvB,MAAMf,EAAO5E,KAAK6F,EAAiBV,EAAOpW,OACtC6V,GACF5E,KAAK2B,EAAuBvR,IAAI8U,EAAY,CAC1CN,OACAc,aAGN,CAEA,OAAOP,CACT,CAEQS,EAA0BjB,GAChC,MAAM9U,EAAOmQ,KAAK2B,EAAuBxR,IAAIwU,GACzC9U,IACFA,EAAK+U,OACL5E,KAAK2B,EAAuBhS,OAAOgV,GAEvC,CAEQvC,EAA0BqD,GAChC,MAAM7U,EAAcoP,KAAKe,EAAKnQ,aAAe,GAGvCxC,EAAO,IAAIxB,IAAIgE,GACrBoP,KAAK2B,EAAuB9U,SAAQ,CAAC0P,EAAGC,KACjCpO,EAAKkC,IAAIkM,KACZD,EAAEqI,OACF5E,KAAK2B,EAAuBhS,OAAO6M,GACrC,IAIF5L,EAAY/D,SAAS8X,IACnB3E,KAAKwF,EAAmBb,EAAKc,EAAW,GAE5C,CAEQJ,EAAsBH,EAA2BC,GACvD,MAAM7W,EAAM4W,EAAW5W,IAGjBwX,EAAO9F,KAAKwB,EAAUrR,IAAI7B,GAG7BwX,GACDA,EAAKX,OAAOQ,eAAiBR,EAAOQ,cACpCG,EAAKX,OAAOY,cAAgBZ,EAAOY,cAEnC/F,KAAKwB,EAAUpR,IAAI9B,EAAK,CAAE4W,aAAYC,WACtCnF,KAAKoB,EAAevU,SAASwF,IAC3B,IACEA,EAAG6S,EAAYC,EAGjB,CAFE,MAAO/Y,GACP8O,QAAQC,MAAM/O,EAChB,KAGN,CAEQ4Z,EAAmB1X,EAAaqD,GAEtC,GAAmB,aAAfA,EAAIsU,OAAuB,OAG/B,MAAMC,EAAmBhb,KAAKC,UAAUwG,EAAI5C,OAC5C,GAAIiR,KAAKkB,EAAiB5S,KAAS4X,EAAnC,CAIA,GAHAlG,KAAKkB,EAAiB5S,GAAO4X,EAGzBlG,KAAKe,EAAKoF,eACZ,IACEnG,KAAKe,EAAKoF,eAAe7X,EAAKqD,EAE9B,CADA,MAAOvF,GACP,CAKCoT,IAAchU,OAAO3B,QAC1BmW,KAAKqB,EAAS9H,KAAK,CACjBjL,MACA8X,GAAIzU,EAAIyU,KAELpG,KAAKsB,IACRtB,KAAKsB,EAAW9V,OAAOM,YAAW,KAEhCkU,KAAKsB,EAAW,EAChB,MAAM+E,EAAI,IAAIrG,KAAKqB,GACnBrB,KAAKqB,EAAW,GAGXrB,KAAKe,EAAKuF,aAEf9a,OACG3B,MAAK,iCAAAc,OAEFqV,KAAKe,EAAKuF,YAAW,YAAA3b,OACZkW,mBAAmB3V,KAAKC,UAAUkb,KAE7C,CACE7Z,MAAO,WACP+Z,KAAM,YAGTxU,OAAM,QAEL,GACHiO,KAAKe,EAAKyF,kBAAoB,MA1CkB,CA4CvD,CAEQC,EACNnY,EACAS,EACAkX,EACAS,EACAxB,EACAC,GAEA,MAAMwB,EAAqB,CACzB5X,QACAqX,KAAMrX,EACN6X,KAAM7X,EACNkX,SACAS,OAAQA,GAAU,IAQpB,OANIxB,IAAYyB,EAAIzB,WAAaA,GAC7BC,IAAQwB,EAAIE,iBAAmB1B,GAGnCnF,KAAKgG,EAAmB1X,EAAKqY,GAEtBA,CACT,CAEOG,KAAoDxY,GACzD,OAAO0R,KAAK+G,YAAYzY,GAAK8X,EAC/B,CAEOY,MAAqD1Y,GAC1D,OAAO0R,KAAK+G,YAAYzY,GAAKsY,GAC/B,CAEOK,gBAGL3Y,EAAQ4Y,GACR,MAAMnY,EAAQiR,KAAK+G,YAAmCzY,GAAKS,MAC3D,OAAiB,OAAVA,EAAkBmY,EAAsCnY,CACjE,CAOOoY,QAGLC,GACA,OAAOpH,KAAK+G,YAAYK,EAC1B,CAEOL,YAGLK,GAEA,GAAIpH,KAAKyB,EAAqBnR,IAAI8W,GAMhC,OAAOpH,KAAKyG,EACVW,EACApH,KAAKyB,EAAqBtR,IAAIiX,GAC9B,YAKJ,IAAKpH,KAAKe,EAAKhQ,WAAaiP,KAAKe,EAAKhQ,SAASqW,GAG7C,OAAOpH,KAAKyG,EAAkBW,EAAI,KAAM,kBAI1C,MAAMD,EAAgCnH,KAAKe,EAAKhQ,SAASqW,GAGzD,GAAID,EAAQE,MACV,IAAK,MAAMC,KAAQH,EAAQE,MAAO,CAEhC,GAAIC,EAAKC,SAAWvH,KAAKwH,EAAeF,EAAKC,SAM3C,SAIF,GAAI,UAAWD,EAAM,CAEnB,GAAIA,EAAKlJ,YAAc4B,KAAKyH,EAAiBH,EAAKlJ,WAMhD,SAIF,IACG4B,KAAK0H,EACJJ,EAAK7M,MAAQ2M,EACbE,EAAKK,cACL3H,KAAKe,EAAKkD,sBAAwBqD,EAAKM,uBACnCN,EAAKO,uBACL7d,EACJsd,EAAK1M,MACL0M,EAAKQ,SACLR,EAAKS,aAQP,SAgBF,OANIT,EAAKU,QACPV,EAAKU,OAAOnb,SAASyS,IACnBU,KAAKiI,EAAO3I,EAAE4F,WAAY5F,EAAE6F,OAAO,IAIhCnF,KAAKyG,EAAkBW,EAAIE,EAAKY,MAAY,QAASZ,EAAKF,GACnE,CACA,IAAKE,EAAKa,WAOR,SAIF,MAAMxD,EAAqB,CACzBwD,WAAYb,EAAKa,WACjB7Z,IAAKgZ,EAAKhZ,KAAO8Y,GAEf,aAAcE,IAAM3C,EAAImD,SAAWR,EAAKQ,UACxCR,EAAKc,UAASzD,EAAIyD,QAAUd,EAAKc,SACjCd,EAAKK,gBAAehD,EAAIgD,cAAgBL,EAAKK,eAC7CL,EAAKO,oBACPlD,EAAIkD,kBAAoBP,EAAKO,mBAC3BP,EAAKM,yBACPjD,EAAIiD,uBAAyBN,EAAKM,6BACT5d,IAAvBsd,EAAKe,gBACP1D,EAAI0D,cAAgBf,EAAKe,oBACGre,IAA1Bsd,EAAKgB,mBACP3D,EAAI2D,iBAAmBhB,EAAKgB,kBAC1BhB,EAAKiB,YAAW5D,EAAI4D,UAAYjB,EAAKiB,WACrCjB,EAAKkB,OAAM7D,EAAI6D,KAAOlB,EAAKkB,MAC3BlB,EAAKmB,SAAQ9D,EAAI8D,OAASnB,EAAKmB,QAC/BnB,EAAKhK,OAAMqH,EAAIrH,KAAOgK,EAAKhK,MAC3BgK,EAAKoB,QAAO/D,EAAI+D,MAAQpB,EAAKoB,OAC7BpB,EAAK7M,OAAMkK,EAAIlK,KAAO6M,EAAK7M,MAC3B6M,EAAKS,cAAapD,EAAIoD,YAAcT,EAAKS,aACzCT,EAAKC,UAAS5C,EAAI4C,QAAUD,EAAKC,SACjCD,EAAKlJ,YAAWuG,EAAIvG,UAAYkJ,EAAKlJ,WAGzC,MAAMzM,EAAMqO,KAAKoF,EAAKT,EAAKyC,GAE3B,GADApH,KAAKqF,EAAmBV,EAAKhT,GACzBA,EAAIgU,eAAiBhU,EAAIgX,YAC3B,OAAO3I,KAAKyG,EACVW,EACAzV,EAAI5C,MACJ,aACAuY,EAAKF,GACLzC,EACAhT,EAGN,CAUF,OAAOqO,KAAKyG,EACVW,OACyBpd,IAAzBmd,EAAQD,aAA6B,KAAOC,EAAQD,aACpD,eAEJ,CAEQQ,EACNjN,EACAkN,EACAE,EACAjN,EACAkN,EACAC,GAEA,IAAKnN,QAAsB5Q,IAAb8d,EAAwB,OAAO,EAE7C,MAAMc,UAAEA,GAAc5I,KAAK6I,EACzBlB,EACAE,GAEF,IAAKe,EACH,OAAO,EAGT,MAAMjO,EAAIH,GAAKC,EAAMmO,EAAWb,GAAe,GAC/C,OAAU,OAANpN,IAEGC,EACHF,GAAQC,EAAGC,QACE5Q,IAAb8d,GACAnN,GAAKmN,EAEX,CAEQL,EAAiBrJ,GACvB,OAAOF,GAAc8B,KAAKhS,gBAAiBoQ,EAC7C,CAEQoJ,EAAeD,GACrB,OAAOA,EAAQ9K,MAAMxG,IACnB,MAAM2S,UAAEA,GAAc5I,KAAK6I,EAAkB5S,EAAOqD,WACpD,IAAKsP,EAAW,OAAO,EACvB,MAAMjO,EAAIH,GAAKvE,EAAOwE,KAAMmO,EAAW3S,EAAO8R,aAAe,GAC7D,OAAU,OAANpN,IACI1E,EAAOwS,OAAOhM,MAAMqM,GAAMpO,GAAQC,EAAGmO,IAAG,GAEpD,CAEQ1D,EACNF,EACA6D,GAEA,MAAMza,EAAM4W,EAAW5W,IACjB0a,EAAgB9D,EAAWiD,WAAW/P,OAG5C,GAAI4Q,EAAgB,EAGlB,OAAOhJ,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,IAA0B,IAAtB/I,KAAKe,EAAKmI,QAGZ,OAAOlJ,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAOhD,IAHA7D,EAAalF,KAAKmJ,EAAgBjE,IAIrBkE,cACVhO,GAAc4E,KAAKqJ,IAAkBnE,EAAWkE,aAMjD,OAAOpJ,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAMO,EF/pBH,SACLlC,EACA3Y,EACAua,GAEA,IAAKva,EACH,OAAO,KAGT,MAAM8a,EAAS9a,EAAIuH,MAAM,KAAK,GAC9B,IAAKuT,EACH,OAAO,KAGT,MAAM/N,EAAQ+N,EACXvO,QAAQ,MAAO,IACfhF,MAAM,KACNnH,KAAK2a,GAAOA,EAAGxT,MAAM,IAAK,KAC1BC,QAAO1L,IAAA,IAAEiS,GAAEjS,EAAA,OAAKiS,IAAM4K,CAAE,IACxBvY,KAAIhE,IAAA,IAAI0R,CAAAA,GAAE1R,EAAA,OAAK4e,SAASlN,EAAE,IAE7B,OAAIf,EAAMpD,OAAS,GAAKoD,EAAM,IAAM,GAAKA,EAAM,GAAKwN,EAC3CxN,EAAM,GAER,IACT,CEsoBuBkO,CACjBpb,EACA0R,KAAKqJ,IACLL,GAEF,GAAmB,OAAfM,EAMF,OAAOtJ,KAAKiJ,EAAW/D,EAAYoE,GAAY,EAAOP,GAIxD,GAAI/I,KAAKe,EAAKxP,kBAAoBjD,KAAO0R,KAAKe,EAAKxP,iBAOjD,OAAOyO,KAAKiJ,EAAW/D,EANLlF,KAAKe,EAAKxP,iBAAiBjD,IAMC,EAAOya,GAIvD,GAA0B,UAAtB7D,EAAWyE,SAA4C,IAAtBzE,EAAW0E,OAK9C,OAAO5J,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAMpB,cAAEA,EAAaiB,UAAEA,GAAc5I,KAAK6I,EACxC3D,EAAWyC,cACX3H,KAAKe,EAAKkD,sBAAwBiB,EAAW0C,uBACzC1C,EAAW2C,uBACX7d,GAEN,IAAK4e,EAKH,OAAO5I,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAGhD,IAAIc,GAAY,EAEZC,GAAoB,EACpBC,GAA+B,EACnC,GAAI/J,KAAKe,EAAKkD,sBAAwBiB,EAAW0C,uBAAwB,CACvE,MAAM5C,UAAEA,EAASgF,iBAAEA,GAAqBhK,KAAKiK,EAC3C/E,EAAW5W,IACX4W,EAAWmD,cACXnD,EAAWoD,iBACXpD,EAAWsD,MAEbsB,EAAoB9E,GAAa,EACjC6E,EAAW7E,EACX+E,IAAiCC,CACnC,CAGA,IAAKF,EAAmB,CAEtB,GAAI5E,EAAWqC,SACb,GAAIvH,KAAKwH,EAAetC,EAAWqC,SAKjC,OAAOvH,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,QAE3C,GACL7D,EAAWqD,YF55BZ,SACLK,EACAL,GAEA,MAAM5N,EAAIH,GAAK,KAAO+N,EAAU,GAAIK,EAAW,GAC/C,OAAU,OAANjO,GACGA,GAAK4N,EAAU,IAAM5N,EAAI4N,EAAU,EAC5C,CEs5BS2B,CAAYtB,EAAW1D,EAAWqD,WAMnC,OAAOvI,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GAAI7D,EAAWvJ,UF5tBd,SAAoBA,GACzB,IACE,OAAOA,GAIT,CAHE,MAAOvP,GAEP,OADA8O,QAAQC,MAAM/O,IACP,CACT,CACF,CEqtBiCmP,CAAW2J,EAAWvJ,SAK/C,OAAOqE,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GACE7D,EAAW9G,YACV4B,KAAKyH,EAAiBvC,EAAW9G,WAMlC,OAAO4B,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GACE7D,EAAWiF,SACVnK,KAAKoK,EAAiBlF,EAAWiF,QAMlC,OAAOnK,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,EAElD,CAGA,GAAI7D,EAAWzW,MAAQuR,KAAKqK,EAAYnF,EAAWzW,KAKjD,OAAOuR,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAMpO,EAAIH,GACR0K,EAAWzK,MAAQnM,EACnBsa,EACA1D,EAAW6C,aAAe,GAE5B,GAAU,OAANpN,EAKF,OAAOqF,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAehD,GAZKe,IAQHD,EF99BC,SAAyBlP,EAAW8N,GACzC,IAAK,IAAI/Y,EAAI,EAAGA,EAAI+Y,EAAOrQ,OAAQ1I,IACjC,GAAIgL,GAAQC,EAAG8N,EAAO/Y,IACpB,OAAOA,EAGX,OAAQ,CACV,CEu9BiB4a,CAAgB3P,EANzBuK,EAAWuD,QFn2BZ,SACLO,EACAlB,EACAM,IAEAN,OAAwB9d,IAAb8d,EAAyB,EAAIA,GAGzB,EAIbA,EAAW,EACFA,EAAW,IAIpBA,EAAW,GAIb,MAAMyC,GA5JwB5P,EA4JAqO,IA3JrB,EAAU,GACZ,IAAI3b,MAAMsN,GAAG6P,KAAK,EAAI7P,GAFxB,IAAyBA,GA6J9ByN,EAAUA,GAAWmC,GACTnS,SAAW4Q,IAMrBZ,EAAUmC,GAIZ,MAAME,EAAcrC,EAAQsC,QAAO,CAACC,EAAGC,IAAQA,EAAMD,GAAG,IACpDF,EAAc,KAAQA,EAAc,QAItCrC,EAAUmC,GAIZ,IAAIM,EAAa,EACjB,OAAOzC,EAAQvZ,KAAK8b,IAClB,MAAMG,EAAQD,EAEd,OADAA,GAAcF,EACP,CAACG,EAAOA,EAAShD,EAAsB6C,EAAE,GAEpD,CEozBQI,CACE/B,OACwBhf,IAAxBkb,EAAW4C,SAAyB,EAAI5C,EAAW4C,SACnD5C,EAAWkD,WAMb2B,EAKF,OAAO/J,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,OAAW/e,GAAW,GAItE,GAAI6f,EAAW,EAKb,OAAO7J,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GAAI,UAAW7D,EAMb,OAAOlF,KAAKiJ,EACV/D,OACqBlb,IAArBkb,EAAWgD,OAAuB,EAAIhD,EAAWgD,OACjD,EACAa,GAKJ,GAAI/I,KAAKe,EAAKiK,OAKZ,OAAOhL,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,GAA0B,YAAtB7D,EAAWyE,OAKb,OAAO3J,KAAKiJ,EAAW/D,GAAa,GAAG,EAAO6D,GAIhD,MAAM5D,EAASnF,KAAKiJ,EAClB/D,EACA2E,GACA,EACAd,EACApO,EACAmP,GAIF,GAAI9J,KAAKe,EAAKkD,sBAAwBiB,EAAW0C,uBAAwB,CACvE,MAAMqD,QACJA,EACA3c,IAAK4c,EAAOhL,IACZA,GACEF,KAAKmL,EACPxD,EACA/J,GAASgL,GACT,CACE,CAAC5I,KAAKoL,EACJlG,EAAW5W,IACX4W,EAAWmD,gBACTlD,EAAO7W,MAGX2c,IAEFjL,KAAKe,EAAKwD,2BACRvE,KAAKe,EAAKwD,4BAA8B,GAC1CvE,KAAKe,EAAKwD,2BAA2B2G,GAAWhL,EAEhDF,KAAKe,EAAKkD,oBAAoBoH,gBAAgBnL,GAElD,CAWA,OARAF,KAAKiI,EAAO/C,EAAYC,GAQjBA,CACT,CAEAmG,IAAIC,EAAaC,GACVxL,KAAKmB,QACNnB,KAAKe,EAAKuK,IAAKtL,KAAKe,EAAKuK,IAAIC,EAAKC,GACjCtQ,QAAQoQ,IAAIC,EAAKC,GACxB,CAEQvD,EAAU/C,EAA2BC,GAC3C,IAAKnF,KAAKe,EAAK0K,iBAAkB,OAEjC,MAGMjP,EACJ2I,EAAOwC,cAAgBxC,EAAOyD,UAJpB1D,EAAW5W,IAI2B6W,EAAOY,YACzD,IAAI/F,KAAKiB,EAAoB3Q,IAAIkM,GAAjC,CACAwD,KAAKiB,EAAoBrP,IAAI4K,GAE7B,IACEwD,KAAKe,EAAK0K,iBAAiBvG,EAAYC,EAGzC,CAFE,MAAO/Y,GACP8O,QAAQC,MAAM/O,EAChB,CAPqC,CAQvC,CAEQ+c,EAAmBjE,GACzB,MAAM5W,EAAM4W,EAAW5W,IACjBod,EAAI1L,KAAKe,EAAKoD,UAWpB,OAVIuH,GAAKA,EAAEpd,IAEqB,iBAD9B4W,EAAa/W,OAAOwd,OAAO,CAAA,EAAIzG,EAAYwG,EAAEpd,KACvBG,MACpByW,EAAWzW,IAAMoM,GAEfqK,EAAWzW,MAKVyW,CACT,CAEQ2D,EAAkB7U,EAAe4X,GACvC,IAAIjE,EAAgB3T,GAAQ,KAExB4U,EAAiB,GAwBrB,OAtBI5I,KAAK0B,EAAoBiG,GAC3BiB,EAAY5I,KAAK0B,EAAoBiG,GAC5B3H,KAAKe,EAAKhT,WACnB6a,EAAY5I,KAAKe,EAAKhT,WAAW4Z,IAAkB,GAC1C3H,KAAKe,EAAK8K,OACnBjD,EAAY5I,KAAKe,EAAK8K,KAAKlE,IAAkB,KAI1CiB,GAAagD,IACZ5L,KAAK0B,EAAoBkK,GAC3BhD,EAAY5I,KAAK0B,EAAoBkK,GAC5B5L,KAAKe,EAAKhT,WACnB6a,EAAY5I,KAAKe,EAAKhT,WAAW6d,IAAa,GACrC5L,KAAKe,EAAK8K,OACnBjD,EAAY5I,KAAKe,EAAK8K,KAAKD,IAAa,IAEtChD,IACFjB,EAAgBiE,IAIb,CAAEjE,gBAAeiB,YAC1B,CAEQK,EACN/D,EACA4G,EACAC,EACAhD,EACAiD,EACAC,GAEA,IAAItG,GAAe,GAEfmG,EAAiB,GAAKA,GAAkB5G,EAAWiD,WAAW/P,UAChE0T,EAAiB,EACjBnG,GAAe,GAGjB,MAAMgC,cAAEA,EAAaiB,UAAEA,GAAc5I,KAAK6I,EACxC3D,EAAWyC,cACX3H,KAAKe,EAAKkD,sBAAwBiB,EAAW0C,uBACzC1C,EAAW2C,uBACX7d,GAGAwe,EAA+BtD,EAAWsD,KAC5CtD,EAAWsD,KAAKsD,GAChB,GAEEna,EAAiB,CACrBrD,IAAKka,EAAKla,KAAO,GAAKwd,EACtB/C,YACApD,eACAoG,WACAhG,YAAa+F,EACb/c,MAAOmW,EAAWiD,WAAW2D,GAC7BnE,gBACAiB,YACAqD,mBAAoBA,GAOtB,OAJIzD,EAAKlL,OAAM3L,EAAI2L,KAAOkL,EAAKlL,WAChBtT,IAAXgiB,IAAsBra,EAAIqa,OAASA,GACnCxD,EAAKG,cAAahX,EAAIgX,YAAcH,EAAKG,aAEtChX,CACT,CAEQ0X,IACN,OAAOrJ,KAAKe,EAAKtS,MAAQ+Q,GAAYhU,OAAO0gB,SAASnQ,KAAO,GAC9D,CAEQsO,EAAY8B,GAClB,MAAM1d,EAAMuR,KAAKqJ,IACjB,IAAK5a,EAAK,OAAO,EAEjB,MAAM2d,EAAW3d,EAAIuM,QAAQ,eAAgB,IAAIA,QAAQ,WAAY,KAErE,QAAImR,EAASpS,KAAKtL,MACd0d,EAASpS,KAAKqS,EAEpB,CAEQhC,EAAiBiC,GACvB,MAAMlC,EAASnK,KAAKe,EAAKoJ,QAAU,CAAA,EACnC,IAAK,IAAIza,EAAI,EAAGA,EAAI2c,EAAUjU,OAAQ1I,IACpC,GAAIya,EAAOkC,EAAU3c,IAAK,OAAO,EAEnC,OAAO,CACT,CAEQmW,EAAiByG,GACvB,IAAK9M,GAAW,OAChB,MAAMoF,EAAuB,GAC7B,GAAI0H,EAAQC,IAAK,CACf,MAAM7H,EAAIjZ,SAASmK,cAAc,SACjC8O,EAAE7O,UAAYyW,EAAQC,IACtB9gB,SAAS+gB,KAAKC,YAAY/H,GAC1BE,EAAKrL,MAAK,IAAMmL,EAAEgI,UACpB,CACA,GAAIJ,EAAQK,GAAI,CACd,MAAMC,EAASnhB,SAASmK,cAAc,UACtCgX,EAAO/W,UAAYyW,EAAQK,GAC3BlhB,SAAS+gB,KAAKC,YAAYG,GAC1BhI,EAAKrL,MAAK,IAAMqT,EAAOF,UACzB,CAMA,OALIJ,EAAQO,cACVP,EAAQO,aAAahgB,SAASoM,IAC5B2L,EAAKrL,KHr0Bb,SAAA1O,GACEwO,IAAAA,EAAAA,EAAAA,SACAyT,EAAAA,EAAAA,OACA/d,EAAAA,EAAAA,MACWiF,EAAXsF,EAAAA,UACA/C,EAAAA,EAAAA,eACAC,EAAAA,EAAAA,qBAEA,GAAa,SAATxC,EAAiB,CACnB,GAAe,WAAX8Y,EACF,OAAOpX,GAAK2D,GAAU,SAAGjE,GAAA,OAAIA,SAAOrG,EAAAA,EAAS,GAApB,IACpB,GAAe,QAAX+d,EACT,OAAOpX,GAAK2D,GAAU,WAAA,OAAA,MAAMtK,EAAAA,EAAS,EAAf,GAEzB,MAAM,GAAa,UAATiF,EAAkB,CAC3B,GAAe,WAAX8Y,EACF,OAAOlV,GAAQyB,GAAU,SAAGjE,GACtBrG,GAAOqG,EAAIxD,IAAI7C,EACpB,IACI,GAAe,WAAX+d,EACT,OAAOlV,GAAQyB,GAAU,SAAGjE,GACtBrG,GAAOqG,EAAG,OAAQrG,EACvB,IACI,GAAe,QAAX+d,EACT,OAAOlV,GAAQyB,GAAU,SAAGjE,GAC1BA,EAAI/B,QACAtE,GAAOqG,EAAIxD,IAAI7C,EACpB,GAEJ,MAAM,GAAa,aAATiF,GACT,GAAe,QAAX8Y,GAAoBvW,EACtB,OAnFN,SACE8C,EACA5D,GAEA,OAAOiE,EAAY,CACjBR,KAAM,WACNzF,SAAU,IAAI7G,IACd6I,OA4E4B,WAAA,MAAO,CAC/Be,qBAAAA,EACAD,eAAAA,EAFwB,EA3E5B8C,SAAAA,GAEH,CAyEY/B,CAAS+B,OAKb,CACL,GAAe,WAAXyT,EACF,OAAOxT,GAAUD,EAAUrF,GAAM,SAAGoB,GAAA,OAC1B,OAARA,EAAeA,GAAG,MAAIrG,EAAAA,EAAS,IAAMA,MAAAA,EAAAA,EAAS,EADZ,IAG/B,GAAe,QAAX+d,EACT,OAAOxT,GAAUD,EAAUrF,GAAM,WAAA,OAAA,MAAMjF,EAAAA,EAAS,EAAf,IAC5B,GAAe,WAAX+d,EACT,OAAOxT,GAAUD,EAAUrF,GAAM,WAAA,OAAM,IAAN,GAEpC,CACD,OAAOT,CACR,CGqxBiBkC,CAAmBwD,GAAiCzF,OAAO,IAGlE,KACLoR,EAAK/X,SAASkgB,GAAOA,KAAK,CAE9B,CAEQC,GAAwCnd,GAC9C,MAAM9B,EAAa,IAAInB,IACjBmE,EAAWlB,GAAQA,EAAKkB,SAAWlB,EAAKkB,SAAWiP,KAAKhP,cACxDJ,EACJf,GAAQA,EAAKe,YAAcf,EAAKe,YAAcoP,KAAKnP,iBAoBrD,OAnBA1C,OAAOC,KAAK2C,GAAUlE,SAASua,IAC7B,MAAMD,EAAUpW,EAASqW,GACzB,GAAID,EAAQE,MACV,IAAK,MAAMC,KAAQH,EAAQE,MACrBC,EAAKa,aACPpa,EAAW6D,IAAI0V,EAAKK,eAAiB,MACjCL,EAAKO,mBACP9Z,EAAW6D,IAAI0V,EAAKO,mBAI5B,IAEFjX,EAAY/B,KAAKqW,IACfnX,EAAW6D,IAAIsT,EAAWyC,eAAiB,MACvCzC,EAAW2C,mBACb9Z,EAAW6D,IAAIsT,EAAW2C,kBAC5B,IAEKxa,MAAMC,KAAKS,EACpB,CAEAb,2BAAkC2C,GAChC,GAAImQ,KAAKe,EAAKkD,oBAAqB,CACjC,MAAMlW,EAAaiS,KAAKiN,GAA2Bpd,GACnDmQ,KAAKe,EAAKwD,iCAAmCvE,KAAKe,EAAKkD,oBAAoBiJ,kBACzEnf,EAEJ,CACF,CAEQof,KACN,MAAMC,EAAuC,CAAA,EAI7C,OAHAjf,OAAOkf,OAAOrN,KAAKe,EAAKwD,4BAA8B,IAAI1X,SAASqT,IAC7DA,EAAIO,aAAatS,OAAOwd,OAAOyB,EAAmBlN,EAAIO,YAAY,IAEjE2M,CACT,CAEQnD,EACNqD,EACAC,EACAC,EACAhF,GAMAgF,EAA6BA,GAA8B,EAC3DhF,EAAOA,GAAQ,GACf,MAAMpB,EAAKpH,KAAKoL,EACdkC,EAJFC,EAA0BA,GAA2B,GAO/C9M,EAAcT,KAAKmN,KAGzB,GAAIK,EAA6B,EAC/B,IAAK,IAAI9d,EAAI,EAAGA,GAAK8d,EAA4B9d,IAE/C,QAAgC1F,IAA5ByW,EADeT,KAAKoL,EAA8BkC,EAAe5d,IAEnE,MAAO,CACLsV,WAAY,EACZgF,kBAAkB,GAK1B,MAAMyD,EAAehN,EAAY2G,GACjC,QAAqBpd,IAAjByjB,EAEF,MAAO,CAAEzI,WAAY,GACvB,MAAMA,EAAYwD,EAAKkF,WAAWlY,GAAMA,EAAElH,MAAQmf,IAClD,OAAIzI,EAAY,EAEP,CAAEA,WAAY,GAEhB,CAAEA,YACX,CAEQoG,EACNkC,EACAC,GAGA,OADAA,EAA0BA,GAA2B,EAC3CD,GAAAA,OAAAA,eAAkBC,EAC9B,CAEQN,GACNpd,GAEA,MAAM9B,EAAqC,CAAA,EAS3C,OARAiS,KAAKe,EAAK4M,iCAAoC3N,KAAKe,EAChD4M,iCAEC3N,KAAKe,EAAK4M,iCADV3N,KAAKgN,GAAwCnd,GAEjDmQ,KAAKe,EAAK4M,iCAAiC9gB,SAASmH,IAClD,MAAM4U,UAAEA,GAAc5I,KAAK6I,EAAkB7U,GAC7CjG,EAAWiG,GAAQ4J,GAASgL,EAAU,IAEjC7a,CACT,CAEQod,EACNrL,EACAC,EACAU,GAMA,MAAMnS,EAAG,GAAA3D,OAAMmV,EAAa,MAAAnV,OAAKoV,GAC3B6N,EACJ5N,KAAKe,EAAKwD,4BACVvE,KAAKe,EAAKwD,2BAA2BjW,IACjC0R,KAAKe,EAAKwD,2BAA2BjW,GAAKmS,aAC1C,GACAoN,EAAiB,IAAKD,KAAwBnN,GAIpD,MAAO,CACLnS,MACA4R,IAAK,CACHJ,gBACAC,iBACAU,YAAaoN,GAEf5C,QATA/f,KAAKC,UAAUyiB,KAAyB1iB,KAAKC,UAAU0iB,GAW3D,qCCl1CK,cAA8ClO,GAGnDQ,YAAY2N,GACVA,EAAOA,GAAQ,GACfvN,QACAP,KAAKI,OAAS0N,EAAK1N,QAAU,oBAC7B,IACEJ,KAAK7T,aAAe2hB,EAAK3hB,cAAgBrC,WAAWqC,YAEpD,CADA,MAAOC,GACP,CAEJ,CACAc,qBAAqB4S,EAAuBC,GAC1C,MAAMzR,EAAG,GAAA3D,OAAMmV,EAAa,MAAAnV,OAAKoV,GACjC,IAAIG,EAAwC,KAC5C,IAAKF,KAAK7T,aAAc,OAAO+T,EAC/B,IACE,MAAMM,QAAaR,KAAK7T,aAAa+W,QAAQlD,KAAKI,OAAS9R,IAAS,KAC9DuB,EAAO3E,KAAKsH,MAAMgO,GACpB3Q,EAAKiQ,eAAiBjQ,EAAKkQ,gBAAkBlQ,EAAK4Q,cACpDP,EAAMrQ,EAGR,CADA,MAAOzD,GACP,CAEF,OAAO8T,CACT,CACAhT,sBAAsBgT,GACpB,MAAM5R,YAAS4R,EAAIJ,cAAkBI,MAAAA,OAAAA,EAAIH,gBACzC,GAAKC,KAAK7T,aACV,UACQ6T,KAAK7T,aAAaiB,QAAQ4S,KAAKI,OAAS9R,EAAKpD,KAAKC,UAAU+U,GAElE,CADA,MAAO9T,GACP,CAEJ,8BA2GK,cAAuCuT,GAG5CQ,YAAiDgD,GAAA,IAArC4K,MAAEA,GAAiC5K,EAC7C5C,QACAP,KAAK+N,MAAQA,CACf,CAEA7gB,wBACEa,GAEA,MAAM6R,EAA8D,CAAA,EAC9DxR,EAAOD,OAAOZ,QAAQQ,GAAYc,KACtCC,IAAA,IAAEgR,EAAeC,GAAejR,EAAA,MAAQgR,GAAAA,OAAAA,eAAkBC,EAAc,IAE1E,OAAKC,KAAK+N,OACV/N,KAAK+N,MAAMC,QAAQ5f,GAAMsD,MAAM2b,IAC7BA,EAAOxgB,SAAS2T,IACd,IACE,MAAM3Q,EAAO3E,KAAKsH,MAAMgO,GAAO,MAC/B,GAAI3Q,EAAKiQ,eAAiBjQ,EAAKkQ,gBAAkBlQ,EAAK4Q,YAAa,CACjE,MAAMnS,YAASuB,EAAKiQ,cAAkBjQ,MAAAA,OAAAA,EAAKkQ,gBAC3CH,EAAKtR,GAAOuB,CACd,CAEA,CADA,MAAOzD,GACP,IAEF,IAEGwT,GAdiBA,CAe1B,CAEA1S,qBAAqB+gB,EAAwBC,GAE3C,OAAO,IACT,CAEAhhB,sBAAsBgT,GACpB,MAAM5R,YAAS4R,EAAIJ,cAAkBI,MAAAA,OAAAA,EAAIH,gBACpCC,KAAK+N,aACJ/N,KAAK+N,MAAM3d,IAAI9B,EAAKpD,KAAKC,UAAU+U,GAC3C,yCLhJKhT,iBACLV,EAAM6G,QACN5G,EAAc4G,QACdD,IACA7G,GAAmB,QACbY,GACR,mBAbO,SAAwBgX,GAC7BhW,OAAOwd,OAAOviB,EAAe+a,GACxB/a,EAAcI,gBACjB4J,GAEJ,2EARO,SAAsB+Q,GAC3BhW,OAAOwd,OAAO/hB,EAAWua,EAC3B"}