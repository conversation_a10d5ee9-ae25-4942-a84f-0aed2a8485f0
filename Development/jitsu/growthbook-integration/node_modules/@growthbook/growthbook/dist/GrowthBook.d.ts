import type { ApiHost, Attributes, AutoExperiment, AutoExperimentVariation, ClientKey, Context, Experiment, FeatureApiResponse, FeatureDefinition, FeatureResult, LoadFeaturesOptions, RefreshFeaturesOptions, Result, StickyAssignmentsDocument, SubscriptionFunction, WidenPrimitives } from "./types/growthbook";
export declare class GrowthBook<AppFeatures extends Record<string, any> = Record<string, any>> {
    private context;
    debug: boolean;
    ready: boolean;
    version: string;
    private _ctx;
    private _renderer;
    private _trackedExperiments;
    private _trackedFeatures;
    private _subscriptions;
    private _rtQueue;
    private _rtTimer;
    private _assigned;
    private _forcedFeatureValues;
    private _attributeOverrides;
    private _activeAutoExperiments;
    private _triggeredExpKeys;
    private _loadFeaturesCalled;
    constructor(context?: Context);
    loadFeatures(options?: LoadFeaturesOptions): Promise<void>;
    refreshFeatures(options?: RefreshFeaturesOptions): Promise<void>;
    getApiInfo(): [ApiHost, ClientKey];
    getApiHosts(): {
        apiHost: string;
        streamingHost: string;
        apiRequestHeaders?: Record<string, string>;
        streamingHostRequestHeaders?: Record<string, string>;
    };
    getClientKey(): string;
    isRemoteEval(): boolean;
    getCacheKeyAttributes(): (keyof Attributes)[] | undefined;
    private _refresh;
    private _render;
    setFeatures(features: Record<string, FeatureDefinition>): void;
    setEncryptedFeatures(encryptedString: string, decryptionKey?: string, subtle?: SubtleCrypto): Promise<void>;
    setExperiments(experiments: AutoExperiment[]): void;
    setEncryptedExperiments(encryptedString: string, decryptionKey?: string, subtle?: SubtleCrypto): Promise<void>;
    decryptPayload(data: FeatureApiResponse, decryptionKey?: string, subtle?: SubtleCrypto): Promise<FeatureApiResponse>;
    setAttributes(attributes: Attributes): Promise<void>;
    setAttributeOverrides(overrides: Attributes): Promise<void>;
    setForcedVariations(vars: Record<string, number>): Promise<void>;
    setForcedFeatures(map: Map<string, any>): void;
    setURL(url: string): Promise<void>;
    getAttributes(): {
        [x: string]: any;
    };
    getForcedVariations(): Record<string, number>;
    getForcedFeatures(): Map<string, any>;
    getStickyBucketAssignmentDocs(): Record<string, StickyAssignmentsDocument>;
    getUrl(): string;
    getFeatures(): Record<string, FeatureDefinition<any>>;
    getExperiments(): AutoExperiment[];
    subscribe(cb: SubscriptionFunction): () => void;
    private _canSubscribe;
    private _refreshForRemoteEval;
    getAllResults(): Map<string, {
        experiment: Experiment<any>;
        result: Result<any>;
    }>;
    destroy(): void;
    setRenderer(renderer: () => void): void;
    forceVariation(key: string, variation: number): void;
    run<T>(experiment: Experiment<T>): Result<T>;
    triggerExperiment(key: string): (Result<AutoExperimentVariation> | null)[] | null;
    private _runAutoExperiment;
    private _undoActiveAutoExperiment;
    private _updateAllAutoExperiments;
    private _fireSubscriptions;
    private _trackFeatureUsage;
    private _getFeatureResult;
    isOn<K extends string & keyof AppFeatures = string>(key: K): boolean;
    isOff<K extends string & keyof AppFeatures = string>(key: K): boolean;
    getFeatureValue<V extends AppFeatures[K], K extends string & keyof AppFeatures = string>(key: K, defaultValue: V): WidenPrimitives<V>;
    /**
     * @deprecated Use {@link evalFeature}
     * @param id
     */
    feature<V extends AppFeatures[K], K extends string & keyof AppFeatures = string>(id: K): FeatureResult<V | null>;
    evalFeature<V extends AppFeatures[K], K extends string & keyof AppFeatures = string>(id: K): FeatureResult<V | null>;
    private _isIncludedInRollout;
    private _conditionPasses;
    private _isFilteredOut;
    private _run;
    log(msg: string, ctx: Record<string, unknown>): void;
    private _track;
    private _mergeOverrides;
    private _getHashAttribute;
    private _getResult;
    private _getContextUrl;
    private _urlIsValid;
    private _hasGroupOverlap;
    private _applyDOMChanges;
    private _deriveStickyBucketIdentifierAttributes;
    refreshStickyBuckets(data?: FeatureApiResponse): Promise<void>;
    private _getStickyBucketAssignments;
    private _getStickyBucketVariation;
    private _getStickyBucketExperimentKey;
    private _getStickyBucketAttributes;
    private _generateStickyBucketAssignmentDoc;
}
//# sourceMappingURL=GrowthBook.d.ts.map