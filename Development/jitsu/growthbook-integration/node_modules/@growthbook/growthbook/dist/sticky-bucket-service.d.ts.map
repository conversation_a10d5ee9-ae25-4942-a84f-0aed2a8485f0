{"version": 3, "file": "sticky-bucket-service.d.ts", "sourceRoot": "", "sources": ["../src/sticky-bucket-service.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,kBAAkB,EAClB,yBAAyB,EACzB,kBAAkB,EACnB,MAAM,oBAAoB,CAAC;AAE5B,MAAM,WAAW,gBAAgB;IAC/B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,CAAC;IACpC,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC1B,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;IAC5B,MAAM,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IAC7B,QAAQ,CAAC,EAAE,QAAQ,GAAG,QAAQ,GAAG,KAAK,GAAG,KAAK,GAAG,MAAM,GAAG,MAAM,GAAG,SAAS,CAAC;IAE7E,CAAC,QAAQ,EAAE,MAAM,GAAG,GAAG,CAAC;CACzB;AACD,MAAM,WAAW,eAAe,CAAC,CAAC,GAAG,MAAM;IACzC,GAAG,CACD,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,GAAG,CAAC,EACjB,OAAO,CAAC,EAAE,gBAAgB,GACzB,MAAM,GAAG,SAAS,CAAC;IACtB,GAAG,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM,GAAG,CAAC,GAAG,SAAS,CAAC;IAC1C,GAAG,IAAI;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;IACjC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,gBAAgB,GAAG,IAAI,CAAC;CACxD;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;IAC3C,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CAClD;AAED,MAAM,WAAW,aAAa;IAC5B,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAChC,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;CACxB;AACD,MAAM,WAAW,cAAc;IAC7B,MAAM,CACJ,IAAI,EAAE,MAAM,EACZ,KAAK,EAAE,MAAM,EACb,OAAO,CAAC,EAAE,gBAAgB,GACzB,cAAc,CAAC;IAClB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;CACxB;AAED;;GAEG;AACH,8BAAsB,mBAAmB;IACvC,QAAQ,CAAC,cAAc,CACrB,aAAa,EAAE,MAAM,EACrB,cAAc,EAAE,MAAM,GACrB,OAAO,CAAC,yBAAyB,GAAG,IAAI,CAAC;IAE5C,QAAQ,CAAC,eAAe,CAAC,GAAG,EAAE,yBAAyB,GAAG,OAAO,CAAC,OAAO,CAAC;IAE1E;;;;OAIG;IACG,iBAAiB,CACrB,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GACjC,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;CAgBlE;AAED,qBAAa,+BAAgC,SAAQ,mBAAmB;IACtE,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,YAAY,CAAiC;gBACzC,IAAI,CAAC,EAAE;QAAE,MAAM,CAAC,EAAE,MAAM,CAAC;QAAC,YAAY,CAAC,EAAE,kBAAkB,CAAA;KAAE;IAUnE,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM;IAe5D,eAAe,CAAC,GAAG,EAAE,yBAAyB;CASrD;AAED,qBAAa,gCAAiC,SAAQ,mBAAmB;IACvE;;;;;;OAMG;IACH,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,GAAG,CAAgB;IAC3B,OAAO,CAAC,GAAG,CAAiB;IAC5B,OAAO,CAAC,gBAAgB,CAAmB;gBAC/B,EACV,MAA4B,EAC5B,GAAG,EACH,GAAG,EACH,gBAAqB,GACtB,EAAE;QACD,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,GAAG,EAAE,aAAa,CAAC;QACnB,GAAG,EAAE,cAAc,CAAC;QACpB,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;KACrC;IAOK,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM;IAe5D,eAAe,CAAC,GAAG,EAAE,yBAAyB;CAUrD;AAED,qBAAa,gCAAiC,SAAQ,mBAAmB;IACvE;;;;;;OAMG;IACH,OAAO,CAAC,MAAM,CAAS;IACvB,OAAO,CAAC,QAAQ,CAAkB;IAClC,OAAO,CAAC,gBAAgB,CAAmB;gBAC/B,EACV,MAA4B,EAC5B,QAAQ,EACR,gBAAqB,GACtB,EAAE;QACD,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,QAAQ,EAAE,eAAe,CAAC;QAC1B,gBAAgB,CAAC,EAAE,gBAAgB,CAAC;KACrC;IAMK,cAAc,CAAC,aAAa,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM;IAe5D,eAAe,CAAC,GAAG,EAAE,yBAAyB;CAMrD;AAED,qBAAa,wBAAyB,SAAQ,mBAAmB;IAC/D,gDAAgD;IAChD,OAAO,CAAC,KAAK,CAA4B;gBAC7B,EAAE,KAAK,EAAE,EAAE;QAAE,KAAK,EAAE,aAAa,CAAA;KAAE;IAKzC,iBAAiB,CACrB,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GACjC,OAAO,CAAC,MAAM,CAAC,kBAAkB,EAAE,yBAAyB,CAAC,CAAC;IAsB3D,cAAc,CAAC,cAAc,EAAE,MAAM,EAAE,eAAe,EAAE,MAAM;IAK9D,eAAe,CAAC,GAAG,EAAE,yBAAyB;CAKrD"}