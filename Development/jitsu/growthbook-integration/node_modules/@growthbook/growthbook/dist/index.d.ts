export type { Context, Attributes, Polyfills, CacheSettings, FeatureApiResponse, LoadFeaturesOptions, RefreshFeaturesOptions, FeatureDefinitions, FeatureDefinition, FeatureRule, FeatureResult, FeatureResultSource, Experiment, Result, ExperimentOverride, ExperimentStatus, JSONValue, SubscriptionFunction, LocalStorageCompat, WidenPrimitives, VariationMeta, Filter, VariationRange, UrlTarget, AutoExperiment, AutoExperimentVariation, UrlTargetType, } from "./types/growthbook";
export type { ConditionInterface } from "./types/mongrule";
export { setPolyfills, clearCache, configureCache, helpers, onVisible, onHidden, } from "./feature-repository";
export { GrowthBook } from "./GrowthBook";
export { StickyBucketService, LocalStorageStickyBucketService, ExpressCookieStickyBucketService, BrowserCookieStickyBucketService, RedisStickyBucketService, } from "./sticky-bucket-service";
export { isURLTargeted } from "./util";
//# sourceMappingURL=index.d.ts.map