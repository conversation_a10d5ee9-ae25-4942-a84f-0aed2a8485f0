{"version": 3, "file": "util.js", "names": ["hashFnv32a", "str", "hval", "l", "length", "i", "charCodeAt", "hash", "seed", "value", "version", "getEqualWeights", "n", "Array", "fill", "inRange", "range", "inNamespace", "hashValue", "namespace", "chooseVariation", "ranges", "getUrlRegExp", "regexString", "escaped", "replace", "RegExp", "e", "console", "error", "undefined", "isURLTargeted", "url", "targets", "hasIncludeRules", "isIncluded", "match", "_evalURLTarget", "type", "pattern", "include", "_evalSimpleUrlPart", "actual", "isPath", "regex", "test", "_evalSimpleUrlTarget", "expected", "URL", "comps", "host", "pathname", "push", "searchParams", "for<PERSON>ach", "v", "k", "get", "some", "data", "parsed", "href", "substring", "origin", "getBucketRanges", "numVariations", "coverage", "weights", "process", "env", "NODE_ENV", "equal", "totalWeight", "reduce", "w", "sum", "cumulative", "map", "start", "getQueryStringOverride", "id", "search", "split", "kv", "filter", "parseInt", "base64ToBuf", "b", "Uint8Array", "from", "atob", "c", "decrypt", "encryptedString", "decryptionKey", "subtle", "globalThis", "crypto", "Error", "key", "importKey", "name", "iv", "cipherText", "plainTextBuffer", "TextDecoder", "decode", "toString", "input", "JSON", "stringify", "paddedVersionString", "parts", "padStart", "join", "loadSDKVersion"], "sources": ["../../src/util.ts"], "sourcesContent": ["import { UrlTarget, UrlTargetType, VariationRange } from \"./types/growthbook\";\n\nfunction hashFnv32a(str: string): number {\n  let hval = 0x811c9dc5;\n  const l = str.length;\n\n  for (let i = 0; i < l; i++) {\n    hval ^= str.charCodeAt(i);\n    hval +=\n      (hval << 1) + (hval << 4) + (hval << 7) + (hval << 8) + (hval << 24);\n  }\n  return hval >>> 0;\n}\n\nexport function hash(\n  seed: string,\n  value: string,\n  version: number\n): number | null {\n  // New unbiased hashing algorithm\n  if (version === 2) {\n    return (hashFnv32a(hashFnv32a(seed + value) + \"\") % 10000) / 10000;\n  }\n  // Original biased hashing algorithm (keep for backwards compatibility)\n  if (version === 1) {\n    return (hashFnv32a(value + seed) % 1000) / 1000;\n  }\n\n  // Unknown hash version\n  return null;\n}\n\nexport function getEqualWeights(n: number): number[] {\n  if (n <= 0) return [];\n  return new Array(n).fill(1 / n);\n}\n\nexport function inRange(n: number, range: VariationRange): boolean {\n  return n >= range[0] && n < range[1];\n}\n\nexport function inNamespace(\n  hashValue: string,\n  namespace: [string, number, number]\n): boolean {\n  const n = hash(\"__\" + namespace[0], hashValue, 1);\n  if (n === null) return false;\n  return n >= namespace[1] && n < namespace[2];\n}\n\nexport function chooseVariation(n: number, ranges: VariationRange[]): number {\n  for (let i = 0; i < ranges.length; i++) {\n    if (inRange(n, ranges[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\n\nexport function getUrlRegExp(regexString: string): RegExp | undefined {\n  try {\n    const escaped = regexString.replace(/([^\\\\])\\//g, \"$1\\\\/\");\n    return new RegExp(escaped);\n  } catch (e) {\n    console.error(e);\n    return undefined;\n  }\n}\n\nexport function isURLTargeted(url: string, targets: UrlTarget[]) {\n  if (!targets.length) return false;\n  let hasIncludeRules = false;\n  let isIncluded = false;\n\n  for (let i = 0; i < targets.length; i++) {\n    const match = _evalURLTarget(url, targets[i].type, targets[i].pattern);\n    if (targets[i].include === false) {\n      if (match) return false;\n    } else {\n      hasIncludeRules = true;\n      if (match) isIncluded = true;\n    }\n  }\n\n  return isIncluded || !hasIncludeRules;\n}\n\nfunction _evalSimpleUrlPart(\n  actual: string,\n  pattern: string,\n  isPath: boolean\n): boolean {\n  try {\n    // Escape special regex characters and change wildcard `_____` to `.*`\n    let escaped = pattern\n      .replace(/[*.+?^${}()|[\\]\\\\]/g, \"\\\\$&\")\n      .replace(/_____/g, \".*\");\n\n    if (isPath) {\n      // When matching pathname, make leading/trailing slashes optional\n      escaped = \"\\\\/?\" + escaped.replace(/(^\\/|\\/$)/g, \"\") + \"\\\\/?\";\n    }\n\n    const regex = new RegExp(\"^\" + escaped + \"$\", \"i\");\n    return regex.test(actual);\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalSimpleUrlTarget(actual: URL, pattern: string) {\n  try {\n    // If a protocol is missing, but a host is specified, add `https://` to the front\n    // Use \"_____\" as the wildcard since `*` is not a valid hostname in some browsers\n    const expected = new URL(\n      pattern.replace(/^([^:/?]*)\\./i, \"https://$1.\").replace(/\\*/g, \"_____\"),\n      \"https://_____\"\n    );\n\n    // Compare each part of the URL separately\n    const comps: Array<[string, string, boolean]> = [\n      [actual.host, expected.host, false],\n      [actual.pathname, expected.pathname, true],\n    ];\n    // We only want to compare hashes if it's explicitly being targeted\n    if (expected.hash) {\n      comps.push([actual.hash, expected.hash, false]);\n    }\n\n    expected.searchParams.forEach((v, k) => {\n      comps.push([actual.searchParams.get(k) || \"\", v, false]);\n    });\n\n    // If any comparisons fail, the whole thing fails\n    return !comps.some(\n      (data) => !_evalSimpleUrlPart(data[0], data[1], data[2])\n    );\n  } catch (e) {\n    return false;\n  }\n}\n\nfunction _evalURLTarget(\n  url: string,\n  type: UrlTargetType,\n  pattern: string\n): boolean {\n  try {\n    const parsed = new URL(url, \"https://_\");\n\n    if (type === \"regex\") {\n      const regex = getUrlRegExp(pattern);\n      if (!regex) return false;\n      return (\n        regex.test(parsed.href) ||\n        regex.test(parsed.href.substring(parsed.origin.length))\n      );\n    } else if (type === \"simple\") {\n      return _evalSimpleUrlTarget(parsed, pattern);\n    }\n\n    return false;\n  } catch (e) {\n    return false;\n  }\n}\n\nexport function getBucketRanges(\n  numVariations: number,\n  coverage: number | undefined,\n  weights?: number[]\n): VariationRange[] {\n  coverage = coverage === undefined ? 1 : coverage;\n\n  // Make sure coverage is within bounds\n  if (coverage < 0) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be greater than or equal to 0\");\n    }\n    coverage = 0;\n  } else if (coverage > 1) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.coverage must be less than or equal to 1\");\n    }\n    coverage = 1;\n  }\n\n  // Default to equal weights if missing or invalid\n  const equal = getEqualWeights(numVariations);\n  weights = weights || equal;\n  if (weights.length !== numVariations) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\n        \"Experiment.weights array must be the same length as Experiment.variations\"\n      );\n    }\n    weights = equal;\n  }\n\n  // If weights don't add up to 1 (or close to it), default to equal weights\n  const totalWeight = weights.reduce((w, sum) => sum + w, 0);\n  if (totalWeight < 0.99 || totalWeight > 1.01) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.error(\"Experiment.weights must add up to 1\");\n    }\n    weights = equal;\n  }\n\n  // Covert weights to ranges\n  let cumulative = 0;\n  return weights.map((w) => {\n    const start = cumulative;\n    cumulative += w;\n    return [start, start + (coverage as number) * w];\n  }) as VariationRange[];\n}\n\nexport function getQueryStringOverride(\n  id: string,\n  url: string,\n  numVariations: number\n) {\n  if (!url) {\n    return null;\n  }\n\n  const search = url.split(\"?\")[1];\n  if (!search) {\n    return null;\n  }\n\n  const match = search\n    .replace(/#.*/, \"\") // Get rid of anchor\n    .split(\"&\") // Split into key/value pairs\n    .map((kv) => kv.split(\"=\", 2))\n    .filter(([k]) => k === id) // Look for key that matches the experiment id\n    .map(([, v]) => parseInt(v)); // Parse the value into an integer\n\n  if (match.length > 0 && match[0] >= 0 && match[0] < numVariations)\n    return match[0];\n\n  return null;\n}\n\nexport function isIncluded(include: () => boolean) {\n  try {\n    return include();\n  } catch (e) {\n    console.error(e);\n    return false;\n  }\n}\n\nconst base64ToBuf = (b: string) =>\n  Uint8Array.from(atob(b), (c) => c.charCodeAt(0));\n\nexport async function decrypt(\n  encryptedString: string,\n  decryptionKey?: string,\n  subtle?: SubtleCrypto\n): Promise<string> {\n  decryptionKey = decryptionKey || \"\";\n  subtle = subtle || (globalThis.crypto && globalThis.crypto.subtle);\n  if (!subtle) {\n    throw new Error(\"No SubtleCrypto implementation found\");\n  }\n  try {\n    const key = await subtle.importKey(\n      \"raw\",\n      base64ToBuf(decryptionKey),\n      { name: \"AES-CBC\", length: 128 },\n      true,\n      [\"encrypt\", \"decrypt\"]\n    );\n    const [iv, cipherText] = encryptedString.split(\".\");\n    const plainTextBuffer = await subtle.decrypt(\n      { name: \"AES-CBC\", iv: base64ToBuf(iv) },\n      key,\n      base64ToBuf(cipherText)\n    );\n\n    return new TextDecoder().decode(plainTextBuffer);\n  } catch (e) {\n    throw new Error(\"Failed to decrypt\");\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function toString(input: any): string {\n  if (typeof input === \"string\") return input;\n  return JSON.stringify(input);\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function paddedVersionString(input: any): string {\n  if (typeof input === \"number\") {\n    input = input + \"\";\n  }\n  if (!input || typeof input !== \"string\") {\n    input = \"0\";\n  }\n  // Remove build info and leading `v` if any\n  // Split version into parts (both core version numbers and pre-release tags)\n  // \"v1.2.3-rc.1+build123\" -> [\"1\",\"2\",\"3\",\"rc\",\"1\"]\n  const parts = (input as string).replace(/(^v|\\+.*$)/g, \"\").split(/[-.]/);\n\n  // If it's SemVer without a pre-release, add `~` to the end\n  // [\"1\",\"0\",\"0\"] -> [\"1\",\"0\",\"0\",\"~\"]\n  // \"~\" is the largest ASCII character, so this will make \"1.0.0\" greater than \"1.0.0-beta\" for example\n  if (parts.length === 3) {\n    parts.push(\"~\");\n  }\n\n  // Left pad each numeric part with spaces so string comparisons will work (\"9\">\"10\", but \" 9\"<\"10\")\n  // Then, join back together into a single string\n  return parts\n    .map((v) => (v.match(/^[0-9]+$/) ? v.padStart(5, \" \") : v))\n    .join(\"-\");\n}\n\nexport function loadSDKVersion(): string {\n  let version: string;\n  try {\n    // @ts-expect-error right-hand value to be replaced by build with string literal\n    version = __SDK_VERSION__;\n  } catch (e) {\n    version = \"\";\n  }\n  return version;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA,SAASA,UAAU,CAACC,GAAW,EAAU;EACvC,IAAIC,IAAI,GAAG,UAAU;EACrB,MAAMC,CAAC,GAAGF,GAAG,CAACG,MAAM;EAEpB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,EAAEE,CAAC,EAAE,EAAE;IAC1BH,IAAI,IAAID,GAAG,CAACK,UAAU,CAACD,CAAC,CAAC;IACzBH,IAAI,IACF,CAACA,IAAI,IAAI,CAAC,KAAKA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,CAAC,CAAC,IAAIA,IAAI,IAAI,EAAE,CAAC;EACxE;EACA,OAAOA,IAAI,KAAK,CAAC;AACnB;AAEO,SAASK,IAAI,CAClBC,IAAY,EACZC,KAAa,EACbC,OAAe,EACA;EACf;EACA,IAAIA,OAAO,KAAK,CAAC,EAAE;IACjB,OAAQV,UAAU,CAACA,UAAU,CAACQ,IAAI,GAAGC,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,KAAK,GAAI,KAAK;EACpE;EACA;EACA,IAAIC,OAAO,KAAK,CAAC,EAAE;IACjB,OAAQV,UAAU,CAACS,KAAK,GAAGD,IAAI,CAAC,GAAG,IAAI,GAAI,IAAI;EACjD;;EAEA;EACA,OAAO,IAAI;AACb;AAEO,SAASG,eAAe,CAACC,CAAS,EAAY;EACnD,IAAIA,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE;EACrB,OAAO,IAAIC,KAAK,CAACD,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,GAAGF,CAAC,CAAC;AACjC;AAEO,SAASG,OAAO,CAACH,CAAS,EAAEI,KAAqB,EAAW;EACjE,OAAOJ,CAAC,IAAII,KAAK,CAAC,CAAC,CAAC,IAAIJ,CAAC,GAAGI,KAAK,CAAC,CAAC,CAAC;AACtC;AAEO,SAASC,WAAW,CACzBC,SAAiB,EACjBC,SAAmC,EAC1B;EACT,MAAMP,CAAC,GAAGL,IAAI,CAAC,IAAI,GAAGY,SAAS,CAAC,CAAC,CAAC,EAAED,SAAS,EAAE,CAAC,CAAC;EACjD,IAAIN,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK;EAC5B,OAAOA,CAAC,IAAIO,SAAS,CAAC,CAAC,CAAC,IAAIP,CAAC,GAAGO,SAAS,CAAC,CAAC,CAAC;AAC9C;AAEO,SAASC,eAAe,CAACR,CAAS,EAAES,MAAwB,EAAU;EAC3E,KAAK,IAAIhB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgB,MAAM,CAACjB,MAAM,EAAEC,CAAC,EAAE,EAAE;IACtC,IAAIU,OAAO,CAACH,CAAC,EAAES,MAAM,CAAChB,CAAC,CAAC,CAAC,EAAE;MACzB,OAAOA,CAAC;IACV;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEO,SAASiB,YAAY,CAACC,WAAmB,EAAsB;EACpE,IAAI;IACF,MAAMC,OAAO,GAAGD,WAAW,CAACE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC;IAC1D,OAAO,IAAIC,MAAM,CAACF,OAAO,CAAC;EAC5B,CAAC,CAAC,OAAOG,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;IAChB,OAAOG,SAAS;EAClB;AACF;AAEO,SAASC,aAAa,CAACC,GAAW,EAAEC,OAAoB,EAAE;EAC/D,IAAI,CAACA,OAAO,CAAC7B,MAAM,EAAE,OAAO,KAAK;EACjC,IAAI8B,eAAe,GAAG,KAAK;EAC3B,IAAIC,UAAU,GAAG,KAAK;EAEtB,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,OAAO,CAAC7B,MAAM,EAAEC,CAAC,EAAE,EAAE;IACvC,MAAM+B,KAAK,GAAGC,cAAc,CAACL,GAAG,EAAEC,OAAO,CAAC5B,CAAC,CAAC,CAACiC,IAAI,EAAEL,OAAO,CAAC5B,CAAC,CAAC,CAACkC,OAAO,CAAC;IACtE,IAAIN,OAAO,CAAC5B,CAAC,CAAC,CAACmC,OAAO,KAAK,KAAK,EAAE;MAChC,IAAIJ,KAAK,EAAE,OAAO,KAAK;IACzB,CAAC,MAAM;MACLF,eAAe,GAAG,IAAI;MACtB,IAAIE,KAAK,EAAED,UAAU,GAAG,IAAI;IAC9B;EACF;EAEA,OAAOA,UAAU,IAAI,CAACD,eAAe;AACvC;AAEA,SAASO,kBAAkB,CACzBC,MAAc,EACdH,OAAe,EACfI,MAAe,EACN;EACT,IAAI;IACF;IACA,IAAInB,OAAO,GAAGe,OAAO,CAClBd,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CACtCA,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;IAE1B,IAAIkB,MAAM,EAAE;MACV;MACAnB,OAAO,GAAG,MAAM,GAAGA,OAAO,CAACC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,GAAG,MAAM;IAC/D;IAEA,MAAMmB,KAAK,GAAG,IAAIlB,MAAM,CAAC,GAAG,GAAGF,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC;IAClD,OAAOoB,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC;EAC3B,CAAC,CAAC,OAAOf,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEA,SAASmB,oBAAoB,CAACJ,MAAW,EAAEH,OAAe,EAAE;EAC1D,IAAI;IACF;IACA;IACA,MAAMQ,QAAQ,GAAG,IAAIC,GAAG,CACtBT,OAAO,CAACd,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,EACvE,eAAe,CAChB;;IAED;IACA,MAAMwB,KAAuC,GAAG,CAC9C,CAACP,MAAM,CAACQ,IAAI,EAAEH,QAAQ,CAACG,IAAI,EAAE,KAAK,CAAC,EACnC,CAACR,MAAM,CAACS,QAAQ,EAAEJ,QAAQ,CAACI,QAAQ,EAAE,IAAI,CAAC,CAC3C;IACD;IACA,IAAIJ,QAAQ,CAACxC,IAAI,EAAE;MACjB0C,KAAK,CAACG,IAAI,CAAC,CAACV,MAAM,CAACnC,IAAI,EAAEwC,QAAQ,CAACxC,IAAI,EAAE,KAAK,CAAC,CAAC;IACjD;IAEAwC,QAAQ,CAACM,YAAY,CAACC,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtCP,KAAK,CAACG,IAAI,CAAC,CAACV,MAAM,CAACW,YAAY,CAACI,GAAG,CAACD,CAAC,CAAC,IAAI,EAAE,EAAED,CAAC,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC,CAAC;;IAEF;IACA,OAAO,CAACN,KAAK,CAACS,IAAI,CACfC,IAAI,IAAK,CAAClB,kBAAkB,CAACkB,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CACzD;EACH,CAAC,CAAC,OAAOhC,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEA,SAASU,cAAc,CACrBL,GAAW,EACXM,IAAmB,EACnBC,OAAe,EACN;EACT,IAAI;IACF,MAAMqB,MAAM,GAAG,IAAIZ,GAAG,CAAChB,GAAG,EAAE,WAAW,CAAC;IAExC,IAAIM,IAAI,KAAK,OAAO,EAAE;MACpB,MAAMM,KAAK,GAAGtB,YAAY,CAACiB,OAAO,CAAC;MACnC,IAAI,CAACK,KAAK,EAAE,OAAO,KAAK;MACxB,OACEA,KAAK,CAACC,IAAI,CAACe,MAAM,CAACC,IAAI,CAAC,IACvBjB,KAAK,CAACC,IAAI,CAACe,MAAM,CAACC,IAAI,CAACC,SAAS,CAACF,MAAM,CAACG,MAAM,CAAC3D,MAAM,CAAC,CAAC;IAE3D,CAAC,MAAM,IAAIkC,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOQ,oBAAoB,CAACc,MAAM,EAAErB,OAAO,CAAC;IAC9C;IAEA,OAAO,KAAK;EACd,CAAC,CAAC,OAAOZ,CAAC,EAAE;IACV,OAAO,KAAK;EACd;AACF;AAEO,SAASqC,eAAe,CAC7BC,aAAqB,EACrBC,QAA4B,EAC5BC,OAAkB,EACA;EAClBD,QAAQ,GAAGA,QAAQ,KAAKpC,SAAS,GAAG,CAAC,GAAGoC,QAAQ;;EAEhD;EACA,IAAIA,QAAQ,GAAG,CAAC,EAAE;IAChB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC1C,OAAO,CAACC,KAAK,CAAC,wDAAwD,CAAC;IACzE;IACAqC,QAAQ,GAAG,CAAC;EACd,CAAC,MAAM,IAAIA,QAAQ,GAAG,CAAC,EAAE;IACvB,IAAIE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC1C,OAAO,CAACC,KAAK,CAAC,qDAAqD,CAAC;IACtE;IACAqC,QAAQ,GAAG,CAAC;EACd;;EAEA;EACA,MAAMK,KAAK,GAAG5D,eAAe,CAACsD,aAAa,CAAC;EAC5CE,OAAO,GAAGA,OAAO,IAAII,KAAK;EAC1B,IAAIJ,OAAO,CAAC/D,MAAM,KAAK6D,aAAa,EAAE;IACpC,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC1C,OAAO,CAACC,KAAK,CACX,2EAA2E,CAC5E;IACH;IACAsC,OAAO,GAAGI,KAAK;EACjB;;EAEA;EACA,MAAMC,WAAW,GAAGL,OAAO,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEC,GAAG,KAAKA,GAAG,GAAGD,CAAC,EAAE,CAAC,CAAC;EAC1D,IAAIF,WAAW,GAAG,IAAI,IAAIA,WAAW,GAAG,IAAI,EAAE;IAC5C,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC1C,OAAO,CAACC,KAAK,CAAC,qCAAqC,CAAC;IACtD;IACAsC,OAAO,GAAGI,KAAK;EACjB;;EAEA;EACA,IAAIK,UAAU,GAAG,CAAC;EAClB,OAAOT,OAAO,CAACU,GAAG,CAAEH,CAAC,IAAK;IACxB,MAAMI,KAAK,GAAGF,UAAU;IACxBA,UAAU,IAAIF,CAAC;IACf,OAAO,CAACI,KAAK,EAAEA,KAAK,GAAIZ,QAAQ,GAAcQ,CAAC,CAAC;EAClD,CAAC,CAAC;AACJ;AAEO,SAASK,sBAAsB,CACpCC,EAAU,EACVhD,GAAW,EACXiC,aAAqB,EACrB;EACA,IAAI,CAACjC,GAAG,EAAE;IACR,OAAO,IAAI;EACb;EAEA,MAAMiD,MAAM,GAAGjD,GAAG,CAACkD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAChC,IAAI,CAACD,MAAM,EAAE;IACX,OAAO,IAAI;EACb;EAEA,MAAM7C,KAAK,GAAG6C,MAAM,CACjBxD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;EAAA,CACnByD,KAAK,CAAC,GAAG,CAAC,CAAC;EAAA,CACXL,GAAG,CAAEM,EAAE,IAAKA,EAAE,CAACD,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAC7BE,MAAM,CAAC;IAAA,IAAC,CAAC5B,CAAC,CAAC;IAAA,OAAKA,CAAC,KAAKwB,EAAE;EAAA,EAAC,CAAC;EAAA,CAC1BH,GAAG,CAAC;IAAA,IAAC,GAAGtB,CAAC,CAAC;IAAA,OAAK8B,QAAQ,CAAC9B,CAAC,CAAC;EAAA,EAAC,CAAC,CAAC;;EAEhC,IAAInB,KAAK,CAAChC,MAAM,GAAG,CAAC,IAAIgC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAG6B,aAAa,EAC/D,OAAO7B,KAAK,CAAC,CAAC,CAAC;EAEjB,OAAO,IAAI;AACb;AAEO,SAASD,UAAU,CAACK,OAAsB,EAAE;EACjD,IAAI;IACF,OAAOA,OAAO,EAAE;EAClB,CAAC,CAAC,OAAOb,CAAC,EAAE;IACVC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;IAChB,OAAO,KAAK;EACd;AACF;AAEA,MAAM2D,WAAW,GAAIC,CAAS,IAC5BC,UAAU,CAACC,IAAI,CAACC,IAAI,CAACH,CAAC,CAAC,EAAGI,CAAC,IAAKA,CAAC,CAACrF,UAAU,CAAC,CAAC,CAAC,CAAC;AAE3C,eAAesF,OAAO,CAC3BC,eAAuB,EACvBC,aAAsB,EACtBC,MAAqB,EACJ;EACjBD,aAAa,GAAGA,aAAa,IAAI,EAAE;EACnCC,MAAM,GAAGA,MAAM,IAAKC,UAAU,CAACC,MAAM,IAAID,UAAU,CAACC,MAAM,CAACF,MAAO;EAClE,IAAI,CAACA,MAAM,EAAE;IACX,MAAM,IAAIG,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,IAAI;IACF,MAAMC,GAAG,GAAG,MAAMJ,MAAM,CAACK,SAAS,CAChC,KAAK,EACLd,WAAW,CAACQ,aAAa,CAAC,EAC1B;MAAEO,IAAI,EAAE,SAAS;MAAEjG,MAAM,EAAE;IAAI,CAAC,EAChC,IAAI,EACJ,CAAC,SAAS,EAAE,SAAS,CAAC,CACvB;IACD,MAAM,CAACkG,EAAE,EAAEC,UAAU,CAAC,GAAGV,eAAe,CAACX,KAAK,CAAC,GAAG,CAAC;IACnD,MAAMsB,eAAe,GAAG,MAAMT,MAAM,CAACH,OAAO,CAC1C;MAAES,IAAI,EAAE,SAAS;MAAEC,EAAE,EAAEhB,WAAW,CAACgB,EAAE;IAAE,CAAC,EACxCH,GAAG,EACHb,WAAW,CAACiB,UAAU,CAAC,CACxB;IAED,OAAO,IAAIE,WAAW,EAAE,CAACC,MAAM,CAACF,eAAe,CAAC;EAClD,CAAC,CAAC,OAAO7E,CAAC,EAAE;IACV,MAAM,IAAIuE,KAAK,CAAC,mBAAmB,CAAC;EACtC;AACF;;AAEA;AACO,SAASS,QAAQ,CAACC,KAAU,EAAU;EAC3C,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOA,KAAK;EAC3C,OAAOC,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC;AAC9B;;AAEA;AACO,SAASG,mBAAmB,CAACH,KAAU,EAAU;EACtD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7BA,KAAK,GAAGA,KAAK,GAAG,EAAE;EACpB;EACA,IAAI,CAACA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IACvCA,KAAK,GAAG,GAAG;EACb;EACA;EACA;EACA;EACA,MAAMI,KAAK,GAAIJ,KAAK,CAAYnF,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC,CAACyD,KAAK,CAAC,MAAM,CAAC;;EAExE;EACA;EACA;EACA,IAAI8B,KAAK,CAAC5G,MAAM,KAAK,CAAC,EAAE;IACtB4G,KAAK,CAAC5D,IAAI,CAAC,GAAG,CAAC;EACjB;;EAEA;EACA;EACA,OAAO4D,KAAK,CACTnC,GAAG,CAAEtB,CAAC,IAAMA,CAAC,CAACnB,KAAK,CAAC,UAAU,CAAC,GAAGmB,CAAC,CAAC0D,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG1D,CAAE,CAAC,CAC1D2D,IAAI,CAAC,GAAG,CAAC;AACd;AAEO,SAASC,cAAc,GAAW;EACvC,IAAIzG,OAAe;EACnB,IAAI;IACF;IACAA,OAAO,WAAkB;EAC3B,CAAC,CAAC,OAAOiB,CAAC,EAAE;IACVjB,OAAO,GAAG,EAAE;EACd;EACA,OAAOA,OAAO;AAChB"}