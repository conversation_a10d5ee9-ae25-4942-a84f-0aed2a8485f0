{"version": 3, "file": "sticky-bucket-service.js", "names": ["StickyBucketService", "getAllAssignments", "attributes", "docs", "Promise", "all", "Object", "entries", "map", "attributeName", "attributeValue", "getAssignments", "for<PERSON>ach", "doc", "key", "LocalStorageStickyBucketService", "constructor", "opts", "prefix", "localStorage", "globalThis", "e", "raw", "getItem", "data", "JSON", "parse", "assignments", "saveAssignments", "setItem", "stringify", "ExpressCookieStickyBucketService", "req", "res", "cookieAttributes", "cookies", "str", "cookie", "encodeURIComponent", "BrowserCookieStickyBucketService", "js<PERSON><PERSON><PERSON>", "get", "set", "RedisStickyBucketService", "redis", "keys", "mget", "then", "values", "_attributeName", "_attributeValue"], "sources": ["../../src/sticky-bucket-service.ts"], "sourcesContent": ["import {\n  LocalStorageCompat,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n} from \"./types/growthbook\";\n\nexport interface CookieAttributes {\n  expires?: number | Date | undefined;\n  path?: string | undefined;\n  domain?: string | undefined;\n  secure?: boolean | undefined;\n  sameSite?: \"strict\" | \"Strict\" | \"lax\" | \"Lax\" | \"none\" | \"None\" | undefined;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  [property: string]: any;\n}\nexport interface JsCookiesCompat<T = string> {\n  set(\n    name: string,\n    value: string | T,\n    options?: CookieAttributes\n  ): string | undefined;\n  get(name: string): string | T | undefined;\n  get(): { [key: string]: string };\n  remove(name: string, options?: CookieAttributes): void;\n}\n\nexport interface IORedisCompat {\n  mget(...keys: string[]): Promise<string[]>;\n  set(key: string, value: string): Promise<string>;\n}\n\nexport interface RequestCompat {\n  cookies: Record<string, string>;\n  [key: string]: unknown;\n}\nexport interface ResponseCompat {\n  cookie(\n    name: string,\n    value: string,\n    options?: CookieAttributes\n  ): ResponseCompat;\n  [key: string]: unknown;\n}\n\n/**\n * Responsible for reading and writing documents which describe sticky bucket assignments.\n */\nexport abstract class StickyBucketService {\n  abstract getAssignments(\n    attributeName: string,\n    attributeValue: string\n  ): Promise<StickyAssignmentsDocument | null>;\n\n  abstract saveAssignments(doc: StickyAssignmentsDocument): Promise<unknown>;\n\n  /**\n   * The SDK calls getAllAssignments to populate sticky buckets. This in turn will\n   * typically loop through individual getAssignments calls. However, some StickyBucketService\n   * instances (i.e. Redis) will instead perform a multi-query inside getAllAssignments instead.\n   */\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<string, StickyAssignmentsDocument> = {};\n    (\n      await Promise.all(\n        Object.entries(attributes).map(([attributeName, attributeValue]) =>\n          this.getAssignments(attributeName, attributeValue)\n        )\n      )\n    ).forEach((doc) => {\n      if (doc) {\n        const key = `${doc.attributeName}||${doc.attributeValue}`;\n        docs[key] = doc;\n      }\n    });\n    return docs;\n  }\n}\n\nexport class LocalStorageStickyBucketService extends StickyBucketService {\n  private prefix: string;\n  private localStorage: LocalStorageCompat | undefined;\n  constructor(opts?: { prefix?: string; localStorage?: LocalStorageCompat }) {\n    opts = opts || {};\n    super();\n    this.prefix = opts.prefix || \"gbStickyBuckets__\";\n    try {\n      this.localStorage = opts.localStorage || globalThis.localStorage;\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.localStorage) return doc;\n    try {\n      const raw = (await this.localStorage.getItem(this.prefix + key)) || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.localStorage) return;\n    try {\n      await this.localStorage.setItem(this.prefix + key, JSON.stringify(doc));\n    } catch (e) {\n      // Ignore localStorage errors\n    }\n  }\n}\n\nexport class ExpressCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with cookieParser() middleware from npm: 'cookie-parser'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value must be manually encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private req: RequestCompat;\n  private res: ResponseCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    req,\n    res,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    req: RequestCompat;\n    res: ResponseCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.req = req;\n    this.res = res;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.req) return doc;\n    try {\n      const raw = this.req.cookies[this.prefix + key] || \"{}\";\n      const data = JSON.parse(raw);\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.res) return;\n    const str = JSON.stringify(doc);\n    this.res.cookie(\n      encodeURIComponent(this.prefix + key),\n      encodeURIComponent(str),\n      this.cookieAttributes\n    );\n  }\n}\n\nexport class BrowserCookieStickyBucketService extends StickyBucketService {\n  /**\n   * Intended to be used with npm: 'js-cookie'.\n   * Assumes:\n   *  - reading a cookie is automatically decoded via decodeURIComponent() or similar\n   *  - writing a cookie name & value is automatically encoded via encodeURIComponent() or similar\n   *  - all cookie bodies are JSON encoded strings and are manually encoded/decoded\n   */\n  private prefix: string;\n  private jsCookie: JsCookiesCompat;\n  private cookieAttributes: CookieAttributes;\n  constructor({\n    prefix = \"gbStickyBuckets__\",\n    jsCookie,\n    cookieAttributes = {},\n  }: {\n    prefix?: string;\n    jsCookie: JsCookiesCompat;\n    cookieAttributes?: CookieAttributes;\n  }) {\n    super();\n    this.prefix = prefix;\n    this.jsCookie = jsCookie;\n    this.cookieAttributes = cookieAttributes;\n  }\n  async getAssignments(attributeName: string, attributeValue: string) {\n    const key = `${attributeName}||${attributeValue}`;\n    let doc: StickyAssignmentsDocument | null = null;\n    if (!this.jsCookie) return doc;\n    try {\n      const raw = this.jsCookie.get(this.prefix + key);\n      const data = JSON.parse(raw || \"{}\");\n      if (data.attributeName && data.attributeValue && data.assignments) {\n        doc = data;\n      }\n    } catch (e) {\n      // Ignore cookie errors\n    }\n    return doc;\n  }\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.jsCookie) return;\n    const str = JSON.stringify(doc);\n    this.jsCookie.set(this.prefix + key, str, this.cookieAttributes);\n  }\n}\n\nexport class RedisStickyBucketService extends StickyBucketService {\n  /** Intended to be used with npm: 'ioredis'. **/\n  private redis: IORedisCompat | undefined;\n  constructor({ redis }: { redis: IORedisCompat }) {\n    super();\n    this.redis = redis;\n  }\n\n  async getAllAssignments(\n    attributes: Record<string, string>\n  ): Promise<Record<StickyAttributeKey, StickyAssignmentsDocument>> {\n    const docs: Record<StickyAttributeKey, StickyAssignmentsDocument> = {};\n    const keys = Object.entries(attributes).map(\n      ([attributeName, attributeValue]) => `${attributeName}||${attributeValue}`\n    );\n    if (!this.redis) return docs;\n    this.redis.mget(...keys).then((values) => {\n      values.forEach((raw) => {\n        try {\n          const data = JSON.parse(raw || \"{}\");\n          if (data.attributeName && data.attributeValue && data.assignments) {\n            const key = `${data.attributeName}||${data.attributeValue}`;\n            docs[key] = data;\n          }\n        } catch (e) {\n          // ignore redis doc parse errors\n        }\n      });\n    });\n    return docs;\n  }\n\n  async getAssignments(_attributeName: string, _attributeValue: string) {\n    // not implemented\n    return null;\n  }\n\n  async saveAssignments(doc: StickyAssignmentsDocument) {\n    const key = `${doc.attributeName}||${doc.attributeValue}`;\n    if (!this.redis) return;\n    await this.redis.set(key, JSON.stringify(doc));\n  }\n}\n"], "mappings": ";;;;;;AA4CA;AACA;AACA;AACO,MAAeA,mBAAmB,CAAC;EAQxC;AACF;AACA;AACA;AACA;EACE,MAAMC,iBAAiB,CACrBC,UAAkC,EAC8B;IAChE,MAAMC,IAA+C,GAAG,CAAC,CAAC;IAC1D,CACE,MAAMC,OAAO,CAACC,GAAG,CACfC,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,CAACM,GAAG,CAAC;MAAA,IAAC,CAACC,aAAa,EAAEC,cAAc,CAAC;MAAA,OAC7D,IAAI,CAACC,cAAc,CAACF,aAAa,EAAEC,cAAc,CAAC;IAAA,EACnD,CACF,EACDE,OAAO,CAAEC,GAAG,IAAK;MACjB,IAAIA,GAAG,EAAE;QACP,MAAMC,GAAG,aAAMD,GAAG,CAACJ,aAAa,eAAKI,GAAG,CAACH,cAAc,CAAE;QACzDP,IAAI,CAACW,GAAG,CAAC,GAAGD,GAAG;MACjB;IACF,CAAC,CAAC;IACF,OAAOV,IAAI;EACb;AACF;AAAC;AAEM,MAAMY,+BAA+B,SAASf,mBAAmB,CAAC;EAGvEgB,WAAW,CAACC,IAA6D,EAAE;IACzEA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,KAAK,EAAE;IACP,IAAI,CAACC,MAAM,GAAGD,IAAI,CAACC,MAAM,IAAI,mBAAmB;IAChD,IAAI;MACF,IAAI,CAACC,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIC,UAAU,CAACD,YAAY;IAClE,CAAC,CAAC,OAAOE,CAAC,EAAE;MACV;IAAA;EAEJ;EACA,MAAMV,cAAc,CAACF,aAAqB,EAAEC,cAAsB,EAAE;IAClE,MAAMI,GAAG,aAAML,aAAa,eAAKC,cAAc,CAAE;IACjD,IAAIG,GAAqC,GAAG,IAAI;IAChD,IAAI,CAAC,IAAI,CAACM,YAAY,EAAE,OAAON,GAAG;IAClC,IAAI;MACF,MAAMS,GAAG,GAAG,CAAC,MAAM,IAAI,CAACH,YAAY,CAACI,OAAO,CAAC,IAAI,CAACL,MAAM,GAAGJ,GAAG,CAAC,KAAK,IAAI;MACxE,MAAMU,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC;MAC5B,IAAIE,IAAI,CAACf,aAAa,IAAIe,IAAI,CAACd,cAAc,IAAIc,IAAI,CAACG,WAAW,EAAE;QACjEd,GAAG,GAAGW,IAAI;MACZ;IACF,CAAC,CAAC,OAAOH,CAAC,EAAE;MACV;IAAA;IAEF,OAAOR,GAAG;EACZ;EACA,MAAMe,eAAe,CAACf,GAA8B,EAAE;IACpD,MAAMC,GAAG,aAAMD,GAAG,CAACJ,aAAa,eAAKI,GAAG,CAACH,cAAc,CAAE;IACzD,IAAI,CAAC,IAAI,CAACS,YAAY,EAAE;IACxB,IAAI;MACF,MAAM,IAAI,CAACA,YAAY,CAACU,OAAO,CAAC,IAAI,CAACX,MAAM,GAAGJ,GAAG,EAAEW,IAAI,CAACK,SAAS,CAACjB,GAAG,CAAC,CAAC;IACzE,CAAC,CAAC,OAAOQ,CAAC,EAAE;MACV;IAAA;EAEJ;AACF;AAAC;AAEM,MAAMU,gCAAgC,SAAS/B,mBAAmB,CAAC;EACxE;AACF;AACA;AACA;AACA;AACA;AACA;;EAKEgB,WAAW,QAUR;IAAA,IAVS;MACVE,MAAM,GAAG,mBAAmB;MAC5Bc,GAAG;MACHC,GAAG;MACHC,gBAAgB,GAAG,CAAC;IAMtB,CAAC;IACC,KAAK,EAAE;IACP,IAAI,CAAChB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACc,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;EAC1C;EACA,MAAMvB,cAAc,CAACF,aAAqB,EAAEC,cAAsB,EAAE;IAClE,MAAMI,GAAG,aAAML,aAAa,eAAKC,cAAc,CAAE;IACjD,IAAIG,GAAqC,GAAG,IAAI;IAChD,IAAI,CAAC,IAAI,CAACmB,GAAG,EAAE,OAAOnB,GAAG;IACzB,IAAI;MACF,MAAMS,GAAG,GAAG,IAAI,CAACU,GAAG,CAACG,OAAO,CAAC,IAAI,CAACjB,MAAM,GAAGJ,GAAG,CAAC,IAAI,IAAI;MACvD,MAAMU,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAAC;MAC5B,IAAIE,IAAI,CAACf,aAAa,IAAIe,IAAI,CAACd,cAAc,IAAIc,IAAI,CAACG,WAAW,EAAE;QACjEd,GAAG,GAAGW,IAAI;MACZ;IACF,CAAC,CAAC,OAAOH,CAAC,EAAE;MACV;IAAA;IAEF,OAAOR,GAAG;EACZ;EACA,MAAMe,eAAe,CAACf,GAA8B,EAAE;IACpD,MAAMC,GAAG,aAAMD,GAAG,CAACJ,aAAa,eAAKI,GAAG,CAACH,cAAc,CAAE;IACzD,IAAI,CAAC,IAAI,CAACuB,GAAG,EAAE;IACf,MAAMG,GAAG,GAAGX,IAAI,CAACK,SAAS,CAACjB,GAAG,CAAC;IAC/B,IAAI,CAACoB,GAAG,CAACI,MAAM,CACbC,kBAAkB,CAAC,IAAI,CAACpB,MAAM,GAAGJ,GAAG,CAAC,EACrCwB,kBAAkB,CAACF,GAAG,CAAC,EACvB,IAAI,CAACF,gBAAgB,CACtB;EACH;AACF;AAAC;AAEM,MAAMK,gCAAgC,SAASvC,mBAAmB,CAAC;EACxE;AACF;AACA;AACA;AACA;AACA;AACA;;EAIEgB,WAAW,QAQR;IAAA,IARS;MACVE,MAAM,GAAG,mBAAmB;MAC5BsB,QAAQ;MACRN,gBAAgB,GAAG,CAAC;IAKtB,CAAC;IACC,KAAK,EAAE;IACP,IAAI,CAAChB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACsB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACN,gBAAgB,GAAGA,gBAAgB;EAC1C;EACA,MAAMvB,cAAc,CAACF,aAAqB,EAAEC,cAAsB,EAAE;IAClE,MAAMI,GAAG,aAAML,aAAa,eAAKC,cAAc,CAAE;IACjD,IAAIG,GAAqC,GAAG,IAAI;IAChD,IAAI,CAAC,IAAI,CAAC2B,QAAQ,EAAE,OAAO3B,GAAG;IAC9B,IAAI;MACF,MAAMS,GAAG,GAAG,IAAI,CAACkB,QAAQ,CAACC,GAAG,CAAC,IAAI,CAACvB,MAAM,GAAGJ,GAAG,CAAC;MAChD,MAAMU,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,IAAI,IAAI,CAAC;MACpC,IAAIE,IAAI,CAACf,aAAa,IAAIe,IAAI,CAACd,cAAc,IAAIc,IAAI,CAACG,WAAW,EAAE;QACjEd,GAAG,GAAGW,IAAI;MACZ;IACF,CAAC,CAAC,OAAOH,CAAC,EAAE;MACV;IAAA;IAEF,OAAOR,GAAG;EACZ;EACA,MAAMe,eAAe,CAACf,GAA8B,EAAE;IACpD,MAAMC,GAAG,aAAMD,GAAG,CAACJ,aAAa,eAAKI,GAAG,CAACH,cAAc,CAAE;IACzD,IAAI,CAAC,IAAI,CAAC8B,QAAQ,EAAE;IACpB,MAAMJ,GAAG,GAAGX,IAAI,CAACK,SAAS,CAACjB,GAAG,CAAC;IAC/B,IAAI,CAAC2B,QAAQ,CAACE,GAAG,CAAC,IAAI,CAACxB,MAAM,GAAGJ,GAAG,EAAEsB,GAAG,EAAE,IAAI,CAACF,gBAAgB,CAAC;EAClE;AACF;AAAC;AAEM,MAAMS,wBAAwB,SAAS3C,mBAAmB,CAAC;EAChE;;EAEAgB,WAAW,QAAsC;IAAA,IAArC;MAAE4B;IAAgC,CAAC;IAC7C,KAAK,EAAE;IACP,IAAI,CAACA,KAAK,GAAGA,KAAK;EACpB;EAEA,MAAM3C,iBAAiB,CACrBC,UAAkC,EAC8B;IAChE,MAAMC,IAA2D,GAAG,CAAC,CAAC;IACtE,MAAM0C,IAAI,GAAGvC,MAAM,CAACC,OAAO,CAACL,UAAU,CAAC,CAACM,GAAG,CACzC;MAAA,IAAC,CAACC,aAAa,EAAEC,cAAc,CAAC;MAAA,iBAAQD,aAAa,eAAKC,cAAc;IAAA,CAAE,CAC3E;IACD,IAAI,CAAC,IAAI,CAACkC,KAAK,EAAE,OAAOzC,IAAI;IAC5B,IAAI,CAACyC,KAAK,CAACE,IAAI,CAAC,GAAGD,IAAI,CAAC,CAACE,IAAI,CAAEC,MAAM,IAAK;MACxCA,MAAM,CAACpC,OAAO,CAAEU,GAAG,IAAK;QACtB,IAAI;UACF,MAAME,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,IAAI,IAAI,CAAC;UACpC,IAAIE,IAAI,CAACf,aAAa,IAAIe,IAAI,CAACd,cAAc,IAAIc,IAAI,CAACG,WAAW,EAAE;YACjE,MAAMb,GAAG,aAAMU,IAAI,CAACf,aAAa,eAAKe,IAAI,CAACd,cAAc,CAAE;YAC3DP,IAAI,CAACW,GAAG,CAAC,GAAGU,IAAI;UAClB;QACF,CAAC,CAAC,OAAOH,CAAC,EAAE;UACV;QAAA;MAEJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAOlB,IAAI;EACb;EAEA,MAAMQ,cAAc,CAACsC,cAAsB,EAAEC,eAAuB,EAAE;IACpE;IACA,OAAO,IAAI;EACb;EAEA,MAAMtB,eAAe,CAACf,GAA8B,EAAE;IACpD,MAAMC,GAAG,aAAMD,GAAG,CAACJ,aAAa,eAAKI,GAAG,CAACH,cAAc,CAAE;IACzD,IAAI,CAAC,IAAI,CAACkC,KAAK,EAAE;IACjB,MAAM,IAAI,CAACA,KAAK,CAACF,GAAG,CAAC5B,GAAG,EAAEW,IAAI,CAACK,SAAS,CAACjB,GAAG,CAAC,CAAC;EAChD;AACF;AAAC"}