{"version": 3, "file": "GrowthBook.js", "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "SDK_VERSION", "loadSDKVersion", "GrowthBook", "constructor", "context", "version", "_ctx", "_renderer", "_trackedExperiments", "Set", "_trackedFeatures", "debug", "_subscriptions", "_rtQueue", "_rtTimer", "ready", "_assigned", "Map", "_forcedFeatureV<PERSON>ues", "_attributeOverrides", "_activeAutoExperiments", "_triggeredExpKeys", "_loadFeaturesCalled", "remoteEval", "decryptionKey", "Error", "client<PERSON>ey", "isGbHost", "URL", "apiHost", "hostname", "match", "e", "cacheKeyAttributes", "features", "enableDevMode", "_growthbook", "dispatchEvent", "Event", "experiments", "_updateAllAutoExperiments", "_refresh", "loadFeatures", "options", "autoRefresh", "subscribeToChanges", "_canSubscribe", "subscribe", "refreshFeatures", "getApiInfo", "getApiHosts", "getClientKey", "defaultHost", "replace", "streamingHost", "apiRequestHeaders", "apiHostRequestHeaders", "streamingHostRequestHeaders", "isRemoteEval", "getCacheKeyAttributes", "allowStale", "updateInstance", "timeout", "<PERSON><PERSON><PERSON>", "backgroundSync", "_render", "setFeatures", "setEncryptedFeatures", "encryptedString", "subtle", "featuresJSON", "decrypt", "JSON", "parse", "setExperiments", "setEncryptedExperiments", "experimentsJSON", "decryptPayload", "data", "encryptedFeatures", "encryptedExperiments", "setAttributes", "attributes", "stickyBucketService", "refreshStickyBuckets", "_refreshForRemoteEval", "setAttributeOverrides", "overrides", "setForcedVariations", "vars", "forcedVariations", "setForcedFeatures", "map", "setURL", "url", "getAttributes", "getForcedVariations", "getForcedFeatures", "getStickyBucketAssignmentDocs", "stickyBucketAssignmentDocs", "getUrl", "getFeatures", "getExperiments", "cb", "add", "delete", "catch", "getAllResults", "destroy", "clear", "clearTimeout", "unsubscribe", "for<PERSON>ach", "exp", "undo", "<PERSON><PERSON><PERSON><PERSON>", "renderer", "forceVariation", "key", "variation", "run", "experiment", "result", "_run", "_fireSubscriptions", "triggerExperiment", "filter", "manual", "_runAutoExperiment", "res", "forceRerun", "existing", "get", "has", "valueHash", "stringify", "value", "inExperiment", "_undoActiveAutoExperiment", "_applyDOMChanges", "set", "keys", "v", "k", "prev", "variationId", "console", "error", "_trackFeatureUsage", "source", "stringifiedValue", "onFeatureUsage", "fetch", "push", "on", "setTimeout", "q", "realtimeKey", "encodeURIComponent", "cache", "mode", "realtimeInterval", "_getFeatureResult", "ruleId", "ret", "off", "experimentResult", "isOn", "evalFeature", "isOff", "getFeatureValue", "defaultValue", "feature", "id", "process", "env", "NODE_ENV", "log", "rules", "rule", "filters", "_isFilteredOut", "condition", "_conditionPasses", "_isIncludedInRollout", "seed", "hashAttribute", "disableStickyBucketing", "fallbackAttribute", "undefined", "range", "coverage", "hashVersion", "tracks", "t", "_track", "force", "variations", "weights", "bucketVersion", "minBucketVersion", "namespace", "meta", "ranges", "name", "phase", "passthrough", "hashValue", "_getHashAttribute", "n", "hash", "inRange", "evalCondition", "some", "attribute", "r", "featureId", "numVariations", "length", "_getResult", "enabled", "_mergeOverrides", "urlPatterns", "isURLTargeted", "_getContextUrl", "qsOverride", "getQueryStringOverride", "status", "active", "assigned", "foundStickyBucket", "stickyBucketVersionIsBlocked", "versionIsBlocked", "_getStickyBucketVariation", "inNamespace", "include", "isIncluded", "groups", "_hasGroupOverlap", "_urlIsValid", "getBucketRanges", "chooseVariation", "qaMode", "changed", "attrKey", "doc", "_generateStickyBucketAssignmentDoc", "toString", "_getStickyBucketExperimentKey", "saveAssignments", "msg", "ctx", "trackingCallback", "o", "Object", "assign", "getUrlRegExp", "attr", "fallback", "user", "variationIndex", "hashUsed", "bucket", "stickyBucketUsed", "location", "href", "urlRegex", "pathOnly", "test", "expGroups", "i", "changes", "css", "s", "createElement", "innerHTML", "head", "append<PERSON><PERSON><PERSON>", "remove", "js", "script", "domMutations", "mutation", "mutate", "declarative", "revert", "fn", "_deriveStickyBucketIdentifierAttributes", "Array", "from", "_getStickyBucketAttributes", "getAllAssignments", "_getStickyBucketAssignments", "mergedAssignments", "values", "assignments", "<PERSON><PERSON><PERSON>", "experimentBucketVersion", "minExperimentBucketVersion", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "findIndex", "m", "stickyBucketIdentifierAttributes", "attributeName", "attributeValue", "existingAssignments", "newAssignments"], "sources": ["../../src/GrowthBook.ts"], "sourcesContent": ["import mutate, { DeclarativeMutation } from \"dom-mutator\";\nimport type {\n  ApiHost,\n  Attributes,\n  AutoExperiment,\n  AutoExperimentVariation,\n  ClientKey,\n  Context,\n  Experiment,\n  FeatureApiResponse,\n  FeatureDefinition,\n  FeatureResult,\n  FeatureResultSource,\n  Filter,\n  LoadFeaturesOptions,\n  RealtimeUsageData,\n  RefreshFeaturesOptions,\n  Result,\n  StickyAssignments,\n  StickyAssignmentsDocument,\n  StickyAttributeKey,\n  StickyExperimentKey,\n  SubscriptionFunction,\n  VariationMeta,\n  VariationRange,\n  WidenPrimitives,\n} from \"./types/growthbook\";\nimport type { ConditionInterface } from \"./types/mongrule\";\nimport {\n  chooseVariation,\n  decrypt,\n  getBucketRanges,\n  getQueryStringOverride,\n  getUrlRegExp,\n  hash,\n  inNamespace,\n  inRange,\n  isIncluded,\n  isURLTargeted,\n  loadSDKVersion,\n  toString,\n} from \"./util\";\nimport { evalCondition } from \"./mongrule\";\nimport { refreshFeatures, subscribe, unsubscribe } from \"./feature-repository\";\n\nconst isBrowser =\n  typeof window !== \"undefined\" && typeof document !== \"undefined\";\n\nconst SDK_VERSION = loadSDKVersion();\n\nexport class GrowthBook<\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  AppFeatures extends Record<string, any> = Record<string, any>\n> {\n  // context is technically private, but some tools depend on it so we can't mangle the name\n  // _ctx below is a clone of this property that we use internally\n  private context: Context;\n  public debug: boolean;\n  public ready: boolean;\n  public version: string;\n\n  // Properties and methods that start with \"_\" are mangled by Terser (saves ~150 bytes)\n  private _ctx: Context;\n  private _renderer: null | (() => void);\n  private _trackedExperiments: Set<unknown>;\n  private _trackedFeatures: Record<string, string>;\n  private _subscriptions: Set<SubscriptionFunction>;\n  private _rtQueue: RealtimeUsageData[];\n  private _rtTimer: number;\n  private _assigned: Map<\n    string,\n    {\n      // eslint-disable-next-line\n      experiment: Experiment<any>;\n      // eslint-disable-next-line\n      result: Result<any>;\n    }\n  >;\n  // eslint-disable-next-line\n  private _forcedFeatureValues: Map<string, any>;\n  private _attributeOverrides: Attributes;\n  private _activeAutoExperiments: Map<\n    AutoExperiment,\n    { valueHash: string; undo: () => void }\n  >;\n  private _triggeredExpKeys: Set<string>;\n  private _loadFeaturesCalled: boolean;\n\n  constructor(context?: Context) {\n    context = context || {};\n    // These properties are all initialized in the constructor instead of above\n    // This saves ~80 bytes in the final output\n    this.version = SDK_VERSION;\n    this._ctx = this.context = context;\n    this._renderer = null;\n    this._trackedExperiments = new Set();\n    this._trackedFeatures = {};\n    this.debug = false;\n    this._subscriptions = new Set();\n    this._rtQueue = [];\n    this._rtTimer = 0;\n    this.ready = false;\n    this._assigned = new Map();\n    this._forcedFeatureValues = new Map();\n    this._attributeOverrides = {};\n    this._activeAutoExperiments = new Map();\n    this._triggeredExpKeys = new Set();\n    this._loadFeaturesCalled = false;\n\n    if (context.remoteEval) {\n      if (context.decryptionKey) {\n        throw new Error(\"Encryption is not available for remoteEval\");\n      }\n      if (!context.clientKey) {\n        throw new Error(\"Missing clientKey\");\n      }\n      let isGbHost = false;\n      try {\n        isGbHost = !!new URL(context.apiHost || \"\").hostname.match(\n          /growthbook\\.io$/i\n        );\n      } catch (e) {\n        // ignore invalid URLs\n      }\n      if (isGbHost) {\n        throw new Error(\"Cannot use remoteEval on GrowthBook Cloud\");\n      }\n    } else {\n      if (context.cacheKeyAttributes) {\n        throw new Error(\"cacheKeyAttributes are only used for remoteEval\");\n      }\n    }\n\n    if (context.features) {\n      this.ready = true;\n    }\n\n    if (isBrowser && context.enableDevMode) {\n      window._growthbook = this;\n      document.dispatchEvent(new Event(\"gbloaded\"));\n    }\n\n    if (context.experiments) {\n      this.ready = true;\n      this._updateAllAutoExperiments();\n    }\n\n    if (context.clientKey && !context.remoteEval) {\n      this._refresh({}, true, false);\n    }\n  }\n\n  public async loadFeatures(options?: LoadFeaturesOptions): Promise<void> {\n    if (options && options.autoRefresh) {\n      // interpret deprecated autoRefresh option as subscribeToChanges\n      this._ctx.subscribeToChanges = true;\n    }\n    this._loadFeaturesCalled = true;\n\n    await this._refresh(options, true, true);\n\n    if (this._canSubscribe()) {\n      subscribe(this);\n    }\n  }\n\n  public async refreshFeatures(\n    options?: RefreshFeaturesOptions\n  ): Promise<void> {\n    await this._refresh(options, false, true);\n  }\n\n  public getApiInfo(): [ApiHost, ClientKey] {\n    return [this.getApiHosts().apiHost, this.getClientKey()];\n  }\n  public getApiHosts(): {\n    apiHost: string;\n    streamingHost: string;\n    apiRequestHeaders?: Record<string, string>;\n    streamingHostRequestHeaders?: Record<string, string>;\n  } {\n    const defaultHost = this._ctx.apiHost || \"https://cdn.growthbook.io\";\n    return {\n      apiHost: defaultHost.replace(/\\/*$/, \"\"),\n      streamingHost: (this._ctx.streamingHost || defaultHost).replace(\n        /\\/*$/,\n        \"\"\n      ),\n      apiRequestHeaders: this._ctx.apiHostRequestHeaders,\n      streamingHostRequestHeaders: this._ctx.streamingHostRequestHeaders,\n    };\n  }\n  public getClientKey(): string {\n    return this._ctx.clientKey || \"\";\n  }\n\n  public isRemoteEval(): boolean {\n    return this._ctx.remoteEval || false;\n  }\n\n  public getCacheKeyAttributes(): (keyof Attributes)[] | undefined {\n    return this._ctx.cacheKeyAttributes;\n  }\n\n  private async _refresh(\n    options?: RefreshFeaturesOptions,\n    allowStale?: boolean,\n    updateInstance?: boolean\n  ) {\n    options = options || {};\n    if (!this._ctx.clientKey) {\n      throw new Error(\"Missing clientKey\");\n    }\n    await refreshFeatures(\n      this,\n      options.timeout,\n      options.skipCache || this._ctx.enableDevMode,\n      allowStale,\n      updateInstance,\n      this._ctx.backgroundSync !== false\n    );\n  }\n\n  private _render() {\n    if (this._renderer) {\n      this._renderer();\n    }\n  }\n\n  public setFeatures(features: Record<string, FeatureDefinition>) {\n    this._ctx.features = features;\n    this.ready = true;\n    this._render();\n  }\n\n  public async setEncryptedFeatures(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const featuresJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setFeatures(\n      JSON.parse(featuresJSON) as Record<string, FeatureDefinition>\n    );\n  }\n\n  public setExperiments(experiments: AutoExperiment[]): void {\n    this._ctx.experiments = experiments;\n    this.ready = true;\n    this._updateAllAutoExperiments();\n  }\n\n  public async setEncryptedExperiments(\n    encryptedString: string,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<void> {\n    const experimentsJSON = await decrypt(\n      encryptedString,\n      decryptionKey || this._ctx.decryptionKey,\n      subtle\n    );\n    this.setExperiments(JSON.parse(experimentsJSON) as AutoExperiment[]);\n  }\n\n  public async decryptPayload(\n    data: FeatureApiResponse,\n    decryptionKey?: string,\n    subtle?: SubtleCrypto\n  ): Promise<FeatureApiResponse> {\n    if (data.encryptedFeatures) {\n      data.features = JSON.parse(\n        await decrypt(\n          data.encryptedFeatures,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedFeatures;\n    }\n    if (data.encryptedExperiments) {\n      data.experiments = JSON.parse(\n        await decrypt(\n          data.encryptedExperiments,\n          decryptionKey || this._ctx.decryptionKey,\n          subtle\n        )\n      );\n      delete data.encryptedExperiments;\n    }\n    return data;\n  }\n\n  public async setAttributes(attributes: Attributes) {\n    this._ctx.attributes = attributes;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setAttributeOverrides(overrides: Attributes) {\n    this._attributeOverrides = overrides;\n    if (this._ctx.stickyBucketService) {\n      await this.refreshStickyBuckets();\n    }\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  public async setForcedVariations(vars: Record<string, number>) {\n    this._ctx.forcedVariations = vars || {};\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      return;\n    }\n    this._render();\n    this._updateAllAutoExperiments();\n  }\n\n  // eslint-disable-next-line\n  public setForcedFeatures(map: Map<string, any>) {\n    this._forcedFeatureValues = map;\n    this._render();\n  }\n\n  public async setURL(url: string) {\n    this._ctx.url = url;\n    if (this._ctx.remoteEval) {\n      await this._refreshForRemoteEval();\n      this._updateAllAutoExperiments(true);\n      return;\n    }\n    this._updateAllAutoExperiments(true);\n  }\n\n  public getAttributes() {\n    return { ...this._ctx.attributes, ...this._attributeOverrides };\n  }\n\n  public getForcedVariations() {\n    return this._ctx.forcedVariations || {};\n  }\n\n  public getForcedFeatures() {\n    // eslint-disable-next-line\n    return this._forcedFeatureValues || new Map<string, any>();\n  }\n\n  public getStickyBucketAssignmentDocs() {\n    return this._ctx.stickyBucketAssignmentDocs || {};\n  }\n\n  public getUrl() {\n    return this._ctx.url || \"\";\n  }\n\n  public getFeatures() {\n    return this._ctx.features || {};\n  }\n\n  public getExperiments() {\n    return this._ctx.experiments || [];\n  }\n\n  public subscribe(cb: SubscriptionFunction): () => void {\n    this._subscriptions.add(cb);\n    return () => {\n      this._subscriptions.delete(cb);\n    };\n  }\n\n  private _canSubscribe() {\n    return this._ctx.backgroundSync !== false && this._ctx.subscribeToChanges;\n  }\n\n  private async _refreshForRemoteEval() {\n    if (!this._ctx.remoteEval) return;\n    if (!this._loadFeaturesCalled) return;\n    await this._refresh({}, false, true).catch(() => {\n      // Ignore errors\n    });\n  }\n\n  public getAllResults() {\n    return new Map(this._assigned);\n  }\n\n  public destroy() {\n    // Release references to save memory\n    this._subscriptions.clear();\n    this._assigned.clear();\n    this._trackedExperiments.clear();\n    this._trackedFeatures = {};\n    this._rtQueue = [];\n    if (this._rtTimer) {\n      clearTimeout(this._rtTimer);\n    }\n    unsubscribe(this);\n\n    if (isBrowser && window._growthbook === this) {\n      delete window._growthbook;\n    }\n\n    // Undo any active auto experiments\n    this._activeAutoExperiments.forEach((exp) => {\n      exp.undo();\n    });\n    this._activeAutoExperiments.clear();\n    this._triggeredExpKeys.clear();\n  }\n\n  public setRenderer(renderer: () => void) {\n    this._renderer = renderer;\n  }\n\n  public forceVariation(key: string, variation: number) {\n    this._ctx.forcedVariations = this._ctx.forcedVariations || {};\n    this._ctx.forcedVariations[key] = variation;\n    if (this._ctx.remoteEval) {\n      this._refreshForRemoteEval();\n      return;\n    }\n    this._updateAllAutoExperiments();\n    this._render();\n  }\n\n  public run<T>(experiment: Experiment<T>): Result<T> {\n    const result = this._run(experiment, null);\n    this._fireSubscriptions(experiment, result);\n    return result;\n  }\n\n  public triggerExperiment(key: string) {\n    this._triggeredExpKeys.add(key);\n    if (!this._ctx.experiments) return null;\n    const experiments = this._ctx.experiments.filter((exp) => exp.key === key);\n    return experiments\n      .map((exp) => {\n        if (!exp.manual) return null;\n        return this._runAutoExperiment(exp);\n      })\n      .filter((res) => res !== null);\n  }\n\n  private _runAutoExperiment(experiment: AutoExperiment, forceRerun?: boolean) {\n    const existing = this._activeAutoExperiments.get(experiment);\n\n    // If this is a manual experiment and it's not already running, skip\n    if (\n      experiment.manual &&\n      !this._triggeredExpKeys.has(experiment.key) &&\n      !existing\n    )\n      return null;\n\n    // Run the experiment\n    const result = this.run(experiment);\n\n    // A hash to quickly tell if the assigned value changed\n    const valueHash = JSON.stringify(result.value);\n\n    // If the changes are already active, no need to re-apply them\n    if (\n      !forceRerun &&\n      result.inExperiment &&\n      existing &&\n      existing.valueHash === valueHash\n    ) {\n      return result;\n    }\n\n    // Undo any existing changes\n    if (existing) this._undoActiveAutoExperiment(experiment);\n\n    // Apply new changes\n    if (result.inExperiment) {\n      const undo = this._applyDOMChanges(result.value);\n      if (undo) {\n        this._activeAutoExperiments.set(experiment, {\n          undo,\n          valueHash,\n        });\n      }\n    }\n\n    return result;\n  }\n\n  private _undoActiveAutoExperiment(exp: AutoExperiment) {\n    const data = this._activeAutoExperiments.get(exp);\n    if (data) {\n      data.undo();\n      this._activeAutoExperiments.delete(exp);\n    }\n  }\n\n  private _updateAllAutoExperiments(forceRerun?: boolean) {\n    const experiments = this._ctx.experiments || [];\n\n    // Stop any experiments that are no longer defined\n    const keys = new Set(experiments);\n    this._activeAutoExperiments.forEach((v, k) => {\n      if (!keys.has(k)) {\n        v.undo();\n        this._activeAutoExperiments.delete(k);\n      }\n    });\n\n    // Re-run all new/updated experiments\n    experiments.forEach((exp) => {\n      this._runAutoExperiment(exp, forceRerun);\n    });\n  }\n\n  private _fireSubscriptions<T>(experiment: Experiment<T>, result: Result<T>) {\n    const key = experiment.key;\n\n    // If assigned variation has changed, fire subscriptions\n    const prev = this._assigned.get(key);\n    // TODO: what if the experiment definition has changed?\n    if (\n      !prev ||\n      prev.result.inExperiment !== result.inExperiment ||\n      prev.result.variationId !== result.variationId\n    ) {\n      this._assigned.set(key, { experiment, result });\n      this._subscriptions.forEach((cb) => {\n        try {\n          cb(experiment, result);\n        } catch (e) {\n          console.error(e);\n        }\n      });\n    }\n  }\n\n  private _trackFeatureUsage(key: string, res: FeatureResult): void {\n    // Don't track feature usage that was forced via an override\n    if (res.source === \"override\") return;\n\n    // Only track a feature once, unless the assigned value changed\n    const stringifiedValue = JSON.stringify(res.value);\n    if (this._trackedFeatures[key] === stringifiedValue) return;\n    this._trackedFeatures[key] = stringifiedValue;\n\n    // Fire user-supplied callback\n    if (this._ctx.onFeatureUsage) {\n      try {\n        this._ctx.onFeatureUsage(key, res);\n      } catch (e) {\n        // Ignore feature usage callback errors\n      }\n    }\n\n    // In browser environments, queue up feature usage to be tracked in batches\n    if (!isBrowser || !window.fetch) return;\n    this._rtQueue.push({\n      key,\n      on: res.on,\n    });\n    if (!this._rtTimer) {\n      this._rtTimer = window.setTimeout(() => {\n        // Reset the queue\n        this._rtTimer = 0;\n        const q = [...this._rtQueue];\n        this._rtQueue = [];\n\n        // Skip logging if a real-time usage key is not configured\n        if (!this._ctx.realtimeKey) return;\n\n        window\n          .fetch(\n            `https://rt.growthbook.io/?key=${\n              this._ctx.realtimeKey\n            }&events=${encodeURIComponent(JSON.stringify(q))}`,\n\n            {\n              cache: \"no-cache\",\n              mode: \"no-cors\",\n            }\n          )\n          .catch(() => {\n            // TODO: retry in case of network errors?\n          });\n      }, this._ctx.realtimeInterval || 2000);\n    }\n  }\n\n  private _getFeatureResult<T>(\n    key: string,\n    value: T,\n    source: FeatureResultSource,\n    ruleId?: string,\n    experiment?: Experiment<T>,\n    result?: Result<T>\n  ): FeatureResult<T> {\n    const ret: FeatureResult = {\n      value,\n      on: !!value,\n      off: !value,\n      source,\n      ruleId: ruleId || \"\",\n    };\n    if (experiment) ret.experiment = experiment;\n    if (result) ret.experimentResult = result;\n\n    // Track the usage of this feature in real-time\n    this._trackFeatureUsage(key, ret);\n\n    return ret;\n  }\n\n  public isOn<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).on;\n  }\n\n  public isOff<K extends string & keyof AppFeatures = string>(key: K): boolean {\n    return this.evalFeature(key).off;\n  }\n\n  public getFeatureValue<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(key: K, defaultValue: V): WidenPrimitives<V> {\n    const value = this.evalFeature<WidenPrimitives<V>, K>(key).value;\n    return value === null ? (defaultValue as WidenPrimitives<V>) : value;\n  }\n\n  /**\n   * @deprecated Use {@link evalFeature}\n   * @param id\n   */\n  // eslint-disable-next-line\n  public feature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    return this.evalFeature(id);\n  }\n\n  public evalFeature<\n    V extends AppFeatures[K],\n    K extends string & keyof AppFeatures = string\n  >(id: K): FeatureResult<V | null> {\n    // Global override\n    if (this._forcedFeatureValues.has(id)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Global override\", {\n          id,\n          value: this._forcedFeatureValues.get(id),\n        });\n      return this._getFeatureResult(\n        id,\n        this._forcedFeatureValues.get(id),\n        \"override\"\n      );\n    }\n\n    // Unknown feature id\n    if (!this._ctx.features || !this._ctx.features[id]) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Unknown feature\", { id });\n      return this._getFeatureResult(id, null, \"unknownFeature\");\n    }\n\n    // Get the feature\n    const feature: FeatureDefinition<V> = this._ctx.features[id];\n\n    // Loop through the rules\n    if (feature.rules) {\n      for (const rule of feature.rules) {\n        // If there are filters for who is included (e.g. namespaces)\n        if (rule.filters && this._isFilteredOut(rule.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip rule because of filters\", {\n              id,\n              rule,\n            });\n          continue;\n        }\n\n        // Feature value is being forced\n        if (\"force\" in rule) {\n          // If it's a conditional rule, skip if the condition doesn't pass\n          if (rule.condition && !this._conditionPasses(rule.condition)) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because of condition ff\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          // If this is a percentage rollout, skip if not included\n          if (\n            !this._isIncludedInRollout(\n              rule.seed || id,\n              rule.hashAttribute,\n              this._ctx.stickyBucketService && !rule.disableStickyBucketing\n                ? rule.fallbackAttribute\n                : undefined,\n              rule.range,\n              rule.coverage,\n              rule.hashVersion\n            )\n          ) {\n            process.env.NODE_ENV !== \"production\" &&\n              this.log(\"Skip rule because user not included in rollout\", {\n                id,\n                rule,\n              });\n            continue;\n          }\n\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Force value from rule\", {\n              id,\n              rule,\n            });\n\n          // If this was a remotely evaluated experiment, fire the tracking callbacks\n          if (rule.tracks) {\n            rule.tracks.forEach((t) => {\n              this._track(t.experiment, t.result);\n            });\n          }\n\n          return this._getFeatureResult(id, rule.force as V, \"force\", rule.id);\n        }\n        if (!rule.variations) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip invalid rule\", {\n              id,\n              rule,\n            });\n\n          continue;\n        }\n\n        // For experiment rules, run an experiment\n        const exp: Experiment<V> = {\n          variations: rule.variations as [V, V, ...V[]],\n          key: rule.key || id,\n        };\n        if (\"coverage\" in rule) exp.coverage = rule.coverage;\n        if (rule.weights) exp.weights = rule.weights;\n        if (rule.hashAttribute) exp.hashAttribute = rule.hashAttribute;\n        if (rule.fallbackAttribute)\n          exp.fallbackAttribute = rule.fallbackAttribute;\n        if (rule.disableStickyBucketing)\n          exp.disableStickyBucketing = rule.disableStickyBucketing;\n        if (rule.bucketVersion !== undefined)\n          exp.bucketVersion = rule.bucketVersion;\n        if (rule.minBucketVersion !== undefined)\n          exp.minBucketVersion = rule.minBucketVersion;\n        if (rule.namespace) exp.namespace = rule.namespace;\n        if (rule.meta) exp.meta = rule.meta;\n        if (rule.ranges) exp.ranges = rule.ranges;\n        if (rule.name) exp.name = rule.name;\n        if (rule.phase) exp.phase = rule.phase;\n        if (rule.seed) exp.seed = rule.seed;\n        if (rule.hashVersion) exp.hashVersion = rule.hashVersion;\n        if (rule.filters) exp.filters = rule.filters;\n        if (rule.condition) exp.condition = rule.condition;\n\n        // Only return a value if the user is part of the experiment\n        const res = this._run(exp, id);\n        this._fireSubscriptions(exp, res);\n        if (res.inExperiment && !res.passthrough) {\n          return this._getFeatureResult(\n            id,\n            res.value,\n            \"experiment\",\n            rule.id,\n            exp,\n            res\n          );\n        }\n      }\n    }\n\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"Use default value\", {\n        id,\n        value: feature.defaultValue,\n      });\n\n    // Fall back to using the default value\n    return this._getFeatureResult(\n      id,\n      feature.defaultValue === undefined ? null : feature.defaultValue,\n      \"defaultValue\"\n    );\n  }\n\n  private _isIncludedInRollout(\n    seed: string,\n    hashAttribute: string | undefined,\n    fallbackAttribute: string | undefined,\n    range: VariationRange | undefined,\n    coverage: number | undefined,\n    hashVersion: number | undefined\n  ): boolean {\n    if (!range && coverage === undefined) return true;\n\n    const { hashValue } = this._getHashAttribute(\n      hashAttribute,\n      fallbackAttribute\n    );\n    if (!hashValue) {\n      return false;\n    }\n\n    const n = hash(seed, hashValue, hashVersion || 1);\n    if (n === null) return false;\n\n    return range\n      ? inRange(n, range)\n      : coverage !== undefined\n      ? n <= coverage\n      : true;\n  }\n\n  private _conditionPasses(condition: ConditionInterface): boolean {\n    return evalCondition(this.getAttributes(), condition);\n  }\n\n  private _isFilteredOut(filters: Filter[]): boolean {\n    return filters.some((filter) => {\n      const { hashValue } = this._getHashAttribute(filter.attribute);\n      if (!hashValue) return true;\n      const n = hash(filter.seed, hashValue, filter.hashVersion || 2);\n      if (n === null) return true;\n      return !filter.ranges.some((r) => inRange(n, r));\n    });\n  }\n\n  private _run<T>(\n    experiment: Experiment<T>,\n    featureId: string | null\n  ): Result<T> {\n    const key = experiment.key;\n    const numVariations = experiment.variations.length;\n\n    // 1. If experiment has less than 2 variations, return immediately\n    if (numVariations < 2) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Invalid experiment\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2. If the context is disabled, return immediately\n    if (this._ctx.enabled === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Context disabled\", { id: key });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 2.5. Merge in experiment overrides from the context\n    experiment = this._mergeOverrides(experiment);\n\n    // 2.6 New, more powerful URL targeting\n    if (\n      experiment.urlPatterns &&\n      !isURLTargeted(this._getContextUrl(), experiment.urlPatterns)\n    ) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url targeting\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 3. If a variation is forced from a querystring, return the forced variation\n    const qsOverride = getQueryStringOverride(\n      key,\n      this._getContextUrl(),\n      numVariations\n    );\n    if (qsOverride !== null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via querystring\", {\n          id: key,\n          variation: qsOverride,\n        });\n      return this._getResult(experiment, qsOverride, false, featureId);\n    }\n\n    // 4. If a variation is forced in the context, return the forced variation\n    if (this._ctx.forcedVariations && key in this._ctx.forcedVariations) {\n      const variation = this._ctx.forcedVariations[key];\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force via dev tools\", {\n          id: key,\n          variation,\n        });\n      return this._getResult(experiment, variation, false, featureId);\n    }\n\n    // 5. Exclude if a draft experiment or not active\n    if (experiment.status === \"draft\" || experiment.active === false) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because inactive\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 6. Get the hash attribute and return if empty\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n    if (!hashValue) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because missing hashAttribute\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    let assigned = -1;\n\n    let foundStickyBucket = false;\n    let stickyBucketVersionIsBlocked = false;\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const { variation, versionIsBlocked } = this._getStickyBucketVariation(\n        experiment.key,\n        experiment.bucketVersion,\n        experiment.minBucketVersion,\n        experiment.meta\n      );\n      foundStickyBucket = variation >= 0;\n      assigned = variation;\n      stickyBucketVersionIsBlocked = !!versionIsBlocked;\n    }\n\n    // Some checks are not needed if we already have a sticky bucket\n    if (!foundStickyBucket) {\n      // 7. Exclude if user is filtered out (used to be called \"namespace\")\n      if (experiment.filters) {\n        if (this._isFilteredOut(experiment.filters)) {\n          process.env.NODE_ENV !== \"production\" &&\n            this.log(\"Skip because of filters\", {\n              id: key,\n            });\n          return this._getResult(experiment, -1, false, featureId);\n        }\n      } else if (\n        experiment.namespace &&\n        !inNamespace(hashValue, experiment.namespace)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of namespace\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 7.5. Exclude if experiment.include returns false or throws\n      if (experiment.include && !isIncluded(experiment.include)) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of include function\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8. Exclude if condition is false\n      if (\n        experiment.condition &&\n        !this._conditionPasses(experiment.condition)\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of condition exp\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n\n      // 8.1. Exclude if user is not in a required group\n      if (\n        experiment.groups &&\n        !this._hasGroupOverlap(experiment.groups as string[])\n      ) {\n        process.env.NODE_ENV !== \"production\" &&\n          this.log(\"Skip because of groups\", {\n            id: key,\n          });\n        return this._getResult(experiment, -1, false, featureId);\n      }\n    }\n\n    // 8.2. Old style URL targeting\n    if (experiment.url && !this._urlIsValid(experiment.url as RegExp)) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of url\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 9. Get the variation from the sticky bucket or get bucket ranges and choose variation\n    const n = hash(\n      experiment.seed || key,\n      hashValue,\n      experiment.hashVersion || 1\n    );\n    if (n === null) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of invalid hash version\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    if (!foundStickyBucket) {\n      const ranges =\n        experiment.ranges ||\n        getBucketRanges(\n          numVariations,\n          experiment.coverage === undefined ? 1 : experiment.coverage,\n          experiment.weights\n        );\n      assigned = chooseVariation(n, ranges);\n    }\n\n    // 9.5 Unenroll if any prior sticky buckets are blocked by version\n    if (stickyBucketVersionIsBlocked) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because sticky bucket version is blocked\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId, undefined, true);\n    }\n\n    // 10. Return if not in experiment\n    if (assigned < 0) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because of coverage\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 11. Experiment has a forced variation\n    if (\"force\" in experiment) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Force variation\", {\n          id: key,\n          variation: experiment.force,\n        });\n      return this._getResult(\n        experiment,\n        experiment.force === undefined ? -1 : experiment.force,\n        false,\n        featureId\n      );\n    }\n\n    // 12. Exclude if in QA mode\n    if (this._ctx.qaMode) {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because QA mode\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 12.5. Exclude if experiment is stopped\n    if (experiment.status === \"stopped\") {\n      process.env.NODE_ENV !== \"production\" &&\n        this.log(\"Skip because stopped\", {\n          id: key,\n        });\n      return this._getResult(experiment, -1, false, featureId);\n    }\n\n    // 13. Build the result object\n    const result = this._getResult(\n      experiment,\n      assigned,\n      true,\n      featureId,\n      n,\n      foundStickyBucket\n    );\n\n    // 13.5. Persist sticky bucket\n    if (this._ctx.stickyBucketService && !experiment.disableStickyBucketing) {\n      const {\n        changed,\n        key: attrKey,\n        doc,\n      } = this._generateStickyBucketAssignmentDoc(\n        hashAttribute,\n        toString(hashValue),\n        {\n          [this._getStickyBucketExperimentKey(\n            experiment.key,\n            experiment.bucketVersion\n          )]: result.key,\n        }\n      );\n      if (changed) {\n        // update local docs\n        this._ctx.stickyBucketAssignmentDocs =\n          this._ctx.stickyBucketAssignmentDocs || {};\n        this._ctx.stickyBucketAssignmentDocs[attrKey] = doc;\n        // save doc\n        this._ctx.stickyBucketService.saveAssignments(doc);\n      }\n    }\n\n    // 14. Fire the tracking callback\n    this._track(experiment, result);\n\n    // 15. Return the result\n    process.env.NODE_ENV !== \"production\" &&\n      this.log(\"In experiment\", {\n        id: key,\n        variation: result.variationId,\n      });\n    return result;\n  }\n\n  log(msg: string, ctx: Record<string, unknown>) {\n    if (!this.debug) return;\n    if (this._ctx.log) this._ctx.log(msg, ctx);\n    else console.log(msg, ctx);\n  }\n\n  private _track<T>(experiment: Experiment<T>, result: Result<T>) {\n    if (!this._ctx.trackingCallback) return;\n\n    const key = experiment.key;\n\n    // Make sure a tracking callback is only fired once per unique experiment\n    const k =\n      result.hashAttribute + result.hashValue + key + result.variationId;\n    if (this._trackedExperiments.has(k)) return;\n    this._trackedExperiments.add(k);\n\n    try {\n      this._ctx.trackingCallback(experiment, result);\n    } catch (e) {\n      console.error(e);\n    }\n  }\n\n  private _mergeOverrides<T>(experiment: Experiment<T>): Experiment<T> {\n    const key = experiment.key;\n    const o = this._ctx.overrides;\n    if (o && o[key]) {\n      experiment = Object.assign({}, experiment, o[key]);\n      if (typeof experiment.url === \"string\") {\n        experiment.url = getUrlRegExp(\n          // eslint-disable-next-line\n          experiment.url as any\n        );\n      }\n    }\n\n    return experiment;\n  }\n\n  private _getHashAttribute(attr?: string, fallback?: string) {\n    let hashAttribute = attr || \"id\";\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    let hashValue: any = \"\";\n\n    if (this._attributeOverrides[hashAttribute]) {\n      hashValue = this._attributeOverrides[hashAttribute];\n    } else if (this._ctx.attributes) {\n      hashValue = this._ctx.attributes[hashAttribute] || \"\";\n    } else if (this._ctx.user) {\n      hashValue = this._ctx.user[hashAttribute] || \"\";\n    }\n\n    // if no match, try fallback\n    if (!hashValue && fallback) {\n      if (this._attributeOverrides[fallback]) {\n        hashValue = this._attributeOverrides[fallback];\n      } else if (this._ctx.attributes) {\n        hashValue = this._ctx.attributes[fallback] || \"\";\n      } else if (this._ctx.user) {\n        hashValue = this._ctx.user[fallback] || \"\";\n      }\n      if (hashValue) {\n        hashAttribute = fallback;\n      }\n    }\n\n    return { hashAttribute, hashValue };\n  }\n\n  private _getResult<T>(\n    experiment: Experiment<T>,\n    variationIndex: number,\n    hashUsed: boolean,\n    featureId: string | null,\n    bucket?: number,\n    stickyBucketUsed?: boolean\n  ): Result<T> {\n    let inExperiment = true;\n    // If assigned variation is not valid, use the baseline and mark the user as not in the experiment\n    if (variationIndex < 0 || variationIndex >= experiment.variations.length) {\n      variationIndex = 0;\n      inExperiment = false;\n    }\n\n    const { hashAttribute, hashValue } = this._getHashAttribute(\n      experiment.hashAttribute,\n      this._ctx.stickyBucketService && !experiment.disableStickyBucketing\n        ? experiment.fallbackAttribute\n        : undefined\n    );\n\n    const meta: Partial<VariationMeta> = experiment.meta\n      ? experiment.meta[variationIndex]\n      : {};\n\n    const res: Result<T> = {\n      key: meta.key || \"\" + variationIndex,\n      featureId,\n      inExperiment,\n      hashUsed,\n      variationId: variationIndex,\n      value: experiment.variations[variationIndex],\n      hashAttribute,\n      hashValue,\n      stickyBucketUsed: !!stickyBucketUsed,\n    };\n\n    if (meta.name) res.name = meta.name;\n    if (bucket !== undefined) res.bucket = bucket;\n    if (meta.passthrough) res.passthrough = meta.passthrough;\n\n    return res;\n  }\n\n  private _getContextUrl() {\n    return this._ctx.url || (isBrowser ? window.location.href : \"\");\n  }\n\n  private _urlIsValid(urlRegex: RegExp): boolean {\n    const url = this._getContextUrl();\n    if (!url) return false;\n\n    const pathOnly = url.replace(/^https?:\\/\\//, \"\").replace(/^[^/]*\\//, \"/\");\n\n    if (urlRegex.test(url)) return true;\n    if (urlRegex.test(pathOnly)) return true;\n    return false;\n  }\n\n  private _hasGroupOverlap(expGroups: string[]): boolean {\n    const groups = this._ctx.groups || {};\n    for (let i = 0; i < expGroups.length; i++) {\n      if (groups[expGroups[i]]) return true;\n    }\n    return false;\n  }\n\n  private _applyDOMChanges(changes: AutoExperimentVariation) {\n    if (!isBrowser) return;\n    const undo: (() => void)[] = [];\n    if (changes.css) {\n      const s = document.createElement(\"style\");\n      s.innerHTML = changes.css;\n      document.head.appendChild(s);\n      undo.push(() => s.remove());\n    }\n    if (changes.js) {\n      const script = document.createElement(\"script\");\n      script.innerHTML = changes.js;\n      document.head.appendChild(script);\n      undo.push(() => script.remove());\n    }\n    if (changes.domMutations) {\n      changes.domMutations.forEach((mutation) => {\n        undo.push(mutate.declarative(mutation as DeclarativeMutation).revert);\n      });\n    }\n    return () => {\n      undo.forEach((fn) => fn());\n    };\n  }\n\n  private _deriveStickyBucketIdentifierAttributes(data?: FeatureApiResponse) {\n    const attributes = new Set<string>();\n    const features = data && data.features ? data.features : this.getFeatures();\n    const experiments =\n      data && data.experiments ? data.experiments : this.getExperiments();\n    Object.keys(features).forEach((id) => {\n      const feature = features[id];\n      if (feature.rules) {\n        for (const rule of feature.rules) {\n          if (rule.variations) {\n            attributes.add(rule.hashAttribute || \"id\");\n            if (rule.fallbackAttribute) {\n              attributes.add(rule.fallbackAttribute);\n            }\n          }\n        }\n      }\n    });\n    experiments.map((experiment) => {\n      attributes.add(experiment.hashAttribute || \"id\");\n      if (experiment.fallbackAttribute) {\n        attributes.add(experiment.fallbackAttribute);\n      }\n    });\n    return Array.from(attributes);\n  }\n\n  public async refreshStickyBuckets(data?: FeatureApiResponse) {\n    if (this._ctx.stickyBucketService) {\n      const attributes = this._getStickyBucketAttributes(data);\n      this._ctx.stickyBucketAssignmentDocs = await this._ctx.stickyBucketService.getAllAssignments(\n        attributes\n      );\n    }\n  }\n\n  private _getStickyBucketAssignments(): StickyAssignments {\n    const mergedAssignments: StickyAssignments = {};\n    Object.values(this._ctx.stickyBucketAssignmentDocs || {}).forEach((doc) => {\n      if (doc.assignments) Object.assign(mergedAssignments, doc.assignments);\n    });\n    return mergedAssignments;\n  }\n\n  private _getStickyBucketVariation(\n    experimentKey: string,\n    experimentBucketVersion?: number,\n    minExperimentBucketVersion?: number,\n    meta?: VariationMeta[]\n  ): {\n    variation: number;\n    versionIsBlocked?: boolean;\n  } {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    minExperimentBucketVersion = minExperimentBucketVersion || 0;\n    meta = meta || [];\n    const id = this._getStickyBucketExperimentKey(\n      experimentKey,\n      experimentBucketVersion\n    );\n    const assignments = this._getStickyBucketAssignments();\n\n    // users with any blocked bucket version (0 to minExperimentBucketVersion) are excluded from the test\n    if (minExperimentBucketVersion > 0) {\n      for (let i = 0; i <= minExperimentBucketVersion; i++) {\n        const blockedKey = this._getStickyBucketExperimentKey(experimentKey, i);\n        if (assignments[blockedKey] !== undefined) {\n          return {\n            variation: -1,\n            versionIsBlocked: true,\n          };\n        }\n      }\n    }\n    const variationKey = assignments[id];\n    if (variationKey === undefined)\n      // no assignment found\n      return { variation: -1 };\n    const variation = meta.findIndex((m) => m.key === variationKey);\n    if (variation < 0)\n      // invalid assignment, treat as \"no assignment found\"\n      return { variation: -1 };\n\n    return { variation };\n  }\n\n  private _getStickyBucketExperimentKey(\n    experimentKey: string,\n    experimentBucketVersion?: number\n  ): StickyExperimentKey {\n    experimentBucketVersion = experimentBucketVersion || 0;\n    return `${experimentKey}__${experimentBucketVersion}`;\n  }\n\n  private _getStickyBucketAttributes(\n    data?: FeatureApiResponse\n  ): Record<string, string> {\n    const attributes: Record<string, string> = {};\n    this._ctx.stickyBucketIdentifierAttributes = !this._ctx\n      .stickyBucketIdentifierAttributes\n      ? this._deriveStickyBucketIdentifierAttributes(data)\n      : this._ctx.stickyBucketIdentifierAttributes;\n    this._ctx.stickyBucketIdentifierAttributes.forEach((attr) => {\n      const { hashValue } = this._getHashAttribute(attr);\n      attributes[attr] = toString(hashValue);\n    });\n    return attributes;\n  }\n\n  private _generateStickyBucketAssignmentDoc(\n    attributeName: string,\n    attributeValue: string,\n    assignments: StickyAssignments\n  ): {\n    key: StickyAttributeKey;\n    doc: StickyAssignmentsDocument;\n    changed: boolean;\n  } {\n    const key = `${attributeName}||${attributeValue}`;\n    const existingAssignments =\n      this._ctx.stickyBucketAssignmentDocs &&\n      this._ctx.stickyBucketAssignmentDocs[key]\n        ? this._ctx.stickyBucketAssignmentDocs[key].assignments || {}\n        : {};\n    const newAssignments = { ...existingAssignments, ...assignments };\n    const changed =\n      JSON.stringify(existingAssignments) !== JSON.stringify(newAssignments);\n\n    return {\n      key,\n      doc: {\n        attributeName,\n        attributeValue,\n        assignments: newAssignments,\n      },\n      changed,\n    };\n  }\n}\n"], "mappings": ";;;;;;AAAA;AA4BA;AAcA;AACA;AAA+E;AAE/E,MAAMA,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW;AAElE,MAAMC,WAAW,GAAG,IAAAC,oBAAc,GAAE;AAE7B,MAAMC,UAAU,CAGrB;EACA;EACA;;EAMA;;EAiBA;;EAUAC,WAAW,CAACC,OAAiB,EAAE;IAC7BA,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB;IACA;IACA,IAAI,CAACC,OAAO,GAAGL,WAAW;IAC1B,IAAI,CAACM,IAAI,GAAG,IAAI,CAACF,OAAO,GAAGA,OAAO;IAClC,IAAI,CAACG,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,mBAAmB,GAAG,IAAIC,GAAG,EAAE;IACpC,IAAI,CAACC,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,cAAc,GAAG,IAAIH,GAAG,EAAE;IAC/B,IAAI,CAACI,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,EAAE;IAC1B,IAAI,CAACC,oBAAoB,GAAG,IAAID,GAAG,EAAE;IACrC,IAAI,CAACE,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAI,CAACC,sBAAsB,GAAG,IAAIH,GAAG,EAAE;IACvC,IAAI,CAACI,iBAAiB,GAAG,IAAIZ,GAAG,EAAE;IAClC,IAAI,CAACa,mBAAmB,GAAG,KAAK;IAEhC,IAAIlB,OAAO,CAACmB,UAAU,EAAE;MACtB,IAAInB,OAAO,CAACoB,aAAa,EAAE;QACzB,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;MAC/D;MACA,IAAI,CAACrB,OAAO,CAACsB,SAAS,EAAE;QACtB,MAAM,IAAID,KAAK,CAAC,mBAAmB,CAAC;MACtC;MACA,IAAIE,QAAQ,GAAG,KAAK;MACpB,IAAI;QACFA,QAAQ,GAAG,CAAC,CAAC,IAAIC,GAAG,CAACxB,OAAO,CAACyB,OAAO,IAAI,EAAE,CAAC,CAACC,QAAQ,CAACC,KAAK,CACxD,kBAAkB,CACnB;MACH,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV;MAAA;MAEF,IAAIL,QAAQ,EAAE;QACZ,MAAM,IAAIF,KAAK,CAAC,2CAA2C,CAAC;MAC9D;IACF,CAAC,MAAM;MACL,IAAIrB,OAAO,CAAC6B,kBAAkB,EAAE;QAC9B,MAAM,IAAIR,KAAK,CAAC,iDAAiD,CAAC;MACpE;IACF;IAEA,IAAIrB,OAAO,CAAC8B,QAAQ,EAAE;MACpB,IAAI,CAACnB,KAAK,GAAG,IAAI;IACnB;IAEA,IAAIlB,SAAS,IAAIO,OAAO,CAAC+B,aAAa,EAAE;MACtCrC,MAAM,CAACsC,WAAW,GAAG,IAAI;MACzBrC,QAAQ,CAACsC,aAAa,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/C;IAEA,IAAIlC,OAAO,CAACmC,WAAW,EAAE;MACvB,IAAI,CAACxB,KAAK,GAAG,IAAI;MACjB,IAAI,CAACyB,yBAAyB,EAAE;IAClC;IAEA,IAAIpC,OAAO,CAACsB,SAAS,IAAI,CAACtB,OAAO,CAACmB,UAAU,EAAE;MAC5C,IAAI,CAACkB,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;IAChC;EACF;EAEA,MAAaC,YAAY,CAACC,OAA6B,EAAiB;IACtE,IAAIA,OAAO,IAAIA,OAAO,CAACC,WAAW,EAAE;MAClC;MACA,IAAI,CAACtC,IAAI,CAACuC,kBAAkB,GAAG,IAAI;IACrC;IACA,IAAI,CAACvB,mBAAmB,GAAG,IAAI;IAE/B,MAAM,IAAI,CAACmB,QAAQ,CAACE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;IAExC,IAAI,IAAI,CAACG,aAAa,EAAE,EAAE;MACxB,IAAAC,4BAAS,EAAC,IAAI,CAAC;IACjB;EACF;EAEA,MAAaC,eAAe,CAC1BL,OAAgC,EACjB;IACf,MAAM,IAAI,CAACF,QAAQ,CAACE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC;EAC3C;EAEOM,UAAU,GAAyB;IACxC,OAAO,CAAC,IAAI,CAACC,WAAW,EAAE,CAACrB,OAAO,EAAE,IAAI,CAACsB,YAAY,EAAE,CAAC;EAC1D;EACOD,WAAW,GAKhB;IACA,MAAME,WAAW,GAAG,IAAI,CAAC9C,IAAI,CAACuB,OAAO,IAAI,2BAA2B;IACpE,OAAO;MACLA,OAAO,EAAEuB,WAAW,CAACC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;MACxCC,aAAa,EAAE,CAAC,IAAI,CAAChD,IAAI,CAACgD,aAAa,IAAIF,WAAW,EAAEC,OAAO,CAC7D,MAAM,EACN,EAAE,CACH;MACDE,iBAAiB,EAAE,IAAI,CAACjD,IAAI,CAACkD,qBAAqB;MAClDC,2BAA2B,EAAE,IAAI,CAACnD,IAAI,CAACmD;IACzC,CAAC;EACH;EACON,YAAY,GAAW;IAC5B,OAAO,IAAI,CAAC7C,IAAI,CAACoB,SAAS,IAAI,EAAE;EAClC;EAEOgC,YAAY,GAAY;IAC7B,OAAO,IAAI,CAACpD,IAAI,CAACiB,UAAU,IAAI,KAAK;EACtC;EAEOoC,qBAAqB,GAAqC;IAC/D,OAAO,IAAI,CAACrD,IAAI,CAAC2B,kBAAkB;EACrC;EAEA,MAAcQ,QAAQ,CACpBE,OAAgC,EAChCiB,UAAoB,EACpBC,cAAwB,EACxB;IACAlB,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAACrC,IAAI,CAACoB,SAAS,EAAE;MACxB,MAAM,IAAID,KAAK,CAAC,mBAAmB,CAAC;IACtC;IACA,MAAM,IAAAuB,kCAAe,EACnB,IAAI,EACJL,OAAO,CAACmB,OAAO,EACfnB,OAAO,CAACoB,SAAS,IAAI,IAAI,CAACzD,IAAI,CAAC6B,aAAa,EAC5CyB,UAAU,EACVC,cAAc,EACd,IAAI,CAACvD,IAAI,CAAC0D,cAAc,KAAK,KAAK,CACnC;EACH;EAEQC,OAAO,GAAG;IAChB,IAAI,IAAI,CAAC1D,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,EAAE;IAClB;EACF;EAEO2D,WAAW,CAAChC,QAA2C,EAAE;IAC9D,IAAI,CAAC5B,IAAI,CAAC4B,QAAQ,GAAGA,QAAQ;IAC7B,IAAI,CAACnB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACkD,OAAO,EAAE;EAChB;EAEA,MAAaE,oBAAoB,CAC/BC,eAAuB,EACvB5C,aAAsB,EACtB6C,MAAqB,EACN;IACf,MAAMC,YAAY,GAAG,MAAM,IAAAC,aAAO,EAChCH,eAAe,EACf5C,aAAa,IAAI,IAAI,CAAClB,IAAI,CAACkB,aAAa,EACxC6C,MAAM,CACP;IACD,IAAI,CAACH,WAAW,CACdM,IAAI,CAACC,KAAK,CAACH,YAAY,CAAC,CACzB;EACH;EAEOI,cAAc,CAACnC,WAA6B,EAAQ;IACzD,IAAI,CAACjC,IAAI,CAACiC,WAAW,GAAGA,WAAW;IACnC,IAAI,CAACxB,KAAK,GAAG,IAAI;IACjB,IAAI,CAACyB,yBAAyB,EAAE;EAClC;EAEA,MAAamC,uBAAuB,CAClCP,eAAuB,EACvB5C,aAAsB,EACtB6C,MAAqB,EACN;IACf,MAAMO,eAAe,GAAG,MAAM,IAAAL,aAAO,EACnCH,eAAe,EACf5C,aAAa,IAAI,IAAI,CAAClB,IAAI,CAACkB,aAAa,EACxC6C,MAAM,CACP;IACD,IAAI,CAACK,cAAc,CAACF,IAAI,CAACC,KAAK,CAACG,eAAe,CAAC,CAAqB;EACtE;EAEA,MAAaC,cAAc,CACzBC,IAAwB,EACxBtD,aAAsB,EACtB6C,MAAqB,EACQ;IAC7B,IAAIS,IAAI,CAACC,iBAAiB,EAAE;MAC1BD,IAAI,CAAC5C,QAAQ,GAAGsC,IAAI,CAACC,KAAK,CACxB,MAAM,IAAAF,aAAO,EACXO,IAAI,CAACC,iBAAiB,EACtBvD,aAAa,IAAI,IAAI,CAAClB,IAAI,CAACkB,aAAa,EACxC6C,MAAM,CACP,CACF;MACD,OAAOS,IAAI,CAACC,iBAAiB;IAC/B;IACA,IAAID,IAAI,CAACE,oBAAoB,EAAE;MAC7BF,IAAI,CAACvC,WAAW,GAAGiC,IAAI,CAACC,KAAK,CAC3B,MAAM,IAAAF,aAAO,EACXO,IAAI,CAACE,oBAAoB,EACzBxD,aAAa,IAAI,IAAI,CAAClB,IAAI,CAACkB,aAAa,EACxC6C,MAAM,CACP,CACF;MACD,OAAOS,IAAI,CAACE,oBAAoB;IAClC;IACA,OAAOF,IAAI;EACb;EAEA,MAAaG,aAAa,CAACC,UAAsB,EAAE;IACjD,IAAI,CAAC5E,IAAI,CAAC4E,UAAU,GAAGA,UAAU;IACjC,IAAI,IAAI,CAAC5E,IAAI,CAAC6E,mBAAmB,EAAE;MACjC,MAAM,IAAI,CAACC,oBAAoB,EAAE;IACnC;IACA,IAAI,IAAI,CAAC9E,IAAI,CAACiB,UAAU,EAAE;MACxB,MAAM,IAAI,CAAC8D,qBAAqB,EAAE;MAClC;IACF;IACA,IAAI,CAACpB,OAAO,EAAE;IACd,IAAI,CAACzB,yBAAyB,EAAE;EAClC;EAEA,MAAa8C,qBAAqB,CAACC,SAAqB,EAAE;IACxD,IAAI,CAACpE,mBAAmB,GAAGoE,SAAS;IACpC,IAAI,IAAI,CAACjF,IAAI,CAAC6E,mBAAmB,EAAE;MACjC,MAAM,IAAI,CAACC,oBAAoB,EAAE;IACnC;IACA,IAAI,IAAI,CAAC9E,IAAI,CAACiB,UAAU,EAAE;MACxB,MAAM,IAAI,CAAC8D,qBAAqB,EAAE;MAClC;IACF;IACA,IAAI,CAACpB,OAAO,EAAE;IACd,IAAI,CAACzB,yBAAyB,EAAE;EAClC;EAEA,MAAagD,mBAAmB,CAACC,IAA4B,EAAE;IAC7D,IAAI,CAACnF,IAAI,CAACoF,gBAAgB,GAAGD,IAAI,IAAI,CAAC,CAAC;IACvC,IAAI,IAAI,CAACnF,IAAI,CAACiB,UAAU,EAAE;MACxB,MAAM,IAAI,CAAC8D,qBAAqB,EAAE;MAClC;IACF;IACA,IAAI,CAACpB,OAAO,EAAE;IACd,IAAI,CAACzB,yBAAyB,EAAE;EAClC;;EAEA;EACOmD,iBAAiB,CAACC,GAAqB,EAAE;IAC9C,IAAI,CAAC1E,oBAAoB,GAAG0E,GAAG;IAC/B,IAAI,CAAC3B,OAAO,EAAE;EAChB;EAEA,MAAa4B,MAAM,CAACC,GAAW,EAAE;IAC/B,IAAI,CAACxF,IAAI,CAACwF,GAAG,GAAGA,GAAG;IACnB,IAAI,IAAI,CAACxF,IAAI,CAACiB,UAAU,EAAE;MACxB,MAAM,IAAI,CAAC8D,qBAAqB,EAAE;MAClC,IAAI,CAAC7C,yBAAyB,CAAC,IAAI,CAAC;MACpC;IACF;IACA,IAAI,CAACA,yBAAyB,CAAC,IAAI,CAAC;EACtC;EAEOuD,aAAa,GAAG;IACrB,OAAO;MAAE,GAAG,IAAI,CAACzF,IAAI,CAAC4E,UAAU;MAAE,GAAG,IAAI,CAAC/D;IAAoB,CAAC;EACjE;EAEO6E,mBAAmB,GAAG;IAC3B,OAAO,IAAI,CAAC1F,IAAI,CAACoF,gBAAgB,IAAI,CAAC,CAAC;EACzC;EAEOO,iBAAiB,GAAG;IACzB;IACA,OAAO,IAAI,CAAC/E,oBAAoB,IAAI,IAAID,GAAG,EAAe;EAC5D;EAEOiF,6BAA6B,GAAG;IACrC,OAAO,IAAI,CAAC5F,IAAI,CAAC6F,0BAA0B,IAAI,CAAC,CAAC;EACnD;EAEOC,MAAM,GAAG;IACd,OAAO,IAAI,CAAC9F,IAAI,CAACwF,GAAG,IAAI,EAAE;EAC5B;EAEOO,WAAW,GAAG;IACnB,OAAO,IAAI,CAAC/F,IAAI,CAAC4B,QAAQ,IAAI,CAAC,CAAC;EACjC;EAEOoE,cAAc,GAAG;IACtB,OAAO,IAAI,CAAChG,IAAI,CAACiC,WAAW,IAAI,EAAE;EACpC;EAEOQ,SAAS,CAACwD,EAAwB,EAAc;IACrD,IAAI,CAAC3F,cAAc,CAAC4F,GAAG,CAACD,EAAE,CAAC;IAC3B,OAAO,MAAM;MACX,IAAI,CAAC3F,cAAc,CAAC6F,MAAM,CAACF,EAAE,CAAC;IAChC,CAAC;EACH;EAEQzD,aAAa,GAAG;IACtB,OAAO,IAAI,CAACxC,IAAI,CAAC0D,cAAc,KAAK,KAAK,IAAI,IAAI,CAAC1D,IAAI,CAACuC,kBAAkB;EAC3E;EAEA,MAAcwC,qBAAqB,GAAG;IACpC,IAAI,CAAC,IAAI,CAAC/E,IAAI,CAACiB,UAAU,EAAE;IAC3B,IAAI,CAAC,IAAI,CAACD,mBAAmB,EAAE;IAC/B,MAAM,IAAI,CAACmB,QAAQ,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAACiE,KAAK,CAAC,MAAM;MAC/C;IAAA,CACD,CAAC;EACJ;EAEOC,aAAa,GAAG;IACrB,OAAO,IAAI1F,GAAG,CAAC,IAAI,CAACD,SAAS,CAAC;EAChC;EAEO4F,OAAO,GAAG;IACf;IACA,IAAI,CAAChG,cAAc,CAACiG,KAAK,EAAE;IAC3B,IAAI,CAAC7F,SAAS,CAAC6F,KAAK,EAAE;IACtB,IAAI,CAACrG,mBAAmB,CAACqG,KAAK,EAAE;IAChC,IAAI,CAACnG,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACG,QAAQ,GAAG,EAAE;IAClB,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjBgG,YAAY,CAAC,IAAI,CAAChG,QAAQ,CAAC;IAC7B;IACA,IAAAiG,8BAAW,EAAC,IAAI,CAAC;IAEjB,IAAIlH,SAAS,IAAIC,MAAM,CAACsC,WAAW,KAAK,IAAI,EAAE;MAC5C,OAAOtC,MAAM,CAACsC,WAAW;IAC3B;;IAEA;IACA,IAAI,CAAChB,sBAAsB,CAAC4F,OAAO,CAAEC,GAAG,IAAK;MAC3CA,GAAG,CAACC,IAAI,EAAE;IACZ,CAAC,CAAC;IACF,IAAI,CAAC9F,sBAAsB,CAACyF,KAAK,EAAE;IACnC,IAAI,CAACxF,iBAAiB,CAACwF,KAAK,EAAE;EAChC;EAEOM,WAAW,CAACC,QAAoB,EAAE;IACvC,IAAI,CAAC7G,SAAS,GAAG6G,QAAQ;EAC3B;EAEOC,cAAc,CAACC,GAAW,EAAEC,SAAiB,EAAE;IACpD,IAAI,CAACjH,IAAI,CAACoF,gBAAgB,GAAG,IAAI,CAACpF,IAAI,CAACoF,gBAAgB,IAAI,CAAC,CAAC;IAC7D,IAAI,CAACpF,IAAI,CAACoF,gBAAgB,CAAC4B,GAAG,CAAC,GAAGC,SAAS;IAC3C,IAAI,IAAI,CAACjH,IAAI,CAACiB,UAAU,EAAE;MACxB,IAAI,CAAC8D,qBAAqB,EAAE;MAC5B;IACF;IACA,IAAI,CAAC7C,yBAAyB,EAAE;IAChC,IAAI,CAACyB,OAAO,EAAE;EAChB;EAEOuD,GAAG,CAAIC,UAAyB,EAAa;IAClD,MAAMC,MAAM,GAAG,IAAI,CAACC,IAAI,CAACF,UAAU,EAAE,IAAI,CAAC;IAC1C,IAAI,CAACG,kBAAkB,CAACH,UAAU,EAAEC,MAAM,CAAC;IAC3C,OAAOA,MAAM;EACf;EAEOG,iBAAiB,CAACP,GAAW,EAAE;IACpC,IAAI,CAACjG,iBAAiB,CAACmF,GAAG,CAACc,GAAG,CAAC;IAC/B,IAAI,CAAC,IAAI,CAAChH,IAAI,CAACiC,WAAW,EAAE,OAAO,IAAI;IACvC,MAAMA,WAAW,GAAG,IAAI,CAACjC,IAAI,CAACiC,WAAW,CAACuF,MAAM,CAAEb,GAAG,IAAKA,GAAG,CAACK,GAAG,KAAKA,GAAG,CAAC;IAC1E,OAAO/E,WAAW,CACfqD,GAAG,CAAEqB,GAAG,IAAK;MACZ,IAAI,CAACA,GAAG,CAACc,MAAM,EAAE,OAAO,IAAI;MAC5B,OAAO,IAAI,CAACC,kBAAkB,CAACf,GAAG,CAAC;IACrC,CAAC,CAAC,CACDa,MAAM,CAAEG,GAAG,IAAKA,GAAG,KAAK,IAAI,CAAC;EAClC;EAEQD,kBAAkB,CAACP,UAA0B,EAAES,UAAoB,EAAE;IAC3E,MAAMC,QAAQ,GAAG,IAAI,CAAC/G,sBAAsB,CAACgH,GAAG,CAACX,UAAU,CAAC;;IAE5D;IACA,IACEA,UAAU,CAACM,MAAM,IACjB,CAAC,IAAI,CAAC1G,iBAAiB,CAACgH,GAAG,CAACZ,UAAU,CAACH,GAAG,CAAC,IAC3C,CAACa,QAAQ,EAET,OAAO,IAAI;;IAEb;IACA,MAAMT,MAAM,GAAG,IAAI,CAACF,GAAG,CAACC,UAAU,CAAC;;IAEnC;IACA,MAAMa,SAAS,GAAG9D,IAAI,CAAC+D,SAAS,CAACb,MAAM,CAACc,KAAK,CAAC;;IAE9C;IACA,IACE,CAACN,UAAU,IACXR,MAAM,CAACe,YAAY,IACnBN,QAAQ,IACRA,QAAQ,CAACG,SAAS,KAAKA,SAAS,EAChC;MACA,OAAOZ,MAAM;IACf;;IAEA;IACA,IAAIS,QAAQ,EAAE,IAAI,CAACO,yBAAyB,CAACjB,UAAU,CAAC;;IAExD;IACA,IAAIC,MAAM,CAACe,YAAY,EAAE;MACvB,MAAMvB,IAAI,GAAG,IAAI,CAACyB,gBAAgB,CAACjB,MAAM,CAACc,KAAK,CAAC;MAChD,IAAItB,IAAI,EAAE;QACR,IAAI,CAAC9F,sBAAsB,CAACwH,GAAG,CAACnB,UAAU,EAAE;UAC1CP,IAAI;UACJoB;QACF,CAAC,CAAC;MACJ;IACF;IAEA,OAAOZ,MAAM;EACf;EAEQgB,yBAAyB,CAACzB,GAAmB,EAAE;IACrD,MAAMnC,IAAI,GAAG,IAAI,CAAC1D,sBAAsB,CAACgH,GAAG,CAACnB,GAAG,CAAC;IACjD,IAAInC,IAAI,EAAE;MACRA,IAAI,CAACoC,IAAI,EAAE;MACX,IAAI,CAAC9F,sBAAsB,CAACqF,MAAM,CAACQ,GAAG,CAAC;IACzC;EACF;EAEQzE,yBAAyB,CAAC0F,UAAoB,EAAE;IACtD,MAAM3F,WAAW,GAAG,IAAI,CAACjC,IAAI,CAACiC,WAAW,IAAI,EAAE;;IAE/C;IACA,MAAMsG,IAAI,GAAG,IAAIpI,GAAG,CAAC8B,WAAW,CAAC;IACjC,IAAI,CAACnB,sBAAsB,CAAC4F,OAAO,CAAC,CAAC8B,CAAC,EAAEC,CAAC,KAAK;MAC5C,IAAI,CAACF,IAAI,CAACR,GAAG,CAACU,CAAC,CAAC,EAAE;QAChBD,CAAC,CAAC5B,IAAI,EAAE;QACR,IAAI,CAAC9F,sBAAsB,CAACqF,MAAM,CAACsC,CAAC,CAAC;MACvC;IACF,CAAC,CAAC;;IAEF;IACAxG,WAAW,CAACyE,OAAO,CAAEC,GAAG,IAAK;MAC3B,IAAI,CAACe,kBAAkB,CAACf,GAAG,EAAEiB,UAAU,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEQN,kBAAkB,CAAIH,UAAyB,EAAEC,MAAiB,EAAE;IAC1E,MAAMJ,GAAG,GAAGG,UAAU,CAACH,GAAG;;IAE1B;IACA,MAAM0B,IAAI,GAAG,IAAI,CAAChI,SAAS,CAACoH,GAAG,CAACd,GAAG,CAAC;IACpC;IACA,IACE,CAAC0B,IAAI,IACLA,IAAI,CAACtB,MAAM,CAACe,YAAY,KAAKf,MAAM,CAACe,YAAY,IAChDO,IAAI,CAACtB,MAAM,CAACuB,WAAW,KAAKvB,MAAM,CAACuB,WAAW,EAC9C;MACA,IAAI,CAACjI,SAAS,CAAC4H,GAAG,CAACtB,GAAG,EAAE;QAAEG,UAAU;QAAEC;MAAO,CAAC,CAAC;MAC/C,IAAI,CAAC9G,cAAc,CAACoG,OAAO,CAAET,EAAE,IAAK;QAClC,IAAI;UACFA,EAAE,CAACkB,UAAU,EAAEC,MAAM,CAAC;QACxB,CAAC,CAAC,OAAO1F,CAAC,EAAE;UACVkH,OAAO,CAACC,KAAK,CAACnH,CAAC,CAAC;QAClB;MACF,CAAC,CAAC;IACJ;EACF;EAEQoH,kBAAkB,CAAC9B,GAAW,EAAEW,GAAkB,EAAQ;IAChE;IACA,IAAIA,GAAG,CAACoB,MAAM,KAAK,UAAU,EAAE;;IAE/B;IACA,MAAMC,gBAAgB,GAAG9E,IAAI,CAAC+D,SAAS,CAACN,GAAG,CAACO,KAAK,CAAC;IAClD,IAAI,IAAI,CAAC9H,gBAAgB,CAAC4G,GAAG,CAAC,KAAKgC,gBAAgB,EAAE;IACrD,IAAI,CAAC5I,gBAAgB,CAAC4G,GAAG,CAAC,GAAGgC,gBAAgB;;IAE7C;IACA,IAAI,IAAI,CAAChJ,IAAI,CAACiJ,cAAc,EAAE;MAC5B,IAAI;QACF,IAAI,CAACjJ,IAAI,CAACiJ,cAAc,CAACjC,GAAG,EAAEW,GAAG,CAAC;MACpC,CAAC,CAAC,OAAOjG,CAAC,EAAE;QACV;MAAA;IAEJ;;IAEA;IACA,IAAI,CAACnC,SAAS,IAAI,CAACC,MAAM,CAAC0J,KAAK,EAAE;IACjC,IAAI,CAAC3I,QAAQ,CAAC4I,IAAI,CAAC;MACjBnC,GAAG;MACHoC,EAAE,EAAEzB,GAAG,CAACyB;IACV,CAAC,CAAC;IACF,IAAI,CAAC,IAAI,CAAC5I,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAGhB,MAAM,CAAC6J,UAAU,CAAC,MAAM;QACtC;QACA,IAAI,CAAC7I,QAAQ,GAAG,CAAC;QACjB,MAAM8I,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC/I,QAAQ,CAAC;QAC5B,IAAI,CAACA,QAAQ,GAAG,EAAE;;QAElB;QACA,IAAI,CAAC,IAAI,CAACP,IAAI,CAACuJ,WAAW,EAAE;QAE5B/J,MAAM,CACH0J,KAAK,yCAEF,IAAI,CAAClJ,IAAI,CAACuJ,WAAW,qBACZC,kBAAkB,CAACtF,IAAI,CAAC+D,SAAS,CAACqB,CAAC,CAAC,CAAC,GAEhD;UACEG,KAAK,EAAE,UAAU;UACjBC,IAAI,EAAE;QACR,CAAC,CACF,CACAtD,KAAK,CAAC,MAAM;UACX;QAAA,CACD,CAAC;MACN,CAAC,EAAE,IAAI,CAACpG,IAAI,CAAC2J,gBAAgB,IAAI,IAAI,CAAC;IACxC;EACF;EAEQC,iBAAiB,CACvB5C,GAAW,EACXkB,KAAQ,EACRa,MAA2B,EAC3Bc,MAAe,EACf1C,UAA0B,EAC1BC,MAAkB,EACA;IAClB,MAAM0C,GAAkB,GAAG;MACzB5B,KAAK;MACLkB,EAAE,EAAE,CAAC,CAAClB,KAAK;MACX6B,GAAG,EAAE,CAAC7B,KAAK;MACXa,MAAM;MACNc,MAAM,EAAEA,MAAM,IAAI;IACpB,CAAC;IACD,IAAI1C,UAAU,EAAE2C,GAAG,CAAC3C,UAAU,GAAGA,UAAU;IAC3C,IAAIC,MAAM,EAAE0C,GAAG,CAACE,gBAAgB,GAAG5C,MAAM;;IAEzC;IACA,IAAI,CAAC0B,kBAAkB,CAAC9B,GAAG,EAAE8C,GAAG,CAAC;IAEjC,OAAOA,GAAG;EACZ;EAEOG,IAAI,CAAgDjD,GAAM,EAAW;IAC1E,OAAO,IAAI,CAACkD,WAAW,CAAClD,GAAG,CAAC,CAACoC,EAAE;EACjC;EAEOe,KAAK,CAAgDnD,GAAM,EAAW;IAC3E,OAAO,IAAI,CAACkD,WAAW,CAAClD,GAAG,CAAC,CAAC+C,GAAG;EAClC;EAEOK,eAAe,CAGpBpD,GAAM,EAAEqD,YAAe,EAAsB;IAC7C,MAAMnC,KAAK,GAAG,IAAI,CAACgC,WAAW,CAAwBlD,GAAG,CAAC,CAACkB,KAAK;IAChE,OAAOA,KAAK,KAAK,IAAI,GAAImC,YAAY,GAA0BnC,KAAK;EACtE;;EAEA;AACF;AACA;AACA;EACE;EACOoC,OAAO,CAGZC,EAAK,EAA2B;IAChC,OAAO,IAAI,CAACL,WAAW,CAACK,EAAE,CAAC;EAC7B;EAEOL,WAAW,CAGhBK,EAAK,EAA2B;IAChC;IACA,IAAI,IAAI,CAAC3J,oBAAoB,CAACmH,GAAG,CAACwC,EAAE,CAAC,EAAE;MACrCC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAC1BJ,EAAE;QACFrC,KAAK,EAAE,IAAI,CAACtH,oBAAoB,CAACkH,GAAG,CAACyC,EAAE;MACzC,CAAC,CAAC;MACJ,OAAO,IAAI,CAACX,iBAAiB,CAC3BW,EAAE,EACF,IAAI,CAAC3J,oBAAoB,CAACkH,GAAG,CAACyC,EAAE,CAAC,EACjC,UAAU,CACX;IACH;;IAEA;IACA,IAAI,CAAC,IAAI,CAACvK,IAAI,CAAC4B,QAAQ,IAAI,CAAC,IAAI,CAAC5B,IAAI,CAAC4B,QAAQ,CAAC2I,EAAE,CAAC,EAAE;MAClDC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAAEJ;MAAG,CAAC,CAAC;MACrC,OAAO,IAAI,CAACX,iBAAiB,CAACW,EAAE,EAAE,IAAI,EAAE,gBAAgB,CAAC;IAC3D;;IAEA;IACA,MAAMD,OAA6B,GAAG,IAAI,CAACtK,IAAI,CAAC4B,QAAQ,CAAC2I,EAAE,CAAC;;IAE5D;IACA,IAAID,OAAO,CAACM,KAAK,EAAE;MACjB,KAAK,MAAMC,IAAI,IAAIP,OAAO,CAACM,KAAK,EAAE;QAChC;QACA,IAAIC,IAAI,CAACC,OAAO,IAAI,IAAI,CAACC,cAAc,CAACF,IAAI,CAACC,OAAO,CAAC,EAAE;UACrDN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,8BAA8B,EAAE;YACvCJ,EAAE;YACFM;UACF,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,IAAI,OAAO,IAAIA,IAAI,EAAE;UACnB;UACA,IAAIA,IAAI,CAACG,SAAS,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACJ,IAAI,CAACG,SAAS,CAAC,EAAE;YAC5DR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,mCAAmC,EAAE;cAC5CJ,EAAE;cACFM;YACF,CAAC,CAAC;YACJ;UACF;;UAEA;UACA,IACE,CAAC,IAAI,CAACK,oBAAoB,CACxBL,IAAI,CAACM,IAAI,IAAIZ,EAAE,EACfM,IAAI,CAACO,aAAa,EAClB,IAAI,CAACpL,IAAI,CAAC6E,mBAAmB,IAAI,CAACgG,IAAI,CAACQ,sBAAsB,GACzDR,IAAI,CAACS,iBAAiB,GACtBC,SAAS,EACbV,IAAI,CAACW,KAAK,EACVX,IAAI,CAACY,QAAQ,EACbZ,IAAI,CAACa,WAAW,CACjB,EACD;YACAlB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,gDAAgD,EAAE;cACzDJ,EAAE;cACFM;YACF,CAAC,CAAC;YACJ;UACF;UAEAL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,uBAAuB,EAAE;YAChCJ,EAAE;YACFM;UACF,CAAC,CAAC;;UAEJ;UACA,IAAIA,IAAI,CAACc,MAAM,EAAE;YACfd,IAAI,CAACc,MAAM,CAACjF,OAAO,CAAEkF,CAAC,IAAK;cACzB,IAAI,CAACC,MAAM,CAACD,CAAC,CAACzE,UAAU,EAAEyE,CAAC,CAACxE,MAAM,CAAC;YACrC,CAAC,CAAC;UACJ;UAEA,OAAO,IAAI,CAACwC,iBAAiB,CAACW,EAAE,EAAEM,IAAI,CAACiB,KAAK,EAAO,OAAO,EAAEjB,IAAI,CAACN,EAAE,CAAC;QACtE;QACA,IAAI,CAACM,IAAI,CAACkB,UAAU,EAAE;UACpBvB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,mBAAmB,EAAE;YAC5BJ,EAAE;YACFM;UACF,CAAC,CAAC;UAEJ;QACF;;QAEA;QACA,MAAMlE,GAAkB,GAAG;UACzBoF,UAAU,EAAElB,IAAI,CAACkB,UAA4B;UAC7C/E,GAAG,EAAE6D,IAAI,CAAC7D,GAAG,IAAIuD;QACnB,CAAC;QACD,IAAI,UAAU,IAAIM,IAAI,EAAElE,GAAG,CAAC8E,QAAQ,GAAGZ,IAAI,CAACY,QAAQ;QACpD,IAAIZ,IAAI,CAACmB,OAAO,EAAErF,GAAG,CAACqF,OAAO,GAAGnB,IAAI,CAACmB,OAAO;QAC5C,IAAInB,IAAI,CAACO,aAAa,EAAEzE,GAAG,CAACyE,aAAa,GAAGP,IAAI,CAACO,aAAa;QAC9D,IAAIP,IAAI,CAACS,iBAAiB,EACxB3E,GAAG,CAAC2E,iBAAiB,GAAGT,IAAI,CAACS,iBAAiB;QAChD,IAAIT,IAAI,CAACQ,sBAAsB,EAC7B1E,GAAG,CAAC0E,sBAAsB,GAAGR,IAAI,CAACQ,sBAAsB;QAC1D,IAAIR,IAAI,CAACoB,aAAa,KAAKV,SAAS,EAClC5E,GAAG,CAACsF,aAAa,GAAGpB,IAAI,CAACoB,aAAa;QACxC,IAAIpB,IAAI,CAACqB,gBAAgB,KAAKX,SAAS,EACrC5E,GAAG,CAACuF,gBAAgB,GAAGrB,IAAI,CAACqB,gBAAgB;QAC9C,IAAIrB,IAAI,CAACsB,SAAS,EAAExF,GAAG,CAACwF,SAAS,GAAGtB,IAAI,CAACsB,SAAS;QAClD,IAAItB,IAAI,CAACuB,IAAI,EAAEzF,GAAG,CAACyF,IAAI,GAAGvB,IAAI,CAACuB,IAAI;QACnC,IAAIvB,IAAI,CAACwB,MAAM,EAAE1F,GAAG,CAAC0F,MAAM,GAAGxB,IAAI,CAACwB,MAAM;QACzC,IAAIxB,IAAI,CAACyB,IAAI,EAAE3F,GAAG,CAAC2F,IAAI,GAAGzB,IAAI,CAACyB,IAAI;QACnC,IAAIzB,IAAI,CAAC0B,KAAK,EAAE5F,GAAG,CAAC4F,KAAK,GAAG1B,IAAI,CAAC0B,KAAK;QACtC,IAAI1B,IAAI,CAACM,IAAI,EAAExE,GAAG,CAACwE,IAAI,GAAGN,IAAI,CAACM,IAAI;QACnC,IAAIN,IAAI,CAACa,WAAW,EAAE/E,GAAG,CAAC+E,WAAW,GAAGb,IAAI,CAACa,WAAW;QACxD,IAAIb,IAAI,CAACC,OAAO,EAAEnE,GAAG,CAACmE,OAAO,GAAGD,IAAI,CAACC,OAAO;QAC5C,IAAID,IAAI,CAACG,SAAS,EAAErE,GAAG,CAACqE,SAAS,GAAGH,IAAI,CAACG,SAAS;;QAElD;QACA,MAAMrD,GAAG,GAAG,IAAI,CAACN,IAAI,CAACV,GAAG,EAAE4D,EAAE,CAAC;QAC9B,IAAI,CAACjD,kBAAkB,CAACX,GAAG,EAAEgB,GAAG,CAAC;QACjC,IAAIA,GAAG,CAACQ,YAAY,IAAI,CAACR,GAAG,CAAC6E,WAAW,EAAE;UACxC,OAAO,IAAI,CAAC5C,iBAAiB,CAC3BW,EAAE,EACF5C,GAAG,CAACO,KAAK,EACT,YAAY,EACZ2C,IAAI,CAACN,EAAE,EACP5D,GAAG,EACHgB,GAAG,CACJ;QACH;MACF;IACF;IAEA6C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,mBAAmB,EAAE;MAC5BJ,EAAE;MACFrC,KAAK,EAAEoC,OAAO,CAACD;IACjB,CAAC,CAAC;;IAEJ;IACA,OAAO,IAAI,CAACT,iBAAiB,CAC3BW,EAAE,EACFD,OAAO,CAACD,YAAY,KAAKkB,SAAS,GAAG,IAAI,GAAGjB,OAAO,CAACD,YAAY,EAChE,cAAc,CACf;EACH;EAEQa,oBAAoB,CAC1BC,IAAY,EACZC,aAAiC,EACjCE,iBAAqC,EACrCE,KAAiC,EACjCC,QAA4B,EAC5BC,WAA+B,EACtB;IACT,IAAI,CAACF,KAAK,IAAIC,QAAQ,KAAKF,SAAS,EAAE,OAAO,IAAI;IAEjD,MAAM;MAAEkB;IAAU,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAC1CtB,aAAa,EACbE,iBAAiB,CAClB;IACD,IAAI,CAACmB,SAAS,EAAE;MACd,OAAO,KAAK;IACd;IAEA,MAAME,CAAC,GAAG,IAAAC,UAAI,EAACzB,IAAI,EAAEsB,SAAS,EAAEf,WAAW,IAAI,CAAC,CAAC;IACjD,IAAIiB,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK;IAE5B,OAAOnB,KAAK,GACR,IAAAqB,aAAO,EAACF,CAAC,EAAEnB,KAAK,CAAC,GACjBC,QAAQ,KAAKF,SAAS,GACtBoB,CAAC,IAAIlB,QAAQ,GACb,IAAI;EACV;EAEQR,gBAAgB,CAACD,SAA6B,EAAW;IAC/D,OAAO,IAAA8B,uBAAa,EAAC,IAAI,CAACrH,aAAa,EAAE,EAAEuF,SAAS,CAAC;EACvD;EAEQD,cAAc,CAACD,OAAiB,EAAW;IACjD,OAAOA,OAAO,CAACiC,IAAI,CAAEvF,MAAM,IAAK;MAC9B,MAAM;QAAEiF;MAAU,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAAClF,MAAM,CAACwF,SAAS,CAAC;MAC9D,IAAI,CAACP,SAAS,EAAE,OAAO,IAAI;MAC3B,MAAME,CAAC,GAAG,IAAAC,UAAI,EAACpF,MAAM,CAAC2D,IAAI,EAAEsB,SAAS,EAAEjF,MAAM,CAACkE,WAAW,IAAI,CAAC,CAAC;MAC/D,IAAIiB,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI;MAC3B,OAAO,CAACnF,MAAM,CAAC6E,MAAM,CAACU,IAAI,CAAEE,CAAC,IAAK,IAAAJ,aAAO,EAACF,CAAC,EAAEM,CAAC,CAAC,CAAC;IAClD,CAAC,CAAC;EACJ;EAEQ5F,IAAI,CACVF,UAAyB,EACzB+F,SAAwB,EACb;IACX,MAAMlG,GAAG,GAAGG,UAAU,CAACH,GAAG;IAC1B,MAAMmG,aAAa,GAAGhG,UAAU,CAAC4E,UAAU,CAACqB,MAAM;;IAElD;IACA,IAAID,aAAa,GAAG,CAAC,EAAE;MACrB3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,oBAAoB,EAAE;QAAEJ,EAAE,EAAEvD;MAAI,CAAC,CAAC;MAC7C,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA,IAAI,IAAI,CAAClN,IAAI,CAACsN,OAAO,KAAK,KAAK,EAAE;MAC/B9C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,kBAAkB,EAAE;QAAEJ,EAAE,EAAEvD;MAAI,CAAC,CAAC;MAC3C,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA/F,UAAU,GAAG,IAAI,CAACoG,eAAe,CAACpG,UAAU,CAAC;;IAE7C;IACA,IACEA,UAAU,CAACqG,WAAW,IACtB,CAAC,IAAAC,mBAAa,EAAC,IAAI,CAACC,cAAc,EAAE,EAAEvG,UAAU,CAACqG,WAAW,CAAC,EAC7D;MACAhD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,+BAA+B,EAAE;QACxCJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA,MAAMS,UAAU,GAAG,IAAAC,4BAAsB,EACvC5G,GAAG,EACH,IAAI,CAAC0G,cAAc,EAAE,EACrBP,aAAa,CACd;IACD,IAAIQ,UAAU,KAAK,IAAI,EAAE;MACvBnD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAChCJ,EAAE,EAAEvD,GAAG;QACPC,SAAS,EAAE0G;MACb,CAAC,CAAC;MACJ,OAAO,IAAI,CAACN,UAAU,CAAClG,UAAU,EAAEwG,UAAU,EAAE,KAAK,EAAET,SAAS,CAAC;IAClE;;IAEA;IACA,IAAI,IAAI,CAAClN,IAAI,CAACoF,gBAAgB,IAAI4B,GAAG,IAAI,IAAI,CAAChH,IAAI,CAACoF,gBAAgB,EAAE;MACnE,MAAM6B,SAAS,GAAG,IAAI,CAACjH,IAAI,CAACoF,gBAAgB,CAAC4B,GAAG,CAAC;MACjDwD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAC9BJ,EAAE,EAAEvD,GAAG;QACPC;MACF,CAAC,CAAC;MACJ,OAAO,IAAI,CAACoG,UAAU,CAAClG,UAAU,EAAEF,SAAS,EAAE,KAAK,EAAEiG,SAAS,CAAC;IACjE;;IAEA;IACA,IAAI/F,UAAU,CAAC0G,MAAM,KAAK,OAAO,IAAI1G,UAAU,CAAC2G,MAAM,KAAK,KAAK,EAAE;MAChEtD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAChCJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA,MAAM;MAAE9B,aAAa;MAAEqB;IAAU,CAAC,GAAG,IAAI,CAACC,iBAAiB,CACzDvF,UAAU,CAACiE,aAAa,EACxB,IAAI,CAACpL,IAAI,CAAC6E,mBAAmB,IAAI,CAACsC,UAAU,CAACkE,sBAAsB,GAC/DlE,UAAU,CAACmE,iBAAiB,GAC5BC,SAAS,CACd;IACD,IAAI,CAACkB,SAAS,EAAE;MACdjC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,oCAAoC,EAAE;QAC7CJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;IAEA,IAAIa,QAAQ,GAAG,CAAC,CAAC;IAEjB,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,4BAA4B,GAAG,KAAK;IACxC,IAAI,IAAI,CAACjO,IAAI,CAAC6E,mBAAmB,IAAI,CAACsC,UAAU,CAACkE,sBAAsB,EAAE;MACvE,MAAM;QAAEpE,SAAS;QAAEiH;MAAiB,CAAC,GAAG,IAAI,CAACC,yBAAyB,CACpEhH,UAAU,CAACH,GAAG,EACdG,UAAU,CAAC8E,aAAa,EACxB9E,UAAU,CAAC+E,gBAAgB,EAC3B/E,UAAU,CAACiF,IAAI,CAChB;MACD4B,iBAAiB,GAAG/G,SAAS,IAAI,CAAC;MAClC8G,QAAQ,GAAG9G,SAAS;MACpBgH,4BAA4B,GAAG,CAAC,CAACC,gBAAgB;IACnD;;IAEA;IACA,IAAI,CAACF,iBAAiB,EAAE;MACtB;MACA,IAAI7G,UAAU,CAAC2D,OAAO,EAAE;QACtB,IAAI,IAAI,CAACC,cAAc,CAAC5D,UAAU,CAAC2D,OAAO,CAAC,EAAE;UAC3CN,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,yBAAyB,EAAE;YAClCJ,EAAE,EAAEvD;UACN,CAAC,CAAC;UACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;QAC1D;MACF,CAAC,MAAM,IACL/F,UAAU,CAACgF,SAAS,IACpB,CAAC,IAAAiC,iBAAW,EAAC3B,SAAS,EAAEtF,UAAU,CAACgF,SAAS,CAAC,EAC7C;QACA3B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,2BAA2B,EAAE;UACpCJ,EAAE,EAAEvD;QACN,CAAC,CAAC;QACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;MAC1D;;MAEA;MACA,IAAI/F,UAAU,CAACkH,OAAO,IAAI,CAAC,IAAAC,gBAAU,EAACnH,UAAU,CAACkH,OAAO,CAAC,EAAE;QACzD7D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,kCAAkC,EAAE;UAC3CJ,EAAE,EAAEvD;QACN,CAAC,CAAC;QACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;MAC1D;;MAEA;MACA,IACE/F,UAAU,CAAC6D,SAAS,IACpB,CAAC,IAAI,CAACC,gBAAgB,CAAC9D,UAAU,CAAC6D,SAAS,CAAC,EAC5C;QACAR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,+BAA+B,EAAE;UACxCJ,EAAE,EAAEvD;QACN,CAAC,CAAC;QACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;MAC1D;;MAEA;MACA,IACE/F,UAAU,CAACoH,MAAM,IACjB,CAAC,IAAI,CAACC,gBAAgB,CAACrH,UAAU,CAACoH,MAAM,CAAa,EACrD;QACA/D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,wBAAwB,EAAE;UACjCJ,EAAE,EAAEvD;QACN,CAAC,CAAC;QACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;MAC1D;IACF;;IAEA;IACA,IAAI/F,UAAU,CAAC3B,GAAG,IAAI,CAAC,IAAI,CAACiJ,WAAW,CAACtH,UAAU,CAAC3B,GAAG,CAAW,EAAE;MACjEgF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAC9BJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA,MAAMP,CAAC,GAAG,IAAAC,UAAI,EACZzF,UAAU,CAACgE,IAAI,IAAInE,GAAG,EACtByF,SAAS,EACTtF,UAAU,CAACuE,WAAW,IAAI,CAAC,CAC5B;IACD,IAAIiB,CAAC,KAAK,IAAI,EAAE;MACdnC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAC/CJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;IAEA,IAAI,CAACc,iBAAiB,EAAE;MACtB,MAAM3B,MAAM,GACVlF,UAAU,CAACkF,MAAM,IACjB,IAAAqC,qBAAe,EACbvB,aAAa,EACbhG,UAAU,CAACsE,QAAQ,KAAKF,SAAS,GAAG,CAAC,GAAGpE,UAAU,CAACsE,QAAQ,EAC3DtE,UAAU,CAAC6E,OAAO,CACnB;MACH+B,QAAQ,GAAG,IAAAY,qBAAe,EAAChC,CAAC,EAAEN,MAAM,CAAC;IACvC;;IAEA;IACA,IAAI4B,4BAA4B,EAAE;MAChCzD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,+CAA+C,EAAE;QACxDJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,EAAE3B,SAAS,EAAE,IAAI,CAAC;IAC3E;;IAEA;IACA,IAAIwC,QAAQ,GAAG,CAAC,EAAE;MAChBvD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,0BAA0B,EAAE;QACnCJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA,IAAI,OAAO,IAAI/F,UAAU,EAAE;MACzBqD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAC1BJ,EAAE,EAAEvD,GAAG;QACPC,SAAS,EAAEE,UAAU,CAAC2E;MACxB,CAAC,CAAC;MACJ,OAAO,IAAI,CAACuB,UAAU,CACpBlG,UAAU,EACVA,UAAU,CAAC2E,KAAK,KAAKP,SAAS,GAAG,CAAC,CAAC,GAAGpE,UAAU,CAAC2E,KAAK,EACtD,KAAK,EACLoB,SAAS,CACV;IACH;;IAEA;IACA,IAAI,IAAI,CAAClN,IAAI,CAAC4O,MAAM,EAAE;MACpBpE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAC/BJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA,IAAI/F,UAAU,CAAC0G,MAAM,KAAK,SAAS,EAAE;MACnCrD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,sBAAsB,EAAE;QAC/BJ,EAAE,EAAEvD;MACN,CAAC,CAAC;MACJ,OAAO,IAAI,CAACqG,UAAU,CAAClG,UAAU,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE+F,SAAS,CAAC;IAC1D;;IAEA;IACA,MAAM9F,MAAM,GAAG,IAAI,CAACiG,UAAU,CAC5BlG,UAAU,EACV4G,QAAQ,EACR,IAAI,EACJb,SAAS,EACTP,CAAC,EACDqB,iBAAiB,CAClB;;IAED;IACA,IAAI,IAAI,CAAChO,IAAI,CAAC6E,mBAAmB,IAAI,CAACsC,UAAU,CAACkE,sBAAsB,EAAE;MACvE,MAAM;QACJwD,OAAO;QACP7H,GAAG,EAAE8H,OAAO;QACZC;MACF,CAAC,GAAG,IAAI,CAACC,kCAAkC,CACzC5D,aAAa,EACb,IAAA6D,cAAQ,EAACxC,SAAS,CAAC,EACnB;QACE,CAAC,IAAI,CAACyC,6BAA6B,CACjC/H,UAAU,CAACH,GAAG,EACdG,UAAU,CAAC8E,aAAa,CACzB,GAAG7E,MAAM,CAACJ;MACb,CAAC,CACF;MACD,IAAI6H,OAAO,EAAE;QACX;QACA,IAAI,CAAC7O,IAAI,CAAC6F,0BAA0B,GAClC,IAAI,CAAC7F,IAAI,CAAC6F,0BAA0B,IAAI,CAAC,CAAC;QAC5C,IAAI,CAAC7F,IAAI,CAAC6F,0BAA0B,CAACiJ,OAAO,CAAC,GAAGC,GAAG;QACnD;QACA,IAAI,CAAC/O,IAAI,CAAC6E,mBAAmB,CAACsK,eAAe,CAACJ,GAAG,CAAC;MACpD;IACF;;IAEA;IACA,IAAI,CAAClD,MAAM,CAAC1E,UAAU,EAAEC,MAAM,CAAC;;IAE/B;IACAoD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnC,IAAI,CAACC,GAAG,CAAC,eAAe,EAAE;MACxBJ,EAAE,EAAEvD,GAAG;MACPC,SAAS,EAAEG,MAAM,CAACuB;IACpB,CAAC,CAAC;IACJ,OAAOvB,MAAM;EACf;EAEAuD,GAAG,CAACyE,GAAW,EAAEC,GAA4B,EAAE;IAC7C,IAAI,CAAC,IAAI,CAAChP,KAAK,EAAE;IACjB,IAAI,IAAI,CAACL,IAAI,CAAC2K,GAAG,EAAE,IAAI,CAAC3K,IAAI,CAAC2K,GAAG,CAACyE,GAAG,EAAEC,GAAG,CAAC,CAAC,KACtCzG,OAAO,CAAC+B,GAAG,CAACyE,GAAG,EAAEC,GAAG,CAAC;EAC5B;EAEQxD,MAAM,CAAI1E,UAAyB,EAAEC,MAAiB,EAAE;IAC9D,IAAI,CAAC,IAAI,CAACpH,IAAI,CAACsP,gBAAgB,EAAE;IAEjC,MAAMtI,GAAG,GAAGG,UAAU,CAACH,GAAG;;IAE1B;IACA,MAAMyB,CAAC,GACLrB,MAAM,CAACgE,aAAa,GAAGhE,MAAM,CAACqF,SAAS,GAAGzF,GAAG,GAAGI,MAAM,CAACuB,WAAW;IACpE,IAAI,IAAI,CAACzI,mBAAmB,CAAC6H,GAAG,CAACU,CAAC,CAAC,EAAE;IACrC,IAAI,CAACvI,mBAAmB,CAACgG,GAAG,CAACuC,CAAC,CAAC;IAE/B,IAAI;MACF,IAAI,CAACzI,IAAI,CAACsP,gBAAgB,CAACnI,UAAU,EAAEC,MAAM,CAAC;IAChD,CAAC,CAAC,OAAO1F,CAAC,EAAE;MACVkH,OAAO,CAACC,KAAK,CAACnH,CAAC,CAAC;IAClB;EACF;EAEQ6L,eAAe,CAAIpG,UAAyB,EAAiB;IACnE,MAAMH,GAAG,GAAGG,UAAU,CAACH,GAAG;IAC1B,MAAMuI,CAAC,GAAG,IAAI,CAACvP,IAAI,CAACiF,SAAS;IAC7B,IAAIsK,CAAC,IAAIA,CAAC,CAACvI,GAAG,CAAC,EAAE;MACfG,UAAU,GAAGqI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEtI,UAAU,EAAEoI,CAAC,CAACvI,GAAG,CAAC,CAAC;MAClD,IAAI,OAAOG,UAAU,CAAC3B,GAAG,KAAK,QAAQ,EAAE;QACtC2B,UAAU,CAAC3B,GAAG,GAAG,IAAAkK,kBAAY;QAC3B;QACAvI,UAAU,CAAC3B,GAAG,CACf;MACH;IACF;IAEA,OAAO2B,UAAU;EACnB;EAEQuF,iBAAiB,CAACiD,IAAa,EAAEC,QAAiB,EAAE;IAC1D,IAAIxE,aAAa,GAAGuE,IAAI,IAAI,IAAI;IAChC;IACA,IAAIlD,SAAc,GAAG,EAAE;IAEvB,IAAI,IAAI,CAAC5L,mBAAmB,CAACuK,aAAa,CAAC,EAAE;MAC3CqB,SAAS,GAAG,IAAI,CAAC5L,mBAAmB,CAACuK,aAAa,CAAC;IACrD,CAAC,MAAM,IAAI,IAAI,CAACpL,IAAI,CAAC4E,UAAU,EAAE;MAC/B6H,SAAS,GAAG,IAAI,CAACzM,IAAI,CAAC4E,UAAU,CAACwG,aAAa,CAAC,IAAI,EAAE;IACvD,CAAC,MAAM,IAAI,IAAI,CAACpL,IAAI,CAAC6P,IAAI,EAAE;MACzBpD,SAAS,GAAG,IAAI,CAACzM,IAAI,CAAC6P,IAAI,CAACzE,aAAa,CAAC,IAAI,EAAE;IACjD;;IAEA;IACA,IAAI,CAACqB,SAAS,IAAImD,QAAQ,EAAE;MAC1B,IAAI,IAAI,CAAC/O,mBAAmB,CAAC+O,QAAQ,CAAC,EAAE;QACtCnD,SAAS,GAAG,IAAI,CAAC5L,mBAAmB,CAAC+O,QAAQ,CAAC;MAChD,CAAC,MAAM,IAAI,IAAI,CAAC5P,IAAI,CAAC4E,UAAU,EAAE;QAC/B6H,SAAS,GAAG,IAAI,CAACzM,IAAI,CAAC4E,UAAU,CAACgL,QAAQ,CAAC,IAAI,EAAE;MAClD,CAAC,MAAM,IAAI,IAAI,CAAC5P,IAAI,CAAC6P,IAAI,EAAE;QACzBpD,SAAS,GAAG,IAAI,CAACzM,IAAI,CAAC6P,IAAI,CAACD,QAAQ,CAAC,IAAI,EAAE;MAC5C;MACA,IAAInD,SAAS,EAAE;QACbrB,aAAa,GAAGwE,QAAQ;MAC1B;IACF;IAEA,OAAO;MAAExE,aAAa;MAAEqB;IAAU,CAAC;EACrC;EAEQY,UAAU,CAChBlG,UAAyB,EACzB2I,cAAsB,EACtBC,QAAiB,EACjB7C,SAAwB,EACxB8C,MAAe,EACfC,gBAA0B,EACf;IACX,IAAI9H,YAAY,GAAG,IAAI;IACvB;IACA,IAAI2H,cAAc,GAAG,CAAC,IAAIA,cAAc,IAAI3I,UAAU,CAAC4E,UAAU,CAACqB,MAAM,EAAE;MACxE0C,cAAc,GAAG,CAAC;MAClB3H,YAAY,GAAG,KAAK;IACtB;IAEA,MAAM;MAAEiD,aAAa;MAAEqB;IAAU,CAAC,GAAG,IAAI,CAACC,iBAAiB,CACzDvF,UAAU,CAACiE,aAAa,EACxB,IAAI,CAACpL,IAAI,CAAC6E,mBAAmB,IAAI,CAACsC,UAAU,CAACkE,sBAAsB,GAC/DlE,UAAU,CAACmE,iBAAiB,GAC5BC,SAAS,CACd;IAED,MAAMa,IAA4B,GAAGjF,UAAU,CAACiF,IAAI,GAChDjF,UAAU,CAACiF,IAAI,CAAC0D,cAAc,CAAC,GAC/B,CAAC,CAAC;IAEN,MAAMnI,GAAc,GAAG;MACrBX,GAAG,EAAEoF,IAAI,CAACpF,GAAG,IAAI,EAAE,GAAG8I,cAAc;MACpC5C,SAAS;MACT/E,YAAY;MACZ4H,QAAQ;MACRpH,WAAW,EAAEmH,cAAc;MAC3B5H,KAAK,EAAEf,UAAU,CAAC4E,UAAU,CAAC+D,cAAc,CAAC;MAC5C1E,aAAa;MACbqB,SAAS;MACTwD,gBAAgB,EAAE,CAAC,CAACA;IACtB,CAAC;IAED,IAAI7D,IAAI,CAACE,IAAI,EAAE3E,GAAG,CAAC2E,IAAI,GAAGF,IAAI,CAACE,IAAI;IACnC,IAAI0D,MAAM,KAAKzE,SAAS,EAAE5D,GAAG,CAACqI,MAAM,GAAGA,MAAM;IAC7C,IAAI5D,IAAI,CAACI,WAAW,EAAE7E,GAAG,CAAC6E,WAAW,GAAGJ,IAAI,CAACI,WAAW;IAExD,OAAO7E,GAAG;EACZ;EAEQ+F,cAAc,GAAG;IACvB,OAAO,IAAI,CAAC1N,IAAI,CAACwF,GAAG,KAAKjG,SAAS,GAAGC,MAAM,CAAC0Q,QAAQ,CAACC,IAAI,GAAG,EAAE,CAAC;EACjE;EAEQ1B,WAAW,CAAC2B,QAAgB,EAAW;IAC7C,MAAM5K,GAAG,GAAG,IAAI,CAACkI,cAAc,EAAE;IACjC,IAAI,CAAClI,GAAG,EAAE,OAAO,KAAK;IAEtB,MAAM6K,QAAQ,GAAG7K,GAAG,CAACzC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,UAAU,EAAE,GAAG,CAAC;IAEzE,IAAIqN,QAAQ,CAACE,IAAI,CAAC9K,GAAG,CAAC,EAAE,OAAO,IAAI;IACnC,IAAI4K,QAAQ,CAACE,IAAI,CAACD,QAAQ,CAAC,EAAE,OAAO,IAAI;IACxC,OAAO,KAAK;EACd;EAEQ7B,gBAAgB,CAAC+B,SAAmB,EAAW;IACrD,MAAMhC,MAAM,GAAG,IAAI,CAACvO,IAAI,CAACuO,MAAM,IAAI,CAAC,CAAC;IACrC,KAAK,IAAIiC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACnD,MAAM,EAAEoD,CAAC,EAAE,EAAE;MACzC,IAAIjC,MAAM,CAACgC,SAAS,CAACC,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI;IACvC;IACA,OAAO,KAAK;EACd;EAEQnI,gBAAgB,CAACoI,OAAgC,EAAE;IACzD,IAAI,CAAClR,SAAS,EAAE;IAChB,MAAMqH,IAAoB,GAAG,EAAE;IAC/B,IAAI6J,OAAO,CAACC,GAAG,EAAE;MACf,MAAMC,CAAC,GAAGlR,QAAQ,CAACmR,aAAa,CAAC,OAAO,CAAC;MACzCD,CAAC,CAACE,SAAS,GAAGJ,OAAO,CAACC,GAAG;MACzBjR,QAAQ,CAACqR,IAAI,CAACC,WAAW,CAACJ,CAAC,CAAC;MAC5B/J,IAAI,CAACuC,IAAI,CAAC,MAAMwH,CAAC,CAACK,MAAM,EAAE,CAAC;IAC7B;IACA,IAAIP,OAAO,CAACQ,EAAE,EAAE;MACd,MAAMC,MAAM,GAAGzR,QAAQ,CAACmR,aAAa,CAAC,QAAQ,CAAC;MAC/CM,MAAM,CAACL,SAAS,GAAGJ,OAAO,CAACQ,EAAE;MAC7BxR,QAAQ,CAACqR,IAAI,CAACC,WAAW,CAACG,MAAM,CAAC;MACjCtK,IAAI,CAACuC,IAAI,CAAC,MAAM+H,MAAM,CAACF,MAAM,EAAE,CAAC;IAClC;IACA,IAAIP,OAAO,CAACU,YAAY,EAAE;MACxBV,OAAO,CAACU,YAAY,CAACzK,OAAO,CAAE0K,QAAQ,IAAK;QACzCxK,IAAI,CAACuC,IAAI,CAACkI,mBAAM,CAACC,WAAW,CAACF,QAAQ,CAAwB,CAACG,MAAM,CAAC;MACvE,CAAC,CAAC;IACJ;IACA,OAAO,MAAM;MACX3K,IAAI,CAACF,OAAO,CAAE8K,EAAE,IAAKA,EAAE,EAAE,CAAC;IAC5B,CAAC;EACH;EAEQC,uCAAuC,CAACjN,IAAyB,EAAE;IACzE,MAAMI,UAAU,GAAG,IAAIzE,GAAG,EAAU;IACpC,MAAMyB,QAAQ,GAAG4C,IAAI,IAAIA,IAAI,CAAC5C,QAAQ,GAAG4C,IAAI,CAAC5C,QAAQ,GAAG,IAAI,CAACmE,WAAW,EAAE;IAC3E,MAAM9D,WAAW,GACfuC,IAAI,IAAIA,IAAI,CAACvC,WAAW,GAAGuC,IAAI,CAACvC,WAAW,GAAG,IAAI,CAAC+D,cAAc,EAAE;IACrEwJ,MAAM,CAACjH,IAAI,CAAC3G,QAAQ,CAAC,CAAC8E,OAAO,CAAE6D,EAAE,IAAK;MACpC,MAAMD,OAAO,GAAG1I,QAAQ,CAAC2I,EAAE,CAAC;MAC5B,IAAID,OAAO,CAACM,KAAK,EAAE;QACjB,KAAK,MAAMC,IAAI,IAAIP,OAAO,CAACM,KAAK,EAAE;UAChC,IAAIC,IAAI,CAACkB,UAAU,EAAE;YACnBnH,UAAU,CAACsB,GAAG,CAAC2E,IAAI,CAACO,aAAa,IAAI,IAAI,CAAC;YAC1C,IAAIP,IAAI,CAACS,iBAAiB,EAAE;cAC1B1G,UAAU,CAACsB,GAAG,CAAC2E,IAAI,CAACS,iBAAiB,CAAC;YACxC;UACF;QACF;MACF;IACF,CAAC,CAAC;IACFrJ,WAAW,CAACqD,GAAG,CAAE6B,UAAU,IAAK;MAC9BvC,UAAU,CAACsB,GAAG,CAACiB,UAAU,CAACiE,aAAa,IAAI,IAAI,CAAC;MAChD,IAAIjE,UAAU,CAACmE,iBAAiB,EAAE;QAChC1G,UAAU,CAACsB,GAAG,CAACiB,UAAU,CAACmE,iBAAiB,CAAC;MAC9C;IACF,CAAC,CAAC;IACF,OAAOoG,KAAK,CAACC,IAAI,CAAC/M,UAAU,CAAC;EAC/B;EAEA,MAAaE,oBAAoB,CAACN,IAAyB,EAAE;IAC3D,IAAI,IAAI,CAACxE,IAAI,CAAC6E,mBAAmB,EAAE;MACjC,MAAMD,UAAU,GAAG,IAAI,CAACgN,0BAA0B,CAACpN,IAAI,CAAC;MACxD,IAAI,CAACxE,IAAI,CAAC6F,0BAA0B,GAAG,MAAM,IAAI,CAAC7F,IAAI,CAAC6E,mBAAmB,CAACgN,iBAAiB,CAC1FjN,UAAU,CACX;IACH;EACF;EAEQkN,2BAA2B,GAAsB;IACvD,MAAMC,iBAAoC,GAAG,CAAC,CAAC;IAC/CvC,MAAM,CAACwC,MAAM,CAAC,IAAI,CAAChS,IAAI,CAAC6F,0BAA0B,IAAI,CAAC,CAAC,CAAC,CAACa,OAAO,CAAEqI,GAAG,IAAK;MACzE,IAAIA,GAAG,CAACkD,WAAW,EAAEzC,MAAM,CAACC,MAAM,CAACsC,iBAAiB,EAAEhD,GAAG,CAACkD,WAAW,CAAC;IACxE,CAAC,CAAC;IACF,OAAOF,iBAAiB;EAC1B;EAEQ5D,yBAAyB,CAC/B+D,aAAqB,EACrBC,uBAAgC,EAChCC,0BAAmC,EACnChG,IAAsB,EAItB;IACA+F,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC;IACtDC,0BAA0B,GAAGA,0BAA0B,IAAI,CAAC;IAC5DhG,IAAI,GAAGA,IAAI,IAAI,EAAE;IACjB,MAAM7B,EAAE,GAAG,IAAI,CAAC2E,6BAA6B,CAC3CgD,aAAa,EACbC,uBAAuB,CACxB;IACD,MAAMF,WAAW,GAAG,IAAI,CAACH,2BAA2B,EAAE;;IAEtD;IACA,IAAIM,0BAA0B,GAAG,CAAC,EAAE;MAClC,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI4B,0BAA0B,EAAE5B,CAAC,EAAE,EAAE;QACpD,MAAM6B,UAAU,GAAG,IAAI,CAACnD,6BAA6B,CAACgD,aAAa,EAAE1B,CAAC,CAAC;QACvE,IAAIyB,WAAW,CAACI,UAAU,CAAC,KAAK9G,SAAS,EAAE;UACzC,OAAO;YACLtE,SAAS,EAAE,CAAC,CAAC;YACbiH,gBAAgB,EAAE;UACpB,CAAC;QACH;MACF;IACF;IACA,MAAMoE,YAAY,GAAGL,WAAW,CAAC1H,EAAE,CAAC;IACpC,IAAI+H,YAAY,KAAK/G,SAAS;MAC5B;MACA,OAAO;QAAEtE,SAAS,EAAE,CAAC;MAAE,CAAC;IAC1B,MAAMA,SAAS,GAAGmF,IAAI,CAACmG,SAAS,CAAEC,CAAC,IAAKA,CAAC,CAACxL,GAAG,KAAKsL,YAAY,CAAC;IAC/D,IAAIrL,SAAS,GAAG,CAAC;MACf;MACA,OAAO;QAAEA,SAAS,EAAE,CAAC;MAAE,CAAC;IAE1B,OAAO;MAAEA;IAAU,CAAC;EACtB;EAEQiI,6BAA6B,CACnCgD,aAAqB,EACrBC,uBAAgC,EACX;IACrBA,uBAAuB,GAAGA,uBAAuB,IAAI,CAAC;IACtD,iBAAUD,aAAa,eAAKC,uBAAuB;EACrD;EAEQP,0BAA0B,CAChCpN,IAAyB,EACD;IACxB,MAAMI,UAAkC,GAAG,CAAC,CAAC;IAC7C,IAAI,CAAC5E,IAAI,CAACyS,gCAAgC,GAAG,CAAC,IAAI,CAACzS,IAAI,CACpDyS,gCAAgC,GAC/B,IAAI,CAAChB,uCAAuC,CAACjN,IAAI,CAAC,GAClD,IAAI,CAACxE,IAAI,CAACyS,gCAAgC;IAC9C,IAAI,CAACzS,IAAI,CAACyS,gCAAgC,CAAC/L,OAAO,CAAEiJ,IAAI,IAAK;MAC3D,MAAM;QAAElD;MAAU,CAAC,GAAG,IAAI,CAACC,iBAAiB,CAACiD,IAAI,CAAC;MAClD/K,UAAU,CAAC+K,IAAI,CAAC,GAAG,IAAAV,cAAQ,EAACxC,SAAS,CAAC;IACxC,CAAC,CAAC;IACF,OAAO7H,UAAU;EACnB;EAEQoK,kCAAkC,CACxC0D,aAAqB,EACrBC,cAAsB,EACtBV,WAA8B,EAK9B;IACA,MAAMjL,GAAG,aAAM0L,aAAa,eAAKC,cAAc,CAAE;IACjD,MAAMC,mBAAmB,GACvB,IAAI,CAAC5S,IAAI,CAAC6F,0BAA0B,IACpC,IAAI,CAAC7F,IAAI,CAAC6F,0BAA0B,CAACmB,GAAG,CAAC,GACrC,IAAI,CAAChH,IAAI,CAAC6F,0BAA0B,CAACmB,GAAG,CAAC,CAACiL,WAAW,IAAI,CAAC,CAAC,GAC3D,CAAC,CAAC;IACR,MAAMY,cAAc,GAAG;MAAE,GAAGD,mBAAmB;MAAE,GAAGX;IAAY,CAAC;IACjE,MAAMpD,OAAO,GACX3K,IAAI,CAAC+D,SAAS,CAAC2K,mBAAmB,CAAC,KAAK1O,IAAI,CAAC+D,SAAS,CAAC4K,cAAc,CAAC;IAExE,OAAO;MACL7L,GAAG;MACH+H,GAAG,EAAE;QACH2D,aAAa;QACbC,cAAc;QACdV,WAAW,EAAEY;MACf,CAAC;MACDhE;IACF,CAAC;EACH;AACF;AAAC"}