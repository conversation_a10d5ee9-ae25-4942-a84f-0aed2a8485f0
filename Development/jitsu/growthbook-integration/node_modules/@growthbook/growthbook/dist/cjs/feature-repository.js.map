{"version": 3, "file": "feature-repository.js", "names": ["cacheSettings", "staleTTL", "maxAge", "cache<PERSON>ey", "backgroundSync", "maxEntries", "disableIdleStreams", "idleStreamInterval", "polyfills", "fetch", "globalThis", "bind", "undefined", "SubtleCrypto", "crypto", "subtle", "EventSource", "helpers", "fetchFeaturesCall", "host", "client<PERSON>ey", "headers", "fetchRemoteEvalCall", "payload", "options", "method", "body", "JSON", "stringify", "eventSourceCall", "startIdleListener", "idleTimeout", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "onVisibilityChange", "visibilityState", "clearTimeout", "onVisible", "setTimeout", "onHidden", "addEventListener", "removeEventListener", "stopIdleListener", "localStorage", "e", "subscribedInstances", "Map", "cacheInitialized", "cache", "activeFetches", "streams", "supportsSSE", "Set", "setPolyfills", "overrides", "Object", "assign", "configure<PERSON>ache", "clearAutoRefresh", "clearCache", "clear", "updatePersistentCache", "refreshFeatures", "instance", "timeout", "<PERSON><PERSON><PERSON>", "allowStale", "updateInstance", "data", "fetchFeaturesWithCache", "refreshInstance", "subscribe", "key", "<PERSON><PERSON><PERSON>", "subs", "get", "add", "set", "unsubscribe", "for<PERSON>ach", "s", "delete", "channel", "state", "disableChannel", "enableChannel", "setItem", "Array", "from", "entries", "get<PERSON><PERSON><PERSON><PERSON>", "now", "Date", "minStaleAt", "getTime", "initializeCache", "existing", "staleAt", "sse", "fetchFeatures", "startAutoRefresh", "promiseTimeout", "apiHost", "getApiInfo", "baseKey", "isRemoteEval", "attributes", "getAttributes", "cacheKeyAttributes", "getCacheKeyAttributes", "keys", "ca", "fv", "getForcedVariations", "url", "getUrl", "promise", "Promise", "resolve", "resolved", "timer", "finish", "then", "catch", "value", "getItem", "parsed", "parse", "isArray", "cleanupCache", "cleanupFn", "entriesWithTimestamps", "map", "sort", "a", "b", "entriesToRemoveCount", "Math", "min", "max", "size", "i", "onNewFeatureData", "version", "dateUpdated", "has", "instances", "decryptPayload", "refreshStickyBuckets", "setExperiments", "experiments", "getExperiments", "setFeatures", "features", "getFeatures", "apiRequestHeaders", "getApiHosts", "getClientKey", "remoteEval", "fetcher", "forcedVariations", "forcedFeatures", "getForcedFeatures", "res", "json", "process", "env", "NODE_ENV", "log", "error", "message", "streamingHost", "streamingHostRequestHeaders", "src", "cb", "event", "type", "errors", "onSSEError", "readyState", "delay", "pow", "random", "includes", "onopen", "onerror", "close", "destroyChannel"], "sources": ["../../src/feature-repository.ts"], "sourcesContent": ["import {\n  Attributes,\n  CacheSettings,\n  FeatureApiResponse,\n  Helpers,\n  Polyfills,\n} from \"./types/growthbook\";\nimport type { GrowthBook } from \".\";\n\ntype CacheEntry = {\n  data: FeatureApiResponse;\n  sse?: boolean;\n  version: string;\n  staleAt: Date;\n};\ntype ScopedChannel = {\n  src: EventSource | null;\n  cb: (event: MessageEvent<string>) => void;\n  host: string;\n  clientKey: string;\n  headers?: Record<string, string>;\n  errors: number;\n  state: \"active\" | \"idle\" | \"disabled\";\n};\n\n// Config settings\nconst cacheSettings: CacheSettings = {\n  // Consider a fetch stale after 1 minute\n  staleTTL: 1000 * 60,\n  // Max time to keep a fetch in cache (24 hours default)\n  maxAge: 1000 * 60 * 60 * 24,\n  cacheKey: \"gbFeaturesCache\",\n  backgroundSync: true,\n  maxEntries: 10,\n  disableIdleStreams: false,\n  idleStreamInterval: 20000,\n};\nconst polyfills: Polyfills = {\n  fetch: globalThis.fetch ? globalThis.fetch.bind(globalThis) : undefined,\n  SubtleCrypto: globalThis.crypto ? globalThis.crypto.subtle : undefined,\n  EventSource: globalThis.EventSource,\n};\nexport const helpers: Helpers = {\n  fetchFeaturesCall: ({ host, clientKey, headers }) => {\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/features/${clientKey}`,\n      { headers }\n    );\n  },\n  fetchRemoteEvalCall: ({ host, clientKey, payload, headers }) => {\n    const options = {\n      method: \"POST\",\n      headers: { \"Content-Type\": \"application/json\", ...headers },\n      body: JSON.stringify(payload),\n    };\n    return (polyfills.fetch as typeof globalThis.fetch)(\n      `${host}/api/eval/${clientKey}`,\n      options\n    );\n  },\n  eventSourceCall: ({ host, clientKey, headers }) => {\n    if (headers) {\n      return new polyfills.EventSource(`${host}/sub/${clientKey}`, {\n        headers,\n      });\n    }\n    return new polyfills.EventSource(`${host}/sub/${clientKey}`);\n  },\n  startIdleListener: () => {\n    let idleTimeout: number | undefined;\n    const isBrowser =\n      typeof window !== \"undefined\" && typeof document !== \"undefined\";\n    if (!isBrowser) return;\n    const onVisibilityChange = () => {\n      if (document.visibilityState === \"visible\") {\n        window.clearTimeout(idleTimeout);\n        onVisible();\n      } else if (document.visibilityState === \"hidden\") {\n        idleTimeout = window.setTimeout(\n          onHidden,\n          cacheSettings.idleStreamInterval\n        );\n      }\n    };\n    document.addEventListener(\"visibilitychange\", onVisibilityChange);\n    return () =>\n      document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n  },\n  stopIdleListener: () => {\n    // No-op, replaced by startIdleListener\n  },\n};\n\ntry {\n  if (globalThis.localStorage) {\n    polyfills.localStorage = globalThis.localStorage;\n  }\n} catch (e) {\n  // Ignore localStorage errors\n}\n\n// Global state\nconst subscribedInstances: Map<string, Set<GrowthBook>> = new Map();\nlet cacheInitialized = false;\nconst cache: Map<string, CacheEntry> = new Map();\nconst activeFetches: Map<string, Promise<FeatureApiResponse>> = new Map();\nconst streams: Map<string, ScopedChannel> = new Map();\nconst supportsSSE: Set<string> = new Set();\n\n// Public functions\nexport function setPolyfills(overrides: Partial<Polyfills>): void {\n  Object.assign(polyfills, overrides);\n}\nexport function configureCache(overrides: Partial<CacheSettings>): void {\n  Object.assign(cacheSettings, overrides);\n  if (!cacheSettings.backgroundSync) {\n    clearAutoRefresh();\n  }\n}\n\nexport async function clearCache(): Promise<void> {\n  cache.clear();\n  activeFetches.clear();\n  clearAutoRefresh();\n  cacheInitialized = false;\n  await updatePersistentCache();\n}\n\nexport async function refreshFeatures(\n  instance: GrowthBook,\n  timeout?: number,\n  skipCache?: boolean,\n  allowStale?: boolean,\n  updateInstance?: boolean,\n  backgroundSync?: boolean\n): Promise<void> {\n  if (!backgroundSync) {\n    cacheSettings.backgroundSync = false;\n  }\n\n  const data = await fetchFeaturesWithCache(\n    instance,\n    allowStale,\n    timeout,\n    skipCache\n  );\n  updateInstance && data && (await refreshInstance(instance, data));\n}\n\n// Subscribe a GrowthBook instance to feature changes\nexport function subscribe(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const subs = subscribedInstances.get(key) || new Set();\n  subs.add(instance);\n  subscribedInstances.set(key, subs);\n}\nexport function unsubscribe(instance: GrowthBook): void {\n  subscribedInstances.forEach((s) => s.delete(instance));\n}\n\nexport function onHidden() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    channel.state = \"idle\";\n    disableChannel(channel);\n  });\n}\n\nexport function onVisible() {\n  streams.forEach((channel) => {\n    if (!channel) return;\n    if (channel.state !== \"idle\") return;\n    enableChannel(channel);\n  });\n}\n\n// Private functions\n\nasync function updatePersistentCache() {\n  try {\n    if (!polyfills.localStorage) return;\n    await polyfills.localStorage.setItem(\n      cacheSettings.cacheKey,\n      JSON.stringify(Array.from(cache.entries()))\n    );\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n}\n\nasync function fetchFeaturesWithCache(\n  instance: GrowthBook,\n  allowStale?: boolean,\n  timeout?: number,\n  skipCache?: boolean\n): Promise<FeatureApiResponse | null> {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const now = new Date();\n\n  const minStaleAt = new Date(\n    now.getTime() - cacheSettings.maxAge + cacheSettings.staleTTL\n  );\n\n  await initializeCache();\n  const existing = cache.get(cacheKey);\n  if (\n    existing &&\n    !skipCache &&\n    (allowStale || existing.staleAt > now) &&\n    existing.staleAt > minStaleAt\n  ) {\n    // Restore from cache whether SSE is supported\n    if (existing.sse) supportsSSE.add(key);\n\n    // Reload features in the background if stale\n    if (existing.staleAt < now) {\n      fetchFeatures(instance);\n    }\n    // Otherwise, if we don't need to refresh now, start a background sync\n    else {\n      startAutoRefresh(instance);\n    }\n    return existing.data;\n  } else {\n    return await promiseTimeout(fetchFeatures(instance), timeout);\n  }\n}\n\nfunction getKey(instance: GrowthBook): string {\n  const [apiHost, clientKey] = instance.getApiInfo();\n  return `${apiHost}||${clientKey}`;\n}\n\nfunction getCacheKey(instance: GrowthBook): string {\n  const baseKey = getKey(instance);\n  if (!instance.isRemoteEval()) return baseKey;\n\n  const attributes = instance.getAttributes();\n  const cacheKeyAttributes =\n    instance.getCacheKeyAttributes() || Object.keys(instance.getAttributes());\n  const ca: Attributes = {};\n  cacheKeyAttributes.forEach((key) => {\n    ca[key] = attributes[key];\n  });\n\n  const fv = instance.getForcedVariations();\n  const url = instance.getUrl();\n\n  return `${baseKey}||${JSON.stringify({\n    ca,\n    fv,\n    url,\n  })}`;\n}\n\n// Guarantee the promise always resolves within {timeout} ms\n// Resolved value will be `null` when there's an error or it takes too long\n// Note: The promise will continue running in the background, even if the timeout is hit\nfunction promiseTimeout<T>(\n  promise: Promise<T>,\n  timeout?: number\n): Promise<T | null> {\n  return new Promise((resolve) => {\n    let resolved = false;\n    let timer: unknown;\n    const finish = (data?: T) => {\n      if (resolved) return;\n      resolved = true;\n      timer && clearTimeout(timer as NodeJS.Timer);\n      resolve(data || null);\n    };\n\n    if (timeout) {\n      timer = setTimeout(() => finish(), timeout);\n    }\n\n    promise.then((data) => finish(data)).catch(() => finish());\n  });\n}\n\n// Populate cache from localStorage (if available)\nasync function initializeCache(): Promise<void> {\n  if (cacheInitialized) return;\n  cacheInitialized = true;\n  try {\n    if (polyfills.localStorage) {\n      const value = await polyfills.localStorage.getItem(\n        cacheSettings.cacheKey\n      );\n      if (value) {\n        const parsed: [string, CacheEntry][] = JSON.parse(value);\n        if (parsed && Array.isArray(parsed)) {\n          parsed.forEach(([key, data]) => {\n            cache.set(key, {\n              ...data,\n              staleAt: new Date(data.staleAt),\n            });\n          });\n        }\n        cleanupCache();\n      }\n    }\n  } catch (e) {\n    // Ignore localStorage errors\n  }\n  if (!cacheSettings.disableIdleStreams) {\n    const cleanupFn = helpers.startIdleListener();\n    if (cleanupFn) {\n      helpers.stopIdleListener = cleanupFn;\n    }\n  }\n}\n\n// Enforce the maxEntries limit\nfunction cleanupCache() {\n  const entriesWithTimestamps = Array.from(cache.entries())\n    .map(([key, value]) => ({\n      key,\n      staleAt: value.staleAt.getTime(),\n    }))\n    .sort((a, b) => a.staleAt - b.staleAt);\n\n  const entriesToRemoveCount = Math.min(\n    Math.max(0, cache.size - cacheSettings.maxEntries),\n    cache.size\n  );\n\n  for (let i = 0; i < entriesToRemoveCount; i++) {\n    cache.delete(entriesWithTimestamps[i].key);\n  }\n}\n\n// Called whenever new features are fetched from the API\nfunction onNewFeatureData(\n  key: string,\n  cacheKey: string,\n  data: FeatureApiResponse\n): void {\n  // If contents haven't changed, ignore the update, extend the stale TTL\n  const version = data.dateUpdated || \"\";\n  const staleAt = new Date(Date.now() + cacheSettings.staleTTL);\n  const existing = cache.get(cacheKey);\n  if (existing && version && existing.version === version) {\n    existing.staleAt = staleAt;\n    updatePersistentCache();\n    return;\n  }\n\n  // Update in-memory cache\n  cache.set(cacheKey, {\n    data,\n    version,\n    staleAt,\n    sse: supportsSSE.has(key),\n  });\n  cleanupCache();\n  // Update local storage (don't await this, just update asynchronously)\n  updatePersistentCache();\n\n  // Update features for all subscribed GrowthBook instances\n  const instances = subscribedInstances.get(key);\n  instances && instances.forEach((instance) => refreshInstance(instance, data));\n}\n\nasync function refreshInstance(\n  instance: GrowthBook,\n  data: FeatureApiResponse\n): Promise<void> {\n  data = await instance.decryptPayload(data, undefined, polyfills.SubtleCrypto);\n\n  await instance.refreshStickyBuckets(data);\n  instance.setExperiments(data.experiments || instance.getExperiments());\n  instance.setFeatures(data.features || instance.getFeatures());\n}\n\nasync function fetchFeatures(\n  instance: GrowthBook\n): Promise<FeatureApiResponse> {\n  const { apiHost, apiRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  const remoteEval = instance.isRemoteEval();\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n\n  let promise = activeFetches.get(cacheKey);\n  if (!promise) {\n    const fetcher: Promise<Response> = remoteEval\n      ? helpers.fetchRemoteEvalCall({\n          host: apiHost,\n          clientKey,\n          payload: {\n            attributes: instance.getAttributes(),\n            forcedVariations: instance.getForcedVariations(),\n            forcedFeatures: Array.from(instance.getForcedFeatures().entries()),\n            url: instance.getUrl(),\n          },\n          headers: apiRequestHeaders,\n        })\n      : helpers.fetchFeaturesCall({\n          host: apiHost,\n          clientKey,\n          headers: apiRequestHeaders,\n        });\n\n    // TODO: auto-retry if status code indicates a temporary error\n    promise = fetcher\n      .then((res) => {\n        if (res.headers.get(\"x-sse-support\") === \"enabled\") {\n          supportsSSE.add(key);\n        }\n        return res.json();\n      })\n      .then((data: FeatureApiResponse) => {\n        onNewFeatureData(key, cacheKey, data);\n        startAutoRefresh(instance);\n        activeFetches.delete(cacheKey);\n        return data;\n      })\n      .catch((e) => {\n        process.env.NODE_ENV !== \"production\" &&\n          instance.log(\"Error fetching features\", {\n            apiHost,\n            clientKey,\n            error: e ? e.message : null,\n          });\n        activeFetches.delete(cacheKey);\n        return Promise.resolve({});\n      });\n    activeFetches.set(cacheKey, promise);\n  }\n  return await promise;\n}\n\n// Watch a feature endpoint for changes\n// Will prefer SSE if enabled, otherwise fall back to cron\nfunction startAutoRefresh(instance: GrowthBook): void {\n  const key = getKey(instance);\n  const cacheKey = getCacheKey(instance);\n  const { streamingHost, streamingHostRequestHeaders } = instance.getApiHosts();\n  const clientKey = instance.getClientKey();\n  if (\n    cacheSettings.backgroundSync &&\n    supportsSSE.has(key) &&\n    polyfills.EventSource\n  ) {\n    if (streams.has(key)) return;\n    const channel: ScopedChannel = {\n      src: null,\n      host: streamingHost,\n      clientKey,\n      headers: streamingHostRequestHeaders,\n      cb: (event: MessageEvent<string>) => {\n        try {\n          if (event.type === \"features-updated\") {\n            const instances = subscribedInstances.get(key);\n            instances &&\n              instances.forEach((instance) => {\n                fetchFeatures(instance);\n              });\n          } else if (event.type === \"features\") {\n            const json: FeatureApiResponse = JSON.parse(event.data);\n            onNewFeatureData(key, cacheKey, json);\n          }\n          // Reset error count on success\n          channel.errors = 0;\n        } catch (e) {\n          process.env.NODE_ENV !== \"production\" &&\n            instance.log(\"SSE Error\", {\n              streamingHost,\n              clientKey,\n              error: e ? (e as Error).message : null,\n            });\n          onSSEError(channel);\n        }\n      },\n      errors: 0,\n      state: \"active\",\n    };\n    streams.set(key, channel);\n    enableChannel(channel);\n  }\n}\n\nfunction onSSEError(channel: ScopedChannel) {\n  if (channel.state === \"idle\") return;\n  channel.errors++;\n  if (channel.errors > 3 || (channel.src && channel.src.readyState === 2)) {\n    // exponential backoff after 4 errors, with jitter\n    const delay =\n      Math.pow(3, channel.errors - 3) * (1000 + Math.random() * 1000);\n    disableChannel(channel);\n    setTimeout(() => {\n      if ([\"idle\", \"active\"].includes(channel.state)) return;\n      enableChannel(channel);\n    }, Math.min(delay, 300000)); // 5 minutes max\n  }\n}\n\nfunction disableChannel(channel: ScopedChannel) {\n  if (!channel.src) return;\n  channel.src.onopen = null;\n  channel.src.onerror = null;\n  channel.src.close();\n  channel.src = null;\n  if (channel.state === \"active\") {\n    channel.state = \"disabled\";\n  }\n}\n\nfunction enableChannel(channel: ScopedChannel) {\n  channel.src = helpers.eventSourceCall({\n    host: channel.host,\n    clientKey: channel.clientKey,\n    headers: channel.headers,\n  }) as EventSource;\n  channel.state = \"active\";\n  channel.src.addEventListener(\"features\", channel.cb);\n  channel.src.addEventListener(\"features-updated\", channel.cb);\n  channel.src.onerror = () => onSSEError(channel);\n  channel.src.onopen = () => {\n    channel.errors = 0;\n  };\n}\n\nfunction destroyChannel(channel: ScopedChannel, key: string) {\n  disableChannel(channel);\n  streams.delete(key);\n}\n\nfunction clearAutoRefresh() {\n  // Clear list of which keys are auto-updated\n  supportsSSE.clear();\n\n  // Stop listening for any SSE events\n  streams.forEach(destroyChannel);\n\n  // Remove all references to GrowthBook instances\n  subscribedInstances.clear();\n\n  // Run the idle stream cleanup function\n  helpers.stopIdleListener();\n}\n"], "mappings": ";;;;;;;;;;;;;;AAyBA;AACA,MAAMA,aAA4B,GAAG;EACnC;EACAC,QAAQ,EAAE,IAAI,GAAG,EAAE;EACnB;EACAC,MAAM,EAAE,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC3BC,QAAQ,EAAE,iBAAiB;EAC3BC,cAAc,EAAE,IAAI;EACpBC,UAAU,EAAE,EAAE;EACdC,kBAAkB,EAAE,KAAK;EACzBC,kBAAkB,EAAE;AACtB,CAAC;AACD,MAAMC,SAAoB,GAAG;EAC3BC,KAAK,EAAEC,UAAU,CAACD,KAAK,GAAGC,UAAU,CAACD,KAAK,CAACE,IAAI,CAACD,UAAU,CAAC,GAAGE,SAAS;EACvEC,YAAY,EAAEH,UAAU,CAACI,MAAM,GAAGJ,UAAU,CAACI,MAAM,CAACC,MAAM,GAAGH,SAAS;EACtEI,WAAW,EAAEN,UAAU,CAACM;AAC1B,CAAC;AACM,MAAMC,OAAgB,GAAG;EAC9BC,iBAAiB,EAAE,QAAkC;IAAA,IAAjC;MAAEC,IAAI;MAAEC,SAAS;MAAEC;IAAQ,CAAC;IAC9C,OAAQb,SAAS,CAACC,KAAK,WAClBU,IAAI,2BAAiBC,SAAS,GACjC;MAAEC;IAAQ,CAAC,CACZ;EACH,CAAC;EACDC,mBAAmB,EAAE,SAA2C;IAAA,IAA1C;MAAEH,IAAI;MAAEC,SAAS;MAAEG,OAAO;MAAEF;IAAQ,CAAC;IACzD,MAAMG,OAAO,GAAG;MACdC,MAAM,EAAE,MAAM;MACdJ,OAAO,EAAE;QAAE,cAAc,EAAE,kBAAkB;QAAE,GAAGA;MAAQ,CAAC;MAC3DK,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACL,OAAO;IAC9B,CAAC;IACD,OAAQf,SAAS,CAACC,KAAK,WAClBU,IAAI,uBAAaC,SAAS,GAC7BI,OAAO,CACR;EACH,CAAC;EACDK,eAAe,EAAE,SAAkC;IAAA,IAAjC;MAAEV,IAAI;MAAEC,SAAS;MAAEC;IAAQ,CAAC;IAC5C,IAAIA,OAAO,EAAE;MACX,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,kBAAQC,SAAS,GAAI;QAC3DC;MACF,CAAC,CAAC;IACJ;IACA,OAAO,IAAIb,SAAS,CAACQ,WAAW,WAAIG,IAAI,kBAAQC,SAAS,EAAG;EAC9D,CAAC;EACDU,iBAAiB,EAAE,MAAM;IACvB,IAAIC,WAA+B;IACnC,MAAMC,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOC,QAAQ,KAAK,WAAW;IAClE,IAAI,CAACF,SAAS,EAAE;IAChB,MAAMG,kBAAkB,GAAG,MAAM;MAC/B,IAAID,QAAQ,CAACE,eAAe,KAAK,SAAS,EAAE;QAC1CH,MAAM,CAACI,YAAY,CAACN,WAAW,CAAC;QAChCO,SAAS,EAAE;MACb,CAAC,MAAM,IAAIJ,QAAQ,CAACE,eAAe,KAAK,QAAQ,EAAE;QAChDL,WAAW,GAAGE,MAAM,CAACM,UAAU,CAC7BC,QAAQ,EACRxC,aAAa,CAACO,kBAAkB,CACjC;MACH;IACF,CAAC;IACD2B,QAAQ,CAACO,gBAAgB,CAAC,kBAAkB,EAAEN,kBAAkB,CAAC;IACjE,OAAO,MACLD,QAAQ,CAACQ,mBAAmB,CAAC,kBAAkB,EAAEP,kBAAkB,CAAC;EACxE,CAAC;EACDQ,gBAAgB,EAAE,MAAM;IACtB;EAAA;AAEJ,CAAC;AAAC;AAEF,IAAI;EACF,IAAIjC,UAAU,CAACkC,YAAY,EAAE;IAC3BpC,SAAS,CAACoC,YAAY,GAAGlC,UAAU,CAACkC,YAAY;EAClD;AACF,CAAC,CAAC,OAAOC,CAAC,EAAE;EACV;AAAA;;AAGF;AACA,MAAMC,mBAAiD,GAAG,IAAIC,GAAG,EAAE;AACnE,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,MAAMC,KAA8B,GAAG,IAAIF,GAAG,EAAE;AAChD,MAAMG,aAAuD,GAAG,IAAIH,GAAG,EAAE;AACzE,MAAMI,OAAmC,GAAG,IAAIJ,GAAG,EAAE;AACrD,MAAMK,WAAwB,GAAG,IAAIC,GAAG,EAAE;;AAE1C;AACO,SAASC,YAAY,CAACC,SAA6B,EAAQ;EAChEC,MAAM,CAACC,MAAM,CAACjD,SAAS,EAAE+C,SAAS,CAAC;AACrC;AACO,SAASG,cAAc,CAACH,SAAiC,EAAQ;EACtEC,MAAM,CAACC,MAAM,CAACzD,aAAa,EAAEuD,SAAS,CAAC;EACvC,IAAI,CAACvD,aAAa,CAACI,cAAc,EAAE;IACjCuD,gBAAgB,EAAE;EACpB;AACF;AAEO,eAAeC,UAAU,GAAkB;EAChDX,KAAK,CAACY,KAAK,EAAE;EACbX,aAAa,CAACW,KAAK,EAAE;EACrBF,gBAAgB,EAAE;EAClBX,gBAAgB,GAAG,KAAK;EACxB,MAAMc,qBAAqB,EAAE;AAC/B;AAEO,eAAeC,eAAe,CACnCC,QAAoB,EACpBC,OAAgB,EAChBC,SAAmB,EACnBC,UAAoB,EACpBC,cAAwB,EACxBhE,cAAwB,EACT;EACf,IAAI,CAACA,cAAc,EAAE;IACnBJ,aAAa,CAACI,cAAc,GAAG,KAAK;EACtC;EAEA,MAAMiE,IAAI,GAAG,MAAMC,sBAAsB,CACvCN,QAAQ,EACRG,UAAU,EACVF,OAAO,EACPC,SAAS,CACV;EACDE,cAAc,IAAIC,IAAI,KAAK,MAAME,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC;AACnE;;AAEA;AACO,SAASG,SAAS,CAACR,QAAoB,EAAQ;EACpD,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC;EAC5B,MAAMW,IAAI,GAAG7B,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC,IAAI,IAAIpB,GAAG,EAAE;EACtDsB,IAAI,CAACE,GAAG,CAACb,QAAQ,CAAC;EAClBlB,mBAAmB,CAACgC,GAAG,CAACL,GAAG,EAAEE,IAAI,CAAC;AACpC;AACO,SAASI,WAAW,CAACf,QAAoB,EAAQ;EACtDlB,mBAAmB,CAACkC,OAAO,CAAEC,CAAC,IAAKA,CAAC,CAACC,MAAM,CAAClB,QAAQ,CAAC,CAAC;AACxD;AAEO,SAASxB,QAAQ,GAAG;EACzBW,OAAO,CAAC6B,OAAO,CAAEG,OAAO,IAAK;IAC3B,IAAI,CAACA,OAAO,EAAE;IACdA,OAAO,CAACC,KAAK,GAAG,MAAM;IACtBC,cAAc,CAACF,OAAO,CAAC;EACzB,CAAC,CAAC;AACJ;AAEO,SAAS7C,SAAS,GAAG;EAC1Ba,OAAO,CAAC6B,OAAO,CAAEG,OAAO,IAAK;IAC3B,IAAI,CAACA,OAAO,EAAE;IACd,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE;IAC9BE,aAAa,CAACH,OAAO,CAAC;EACxB,CAAC,CAAC;AACJ;;AAEA;;AAEA,eAAerB,qBAAqB,GAAG;EACrC,IAAI;IACF,IAAI,CAACtD,SAAS,CAACoC,YAAY,EAAE;IAC7B,MAAMpC,SAAS,CAACoC,YAAY,CAAC2C,OAAO,CAClCvF,aAAa,CAACG,QAAQ,EACtBwB,IAAI,CAACC,SAAS,CAAC4D,KAAK,CAACC,IAAI,CAACxC,KAAK,CAACyC,OAAO,EAAE,CAAC,CAAC,CAC5C;EACH,CAAC,CAAC,OAAO7C,CAAC,EAAE;IACV;EAAA;AAEJ;AAEA,eAAeyB,sBAAsB,CACnCN,QAAoB,EACpBG,UAAoB,EACpBF,OAAgB,EAChBC,SAAmB,EACiB;EACpC,MAAMO,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC;EAC5B,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC;EACtC,MAAM4B,GAAG,GAAG,IAAIC,IAAI,EAAE;EAEtB,MAAMC,UAAU,GAAG,IAAID,IAAI,CACzBD,GAAG,CAACG,OAAO,EAAE,GAAG/F,aAAa,CAACE,MAAM,GAAGF,aAAa,CAACC,QAAQ,CAC9D;EAED,MAAM+F,eAAe,EAAE;EACvB,MAAMC,QAAQ,GAAGhD,KAAK,CAAC2B,GAAG,CAACzE,QAAQ,CAAC;EACpC,IACE8F,QAAQ,IACR,CAAC/B,SAAS,KACTC,UAAU,IAAI8B,QAAQ,CAACC,OAAO,GAAGN,GAAG,CAAC,IACtCK,QAAQ,CAACC,OAAO,GAAGJ,UAAU,EAC7B;IACA;IACA,IAAIG,QAAQ,CAACE,GAAG,EAAE/C,WAAW,CAACyB,GAAG,CAACJ,GAAG,CAAC;;IAEtC;IACA,IAAIwB,QAAQ,CAACC,OAAO,GAAGN,GAAG,EAAE;MAC1BQ,aAAa,CAACpC,QAAQ,CAAC;IACzB;IACA;IAAA,KACK;MACHqC,gBAAgB,CAACrC,QAAQ,CAAC;IAC5B;IACA,OAAOiC,QAAQ,CAAC5B,IAAI;EACtB,CAAC,MAAM;IACL,OAAO,MAAMiC,cAAc,CAACF,aAAa,CAACpC,QAAQ,CAAC,EAAEC,OAAO,CAAC;EAC/D;AACF;AAEA,SAASS,MAAM,CAACV,QAAoB,EAAU;EAC5C,MAAM,CAACuC,OAAO,EAAEnF,SAAS,CAAC,GAAG4C,QAAQ,CAACwC,UAAU,EAAE;EAClD,iBAAUD,OAAO,eAAKnF,SAAS;AACjC;AAEA,SAASuE,WAAW,CAAC3B,QAAoB,EAAU;EACjD,MAAMyC,OAAO,GAAG/B,MAAM,CAACV,QAAQ,CAAC;EAChC,IAAI,CAACA,QAAQ,CAAC0C,YAAY,EAAE,EAAE,OAAOD,OAAO;EAE5C,MAAME,UAAU,GAAG3C,QAAQ,CAAC4C,aAAa,EAAE;EAC3C,MAAMC,kBAAkB,GACtB7C,QAAQ,CAAC8C,qBAAqB,EAAE,IAAItD,MAAM,CAACuD,IAAI,CAAC/C,QAAQ,CAAC4C,aAAa,EAAE,CAAC;EAC3E,MAAMI,EAAc,GAAG,CAAC,CAAC;EACzBH,kBAAkB,CAAC7B,OAAO,CAAEP,GAAG,IAAK;IAClCuC,EAAE,CAACvC,GAAG,CAAC,GAAGkC,UAAU,CAAClC,GAAG,CAAC;EAC3B,CAAC,CAAC;EAEF,MAAMwC,EAAE,GAAGjD,QAAQ,CAACkD,mBAAmB,EAAE;EACzC,MAAMC,GAAG,GAAGnD,QAAQ,CAACoD,MAAM,EAAE;EAE7B,iBAAUX,OAAO,eAAK9E,IAAI,CAACC,SAAS,CAAC;IACnCoF,EAAE;IACFC,EAAE;IACFE;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAASb,cAAc,CACrBe,OAAmB,EACnBpD,OAAgB,EACG;EACnB,OAAO,IAAIqD,OAAO,CAAEC,OAAO,IAAK;IAC9B,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,KAAc;IAClB,MAAMC,MAAM,GAAIrD,IAAQ,IAAK;MAC3B,IAAImD,QAAQ,EAAE;MACdA,QAAQ,GAAG,IAAI;MACfC,KAAK,IAAIpF,YAAY,CAACoF,KAAK,CAAiB;MAC5CF,OAAO,CAAClD,IAAI,IAAI,IAAI,CAAC;IACvB,CAAC;IAED,IAAIJ,OAAO,EAAE;MACXwD,KAAK,GAAGlF,UAAU,CAAC,MAAMmF,MAAM,EAAE,EAAEzD,OAAO,CAAC;IAC7C;IAEAoD,OAAO,CAACM,IAAI,CAAEtD,IAAI,IAAKqD,MAAM,CAACrD,IAAI,CAAC,CAAC,CAACuD,KAAK,CAAC,MAAMF,MAAM,EAAE,CAAC;EAC5D,CAAC,CAAC;AACJ;;AAEA;AACA,eAAe1B,eAAe,GAAkB;EAC9C,IAAIhD,gBAAgB,EAAE;EACtBA,gBAAgB,GAAG,IAAI;EACvB,IAAI;IACF,IAAIxC,SAAS,CAACoC,YAAY,EAAE;MAC1B,MAAMiF,KAAK,GAAG,MAAMrH,SAAS,CAACoC,YAAY,CAACkF,OAAO,CAChD9H,aAAa,CAACG,QAAQ,CACvB;MACD,IAAI0H,KAAK,EAAE;QACT,MAAME,MAA8B,GAAGpG,IAAI,CAACqG,KAAK,CAACH,KAAK,CAAC;QACxD,IAAIE,MAAM,IAAIvC,KAAK,CAACyC,OAAO,CAACF,MAAM,CAAC,EAAE;UACnCA,MAAM,CAAC/C,OAAO,CAAC,SAAiB;YAAA,IAAhB,CAACP,GAAG,EAAEJ,IAAI,CAAC;YACzBpB,KAAK,CAAC6B,GAAG,CAACL,GAAG,EAAE;cACb,GAAGJ,IAAI;cACP6B,OAAO,EAAE,IAAIL,IAAI,CAACxB,IAAI,CAAC6B,OAAO;YAChC,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ;QACAgC,YAAY,EAAE;MAChB;IACF;EACF,CAAC,CAAC,OAAOrF,CAAC,EAAE;IACV;EAAA;EAEF,IAAI,CAAC7C,aAAa,CAACM,kBAAkB,EAAE;IACrC,MAAM6H,SAAS,GAAGlH,OAAO,CAACa,iBAAiB,EAAE;IAC7C,IAAIqG,SAAS,EAAE;MACblH,OAAO,CAAC0B,gBAAgB,GAAGwF,SAAS;IACtC;EACF;AACF;;AAEA;AACA,SAASD,YAAY,GAAG;EACtB,MAAME,qBAAqB,GAAG5C,KAAK,CAACC,IAAI,CAACxC,KAAK,CAACyC,OAAO,EAAE,CAAC,CACtD2C,GAAG,CAAC;IAAA,IAAC,CAAC5D,GAAG,EAAEoD,KAAK,CAAC;IAAA,OAAM;MACtBpD,GAAG;MACHyB,OAAO,EAAE2B,KAAK,CAAC3B,OAAO,CAACH,OAAO;IAChC,CAAC;EAAA,CAAC,CAAC,CACFuC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACrC,OAAO,GAAGsC,CAAC,CAACtC,OAAO,CAAC;EAExC,MAAMuC,oBAAoB,GAAGC,IAAI,CAACC,GAAG,CACnCD,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE3F,KAAK,CAAC4F,IAAI,GAAG7I,aAAa,CAACK,UAAU,CAAC,EAClD4C,KAAK,CAAC4F,IAAI,CACX;EAED,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,oBAAoB,EAAEK,CAAC,EAAE,EAAE;IAC7C7F,KAAK,CAACiC,MAAM,CAACkD,qBAAqB,CAACU,CAAC,CAAC,CAACrE,GAAG,CAAC;EAC5C;AACF;;AAEA;AACA,SAASsE,gBAAgB,CACvBtE,GAAW,EACXtE,QAAgB,EAChBkE,IAAwB,EAClB;EACN;EACA,MAAM2E,OAAO,GAAG3E,IAAI,CAAC4E,WAAW,IAAI,EAAE;EACtC,MAAM/C,OAAO,GAAG,IAAIL,IAAI,CAACA,IAAI,CAACD,GAAG,EAAE,GAAG5F,aAAa,CAACC,QAAQ,CAAC;EAC7D,MAAMgG,QAAQ,GAAGhD,KAAK,CAAC2B,GAAG,CAACzE,QAAQ,CAAC;EACpC,IAAI8F,QAAQ,IAAI+C,OAAO,IAAI/C,QAAQ,CAAC+C,OAAO,KAAKA,OAAO,EAAE;IACvD/C,QAAQ,CAACC,OAAO,GAAGA,OAAO;IAC1BpC,qBAAqB,EAAE;IACvB;EACF;;EAEA;EACAb,KAAK,CAAC6B,GAAG,CAAC3E,QAAQ,EAAE;IAClBkE,IAAI;IACJ2E,OAAO;IACP9C,OAAO;IACPC,GAAG,EAAE/C,WAAW,CAAC8F,GAAG,CAACzE,GAAG;EAC1B,CAAC,CAAC;EACFyD,YAAY,EAAE;EACd;EACApE,qBAAqB,EAAE;;EAEvB;EACA,MAAMqF,SAAS,GAAGrG,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC;EAC9C0E,SAAS,IAAIA,SAAS,CAACnE,OAAO,CAAEhB,QAAQ,IAAKO,eAAe,CAACP,QAAQ,EAAEK,IAAI,CAAC,CAAC;AAC/E;AAEA,eAAeE,eAAe,CAC5BP,QAAoB,EACpBK,IAAwB,EACT;EACfA,IAAI,GAAG,MAAML,QAAQ,CAACoF,cAAc,CAAC/E,IAAI,EAAEzD,SAAS,EAAEJ,SAAS,CAACK,YAAY,CAAC;EAE7E,MAAMmD,QAAQ,CAACqF,oBAAoB,CAAChF,IAAI,CAAC;EACzCL,QAAQ,CAACsF,cAAc,CAACjF,IAAI,CAACkF,WAAW,IAAIvF,QAAQ,CAACwF,cAAc,EAAE,CAAC;EACtExF,QAAQ,CAACyF,WAAW,CAACpF,IAAI,CAACqF,QAAQ,IAAI1F,QAAQ,CAAC2F,WAAW,EAAE,CAAC;AAC/D;AAEA,eAAevD,aAAa,CAC1BpC,QAAoB,EACS;EAC7B,MAAM;IAAEuC,OAAO;IAAEqD;EAAkB,CAAC,GAAG5F,QAAQ,CAAC6F,WAAW,EAAE;EAC7D,MAAMzI,SAAS,GAAG4C,QAAQ,CAAC8F,YAAY,EAAE;EACzC,MAAMC,UAAU,GAAG/F,QAAQ,CAAC0C,YAAY,EAAE;EAC1C,MAAMjC,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC;EAC5B,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC;EAEtC,IAAIqD,OAAO,GAAGnE,aAAa,CAAC0B,GAAG,CAACzE,QAAQ,CAAC;EACzC,IAAI,CAACkH,OAAO,EAAE;IACZ,MAAM2C,OAA0B,GAAGD,UAAU,GACzC9I,OAAO,CAACK,mBAAmB,CAAC;MAC1BH,IAAI,EAAEoF,OAAO;MACbnF,SAAS;MACTG,OAAO,EAAE;QACPoF,UAAU,EAAE3C,QAAQ,CAAC4C,aAAa,EAAE;QACpCqD,gBAAgB,EAAEjG,QAAQ,CAACkD,mBAAmB,EAAE;QAChDgD,cAAc,EAAE1E,KAAK,CAACC,IAAI,CAACzB,QAAQ,CAACmG,iBAAiB,EAAE,CAACzE,OAAO,EAAE,CAAC;QAClEyB,GAAG,EAAEnD,QAAQ,CAACoD,MAAM;MACtB,CAAC;MACD/F,OAAO,EAAEuI;IACX,CAAC,CAAC,GACF3I,OAAO,CAACC,iBAAiB,CAAC;MACxBC,IAAI,EAAEoF,OAAO;MACbnF,SAAS;MACTC,OAAO,EAAEuI;IACX,CAAC,CAAC;;IAEN;IACAvC,OAAO,GAAG2C,OAAO,CACdrC,IAAI,CAAEyC,GAAG,IAAK;MACb,IAAIA,GAAG,CAAC/I,OAAO,CAACuD,GAAG,CAAC,eAAe,CAAC,KAAK,SAAS,EAAE;QAClDxB,WAAW,CAACyB,GAAG,CAACJ,GAAG,CAAC;MACtB;MACA,OAAO2F,GAAG,CAACC,IAAI,EAAE;IACnB,CAAC,CAAC,CACD1C,IAAI,CAAEtD,IAAwB,IAAK;MAClC0E,gBAAgB,CAACtE,GAAG,EAAEtE,QAAQ,EAAEkE,IAAI,CAAC;MACrCgC,gBAAgB,CAACrC,QAAQ,CAAC;MAC1Bd,aAAa,CAACgC,MAAM,CAAC/E,QAAQ,CAAC;MAC9B,OAAOkE,IAAI;IACb,CAAC,CAAC,CACDuD,KAAK,CAAE/E,CAAC,IAAK;MACZyH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnCxG,QAAQ,CAACyG,GAAG,CAAC,yBAAyB,EAAE;QACtClE,OAAO;QACPnF,SAAS;QACTsJ,KAAK,EAAE7H,CAAC,GAAGA,CAAC,CAAC8H,OAAO,GAAG;MACzB,CAAC,CAAC;MACJzH,aAAa,CAACgC,MAAM,CAAC/E,QAAQ,CAAC;MAC9B,OAAOmH,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5B,CAAC,CAAC;IACJrE,aAAa,CAAC4B,GAAG,CAAC3E,QAAQ,EAAEkH,OAAO,CAAC;EACtC;EACA,OAAO,MAAMA,OAAO;AACtB;;AAEA;AACA;AACA,SAAShB,gBAAgB,CAACrC,QAAoB,EAAQ;EACpD,MAAMS,GAAG,GAAGC,MAAM,CAACV,QAAQ,CAAC;EAC5B,MAAM7D,QAAQ,GAAGwF,WAAW,CAAC3B,QAAQ,CAAC;EACtC,MAAM;IAAE4G,aAAa;IAAEC;EAA4B,CAAC,GAAG7G,QAAQ,CAAC6F,WAAW,EAAE;EAC7E,MAAMzI,SAAS,GAAG4C,QAAQ,CAAC8F,YAAY,EAAE;EACzC,IACE9J,aAAa,CAACI,cAAc,IAC5BgD,WAAW,CAAC8F,GAAG,CAACzE,GAAG,CAAC,IACpBjE,SAAS,CAACQ,WAAW,EACrB;IACA,IAAImC,OAAO,CAAC+F,GAAG,CAACzE,GAAG,CAAC,EAAE;IACtB,MAAMU,OAAsB,GAAG;MAC7B2F,GAAG,EAAE,IAAI;MACT3J,IAAI,EAAEyJ,aAAa;MACnBxJ,SAAS;MACTC,OAAO,EAAEwJ,2BAA2B;MACpCE,EAAE,EAAGC,KAA2B,IAAK;QACnC,IAAI;UACF,IAAIA,KAAK,CAACC,IAAI,KAAK,kBAAkB,EAAE;YACrC,MAAM9B,SAAS,GAAGrG,mBAAmB,CAAC8B,GAAG,CAACH,GAAG,CAAC;YAC9C0E,SAAS,IACPA,SAAS,CAACnE,OAAO,CAAEhB,QAAQ,IAAK;cAC9BoC,aAAa,CAACpC,QAAQ,CAAC;YACzB,CAAC,CAAC;UACN,CAAC,MAAM,IAAIgH,KAAK,CAACC,IAAI,KAAK,UAAU,EAAE;YACpC,MAAMZ,IAAwB,GAAG1I,IAAI,CAACqG,KAAK,CAACgD,KAAK,CAAC3G,IAAI,CAAC;YACvD0E,gBAAgB,CAACtE,GAAG,EAAEtE,QAAQ,EAAEkK,IAAI,CAAC;UACvC;UACA;UACAlF,OAAO,CAAC+F,MAAM,GAAG,CAAC;QACpB,CAAC,CAAC,OAAOrI,CAAC,EAAE;UACVyH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnCxG,QAAQ,CAACyG,GAAG,CAAC,WAAW,EAAE;YACxBG,aAAa;YACbxJ,SAAS;YACTsJ,KAAK,EAAE7H,CAAC,GAAIA,CAAC,CAAW8H,OAAO,GAAG;UACpC,CAAC,CAAC;UACJQ,UAAU,CAAChG,OAAO,CAAC;QACrB;MACF,CAAC;MACD+F,MAAM,EAAE,CAAC;MACT9F,KAAK,EAAE;IACT,CAAC;IACDjC,OAAO,CAAC2B,GAAG,CAACL,GAAG,EAAEU,OAAO,CAAC;IACzBG,aAAa,CAACH,OAAO,CAAC;EACxB;AACF;AAEA,SAASgG,UAAU,CAAChG,OAAsB,EAAE;EAC1C,IAAIA,OAAO,CAACC,KAAK,KAAK,MAAM,EAAE;EAC9BD,OAAO,CAAC+F,MAAM,EAAE;EAChB,IAAI/F,OAAO,CAAC+F,MAAM,GAAG,CAAC,IAAK/F,OAAO,CAAC2F,GAAG,IAAI3F,OAAO,CAAC2F,GAAG,CAACM,UAAU,KAAK,CAAE,EAAE;IACvE;IACA,MAAMC,KAAK,GACT3C,IAAI,CAAC4C,GAAG,CAAC,CAAC,EAAEnG,OAAO,CAAC+F,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,GAAGxC,IAAI,CAAC6C,MAAM,EAAE,GAAG,IAAI,CAAC;IACjElG,cAAc,CAACF,OAAO,CAAC;IACvB5C,UAAU,CAAC,MAAM;MACf,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAACiJ,QAAQ,CAACrG,OAAO,CAACC,KAAK,CAAC,EAAE;MAChDE,aAAa,CAACH,OAAO,CAAC;IACxB,CAAC,EAAEuD,IAAI,CAACC,GAAG,CAAC0C,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B;AACF;;AAEA,SAAShG,cAAc,CAACF,OAAsB,EAAE;EAC9C,IAAI,CAACA,OAAO,CAAC2F,GAAG,EAAE;EAClB3F,OAAO,CAAC2F,GAAG,CAACW,MAAM,GAAG,IAAI;EACzBtG,OAAO,CAAC2F,GAAG,CAACY,OAAO,GAAG,IAAI;EAC1BvG,OAAO,CAAC2F,GAAG,CAACa,KAAK,EAAE;EACnBxG,OAAO,CAAC2F,GAAG,GAAG,IAAI;EAClB,IAAI3F,OAAO,CAACC,KAAK,KAAK,QAAQ,EAAE;IAC9BD,OAAO,CAACC,KAAK,GAAG,UAAU;EAC5B;AACF;AAEA,SAASE,aAAa,CAACH,OAAsB,EAAE;EAC7CA,OAAO,CAAC2F,GAAG,GAAG7J,OAAO,CAACY,eAAe,CAAC;IACpCV,IAAI,EAAEgE,OAAO,CAAChE,IAAI;IAClBC,SAAS,EAAE+D,OAAO,CAAC/D,SAAS;IAC5BC,OAAO,EAAE8D,OAAO,CAAC9D;EACnB,CAAC,CAAgB;EACjB8D,OAAO,CAACC,KAAK,GAAG,QAAQ;EACxBD,OAAO,CAAC2F,GAAG,CAACrI,gBAAgB,CAAC,UAAU,EAAE0C,OAAO,CAAC4F,EAAE,CAAC;EACpD5F,OAAO,CAAC2F,GAAG,CAACrI,gBAAgB,CAAC,kBAAkB,EAAE0C,OAAO,CAAC4F,EAAE,CAAC;EAC5D5F,OAAO,CAAC2F,GAAG,CAACY,OAAO,GAAG,MAAMP,UAAU,CAAChG,OAAO,CAAC;EAC/CA,OAAO,CAAC2F,GAAG,CAACW,MAAM,GAAG,MAAM;IACzBtG,OAAO,CAAC+F,MAAM,GAAG,CAAC;EACpB,CAAC;AACH;AAEA,SAASU,cAAc,CAACzG,OAAsB,EAAEV,GAAW,EAAE;EAC3DY,cAAc,CAACF,OAAO,CAAC;EACvBhC,OAAO,CAAC+B,MAAM,CAACT,GAAG,CAAC;AACrB;AAEA,SAASd,gBAAgB,GAAG;EAC1B;EACAP,WAAW,CAACS,KAAK,EAAE;;EAEnB;EACAV,OAAO,CAAC6B,OAAO,CAAC4G,cAAc,CAAC;;EAE/B;EACA9I,mBAAmB,CAACe,KAAK,EAAE;;EAE3B;EACA5C,OAAO,CAAC0B,gBAAgB,EAAE;AAC5B"}