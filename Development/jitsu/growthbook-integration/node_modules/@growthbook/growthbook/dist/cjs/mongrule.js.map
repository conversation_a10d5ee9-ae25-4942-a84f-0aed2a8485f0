{"version": 3, "file": "mongrule.js", "names": ["_regexCache", "evalCondition", "obj", "condition", "evalOr", "evalAnd", "k", "v", "Object", "entries", "evalConditionValue", "<PERSON><PERSON><PERSON>", "path", "parts", "split", "current", "i", "length", "getRegex", "regex", "RegExp", "replace", "value", "Array", "isArray", "isOperatorObject", "JSON", "stringify", "op", "evalOperatorCondition", "keys", "filter", "getType", "t", "includes", "elemMatch", "actual", "expected", "check", "isIn", "some", "el", "operator", "paddedVersionString", "passed", "j", "test", "e", "console", "error", "conditions"], "sources": ["../../src/mongrule.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport {\n  ConditionInterface,\n  TestedObj,\n  ConditionValue,\n  Operator,\n  OperatorConditionValue,\n  VarType,\n} from \"./types/mongrule\";\nimport { paddedVersionString } from \"./util\";\n\nconst _regexCache: { [key: string]: RegExp } = {};\n\n// The top-level condition evaluation function\nexport function evalCondition(\n  obj: TestedObj,\n  condition: ConditionInterface\n): boolean {\n  // Recursive condition\n  if (\"$or\" in condition) {\n    return evalOr(obj, condition[\"$or\"] as ConditionInterface[]);\n  }\n  if (\"$nor\" in condition) {\n    return !evalOr(obj, condition[\"$nor\"] as ConditionInterface[]);\n  }\n  if (\"$and\" in condition) {\n    return evalAnd(obj, condition[\"$and\"] as ConditionInterface[]);\n  }\n  if (\"$not\" in condition) {\n    return !evalCondition(obj, condition[\"$not\"] as ConditionInterface);\n  }\n\n  // Condition is an object, keys are object paths, values are the condition for that path\n  for (const [k, v] of Object.entries(condition)) {\n    if (!evalConditionValue(v, getPath(obj, k))) return false;\n  }\n  return true;\n}\n\n// Return value at dot-separated path of an object\nfunction getPath(obj: TestedObj, path: string) {\n  const parts = path.split(\".\");\n  let current: any = obj;\n  for (let i = 0; i < parts.length; i++) {\n    if (current && typeof current === \"object\" && parts[i] in current) {\n      current = current[parts[i]];\n    } else {\n      return null;\n    }\n  }\n  return current;\n}\n\n// Transform a regex string into a real RegExp object\nfunction getRegex(regex: string): RegExp {\n  if (!_regexCache[regex]) {\n    _regexCache[regex] = new RegExp(regex.replace(/([^\\\\])\\//g, \"$1\\\\/\"));\n  }\n  return _regexCache[regex];\n}\n\n// Evaluate a single value against a condition\nfunction evalConditionValue(condition: ConditionValue, value: any) {\n  // Simple equality comparisons\n  if (typeof condition === \"string\") {\n    return value + \"\" === condition;\n  }\n  if (typeof condition === \"number\") {\n    return value * 1 === condition;\n  }\n  if (typeof condition === \"boolean\") {\n    return !!value === condition;\n  }\n\n  if (condition === null) {\n    return value === null;\n  }\n\n  if (Array.isArray(condition) || !isOperatorObject(condition)) {\n    return JSON.stringify(value) === JSON.stringify(condition);\n  }\n\n  // This is a special operator condition and we should evaluate each one separately\n  for (const op in condition) {\n    if (\n      !evalOperatorCondition(\n        op as Operator,\n        value,\n        condition[op as keyof OperatorConditionValue]\n      )\n    ) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// If the object has only keys that start with '$'\nfunction isOperatorObject(obj: any): boolean {\n  const keys = Object.keys(obj);\n  return (\n    keys.length > 0 && keys.filter((k) => k[0] === \"$\").length === keys.length\n  );\n}\n\n// Return the data type of a value\nfunction getType(v: any): VarType | \"unknown\" {\n  if (v === null) return \"null\";\n  if (Array.isArray(v)) return \"array\";\n  const t = typeof v;\n  if ([\"string\", \"number\", \"boolean\", \"object\", \"undefined\"].includes(t)) {\n    return t as VarType;\n  }\n  return \"unknown\";\n}\n\n// At least one element of actual must match the expected condition/value\nfunction elemMatch(actual: any, expected: any) {\n  if (!Array.isArray(actual)) return false;\n  const check = isOperatorObject(expected)\n    ? (v: any) => evalConditionValue(expected, v)\n    : (v: any) => evalCondition(v, expected);\n  for (let i = 0; i < actual.length; i++) {\n    if (actual[i] && check(actual[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isIn(actual: any, expected: Array<any>): boolean {\n  // Do an intersection is attribute is an array\n  if (Array.isArray(actual)) {\n    return actual.some((el) => expected.includes(el));\n  }\n  return expected.includes(actual);\n}\n\n// Evaluate a single operator condition\nfunction evalOperatorCondition(\n  operator: Operator,\n  actual: any,\n  expected: any\n): boolean {\n  switch (operator) {\n    case \"$veq\":\n      return paddedVersionString(actual) === paddedVersionString(expected);\n    case \"$vne\":\n      return paddedVersionString(actual) !== paddedVersionString(expected);\n    case \"$vgt\":\n      return paddedVersionString(actual) > paddedVersionString(expected);\n    case \"$vgte\":\n      return paddedVersionString(actual) >= paddedVersionString(expected);\n    case \"$vlt\":\n      return paddedVersionString(actual) < paddedVersionString(expected);\n    case \"$vlte\":\n      return paddedVersionString(actual) <= paddedVersionString(expected);\n    case \"$eq\":\n      return actual === expected;\n    case \"$ne\":\n      return actual !== expected;\n    case \"$lt\":\n      return actual < expected;\n    case \"$lte\":\n      return actual <= expected;\n    case \"$gt\":\n      return actual > expected;\n    case \"$gte\":\n      return actual >= expected;\n    case \"$exists\":\n      // Using `!=` and `==` instead of strict checks so it also matches for undefined\n      return expected ? actual != null : actual == null;\n    case \"$in\":\n      if (!Array.isArray(expected)) return false;\n      return isIn(actual, expected);\n    case \"$nin\":\n      if (!Array.isArray(expected)) return false;\n      return !isIn(actual, expected);\n    case \"$not\":\n      return !evalConditionValue(expected, actual);\n    case \"$size\":\n      if (!Array.isArray(actual)) return false;\n      return evalConditionValue(expected, actual.length);\n    case \"$elemMatch\":\n      return elemMatch(actual, expected);\n    case \"$all\":\n      if (!Array.isArray(actual)) return false;\n      for (let i = 0; i < expected.length; i++) {\n        let passed = false;\n        for (let j = 0; j < actual.length; j++) {\n          if (evalConditionValue(expected[i], actual[j])) {\n            passed = true;\n            break;\n          }\n        }\n        if (!passed) return false;\n      }\n      return true;\n    case \"$regex\":\n      try {\n        return getRegex(expected).test(actual);\n      } catch (e) {\n        return false;\n      }\n    case \"$type\":\n      return getType(actual) === expected;\n    default:\n      console.error(\"Unknown operator: \" + operator);\n      return false;\n  }\n}\n\n// Recursive $or rule\nfunction evalOr(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  if (!conditions.length) return true;\n  for (let i = 0; i < conditions.length; i++) {\n    if (evalCondition(obj, conditions[i])) {\n      return true;\n    }\n  }\n  return false;\n}\n\n// Recursive $and rule\nfunction evalAnd(obj: TestedObj, conditions: ConditionInterface[]): boolean {\n  for (let i = 0; i < conditions.length; i++) {\n    if (!evalCondition(obj, conditions[i])) {\n      return false;\n    }\n  }\n  return true;\n}\n"], "mappings": ";;;;;;AAUA;AAVA;;AAYA,MAAMA,WAAsC,GAAG,CAAC,CAAC;;AAEjD;AACO,SAASC,aAAa,CAC3BC,GAAc,EACdC,SAA6B,EACpB;EACT;EACA,IAAI,KAAK,IAAIA,SAAS,EAAE;IACtB,OAAOC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,KAAK,CAAC,CAAyB;EAC9D;EACA,IAAI,MAAM,IAAIA,SAAS,EAAE;IACvB,OAAO,CAACC,MAAM,CAACF,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB;EAChE;EACA,IAAI,MAAM,IAAIA,SAAS,EAAE;IACvB,OAAOE,OAAO,CAACH,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAyB;EAChE;EACA,IAAI,MAAM,IAAIA,SAAS,EAAE;IACvB,OAAO,CAACF,aAAa,CAACC,GAAG,EAAEC,SAAS,CAAC,MAAM,CAAC,CAAuB;EACrE;;EAEA;EACA,KAAK,MAAM,CAACG,CAAC,EAAEC,CAAC,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACN,SAAS,CAAC,EAAE;IAC9C,IAAI,CAACO,kBAAkB,CAACH,CAAC,EAAEI,OAAO,CAACT,GAAG,EAAEI,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;EAC3D;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASK,OAAO,CAACT,GAAc,EAAEU,IAAY,EAAE;EAC7C,MAAMC,KAAK,GAAGD,IAAI,CAACE,KAAK,CAAC,GAAG,CAAC;EAC7B,IAAIC,OAAY,GAAGb,GAAG;EACtB,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,KAAK,CAACI,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAID,OAAO,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIF,KAAK,CAACG,CAAC,CAAC,IAAID,OAAO,EAAE;MACjEA,OAAO,GAAGA,OAAO,CAACF,KAAK,CAACG,CAAC,CAAC,CAAC;IAC7B,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EACA,OAAOD,OAAO;AAChB;;AAEA;AACA,SAASG,QAAQ,CAACC,KAAa,EAAU;EACvC,IAAI,CAACnB,WAAW,CAACmB,KAAK,CAAC,EAAE;IACvBnB,WAAW,CAACmB,KAAK,CAAC,GAAG,IAAIC,MAAM,CAACD,KAAK,CAACE,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;EACvE;EACA,OAAOrB,WAAW,CAACmB,KAAK,CAAC;AAC3B;;AAEA;AACA,SAAST,kBAAkB,CAACP,SAAyB,EAAEmB,KAAU,EAAE;EACjE;EACA,IAAI,OAAOnB,SAAS,KAAK,QAAQ,EAAE;IACjC,OAAOmB,KAAK,GAAG,EAAE,KAAKnB,SAAS;EACjC;EACA,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,OAAOmB,KAAK,GAAG,CAAC,KAAKnB,SAAS;EAChC;EACA,IAAI,OAAOA,SAAS,KAAK,SAAS,EAAE;IAClC,OAAO,CAAC,CAACmB,KAAK,KAAKnB,SAAS;EAC9B;EAEA,IAAIA,SAAS,KAAK,IAAI,EAAE;IACtB,OAAOmB,KAAK,KAAK,IAAI;EACvB;EAEA,IAAIC,KAAK,CAACC,OAAO,CAACrB,SAAS,CAAC,IAAI,CAACsB,gBAAgB,CAACtB,SAAS,CAAC,EAAE;IAC5D,OAAOuB,IAAI,CAACC,SAAS,CAACL,KAAK,CAAC,KAAKI,IAAI,CAACC,SAAS,CAACxB,SAAS,CAAC;EAC5D;;EAEA;EACA,KAAK,MAAMyB,EAAE,IAAIzB,SAAS,EAAE;IAC1B,IACE,CAAC0B,qBAAqB,CACpBD,EAAE,EACFN,KAAK,EACLnB,SAAS,CAACyB,EAAE,CAAiC,CAC9C,EACD;MACA,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;;AAEA;AACA,SAASH,gBAAgB,CAACvB,GAAQ,EAAW;EAC3C,MAAM4B,IAAI,GAAGtB,MAAM,CAACsB,IAAI,CAAC5B,GAAG,CAAC;EAC7B,OACE4B,IAAI,CAACb,MAAM,GAAG,CAAC,IAAIa,IAAI,CAACC,MAAM,CAAEzB,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAACW,MAAM,KAAKa,IAAI,CAACb,MAAM;AAE9E;;AAEA;AACA,SAASe,OAAO,CAACzB,CAAM,EAAuB;EAC5C,IAAIA,CAAC,KAAK,IAAI,EAAE,OAAO,MAAM;EAC7B,IAAIgB,KAAK,CAACC,OAAO,CAACjB,CAAC,CAAC,EAAE,OAAO,OAAO;EACpC,MAAM0B,CAAC,GAAG,OAAO1B,CAAC;EAClB,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC2B,QAAQ,CAACD,CAAC,CAAC,EAAE;IACtE,OAAOA,CAAC;EACV;EACA,OAAO,SAAS;AAClB;;AAEA;AACA,SAASE,SAAS,CAACC,MAAW,EAAEC,QAAa,EAAE;EAC7C,IAAI,CAACd,KAAK,CAACC,OAAO,CAACY,MAAM,CAAC,EAAE,OAAO,KAAK;EACxC,MAAME,KAAK,GAAGb,gBAAgB,CAACY,QAAQ,CAAC,GACnC9B,CAAM,IAAKG,kBAAkB,CAAC2B,QAAQ,EAAE9B,CAAC,CAAC,GAC1CA,CAAM,IAAKN,aAAa,CAACM,CAAC,EAAE8B,QAAQ,CAAC;EAC1C,KAAK,IAAIrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoB,MAAM,CAACnB,MAAM,EAAED,CAAC,EAAE,EAAE;IACtC,IAAIoB,MAAM,CAACpB,CAAC,CAAC,IAAIsB,KAAK,CAACF,MAAM,CAACpB,CAAC,CAAC,CAAC,EAAE;MACjC,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAEA,SAASuB,IAAI,CAACH,MAAW,EAAEC,QAAoB,EAAW;EACxD;EACA,IAAId,KAAK,CAACC,OAAO,CAACY,MAAM,CAAC,EAAE;IACzB,OAAOA,MAAM,CAACI,IAAI,CAAEC,EAAE,IAAKJ,QAAQ,CAACH,QAAQ,CAACO,EAAE,CAAC,CAAC;EACnD;EACA,OAAOJ,QAAQ,CAACH,QAAQ,CAACE,MAAM,CAAC;AAClC;;AAEA;AACA,SAASP,qBAAqB,CAC5Ba,QAAkB,EAClBN,MAAW,EACXC,QAAa,EACJ;EACT,QAAQK,QAAQ;IACd,KAAK,MAAM;MACT,OAAO,IAAAC,yBAAmB,EAACP,MAAM,CAAC,KAAK,IAAAO,yBAAmB,EAACN,QAAQ,CAAC;IACtE,KAAK,MAAM;MACT,OAAO,IAAAM,yBAAmB,EAACP,MAAM,CAAC,KAAK,IAAAO,yBAAmB,EAACN,QAAQ,CAAC;IACtE,KAAK,MAAM;MACT,OAAO,IAAAM,yBAAmB,EAACP,MAAM,CAAC,GAAG,IAAAO,yBAAmB,EAACN,QAAQ,CAAC;IACpE,KAAK,OAAO;MACV,OAAO,IAAAM,yBAAmB,EAACP,MAAM,CAAC,IAAI,IAAAO,yBAAmB,EAACN,QAAQ,CAAC;IACrE,KAAK,MAAM;MACT,OAAO,IAAAM,yBAAmB,EAACP,MAAM,CAAC,GAAG,IAAAO,yBAAmB,EAACN,QAAQ,CAAC;IACpE,KAAK,OAAO;MACV,OAAO,IAAAM,yBAAmB,EAACP,MAAM,CAAC,IAAI,IAAAO,yBAAmB,EAACN,QAAQ,CAAC;IACrE,KAAK,KAAK;MACR,OAAOD,MAAM,KAAKC,QAAQ;IAC5B,KAAK,KAAK;MACR,OAAOD,MAAM,KAAKC,QAAQ;IAC5B,KAAK,KAAK;MACR,OAAOD,MAAM,GAAGC,QAAQ;IAC1B,KAAK,MAAM;MACT,OAAOD,MAAM,IAAIC,QAAQ;IAC3B,KAAK,KAAK;MACR,OAAOD,MAAM,GAAGC,QAAQ;IAC1B,KAAK,MAAM;MACT,OAAOD,MAAM,IAAIC,QAAQ;IAC3B,KAAK,SAAS;MACZ;MACA,OAAOA,QAAQ,GAAGD,MAAM,IAAI,IAAI,GAAGA,MAAM,IAAI,IAAI;IACnD,KAAK,KAAK;MACR,IAAI,CAACb,KAAK,CAACC,OAAO,CAACa,QAAQ,CAAC,EAAE,OAAO,KAAK;MAC1C,OAAOE,IAAI,CAACH,MAAM,EAAEC,QAAQ,CAAC;IAC/B,KAAK,MAAM;MACT,IAAI,CAACd,KAAK,CAACC,OAAO,CAACa,QAAQ,CAAC,EAAE,OAAO,KAAK;MAC1C,OAAO,CAACE,IAAI,CAACH,MAAM,EAAEC,QAAQ,CAAC;IAChC,KAAK,MAAM;MACT,OAAO,CAAC3B,kBAAkB,CAAC2B,QAAQ,EAAED,MAAM,CAAC;IAC9C,KAAK,OAAO;MACV,IAAI,CAACb,KAAK,CAACC,OAAO,CAACY,MAAM,CAAC,EAAE,OAAO,KAAK;MACxC,OAAO1B,kBAAkB,CAAC2B,QAAQ,EAAED,MAAM,CAACnB,MAAM,CAAC;IACpD,KAAK,YAAY;MACf,OAAOkB,SAAS,CAACC,MAAM,EAAEC,QAAQ,CAAC;IACpC,KAAK,MAAM;MACT,IAAI,CAACd,KAAK,CAACC,OAAO,CAACY,MAAM,CAAC,EAAE,OAAO,KAAK;MACxC,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,QAAQ,CAACpB,MAAM,EAAED,CAAC,EAAE,EAAE;QACxC,IAAI4B,MAAM,GAAG,KAAK;QAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACnB,MAAM,EAAE4B,CAAC,EAAE,EAAE;UACtC,IAAInC,kBAAkB,CAAC2B,QAAQ,CAACrB,CAAC,CAAC,EAAEoB,MAAM,CAACS,CAAC,CAAC,CAAC,EAAE;YAC9CD,MAAM,GAAG,IAAI;YACb;UACF;QACF;QACA,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK;MAC3B;MACA,OAAO,IAAI;IACb,KAAK,QAAQ;MACX,IAAI;QACF,OAAO1B,QAAQ,CAACmB,QAAQ,CAAC,CAACS,IAAI,CAACV,MAAM,CAAC;MACxC,CAAC,CAAC,OAAOW,CAAC,EAAE;QACV,OAAO,KAAK;MACd;IACF,KAAK,OAAO;MACV,OAAOf,OAAO,CAACI,MAAM,CAAC,KAAKC,QAAQ;IACrC;MACEW,OAAO,CAACC,KAAK,CAAC,oBAAoB,GAAGP,QAAQ,CAAC;MAC9C,OAAO,KAAK;EAAC;AAEnB;;AAEA;AACA,SAAStC,MAAM,CAACF,GAAc,EAAEgD,UAAgC,EAAW;EACzE,IAAI,CAACA,UAAU,CAACjC,MAAM,EAAE,OAAO,IAAI;EACnC,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,UAAU,CAACjC,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAIf,aAAa,CAACC,GAAG,EAAEgD,UAAU,CAAClC,CAAC,CAAC,CAAC,EAAE;MACrC,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASX,OAAO,CAACH,GAAc,EAAEgD,UAAgC,EAAW;EAC1E,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,UAAU,CAACjC,MAAM,EAAED,CAAC,EAAE,EAAE;IAC1C,IAAI,CAACf,aAAa,CAACC,GAAG,EAAEgD,UAAU,CAAClC,CAAC,CAAC,CAAC,EAAE;MACtC,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb"}