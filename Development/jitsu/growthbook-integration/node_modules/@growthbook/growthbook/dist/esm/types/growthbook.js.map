{"version": 3, "file": "growthbook.js", "names": [], "sources": ["../../../src/types/growthbook.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport type { GrowthBook, StickyBucketService } from \"..\";\nimport { ConditionInterface } from \"./mongrule\";\n\ndeclare global {\n  interface Window {\n    _growthbook?: GrowthBook;\n  }\n}\n\nexport type VariationMeta = {\n  passthrough?: boolean;\n  key?: string;\n  name?: string;\n};\n\nexport type FeatureRule<T = any> = {\n  id?: string;\n  condition?: ConditionInterface;\n  force?: T;\n  variations?: T[];\n  weights?: number[];\n  key?: string;\n  hashAttribute?: string;\n  fallbackAttribute?: string;\n  hashVersion?: number;\n  disableStickyBucketing?: boolean;\n  bucketVersion?: number;\n  minBucketVersion?: number;\n  range?: VariationRange;\n  coverage?: number;\n  /** @deprecated */\n  namespace?: [string, number, number];\n  ranges?: VariationRange[];\n  meta?: VariationMeta[];\n  filters?: Filter[];\n  seed?: string;\n  name?: string;\n  phase?: string;\n  tracks?: Array<{\n    experiment: Experiment<T>;\n    result: Result<T>;\n  }>;\n};\n\nexport interface FeatureDefinition<T = any> {\n  defaultValue?: T;\n  rules?: FeatureRule<T>[];\n}\n\nexport type FeatureResultSource =\n  | \"unknownFeature\"\n  | \"defaultValue\"\n  | \"force\"\n  | \"override\"\n  | \"experiment\";\n\nexport interface FeatureResult<T = any> {\n  value: T | null;\n  source: FeatureResultSource;\n  on: boolean;\n  off: boolean;\n  ruleId: string;\n  experiment?: Experiment<T>;\n  experimentResult?: Result<T>;\n}\n\n/** @deprecated */\nexport type ExperimentStatus = \"draft\" | \"running\" | \"stopped\";\n\nexport type UrlTargetType = \"regex\" | \"simple\";\n\nexport type UrlTarget = {\n  include: boolean;\n  type: UrlTargetType;\n  pattern: string;\n};\n\nexport type Experiment<T> = {\n  key: string;\n  variations: [T, T, ...T[]];\n  ranges?: VariationRange[];\n  meta?: VariationMeta[];\n  filters?: Filter[];\n  seed?: string;\n  name?: string;\n  phase?: string;\n  urlPatterns?: UrlTarget[];\n  weights?: number[];\n  condition?: ConditionInterface;\n  coverage?: number;\n  include?: () => boolean;\n  /** @deprecated */\n  namespace?: [string, number, number];\n  force?: number;\n  hashAttribute?: string;\n  fallbackAttribute?: string;\n  hashVersion?: number;\n  disableStickyBucketing?: boolean;\n  bucketVersion?: number;\n  minBucketVersion?: number;\n  active?: boolean;\n  /** @deprecated */\n  status?: ExperimentStatus;\n  /** @deprecated */\n  url?: RegExp;\n  /** @deprecated */\n  groups?: string[];\n};\n\nexport type AutoExperiment = Experiment<AutoExperimentVariation> & {\n  // If true, require the experiment to be manually triggered\n  manual?: boolean;\n};\n\nexport type ExperimentOverride = {\n  condition?: ConditionInterface;\n  weights?: number[];\n  active?: boolean;\n  status?: ExperimentStatus;\n  force?: number;\n  coverage?: number;\n  groups?: string[];\n  namespace?: [string, number, number];\n  url?: RegExp | string;\n};\n\nexport interface Result<T> {\n  value: T;\n  variationId: number;\n  key: string;\n  name?: string;\n  bucket?: number;\n  passthrough?: boolean;\n  inExperiment: boolean;\n  hashUsed?: boolean;\n  hashAttribute: string;\n  hashValue: string;\n  featureId: string | null;\n  stickyBucketUsed?: boolean;\n}\n\nexport type Attributes = Record<string, any>;\n\nexport type RealtimeUsageData = {\n  key: string;\n  on: boolean;\n};\n\nexport interface Context {\n  enabled?: boolean;\n  attributes?: Attributes;\n  url?: string;\n  features?: Record<string, FeatureDefinition>;\n  experiments?: AutoExperiment[];\n  forcedVariations?: Record<string, number>;\n  stickyBucketAssignmentDocs?: Record<\n    StickyAttributeKey,\n    StickyAssignmentsDocument\n  >;\n  stickyBucketIdentifierAttributes?: string[];\n  stickyBucketService?: StickyBucketService;\n  log?: (msg: string, ctx: any) => void;\n  qaMode?: boolean;\n  backgroundSync?: boolean;\n  subscribeToChanges?: boolean;\n  enableDevMode?: boolean;\n  /* @deprecated */\n  disableDevTools?: boolean;\n  trackingCallback?: (experiment: Experiment<any>, result: Result<any>) => void;\n  onFeatureUsage?: (key: string, result: FeatureResult<any>) => void;\n  realtimeKey?: string;\n  realtimeInterval?: number;\n  cacheKeyAttributes?: (keyof Attributes)[];\n  /* @deprecated */\n  user?: {\n    id?: string;\n    anonId?: string;\n    [key: string]: string | undefined;\n  };\n  /* @deprecated */\n  overrides?: Record<string, ExperimentOverride>;\n  /* @deprecated */\n  groups?: Record<string, boolean>;\n  apiHost?: string;\n  streamingHost?: string;\n  apiHostRequestHeaders?: Record<string, string>;\n  streamingHostRequestHeaders?: Record<string, string>;\n  clientKey?: string;\n  decryptionKey?: string;\n  remoteEval?: boolean;\n}\n\nexport type SubscriptionFunction = (\n  experiment: Experiment<any>,\n  result: Result<any>\n) => void;\n\nexport type VariationRange = [number, number];\n\nexport type JSONValue =\n  | null\n  | number\n  | string\n  | boolean\n  | Array<JSONValue>\n  | Record<string, unknown>\n  | { [key: string]: JSONValue };\n\nexport type WidenPrimitives<T> = T extends string\n  ? string\n  : T extends number\n  ? number\n  : T extends boolean\n  ? boolean\n  : T;\n\nexport type DOMMutation = {\n  selector: string;\n  action: string;\n  attribute: string;\n  value?: string;\n  parentSelector?: string;\n  insertBeforeSelector?: string;\n};\n\nexport type AutoExperimentVariation = {\n  domMutations?: DOMMutation[];\n  css?: string;\n  js?: string;\n};\n\nexport type FeatureDefinitions = Record<string, FeatureDefinition>;\n\nexport type FeatureApiResponse = {\n  features?: FeatureDefinitions;\n  dateUpdated?: string;\n  encryptedFeatures?: string;\n  experiments?: AutoExperiment[];\n  encryptedExperiments?: string;\n};\n\n// Polyfills required for non-standard browser environments (ReactNative, Node, etc.)\n// These are typed as `any` since polyfills like `node-fetch` are not 100% compatible with native types\nexport type Polyfills = {\n  // eslint-disable-next-line\n  fetch: any;\n  // eslint-disable-next-line\n  SubtleCrypto: any;\n  // eslint-disable-next-line\n  EventSource: any;\n  localStorage?: LocalStorageCompat;\n};\n\nexport type Helpers = {\n  fetchFeaturesCall: ({\n    host,\n    clientKey,\n    headers,\n  }: {\n    host: string;\n    clientKey: string;\n    headers?: Record<string, string>;\n  }) => Promise<Response>;\n  fetchRemoteEvalCall: ({\n    host,\n    clientKey,\n    payload,\n    headers,\n  }: {\n    host: string;\n    clientKey: string;\n    // eslint-disable-next-line\n    payload: any;\n    headers?: Record<string, string>;\n  }) => Promise<Response>;\n  eventSourceCall: ({\n    host,\n    clientKey,\n    headers,\n  }: {\n    host: string;\n    clientKey: string;\n    headers?: Record<string, string>;\n  }) => EventSource;\n  startIdleListener: () => (() => void) | void;\n  stopIdleListener: () => void;\n};\n\nexport interface LocalStorageCompat {\n  getItem(key: string): string | null | Promise<string | null>;\n  setItem(key: string, value: string): void | Promise<void>;\n}\n\nexport type CacheSettings = {\n  backgroundSync: boolean;\n  cacheKey: string;\n  staleTTL: number;\n  maxAge: number;\n  maxEntries: number;\n  disableIdleStreams: boolean;\n  idleStreamInterval: number;\n};\n\nexport type ApiHost = string;\nexport type ClientKey = string;\n\nexport type LoadFeaturesOptions = {\n  /** @deprecated */\n  autoRefresh?: boolean;\n  timeout?: number;\n  skipCache?: boolean;\n};\n\nexport type RefreshFeaturesOptions = {\n  timeout?: number;\n  skipCache?: boolean;\n};\n\nexport interface Filter {\n  // Override the hashAttribute used for this filter\n  attribute?: string;\n  // The hash seed\n  seed: string;\n  // The hashing version to use\n  hashVersion: number;\n  // Only include these resulting ranges\n  ranges: VariationRange[];\n}\n\nexport type StickyAttributeKey = string; // `${attributeName}||${attributeValue}`\nexport type StickyExperimentKey = string; // `${experimentId}__{version}`\nexport type StickyAssignments = Record<StickyExperimentKey, string>;\nexport interface StickyAssignmentsDocument {\n  attributeName: string;\n  attributeValue: string;\n  assignments: StickyAssignments;\n}\n"], "mappings": ""}