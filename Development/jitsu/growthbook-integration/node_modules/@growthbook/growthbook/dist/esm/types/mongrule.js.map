{"version": 3, "file": "mongrule.js", "names": [], "sources": ["../../../src/types/mongrule.ts"], "sourcesContent": ["type OrCondition = {\n  $or: ConditionInterface[];\n};\ntype NorCondition = {\n  $nor: ConditionInterface[];\n};\ntype AndCondition = {\n  $and: ConditionInterface[];\n};\ntype NotCondition = {\n  $not: ConditionInterface;\n};\nexport type Operator =\n  | \"$in\"\n  | \"$nin\"\n  | \"$gt\"\n  | \"$gte\"\n  | \"$lt\"\n  | \"$lte\"\n  | \"$regex\"\n  | \"$ne\"\n  | \"$eq\"\n  | \"$size\"\n  | \"$elemMatch\"\n  | \"$all\"\n  | \"$not\"\n  | \"$type\"\n  | \"$exists\"\n  | \"$vgt\"\n  | \"$vgte\"\n  | \"$vlt\"\n  | \"$vlte\"\n  | \"$vne\"\n  | \"$veq\";\nexport type VarType =\n  | \"string\"\n  | \"number\"\n  | \"boolean\"\n  | \"array\"\n  | \"object\"\n  | \"null\"\n  | \"undefined\";\nexport type OperatorConditionValue = {\n  $in?: (string | number)[];\n  $nin?: (string | number)[];\n  $gt?: number | string;\n  $gte?: number | string;\n  $lt?: number | string;\n  $lte?: number | string;\n  $regex?: string;\n  $ne?: number | string;\n  $eq?: number | string;\n  $exists?: boolean;\n  $all?: ConditionValue[];\n  $size?: number | ConditionValue;\n  $type?: VarType;\n  $elemMatch?: ConditionInterface | OperatorConditionValue;\n  $not?: ConditionValue;\n};\n\nexport type ConditionValue =\n  | OperatorConditionValue\n  | string\n  | number\n  | boolean\n  // eslint-disable-next-line\n  | Array<any>\n  // eslint-disable-next-line\n  | Record<string, any>\n  | null;\n\nexport type OperatorCondition = {\n  [key: string]: ConditionValue;\n};\n\nexport type ConditionInterface =\n  | OrCondition\n  | NorCondition\n  | AndCondition\n  | NotCondition\n  | OperatorCondition;\n\n// eslint-disable-next-line\nexport type TestedObj = Record<string, any>;\n"], "mappings": ""}