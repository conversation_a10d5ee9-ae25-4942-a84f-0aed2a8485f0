{"version": 3, "file": "index.js", "names": ["setPolyfills", "clearCache", "configure<PERSON>ache", "helpers", "onVisible", "onHidden", "GrowthBook", "StickyBucketService", "LocalStorageStickyBucketService", "ExpressCookieStickyBucketService", "BrowserCookieStickyBucketService", "RedisStickyBucketService", "isURLTargeted"], "sources": ["../../src/index.ts"], "sourcesContent": ["export type {\n  Context,\n  Attributes,\n  Polyfills,\n  CacheSettings,\n  FeatureApiResponse,\n  LoadFeaturesOptions,\n  RefreshFeaturesOptions,\n  FeatureDefinitions,\n  FeatureDefinition,\n  FeatureRule,\n  FeatureResult,\n  FeatureResultSource,\n  Experiment,\n  Result,\n  ExperimentOverride,\n  ExperimentStatus,\n  JSONValue,\n  SubscriptionFunction,\n  LocalStorageCompat,\n  WidenPrimitives,\n  VariationMeta,\n  Filter,\n  VariationRange,\n  UrlTarget,\n  AutoExperiment,\n  AutoExperimentVariation,\n  UrlTargetType,\n} from \"./types/growthbook\";\n\nexport type { ConditionInterface } from \"./types/mongrule\";\n\nexport {\n  setPolyfills,\n  clearCache,\n  configureCache,\n  helpers,\n  onVisible,\n  onHidden,\n} from \"./feature-repository\";\n\nexport { GrowthBook } from \"./GrowthBook\";\nexport {\n  StickyBucketService,\n  LocalStorageStickyBucketService,\n  ExpressCookieStickyBucketService,\n  BrowserCookieStickyBucketService,\n  RedisStickyBucketService,\n} from \"./sticky-bucket-service\";\nexport { isURLTargeted } from \"./util\";\n"], "mappings": "AAgCA,SACEA,YAAY,EACZC,UAAU,EACVC,cAAc,EACdC,OAAO,EACPC,SAAS,EACTC,QAAQ,QACH,sBAAsB;AAE7B,SAASC,UAAU,QAAQ,cAAc;AACzC,SACEC,mBAAmB,EACnBC,+BAA+B,EAC/BC,gCAAgC,EAChCC,gCAAgC,EAChCC,wBAAwB,QACnB,yBAAyB;AAChC,SAASC,aAAa,QAAQ,QAAQ"}