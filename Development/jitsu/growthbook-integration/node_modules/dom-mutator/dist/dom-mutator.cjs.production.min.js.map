{"version": 3, "file": "dom-mutator.cjs.production.min.js", "sources": ["../src/index.ts"], "sourcesContent": ["export const validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nconst nullController: MutationController = {\n  revert: () => {},\n};\n\nconst elements: Map<Element, ElementRecord> = new Map();\nconst mutations: Set<Mutation> = new Set();\n\nfunction getObserverInit(attr: string): MutationObserverInit {\n  return attr === 'html'\n    ? {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        characterData: true,\n      }\n    : {\n        childList: false,\n        subtree: false,\n        attributes: true,\n        attributeFilter: [attr],\n      };\n}\n\nfunction getElementRecord(element: Element): ElementRecord {\n  let record = elements.get(element);\n\n  if (!record) {\n    record = { element, attributes: {} };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(\n  el: Element,\n  attr: string,\n  getCurrentValue: (el: Element) => any,\n  setValue: (el: Element, val: any) => void,\n  mutationRunner: (record: ElementPropertyRecord<any, any>) => void\n) {\n  const currentValue = getCurrentValue(el);\n  const record: ElementPropertyRecord<any, any> = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el,\n    _positionTimeout: null,\n    observer: new MutationObserver(() => {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;\n      else if (attr === 'position')\n        record._positionTimeout = setTimeout(() => {\n          record._positionTimeout = null;\n        }, 1000);\n\n      const currentValue = getCurrentValue(el);\n      if (\n        attr === 'position' &&\n        currentValue.parentNode === record.virtualValue.parentNode &&\n        currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode\n      )\n        return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner,\n    setValue,\n    getCurrentValue,\n  };\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false,\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n  return record;\n}\n\nfunction queueIfNeeded(\n  val: string | null | ElementPositionWithDomNode,\n  record: ElementPropertyRecord<any, any>\n) {\n  const currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n  if (val && typeof val !== 'string') {\n    if (\n      !currentVal ||\n      val.parentNode !== currentVal.parentNode ||\n      val.insertBeforeNode !== currentVal.insertBeforeNode\n    ) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record: HTMLRecord) {\n  let val = record.originalValue;\n  record.mutations.forEach(m => (val = m.mutate(val)));\n  queueIfNeeded(getTransformedHTML(val), record);\n}\nfunction classMutationRunner(record: ClassnameRecord) {\n  const val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(m => m.mutate(val));\n  queueIfNeeded(\n    Array.from(val)\n      .filter(Boolean)\n      .join(' '),\n    record\n  );\n}\n\nfunction attrMutationRunner(record: AttributeRecord) {\n  let val: string | null = record.originalValue;\n  record.mutations.forEach(m => (val = m.mutate(val)));\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes({\n  parentSelector,\n  insertBeforeSelector,\n}: ElementPosition): ElementPositionWithDomNode | null {\n  const parentNode = document.querySelector<HTMLElement>(parentSelector);\n  if (!parentNode) return null;\n  const insertBeforeNode = insertBeforeSelector\n    ? document.querySelector<HTMLElement>(insertBeforeSelector)\n    : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode,\n    insertBeforeNode,\n  };\n}\n\nfunction positionMutationRunner(record: PositionRecord) {\n  let val = record.originalValue;\n  record.mutations.forEach(m => {\n    const selectors = m.mutate();\n    const newNodes = _loadDOMNodes(selectors);\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nconst getHTMLValue = (el: Element) => el.innerHTML;\nconst setHTMLValue = (el: Element, value: string) => (el.innerHTML = value);\nfunction getElementHTMLRecord(element: Element): HTMLRecord {\n  const elementRecord = getElementRecord(element);\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(\n      element,\n      'html',\n      getHTMLValue,\n      setHTMLValue,\n      htmlMutationRunner\n    );\n  }\n  return elementRecord.html;\n}\n\nconst getElementPosition = (el: Element): ElementPositionWithDomNode => {\n  return {\n    parentNode: el.parentElement as HTMLElement,\n    insertBeforeNode: el.nextElementSibling as HTMLElement | null,\n  };\n};\nconst setElementPosition = (el: Element, value: ElementPositionWithDomNode) => {\n  if (\n    value.insertBeforeNode &&\n    !value.parentNode.contains(value.insertBeforeNode)\n  ) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\nfunction getElementPositionRecord(element: Element): PositionRecord {\n  const elementRecord = getElementRecord(element);\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(\n      element,\n      'position',\n      getElementPosition,\n      setElementPosition,\n      positionMutationRunner\n    );\n  }\n  return elementRecord.position;\n}\n\nconst setClassValue = (el: Element, val: string) =>\n  val ? (el.className = val) : el.removeAttribute('class');\nconst getClassValue = (el: Element) => el.className;\nfunction getElementClassRecord(el: Element): ClassnameRecord {\n  const elementRecord = getElementRecord(el);\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(\n      el,\n      'class',\n      getClassValue,\n      setClassValue,\n      classMutationRunner\n    );\n  }\n  return elementRecord.classes;\n}\n\nconst getAttrValue = (attrName: string) => (el: Element) =>\n  el.getAttribute(attrName) ?? null;\nconst setAttrValue = (attrName: string) => (el: Element, val: string | null) =>\n  val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\nfunction getElementAttributeRecord(el: Element, attr: string): AttributeRecord {\n  const elementRecord = getElementRecord(el);\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(\n      el,\n      attr,\n      getAttrValue(attr),\n      setAttrValue(attr),\n      attrMutationRunner\n    );\n  }\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el: Element, attr: string) {\n  const element = elements.get(el);\n  if (!element) return;\n  if (attr === 'html') {\n    element.html?.observer?.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    element.classes?.observer?.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    element.position?.observer?.disconnect();\n    delete element.position;\n  } else {\n    element.attributes?.[attr]?.observer?.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nlet transformContainer: HTMLDivElement;\nfunction getTransformedHTML(html: string) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue<T extends ElementPropertyRecord<any, any>>(\n  el: Element,\n  attr: string,\n  m: T\n) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  const val = m.virtualValue;\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n  m.setValue(el, val);\n}\n\nfunction setValue(m: ElementRecord, el: Element) {\n  m.html && setPropertyValue<HTMLRecord>(el, 'html', m.html);\n  m.classes && setPropertyValue<ClassnameRecord>(el, 'class', m.classes);\n  m.position && setPropertyValue<PositionRecord>(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(attr => {\n    setPropertyValue<AttributeRecord>(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n}\n\n// find or create ElementPropertyRecord, add mutation to it, then run\nfunction startMutating(mutation: Mutation, element: Element) {\n  let record: ElementPropertyRecord<any, any> | null = null;\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n}\n\n// get (existing) ElementPropertyRecord, remove mutation from it, then run\nfunction stopMutating(mutation: Mutation, el: Element) {\n  let record: ElementPropertyRecord<any, any> | null = null;\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n  if (!record) return;\n  const index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n}\n\n// maintain list of elements associated with mutation\nfunction refreshElementsSet(mutation: Mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n\n  const existingElements = new Set(mutation.elements);\n  const matchingElements = document.querySelectorAll(mutation.selector);\n\n  matchingElements.forEach(el => {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation: Mutation) {\n  mutation.elements.forEach(el => stopMutating(mutation, el));\n  mutation.elements.clear();\n  mutations.delete(mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n}\n\n// Observer for elements that don't exist in the DOM yet\nlet observer: MutationObserver;\nexport function disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nexport function connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(() => {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false,\n  });\n}\n\n// run on init\nconnectGlobalObserver();\n\nfunction newMutation(m: Mutation): MutationController {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController;\n  // add to global index of mutations\n  mutations.add(m);\n  // run refresh on init to establish list of elements associated w/ mutation\n  refreshElementsSet(m);\n  return {\n    revert: () => {\n      revertMutation(m);\n    },\n  };\n}\n\nfunction html(\n  selector: HTMLMutation['selector'],\n  mutate: HTMLMutation['mutate']\n) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction position(\n  selector: PositionMutation['selector'],\n  mutate: PositionMutation['mutate']\n) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction classes(\n  selector: ClassnameMutation['selector'],\n  mutate: ClassnameMutation['mutate']\n) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction attribute(\n  selector: AttrMutation['selector'],\n  attribute: AttrMutation['attribute'],\n  mutate: AttrMutation['mutate']\n) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, classnames => {\n      const mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames\n        .split(/\\s+/g)\n        .filter(Boolean)\n        .forEach(c => classnames.add(c));\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute,\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction declarative({\n  selector,\n  action,\n  value,\n  attribute: attr,\n  parentSelector,\n  insertBeforeSelector,\n}: DeclarativeMutation): MutationController {\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, val => val + (value ?? ''));\n    } else if (action === 'set') {\n      return html(selector, () => value ?? '');\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, val => {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, val => {\n        if (value) val.delete(value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, val => {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, () => ({\n        insertBeforeSelector,\n        parentSelector,\n      }));\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, val =>\n        val !== null ? val + (value ?? '') : value ?? ''\n      );\n    } else if (action === 'set') {\n      return attribute(selector, attr, () => value ?? '');\n    } else if (action === 'remove') {\n      return attribute(selector, attr, () => null);\n    }\n  }\n  return nullController;\n}\n\nexport type MutationController = {\n  revert: () => void;\n};\n\nexport type DeclarativeMutation = {\n  selector: string;\n  attribute: string;\n  action: 'append' | 'set' | 'remove';\n  value?: string;\n  parentSelector?: string;\n  insertBeforeSelector?: string;\n};\n\nexport default {\n  html,\n  classes,\n  attribute,\n  position,\n  declarative,\n};\n"], "names": ["validAttributeName", "nullController", "revert", "elements", "Map", "mutations", "Set", "getElementRecord", "element", "record", "get", "set", "attributes", "createElementPropertyRecord", "el", "attr", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "setTimeout", "parentNode", "insertBeforeNode", "observe", "childList", "subtree", "characterData", "attributeFilter", "getObserverInit", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "for<PERSON>ach", "m", "mutate", "html", "transformContainer", "document", "createElement", "innerHTML", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "Array", "from", "join", "attrMutationRunner", "positionMutationRunner", "newNodes", "insertBeforeSelector", "querySelector", "parentSelector", "_loadDOMNodes", "getHTMLValue", "setHTMLValue", "value", "getElementHTMLRecord", "elementRecord", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getElementAttributeRecord", "attrName", "getAttribute", "setAttribute", "setAttrValue", "setPropertyV<PERSON>ue", "length", "disconnect", "deleteElementPropertyRecord", "Object", "keys", "refreshElementsSet", "mutation", "kind", "size", "existingElements", "querySelectorAll", "selector", "has", "add", "attribute", "push", "startMutating", "refreshAllElementSets", "connectGlobalObserver", "documentElement", "newMutation", "index", "indexOf", "splice", "stopMutating", "clear", "test", "classnames", "mutatedClassnames", "c", "declarative", "action"], "mappings": "wEAAaA,EAAqB,+BAC5BC,EAAqC,CACzCC,OAAQ,cAGJC,EAAwC,IAAIC,IAC5CC,EAA2B,IAAIC,IAkBrC,SAASC,EAAiBC,OACpBC,EAASN,EAASO,IAAIF,UAErBC,GAEHN,EAASQ,IAAIH,EADbC,EAAS,CAAED,QAAAA,EAASI,WAAY,KAI3BH,EAGT,SAASI,EACPC,EACAC,EACAC,EACAC,EACAC,OAEMC,EAAeH,EAAgBF,GAC/BL,EAA0C,CAC9CW,SAAS,EACTC,cAAeF,EACfG,aAAcH,EACdd,UAAW,GACXS,GAAAA,EACAS,iBAAkB,KAClBC,SAAU,IAAIC,kBAAiB,cAKhB,aAATV,IAAuBN,EAAOc,kBAChB,aAATR,IACPN,EAAOc,iBAAmBG,YAAW,WACnCjB,EAAOc,iBAAmB,OACzB,UAECJ,EAAeH,EAAgBF,GAE1B,aAATC,GACAI,EAAaQ,aAAelB,EAAOa,aAAaK,YAChDR,EAAaS,mBAAqBnB,EAAOa,aAAaM,kBAGpDT,IAAiBV,EAAOa,eAC5Bb,EAAOY,cAAgBF,EACvBD,EAAeT,QAEjBS,eAAAA,EACAD,SAAAA,EACAD,gBAAAA,SAEW,aAATD,GAAuBD,EAAGa,WAC5BlB,EAAOe,SAASK,QAAQf,EAAGa,WAAY,CACrCG,WAAW,EACXC,SAAS,EACTnB,YAAY,EACZoB,eAAe,IAGjBvB,EAAOe,SAASK,QAAQf,EA5E5B,SAAyBC,SACP,SAATA,EACH,CACEe,WAAW,EACXC,SAAS,EACTnB,YAAY,EACZoB,eAAe,GAEjB,CACEF,WAAW,EACXC,SAAS,EACTnB,YAAY,EACZqB,gBAAiB,CAAClB,IAgEMmB,CAAgBnB,IAEvCN,EAGT,SAAS0B,EACPC,EACA3B,OAEM4B,EAAa5B,EAAOO,gBAAgBP,EAAOK,IACjDL,EAAOa,aAAec,EAClBA,GAAsB,iBAARA,EAEbC,GACDD,EAAIT,aAAeU,EAAWV,YAC9BS,EAAIR,mBAAqBS,EAAWT,mBAEpCnB,EAAOW,SAAU,EACjBkB,KAEOF,IAAQC,IACjB5B,EAAOW,SAAU,EACjBkB,KAIJ,SAASC,EAAmB9B,OACtB2B,EAAM3B,EAAOY,cACjBZ,EAAOJ,UAAUmC,SAAQ,SAAAC,UAAML,EAAMK,EAAEC,OAAON,MAC9CD,EAkJF,SAA4BQ,UACrBC,IACHA,EAAqBC,SAASC,cAAc,QAE9CF,EAAmBG,UAAYJ,EACxBC,EAAmBG,UAvJZC,CAAmBZ,GAAM3B,GAEzC,SAASwC,EAAoBxC,OACrB2B,EAAM,IAAI9B,IAAIG,EAAOY,cAAc6B,MAAM,OAAOC,OAAOC,UAC7D3C,EAAOJ,UAAUmC,SAAQ,SAAAC,UAAKA,EAAEC,OAAON,MACvCD,EACEkB,MAAMC,KAAKlB,GACRe,OAAOC,SACPG,KAAK,KACR9C,GAIJ,SAAS+C,EAAmB/C,OACtB2B,EAAqB3B,EAAOY,cAChCZ,EAAOJ,UAAUmC,SAAQ,SAAAC,UAAML,EAAMK,EAAEC,OAAON,MAC9CD,EAAcC,EAAK3B,GAmBrB,SAASgD,EAAuBhD,OAC1B2B,EAAM3B,EAAOY,cACjBZ,EAAOJ,UAAUmC,SAAQ,SAAAC,OAEjBiB,EApBV,gBAEEC,IAAAA,qBAEMhC,EAAakB,SAASe,gBAH5BC,oBAIKlC,EAAY,OAAO,SAClBC,EAAmB+B,EACrBd,SAASe,cAA2BD,GACpC,YACAA,IAAyB/B,EAAyB,KAC/C,CACLD,WAAAA,EACAC,iBAAAA,GAQiBkC,CADCrB,EAAEC,UAEpBN,EAAMsB,GAAYtB,KAEpBD,EAAcC,EAAK3B,GAGrB,IAAMsD,EAAe,SAACjD,UAAgBA,EAAGiC,WACnCiB,EAAe,SAAClD,EAAamD,UAAmBnD,EAAGiC,UAAYkB,GACrE,SAASC,EAAqB1D,OACtB2D,EAAgB5D,EAAiBC,UAClC2D,EAAcxB,OACjBwB,EAAcxB,KAAO9B,EACnBL,EACA,OACAuD,EACAC,EACAzB,IAGG4B,EAAcxB,KAGvB,IAAMyB,EAAqB,SAACtD,SACnB,CACLa,WAAYb,EAAGuD,cACfzC,iBAAkBd,EAAGwD,qBAGnBC,EAAqB,SAACzD,EAAamD,GAErCA,EAAMrC,mBACLqC,EAAMtC,WAAW6C,SAASP,EAAMrC,mBAMnCqC,EAAMtC,WAAW8C,aAAa3D,EAAImD,EAAMrC,mBAE1C,SAAS8C,EAAyBlE,OAC1B2D,EAAgB5D,EAAiBC,UAClC2D,EAAcQ,WACjBR,EAAcQ,SAAW9D,EACvBL,EACA,WACA4D,EACAG,EACAd,IAGGU,EAAcQ,SAGvB,IAqDI/B,EAmGApB,EAxJEoD,EAAgB,SAAC9D,EAAasB,UAClCA,EAAOtB,EAAG+D,UAAYzC,EAAOtB,EAAGgE,gBAAgB,UAC5CC,EAAgB,SAACjE,UAAgBA,EAAG+D,WAC1C,SAASG,EAAsBlE,OACvBqD,EAAgB5D,EAAiBO,UAClCqD,EAAcc,UACjBd,EAAcc,QAAUpE,EACtBC,EACA,QACAiE,EACAH,EACA3B,IAGGkB,EAAcc,QAOvB,SAASC,EAA0BpE,EAAaC,OAJ1BoE,EAKdhB,EAAgB5D,EAAiBO,UAClCqD,EAAcvD,WAAWG,KAC5BoD,EAAcvD,WAAWG,GAAQF,EAC/BC,EACAC,GATgBoE,EAUHpE,EAVwB,SAACD,yBAC1CA,EAAGsE,aAAaD,MAAa,OACV,SAACA,UAAqB,SAACrE,EAAasB,UAC/C,OAARA,EAAetB,EAAGuE,aAAaF,EAAU/C,GAAOtB,EAAGgE,gBAAgBK,IAQ/DG,CAAavE,GACbyC,IAGGW,EAAcvD,WAAWG,GA8BlC,SAASwE,EACPzE,EACAC,EACA0B,MAEKA,EAAErB,SACPqB,EAAErB,SAAU,MACNgB,EAAMK,EAAEnB,aACTmB,EAAEpC,UAAUmF,QAnCnB,SAAqC1E,EAAaC,WAC1CP,EAAUL,EAASO,IAAII,MACxBN,KACQ,SAATO,WACFP,EAAQmC,kBAAMnB,aAAUiE,oBACjBjF,EAAQmC,UACV,GAAa,UAAT5B,EAAkB,kBAC3BP,EAAQyE,qBAASzD,aAAUiE,oBACpBjF,EAAQyE,aACV,GAAa,aAATlE,EAAqB,kBAC9BP,EAAQmE,sBAAUnD,aAAUiE,oBACrBjF,EAAQmE,aACV,oBACLnE,EAAQI,wBAAaG,gBAAOS,aAAUiE,oBAC/BjF,EAAQI,WAAWG,IAsB1B2E,CAA4B5E,EAAIC,GAElC0B,EAAExB,SAASH,EAAIsB,IAGjB,SAASnB,EAASwB,EAAkB3B,GAClC2B,EAAEE,MAAQ4C,EAA6BzE,EAAI,OAAQ2B,EAAEE,MACrDF,EAAEwC,SAAWM,EAAkCzE,EAAI,QAAS2B,EAAEwC,SAC9DxC,EAAEkC,UAAYY,EAAiCzE,EAAI,WAAY2B,EAAEkC,UACjEgB,OAAOC,KAAKnD,EAAE7B,YAAY4B,SAAQ,SAAAzB,GAChCwE,EAAkCzE,EAAIC,EAAM0B,EAAE7B,WAAWG,OAI7D,SAASuB,IACPnC,EAASqC,QAAQvB,GAuCnB,SAAS4E,EAAmBC,MAGJ,aAAlBA,EAASC,MAAkD,IAA3BD,EAAS3F,SAAS6F,UAEhDC,EAAmB,IAAI3F,IAAIwF,EAAS3F,UACjB0C,SAASqD,iBAAiBJ,EAASK,UAE3C3D,SAAQ,SAAA1B,GAClBmF,EAAiBG,IAAItF,KACxBgF,EAAS3F,SAASkG,IAAIvF,GA7C5B,SAAuBgF,EAAoBtF,OACrCC,EAAiD,KAC/B,SAAlBqF,EAASC,KACXtF,EAASyD,EAAqB1D,GACH,UAAlBsF,EAASC,KAClBtF,EAASuE,EAAsBxE,GACJ,cAAlBsF,EAASC,KAClBtF,EAASyE,EAA0B1E,EAASsF,EAASQ,WAC1B,aAAlBR,EAASC,OAClBtF,EAASiE,EAAyBlE,IAE/BC,IACLA,EAAOJ,UAAUkG,KAAKT,GACtBrF,EAAOS,eAAeT,IAiClB+F,CAAcV,EAAUhF,QAW9B,SAAS2F,IACPpG,EAAUmC,QAAQqD,YAQJa,IACU,oBAAb7D,WAENrB,IACHA,EAAW,IAAIC,kBAAiB,WAC9BgF,QAIJA,IACAjF,EAASK,QAAQgB,SAAS8D,gBAAiB,CACzC7E,WAAW,EACXC,SAAS,EACTnB,YAAY,EACZoB,eAAe,KAOnB,SAAS4E,EAAYnE,SAEK,oBAAbI,SAAiC5C,GAE5CI,EAAUgG,IAAI5D,GAEdoD,EAAmBpD,GACZ,CACLvC,OAAQ,WA5CZ,IAAwB4F,GAAAA,EA6CHrD,GA5CVtC,SAASqC,SAAQ,SAAA1B,UAnC5B,SAAsBgF,EAAoBhF,OACpCL,EAAiD,QAC/B,SAAlBqF,EAASC,KACXtF,EAASyD,EAAqBpD,GACH,UAAlBgF,EAASC,KAClBtF,EAASuE,EAAsBlE,GACJ,cAAlBgF,EAASC,KAClBtF,EAASyE,EAA0BpE,EAAIgF,EAASQ,WACrB,aAAlBR,EAASC,OAClBtF,EAASiE,EAAyB5D,IAE/BL,OACCoG,EAAQpG,EAAOJ,UAAUyG,QAAQhB,IACxB,IAAXe,GAAcpG,EAAOJ,UAAU0G,OAAOF,EAAO,GACjDpG,EAAOS,eAAeT,IAqBUuG,CAAalB,EAAUhF,MACvDgF,EAAS3F,SAAS8G,QAClB5G,SAAiByF,MA+CnB,SAASnD,EACPwD,EACAzD,UAEOkE,EAAY,CACjBb,KAAM,OACN5F,SAAU,IAAIG,IACdoC,OAAAA,EACAyD,SAAAA,IAIJ,SAASxB,EACPwB,EACAzD,UAEOkE,EAAY,CACjBb,KAAM,WACN5F,SAAU,IAAIG,IACdoC,OAAAA,EACAyD,SAAAA,IAIJ,SAASlB,EACPkB,EACAzD,UAEOkE,EAAY,CACjBb,KAAM,QACN5F,SAAU,IAAIG,IACdoC,OAAAA,EACAyD,SAAAA,IAIJ,SAASG,EACPH,EACAG,EACA5D,UAEK1C,EAAmBkH,KAAKZ,GAEX,UAAdA,GAAuC,cAAdA,EACpBrB,EAAQkB,GAAU,SAAAgB,OACjBC,EAAoB1E,EAAOW,MAAMC,KAAK6D,GAAY5D,KAAK,MAC7D4D,EAAWF,QACNG,GACLA,EACGlE,MAAM,QACNC,OAAOC,SACPZ,SAAQ,SAAA6E,UAAKF,EAAWd,IAAIgB,SAI5BT,EAAY,CACjBb,KAAM,YACNO,UAAAA,EACAnG,SAAU,IAAIG,IACdoC,OAAAA,EACAyD,SAAAA,IAnB8ClG,EAzDlDyG,IA+IA,MAAe,CACb/D,KAAAA,EACAsC,QAAAA,EACAqB,UAAAA,EACA3B,SAAAA,EACA2C,YApEF,gBACEnB,IAAAA,SACAoB,IAAAA,OACAtD,IAAAA,MACWlD,IAAXuF,UACAzC,IAAAA,eACAF,IAAAA,wBAEa,SAAT5C,EAAiB,IACJ,WAAXwG,SACK5E,EAAKwD,GAAU,SAAA/D,UAAOA,SAAO6B,EAAAA,EAAS,OACxC,GAAe,QAAXsD,SACF5E,EAAKwD,GAAU,wBAAMlC,EAAAA,EAAS,WAElC,GAAa,UAATlD,EAAkB,IACZ,WAAXwG,SACKtC,EAAQkB,GAAU,SAAA/D,GACnB6B,GAAO7B,EAAIiE,IAAIpC,MAEhB,GAAe,WAAXsD,SACFtC,EAAQkB,GAAU,SAAA/D,GACnB6B,GAAO7B,SAAW6B,MAEnB,GAAe,QAAXsD,SACFtC,EAAQkB,GAAU,SAAA/D,GACvBA,EAAI6E,QACAhD,GAAO7B,EAAIiE,IAAIpC,WAGlB,GAAa,aAATlD,MACM,QAAXwG,GAAoB1D,SACfc,EAASwB,GAAU,iBAAO,CAC/BxC,qBAAAA,EACAE,eAAAA,UAGC,IACU,WAAX0D,SACKjB,EAAUH,EAAUpF,GAAM,SAAAqB,UACvB,OAARA,EAAeA,SAAO6B,EAAAA,EAAS,UAAMA,EAAAA,EAAS,MAE3C,GAAe,QAAXsD,SACFjB,EAAUH,EAAUpF,GAAM,wBAAMkD,EAAAA,EAAS,MAC3C,GAAe,WAAXsD,SACFjB,EAAUH,EAAUpF,GAAM,kBAAM,eAGpCd,kGApJPuB,GAAYA,EAASiE"}