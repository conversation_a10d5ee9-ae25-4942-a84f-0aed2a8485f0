"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e=/^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/,t={revert:function(){}},n=new Map,r=new Set;function i(e){var t=n.get(e);return t||n.set(e,t={element:e,attributes:{}}),t}function o(e,t,n,r,i){var o=n(e),u={isDirty:!1,originalValue:o,virtualValue:o,mutations:[],el:e,_positionTimeout:null,observer:new MutationObserver((function(){if("position"!==t||!u._positionTimeout){"position"===t&&(u._positionTimeout=setTimeout((function(){u._positionTimeout=null}),1e3));var r=n(e);"position"===t&&r.parentNode===u.virtualValue.parentNode&&r.insertBeforeNode===u.virtualValue.insertBeforeNode||r!==u.virtualValue&&(u.originalValue=r,i(u))}})),mutationRunner:i,setValue:r,getCurrentValue:n};return"position"===t&&e.parentNode?u.observer.observe(e.parentNode,{childList:!0,subtree:!0,attributes:!1,characterData:!1}):u.observer.observe(e,function(e){return"html"===e?{childList:!0,subtree:!0,attributes:!0,characterData:!0}:{childList:!1,subtree:!1,attributes:!0,attributeFilter:[e]}}(t)),u}function u(e,t){var n=t.getCurrentValue(t.el);t.virtualValue=e,e&&"string"!=typeof e?n&&e.parentNode===n.parentNode&&e.insertBeforeNode===n.insertBeforeNode||(t.isDirty=!0,E()):e!==n&&(t.isDirty=!0,E())}function a(e){var t=e.originalValue;e.mutations.forEach((function(e){return t=e.mutate(t)})),u(function(e){return h||(h=document.createElement("div")),h.innerHTML=e,h.innerHTML}(t),e)}function l(e){var t=new Set(e.originalValue.split(/\s+/).filter(Boolean));e.mutations.forEach((function(e){return e.mutate(t)})),u(Array.from(t).filter(Boolean).join(" "),e)}function s(e){var t=e.originalValue;e.mutations.forEach((function(e){return t=e.mutate(t)})),u(t,e)}function c(e){var t=e.originalValue;e.mutations.forEach((function(e){var n=function(e){var t=e.insertBeforeSelector,n=document.querySelector(e.parentSelector);if(!n)return null;var r=t?document.querySelector(t):null;return t&&!r?null:{parentNode:n,insertBeforeNode:r}}(e.mutate());t=n||t})),u(t,e)}var f=function(e){return e.innerHTML},d=function(e,t){return e.innerHTML=t};function m(e){var t=i(e);return t.html||(t.html=o(e,"html",f,d,a)),t.html}var v=function(e){return{parentNode:e.parentElement,insertBeforeNode:e.nextElementSibling}},p=function(e,t){t.insertBeforeNode&&!t.parentNode.contains(t.insertBeforeNode)||t.parentNode.insertBefore(e,t.insertBeforeNode)};function b(e){var t=i(e);return t.position||(t.position=o(e,"position",v,p,c)),t.position}var h,N,S=function(e,t){return t?e.className=t:e.removeAttribute("class")},B=function(e){return e.className};function V(e){var t=i(e);return t.classes||(t.classes=o(e,"class",B,S,l)),t.classes}function g(e,t){var n,r=i(e);return r.attributes[t]||(r.attributes[t]=o(e,t,(n=t,function(e){var t;return null!=(t=e.getAttribute(n))?t:null}),function(e){return function(t,n){return null!==n?t.setAttribute(e,n):t.removeAttribute(e)}}(t),s)),r.attributes[t]}function y(e,t,r){if(r.isDirty){r.isDirty=!1;var i=r.virtualValue;r.mutations.length||function(e,t){var r,i,o=n.get(e);if(o)if("html"===t)null==(r=o.html)||null==(i=r.observer)||i.disconnect(),delete o.html;else if("class"===t){var u,a;null==(u=o.classes)||null==(a=u.observer)||a.disconnect(),delete o.classes}else if("position"===t){var l,s;null==(l=o.position)||null==(s=l.observer)||s.disconnect(),delete o.position}else{var c,f,d;null==(c=o.attributes)||null==(f=c[t])||null==(d=f.observer)||d.disconnect(),delete o.attributes[t]}}(e,t),r.setValue(e,i)}}function k(e,t){e.html&&y(t,"html",e.html),e.classes&&y(t,"class",e.classes),e.position&&y(t,"position",e.position),Object.keys(e.attributes).forEach((function(n){y(t,n,e.attributes[n])}))}function E(){n.forEach(k)}function w(e){if("position"!==e.kind||1!==e.elements.size){var t=new Set(e.elements);document.querySelectorAll(e.selector).forEach((function(n){t.has(n)||(e.elements.add(n),function(e,t){var n=null;"html"===e.kind?n=m(t):"class"===e.kind?n=V(t):"attribute"===e.kind?n=g(t,e.attribute):"position"===e.kind&&(n=b(t)),n&&(n.mutations.push(e),n.mutationRunner(n))}(e,n))}))}}function A(){r.forEach(w)}function T(){"undefined"!=typeof document&&(N||(N=new MutationObserver((function(){A()}))),A(),N.observe(document.documentElement,{childList:!0,subtree:!0,attributes:!1,characterData:!1}))}function D(e){return"undefined"==typeof document?t:(r.add(e),w(e),{revert:function(){var t;(t=e).elements.forEach((function(e){return function(e,t){var n=null;if("html"===e.kind?n=m(t):"class"===e.kind?n=V(t):"attribute"===e.kind?n=g(t,e.attribute):"position"===e.kind&&(n=b(t)),n){var r=n.mutations.indexOf(e);-1!==r&&n.mutations.splice(r,1),n.mutationRunner(n)}}(t,e)})),t.elements.clear(),r.delete(t)}})}function L(e,t){return D({kind:"html",elements:new Set,mutate:t,selector:e})}function M(e,t){return D({kind:"position",elements:new Set,mutate:t,selector:e})}function _(e,t){return D({kind:"class",elements:new Set,mutate:t,selector:e})}function x(n,r,i){return e.test(r)?"class"===r||"className"===r?_(n,(function(e){var t=i(Array.from(e).join(" "));e.clear(),t&&t.split(/\s+/g).filter(Boolean).forEach((function(t){return e.add(t)}))})):D({kind:"attribute",attribute:r,elements:new Set,mutate:i,selector:n}):t}T();var O={html:L,classes:_,attribute:x,position:M,declarative:function(e){var n=e.selector,r=e.action,i=e.value,o=e.attribute,u=e.parentSelector,a=e.insertBeforeSelector;if("html"===o){if("append"===r)return L(n,(function(e){return e+(null!=i?i:"")}));if("set"===r)return L(n,(function(){return null!=i?i:""}))}else if("class"===o){if("append"===r)return _(n,(function(e){i&&e.add(i)}));if("remove"===r)return _(n,(function(e){i&&e.delete(i)}));if("set"===r)return _(n,(function(e){e.clear(),i&&e.add(i)}))}else if("position"===o){if("set"===r&&u)return M(n,(function(){return{insertBeforeSelector:a,parentSelector:u}}))}else{if("append"===r)return x(n,o,(function(e){return null!==e?e+(null!=i?i:""):null!=i?i:""}));if("set"===r)return x(n,o,(function(){return null!=i?i:""}));if("remove"===r)return x(n,o,(function(){return null}))}return t}};exports.connectGlobalObserver=T,exports.default=O,exports.disconnectGlobalObserver=function(){N&&N.disconnect()},exports.validAttributeName=e;
//# sourceMappingURL=dom-mutator.cjs.production.min.js.map
