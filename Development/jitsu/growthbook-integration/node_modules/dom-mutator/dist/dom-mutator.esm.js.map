{"version": 3, "file": "dom-mutator.esm.js", "sources": ["../src/index.ts"], "sourcesContent": ["export const validAttributeName = /^[a-zA-Z:_][a-zA-Z0-9:_.-]*$/;\nconst nullController: MutationController = {\n  revert: () => {},\n};\n\nconst elements: Map<Element, ElementRecord> = new Map();\nconst mutations: Set<Mutation> = new Set();\n\nfunction getObserverInit(attr: string): MutationObserverInit {\n  return attr === 'html'\n    ? {\n        childList: true,\n        subtree: true,\n        attributes: true,\n        characterData: true,\n      }\n    : {\n        childList: false,\n        subtree: false,\n        attributes: true,\n        attributeFilter: [attr],\n      };\n}\n\nfunction getElementRecord(element: Element): ElementRecord {\n  let record = elements.get(element);\n\n  if (!record) {\n    record = { element, attributes: {} };\n    elements.set(element, record);\n  }\n\n  return record;\n}\n\nfunction createElementPropertyRecord(\n  el: Element,\n  attr: string,\n  getCurrentValue: (el: Element) => any,\n  setValue: (el: Element, val: any) => void,\n  mutationRunner: (record: ElementPropertyRecord<any, any>) => void\n) {\n  const currentValue = getCurrentValue(el);\n  const record: ElementPropertyRecord<any, any> = {\n    isDirty: false,\n    originalValue: currentValue,\n    virtualValue: currentValue,\n    mutations: [],\n    el,\n    _positionTimeout: null,\n    observer: new MutationObserver(() => {\n      // enact a 1 second timeout that blocks subsequent firing of the\n      // observer until the timeout is complete. This will prevent multiple\n      // mutations from firing in quick succession, which can cause the\n      // mutation to be reverted before the DOM has a chance to update.\n      if (attr === 'position' && record._positionTimeout) return;\n      else if (attr === 'position')\n        record._positionTimeout = setTimeout(() => {\n          record._positionTimeout = null;\n        }, 1000);\n\n      const currentValue = getCurrentValue(el);\n      if (\n        attr === 'position' &&\n        currentValue.parentNode === record.virtualValue.parentNode &&\n        currentValue.insertBeforeNode === record.virtualValue.insertBeforeNode\n      )\n        return;\n      if (currentValue === record.virtualValue) return;\n      record.originalValue = currentValue;\n      mutationRunner(record);\n    }),\n    mutationRunner,\n    setValue,\n    getCurrentValue,\n  };\n  if (attr === 'position' && el.parentNode) {\n    record.observer.observe(el.parentNode, {\n      childList: true,\n      subtree: true,\n      attributes: false,\n      characterData: false,\n    });\n  } else {\n    record.observer.observe(el, getObserverInit(attr));\n  }\n  return record;\n}\n\nfunction queueIfNeeded(\n  val: string | null | ElementPositionWithDomNode,\n  record: ElementPropertyRecord<any, any>\n) {\n  const currentVal = record.getCurrentValue(record.el);\n  record.virtualValue = val;\n  if (val && typeof val !== 'string') {\n    if (\n      !currentVal ||\n      val.parentNode !== currentVal.parentNode ||\n      val.insertBeforeNode !== currentVal.insertBeforeNode\n    ) {\n      record.isDirty = true;\n      runDOMUpdates();\n    }\n  } else if (val !== currentVal) {\n    record.isDirty = true;\n    runDOMUpdates();\n  }\n}\n\nfunction htmlMutationRunner(record: HTMLRecord) {\n  let val = record.originalValue;\n  record.mutations.forEach(m => (val = m.mutate(val)));\n  queueIfNeeded(getTransformedHTML(val), record);\n}\nfunction classMutationRunner(record: ClassnameRecord) {\n  const val = new Set(record.originalValue.split(/\\s+/).filter(Boolean));\n  record.mutations.forEach(m => m.mutate(val));\n  queueIfNeeded(\n    Array.from(val)\n      .filter(Boolean)\n      .join(' '),\n    record\n  );\n}\n\nfunction attrMutationRunner(record: AttributeRecord) {\n  let val: string | null = record.originalValue;\n  record.mutations.forEach(m => (val = m.mutate(val)));\n  queueIfNeeded(val, record);\n}\n\nfunction _loadDOMNodes({\n  parentSelector,\n  insertBeforeSelector,\n}: ElementPosition): ElementPositionWithDomNode | null {\n  const parentNode = document.querySelector<HTMLElement>(parentSelector);\n  if (!parentNode) return null;\n  const insertBeforeNode = insertBeforeSelector\n    ? document.querySelector<HTMLElement>(insertBeforeSelector)\n    : null;\n  if (insertBeforeSelector && !insertBeforeNode) return null;\n  return {\n    parentNode,\n    insertBeforeNode,\n  };\n}\n\nfunction positionMutationRunner(record: PositionRecord) {\n  let val = record.originalValue;\n  record.mutations.forEach(m => {\n    const selectors = m.mutate();\n    const newNodes = _loadDOMNodes(selectors);\n    val = newNodes || val;\n  });\n  queueIfNeeded(val, record);\n}\n\nconst getHTMLValue = (el: Element) => el.innerHTML;\nconst setHTMLValue = (el: Element, value: string) => (el.innerHTML = value);\nfunction getElementHTMLRecord(element: Element): HTMLRecord {\n  const elementRecord = getElementRecord(element);\n  if (!elementRecord.html) {\n    elementRecord.html = createElementPropertyRecord(\n      element,\n      'html',\n      getHTMLValue,\n      setHTMLValue,\n      htmlMutationRunner\n    );\n  }\n  return elementRecord.html;\n}\n\nconst getElementPosition = (el: Element): ElementPositionWithDomNode => {\n  return {\n    parentNode: el.parentElement as HTMLElement,\n    insertBeforeNode: el.nextElementSibling as HTMLElement | null,\n  };\n};\nconst setElementPosition = (el: Element, value: ElementPositionWithDomNode) => {\n  if (\n    value.insertBeforeNode &&\n    !value.parentNode.contains(value.insertBeforeNode)\n  ) {\n    // skip position mutation - insertBeforeNode not a child of parent. happens\n    // when mutation observer for indvidual element fires out of order\n    return;\n  }\n  value.parentNode.insertBefore(el, value.insertBeforeNode);\n};\nfunction getElementPositionRecord(element: Element): PositionRecord {\n  const elementRecord = getElementRecord(element);\n  if (!elementRecord.position) {\n    elementRecord.position = createElementPropertyRecord(\n      element,\n      'position',\n      getElementPosition,\n      setElementPosition,\n      positionMutationRunner\n    );\n  }\n  return elementRecord.position;\n}\n\nconst setClassValue = (el: Element, val: string) =>\n  val ? (el.className = val) : el.removeAttribute('class');\nconst getClassValue = (el: Element) => el.className;\nfunction getElementClassRecord(el: Element): ClassnameRecord {\n  const elementRecord = getElementRecord(el);\n  if (!elementRecord.classes) {\n    elementRecord.classes = createElementPropertyRecord(\n      el,\n      'class',\n      getClassValue,\n      setClassValue,\n      classMutationRunner\n    );\n  }\n  return elementRecord.classes;\n}\n\nconst getAttrValue = (attrName: string) => (el: Element) =>\n  el.getAttribute(attrName) ?? null;\nconst setAttrValue = (attrName: string) => (el: Element, val: string | null) =>\n  val !== null ? el.setAttribute(attrName, val) : el.removeAttribute(attrName);\nfunction getElementAttributeRecord(el: Element, attr: string): AttributeRecord {\n  const elementRecord = getElementRecord(el);\n  if (!elementRecord.attributes[attr]) {\n    elementRecord.attributes[attr] = createElementPropertyRecord(\n      el,\n      attr,\n      getAttrValue(attr),\n      setAttrValue(attr),\n      attrMutationRunner\n    );\n  }\n  return elementRecord.attributes[attr];\n}\n\nfunction deleteElementPropertyRecord(el: Element, attr: string) {\n  const element = elements.get(el);\n  if (!element) return;\n  if (attr === 'html') {\n    element.html?.observer?.disconnect();\n    delete element.html;\n  } else if (attr === 'class') {\n    element.classes?.observer?.disconnect();\n    delete element.classes;\n  } else if (attr === 'position') {\n    element.position?.observer?.disconnect();\n    delete element.position;\n  } else {\n    element.attributes?.[attr]?.observer?.disconnect();\n    delete element.attributes[attr];\n  }\n}\n\nlet transformContainer: HTMLDivElement;\nfunction getTransformedHTML(html: string) {\n  if (!transformContainer) {\n    transformContainer = document.createElement('div');\n  }\n  transformContainer.innerHTML = html;\n  return transformContainer.innerHTML;\n}\n\nfunction setPropertyValue<T extends ElementPropertyRecord<any, any>>(\n  el: Element,\n  attr: string,\n  m: T\n) {\n  if (!m.isDirty) return;\n  m.isDirty = false;\n  const val = m.virtualValue;\n  if (!m.mutations.length) {\n    deleteElementPropertyRecord(el, attr);\n  }\n  m.setValue(el, val);\n}\n\nfunction setValue(m: ElementRecord, el: Element) {\n  m.html && setPropertyValue<HTMLRecord>(el, 'html', m.html);\n  m.classes && setPropertyValue<ClassnameRecord>(el, 'class', m.classes);\n  m.position && setPropertyValue<PositionRecord>(el, 'position', m.position);\n  Object.keys(m.attributes).forEach(attr => {\n    setPropertyValue<AttributeRecord>(el, attr, m.attributes[attr]);\n  });\n}\n\nfunction runDOMUpdates() {\n  elements.forEach(setValue);\n}\n\n// find or create ElementPropertyRecord, add mutation to it, then run\nfunction startMutating(mutation: Mutation, element: Element) {\n  let record: ElementPropertyRecord<any, any> | null = null;\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(element);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(element);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(element, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(element);\n  }\n  if (!record) return;\n  record.mutations.push(mutation);\n  record.mutationRunner(record);\n}\n\n// get (existing) ElementPropertyRecord, remove mutation from it, then run\nfunction stopMutating(mutation: Mutation, el: Element) {\n  let record: ElementPropertyRecord<any, any> | null = null;\n  if (mutation.kind === 'html') {\n    record = getElementHTMLRecord(el);\n  } else if (mutation.kind === 'class') {\n    record = getElementClassRecord(el);\n  } else if (mutation.kind === 'attribute') {\n    record = getElementAttributeRecord(el, mutation.attribute);\n  } else if (mutation.kind === 'position') {\n    record = getElementPositionRecord(el);\n  }\n  if (!record) return;\n  const index = record.mutations.indexOf(mutation);\n  if (index !== -1) record.mutations.splice(index, 1);\n  record.mutationRunner(record);\n}\n\n// maintain list of elements associated with mutation\nfunction refreshElementsSet(mutation: Mutation) {\n  // if a position mutation has already found an element to move, don't move\n  // any more elements\n  if (mutation.kind === 'position' && mutation.elements.size === 1) return;\n\n  const existingElements = new Set(mutation.elements);\n  const matchingElements = document.querySelectorAll(mutation.selector);\n\n  matchingElements.forEach(el => {\n    if (!existingElements.has(el)) {\n      mutation.elements.add(el);\n      startMutating(mutation, el);\n    }\n  });\n}\n\nfunction revertMutation(mutation: Mutation) {\n  mutation.elements.forEach(el => stopMutating(mutation, el));\n  mutation.elements.clear();\n  mutations.delete(mutation);\n}\n\nfunction refreshAllElementSets() {\n  mutations.forEach(refreshElementsSet);\n}\n\n// Observer for elements that don't exist in the DOM yet\nlet observer: MutationObserver;\nexport function disconnectGlobalObserver() {\n  observer && observer.disconnect();\n}\nexport function connectGlobalObserver() {\n  if (typeof document === 'undefined') return;\n\n  if (!observer) {\n    observer = new MutationObserver(() => {\n      refreshAllElementSets();\n    });\n  }\n\n  refreshAllElementSets();\n  observer.observe(document.documentElement, {\n    childList: true,\n    subtree: true,\n    attributes: false,\n    characterData: false,\n  });\n}\n\n// run on init\nconnectGlobalObserver();\n\nfunction newMutation(m: Mutation): MutationController {\n  // Not in a browser\n  if (typeof document === 'undefined') return nullController;\n  // add to global index of mutations\n  mutations.add(m);\n  // run refresh on init to establish list of elements associated w/ mutation\n  refreshElementsSet(m);\n  return {\n    revert: () => {\n      revertMutation(m);\n    },\n  };\n}\n\nfunction html(\n  selector: HTMLMutation['selector'],\n  mutate: HTMLMutation['mutate']\n) {\n  return newMutation({\n    kind: 'html',\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction position(\n  selector: PositionMutation['selector'],\n  mutate: PositionMutation['mutate']\n) {\n  return newMutation({\n    kind: 'position',\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction classes(\n  selector: ClassnameMutation['selector'],\n  mutate: ClassnameMutation['mutate']\n) {\n  return newMutation({\n    kind: 'class',\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction attribute(\n  selector: AttrMutation['selector'],\n  attribute: AttrMutation['attribute'],\n  mutate: AttrMutation['mutate']\n) {\n  if (!validAttributeName.test(attribute)) return nullController;\n\n  if (attribute === 'class' || attribute === 'className') {\n    return classes(selector, classnames => {\n      const mutatedClassnames = mutate(Array.from(classnames).join(' '));\n      classnames.clear();\n      if (!mutatedClassnames) return;\n      mutatedClassnames\n        .split(/\\s+/g)\n        .filter(Boolean)\n        .forEach(c => classnames.add(c));\n    });\n  }\n\n  return newMutation({\n    kind: 'attribute',\n    attribute,\n    elements: new Set(),\n    mutate,\n    selector,\n  });\n}\n\nfunction declarative({\n  selector,\n  action,\n  value,\n  attribute: attr,\n  parentSelector,\n  insertBeforeSelector,\n}: DeclarativeMutation): MutationController {\n  if (attr === 'html') {\n    if (action === 'append') {\n      return html(selector, val => val + (value ?? ''));\n    } else if (action === 'set') {\n      return html(selector, () => value ?? '');\n    }\n  } else if (attr === 'class') {\n    if (action === 'append') {\n      return classes(selector, val => {\n        if (value) val.add(value);\n      });\n    } else if (action === 'remove') {\n      return classes(selector, val => {\n        if (value) val.delete(value);\n      });\n    } else if (action === 'set') {\n      return classes(selector, val => {\n        val.clear();\n        if (value) val.add(value);\n      });\n    }\n  } else if (attr === 'position') {\n    if (action === 'set' && parentSelector) {\n      return position(selector, () => ({\n        insertBeforeSelector,\n        parentSelector,\n      }));\n    }\n  } else {\n    if (action === 'append') {\n      return attribute(selector, attr, val =>\n        val !== null ? val + (value ?? '') : value ?? ''\n      );\n    } else if (action === 'set') {\n      return attribute(selector, attr, () => value ?? '');\n    } else if (action === 'remove') {\n      return attribute(selector, attr, () => null);\n    }\n  }\n  return nullController;\n}\n\nexport type MutationController = {\n  revert: () => void;\n};\n\nexport type DeclarativeMutation = {\n  selector: string;\n  attribute: string;\n  action: 'append' | 'set' | 'remove';\n  value?: string;\n  parentSelector?: string;\n  insertBeforeSelector?: string;\n};\n\nexport default {\n  html,\n  classes,\n  attribute,\n  position,\n  declarative,\n};\n"], "names": ["validAttributeName", "nullController", "revert", "elements", "Map", "mutations", "Set", "getObserverInit", "attr", "childList", "subtree", "attributes", "characterData", "attributeFilter", "getElementRecord", "element", "record", "get", "set", "createElementPropertyRecord", "el", "getCurrentValue", "setValue", "mutationRunner", "currentValue", "isDirty", "originalValue", "virtualValue", "_positionTimeout", "observer", "MutationObserver", "setTimeout", "parentNode", "insertBeforeNode", "observe", "queueIfNeeded", "val", "currentVal", "runDOMUpdates", "htmlMutationRunner", "for<PERSON>ach", "m", "mutate", "getTransformedHTML", "classMutationRunner", "split", "filter", "Boolean", "Array", "from", "join", "attrMutationRunner", "_loadDOMNodes", "parentSelector", "insertBeforeSelector", "document", "querySelector", "positionMutationRunner", "selectors", "newNodes", "getHTMLValue", "innerHTML", "setHTMLValue", "value", "getElementHTMLRecord", "elementRecord", "html", "getElementPosition", "parentElement", "nextElement<PERSON><PERSON>ling", "setElementPosition", "contains", "insertBefore", "getElementPositionRecord", "position", "setClassValue", "className", "removeAttribute", "getClassValue", "getElementClassRecord", "classes", "getAttrValue", "attrName", "getAttribute", "setAttrValue", "setAttribute", "getElementAttributeRecord", "deleteElementPropertyRecord", "disconnect", "transformContainer", "createElement", "setPropertyV<PERSON>ue", "length", "Object", "keys", "startMutating", "mutation", "kind", "attribute", "push", "stopMutating", "index", "indexOf", "splice", "refreshElementsSet", "size", "existingElements", "matchingElements", "querySelectorAll", "selector", "has", "add", "revertMutation", "clear", "refreshAllElementSets", "disconnectGlobalObserver", "connectGlobalObserver", "documentElement", "newMutation", "test", "classnames", "mutatedClassnames", "c", "declarative", "action"], "mappings": "IAAaA,kBAAkB,GAAG;AAClC,IAAMC,cAAc,GAAuB;AACzCC,EAAAA,MAAM,EAAE;AADiC,CAA3C;AAIA,IAAMC,QAAQ,gBAAgC,IAAIC,GAAJ,EAA9C;AACA,IAAMC,SAAS,gBAAkB,IAAIC,GAAJ,EAAjC;;AAEA,SAASC,eAAT,CAAyBC,IAAzB;AACE,SAAOA,IAAI,KAAK,MAAT,GACH;AACEC,IAAAA,SAAS,EAAE,IADb;AAEEC,IAAAA,OAAO,EAAE,IAFX;AAGEC,IAAAA,UAAU,EAAE,IAHd;AAIEC,IAAAA,aAAa,EAAE;AAJjB,GADG,GAOH;AACEH,IAAAA,SAAS,EAAE,KADb;AAEEC,IAAAA,OAAO,EAAE,KAFX;AAGEC,IAAAA,UAAU,EAAE,IAHd;AAIEE,IAAAA,eAAe,EAAE,CAACL,IAAD;AAJnB,GAPJ;AAaD;;AAED,SAASM,gBAAT,CAA0BC,OAA1B;AACE,MAAIC,MAAM,GAAGb,QAAQ,CAACc,GAAT,CAAaF,OAAb,CAAb;;AAEA,MAAI,CAACC,MAAL,EAAa;AACXA,IAAAA,MAAM,GAAG;AAAED,MAAAA,OAAO,EAAPA,OAAF;AAAWJ,MAAAA,UAAU,EAAE;AAAvB,KAAT;AACAR,IAAAA,QAAQ,CAACe,GAAT,CAAaH,OAAb,EAAsBC,MAAtB;AACD;;AAED,SAAOA,MAAP;AACD;;AAED,SAASG,2BAAT,CACEC,EADF,EAEEZ,IAFF,EAGEa,eAHF,EAIEC,QAJF,EAKEC,cALF;AAOE,MAAMC,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC;AACA,MAAMJ,MAAM,GAAoC;AAC9CS,IAAAA,OAAO,EAAE,KADqC;AAE9CC,IAAAA,aAAa,EAAEF,YAF+B;AAG9CG,IAAAA,YAAY,EAAEH,YAHgC;AAI9CnB,IAAAA,SAAS,EAAE,EAJmC;AAK9Ce,IAAAA,EAAE,EAAFA,EAL8C;AAM9CQ,IAAAA,gBAAgB,EAAE,IAN4B;AAO9CC,IAAAA,QAAQ,EAAE,IAAIC,gBAAJ,CAAqB;AAC7B;AACA;AACA;AACA;AACA,UAAItB,IAAI,KAAK,UAAT,IAAuBQ,MAAM,CAACY,gBAAlC,EAAoD,OAApD,KACK,IAAIpB,IAAI,KAAK,UAAb,EACHQ,MAAM,CAACY,gBAAP,GAA0BG,UAAU,CAAC;AACnCf,QAAAA,MAAM,CAACY,gBAAP,GAA0B,IAA1B;AACD,OAFmC,EAEjC,IAFiC,CAApC;AAIF,UAAMJ,YAAY,GAAGH,eAAe,CAACD,EAAD,CAApC;AACA,UACEZ,IAAI,KAAK,UAAT,IACAgB,YAAY,CAACQ,UAAb,KAA4BhB,MAAM,CAACW,YAAP,CAAoBK,UADhD,IAEAR,YAAY,CAACS,gBAAb,KAAkCjB,MAAM,CAACW,YAAP,CAAoBM,gBAHxD,EAKE;AACF,UAAIT,YAAY,KAAKR,MAAM,CAACW,YAA5B,EAA0C;AAC1CX,MAAAA,MAAM,CAACU,aAAP,GAAuBF,YAAvB;AACAD,MAAAA,cAAc,CAACP,MAAD,CAAd;AACD,KArBS,CAPoC;AA6B9CO,IAAAA,cAAc,EAAdA,cA7B8C;AA8B9CD,IAAAA,QAAQ,EAARA,QA9B8C;AA+B9CD,IAAAA,eAAe,EAAfA;AA/B8C,GAAhD;;AAiCA,MAAIb,IAAI,KAAK,UAAT,IAAuBY,EAAE,CAACY,UAA9B,EAA0C;AACxChB,IAAAA,MAAM,CAACa,QAAP,CAAgBK,OAAhB,CAAwBd,EAAE,CAACY,UAA3B,EAAuC;AACrCvB,MAAAA,SAAS,EAAE,IAD0B;AAErCC,MAAAA,OAAO,EAAE,IAF4B;AAGrCC,MAAAA,UAAU,EAAE,KAHyB;AAIrCC,MAAAA,aAAa,EAAE;AAJsB,KAAvC;AAMD,GAPD,MAOO;AACLI,IAAAA,MAAM,CAACa,QAAP,CAAgBK,OAAhB,CAAwBd,EAAxB,EAA4Bb,eAAe,CAACC,IAAD,CAA3C;AACD;;AACD,SAAOQ,MAAP;AACD;;AAED,SAASmB,aAAT,CACEC,GADF,EAEEpB,MAFF;AAIE,MAAMqB,UAAU,GAAGrB,MAAM,CAACK,eAAP,CAAuBL,MAAM,CAACI,EAA9B,CAAnB;AACAJ,EAAAA,MAAM,CAACW,YAAP,GAAsBS,GAAtB;;AACA,MAAIA,GAAG,IAAI,OAAOA,GAAP,KAAe,QAA1B,EAAoC;AAClC,QACE,CAACC,UAAD,IACAD,GAAG,CAACJ,UAAJ,KAAmBK,UAAU,CAACL,UAD9B,IAEAI,GAAG,CAACH,gBAAJ,KAAyBI,UAAU,CAACJ,gBAHtC,EAIE;AACAjB,MAAAA,MAAM,CAACS,OAAP,GAAiB,IAAjB;AACAa,MAAAA,aAAa;AACd;AACF,GATD,MASO,IAAIF,GAAG,KAAKC,UAAZ,EAAwB;AAC7BrB,IAAAA,MAAM,CAACS,OAAP,GAAiB,IAAjB;AACAa,IAAAA,aAAa;AACd;AACF;;AAED,SAASC,kBAAT,CAA4BvB,MAA5B;AACE,MAAIoB,GAAG,GAAGpB,MAAM,CAACU,aAAjB;AACAV,EAAAA,MAAM,CAACX,SAAP,CAAiBmC,OAAjB,CAAyB,UAAAC,CAAC;AAAA,WAAKL,GAAG,GAAGK,CAAC,CAACC,MAAF,CAASN,GAAT,CAAX;AAAA,GAA1B;AACAD,EAAAA,aAAa,CAACQ,kBAAkB,CAACP,GAAD,CAAnB,EAA0BpB,MAA1B,CAAb;AACD;;AACD,SAAS4B,mBAAT,CAA6B5B,MAA7B;AACE,MAAMoB,GAAG,GAAG,IAAI9B,GAAJ,CAAQU,MAAM,CAACU,aAAP,CAAqBmB,KAArB,CAA2B,KAA3B,EAAkCC,MAAlC,CAAyCC,OAAzC,CAAR,CAAZ;AACA/B,EAAAA,MAAM,CAACX,SAAP,CAAiBmC,OAAjB,CAAyB,UAAAC,CAAC;AAAA,WAAIA,CAAC,CAACC,MAAF,CAASN,GAAT,CAAJ;AAAA,GAA1B;AACAD,EAAAA,aAAa,CACXa,KAAK,CAACC,IAAN,CAAWb,GAAX,EACGU,MADH,CACUC,OADV,EAEGG,IAFH,CAEQ,GAFR,CADW,EAIXlC,MAJW,CAAb;AAMD;;AAED,SAASmC,kBAAT,CAA4BnC,MAA5B;AACE,MAAIoB,GAAG,GAAkBpB,MAAM,CAACU,aAAhC;AACAV,EAAAA,MAAM,CAACX,SAAP,CAAiBmC,OAAjB,CAAyB,UAAAC,CAAC;AAAA,WAAKL,GAAG,GAAGK,CAAC,CAACC,MAAF,CAASN,GAAT,CAAX;AAAA,GAA1B;AACAD,EAAAA,aAAa,CAACC,GAAD,EAAMpB,MAAN,CAAb;AACD;;AAED,SAASoC,aAAT;MACEC,sBAAAA;MACAC,4BAAAA;AAEA,MAAMtB,UAAU,GAAGuB,QAAQ,CAACC,aAAT,CAAoCH,cAApC,CAAnB;AACA,MAAI,CAACrB,UAAL,EAAiB,OAAO,IAAP;AACjB,MAAMC,gBAAgB,GAAGqB,oBAAoB,GACzCC,QAAQ,CAACC,aAAT,CAAoCF,oBAApC,CADyC,GAEzC,IAFJ;AAGA,MAAIA,oBAAoB,IAAI,CAACrB,gBAA7B,EAA+C,OAAO,IAAP;AAC/C,SAAO;AACLD,IAAAA,UAAU,EAAVA,UADK;AAELC,IAAAA,gBAAgB,EAAhBA;AAFK,GAAP;AAID;;AAED,SAASwB,sBAAT,CAAgCzC,MAAhC;AACE,MAAIoB,GAAG,GAAGpB,MAAM,CAACU,aAAjB;AACAV,EAAAA,MAAM,CAACX,SAAP,CAAiBmC,OAAjB,CAAyB,UAAAC,CAAC;AACxB,QAAMiB,SAAS,GAAGjB,CAAC,CAACC,MAAF,EAAlB;;AACA,QAAMiB,QAAQ,GAAGP,aAAa,CAACM,SAAD,CAA9B;;AACAtB,IAAAA,GAAG,GAAGuB,QAAQ,IAAIvB,GAAlB;AACD,GAJD;AAKAD,EAAAA,aAAa,CAACC,GAAD,EAAMpB,MAAN,CAAb;AACD;;AAED,IAAM4C,YAAY,GAAG,SAAfA,YAAe,CAACxC,EAAD;AAAA,SAAiBA,EAAE,CAACyC,SAApB;AAAA,CAArB;;AACA,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAAC1C,EAAD,EAAc2C,KAAd;AAAA,SAAiC3C,EAAE,CAACyC,SAAH,GAAeE,KAAhD;AAAA,CAArB;;AACA,SAASC,oBAAT,CAA8BjD,OAA9B;AACE,MAAMkD,aAAa,GAAGnD,gBAAgB,CAACC,OAAD,CAAtC;;AACA,MAAI,CAACkD,aAAa,CAACC,IAAnB,EAAyB;AACvBD,IAAAA,aAAa,CAACC,IAAd,GAAqB/C,2BAA2B,CAC9CJ,OAD8C,EAE9C,MAF8C,EAG9C6C,YAH8C,EAI9CE,YAJ8C,EAK9CvB,kBAL8C,CAAhD;AAOD;;AACD,SAAO0B,aAAa,CAACC,IAArB;AACD;;AAED,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAC/C,EAAD;AACzB,SAAO;AACLY,IAAAA,UAAU,EAAEZ,EAAE,CAACgD,aADV;AAELnC,IAAAA,gBAAgB,EAAEb,EAAE,CAACiD;AAFhB,GAAP;AAID,CALD;;AAMA,IAAMC,kBAAkB,GAAG,SAArBA,kBAAqB,CAAClD,EAAD,EAAc2C,KAAd;AACzB,MACEA,KAAK,CAAC9B,gBAAN,IACA,CAAC8B,KAAK,CAAC/B,UAAN,CAAiBuC,QAAjB,CAA0BR,KAAK,CAAC9B,gBAAhC,CAFH,EAGE;AACA;AACA;AACA;AACD;;AACD8B,EAAAA,KAAK,CAAC/B,UAAN,CAAiBwC,YAAjB,CAA8BpD,EAA9B,EAAkC2C,KAAK,CAAC9B,gBAAxC;AACD,CAVD;;AAWA,SAASwC,wBAAT,CAAkC1D,OAAlC;AACE,MAAMkD,aAAa,GAAGnD,gBAAgB,CAACC,OAAD,CAAtC;;AACA,MAAI,CAACkD,aAAa,CAACS,QAAnB,EAA6B;AAC3BT,IAAAA,aAAa,CAACS,QAAd,GAAyBvD,2BAA2B,CAClDJ,OADkD,EAElD,UAFkD,EAGlDoD,kBAHkD,EAIlDG,kBAJkD,EAKlDb,sBALkD,CAApD;AAOD;;AACD,SAAOQ,aAAa,CAACS,QAArB;AACD;;AAED,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAACvD,EAAD,EAAcgB,GAAd;AAAA,SACpBA,GAAG,GAAIhB,EAAE,CAACwD,SAAH,GAAexC,GAAnB,GAA0BhB,EAAE,CAACyD,eAAH,CAAmB,OAAnB,CADT;AAAA,CAAtB;;AAEA,IAAMC,aAAa,GAAG,SAAhBA,aAAgB,CAAC1D,EAAD;AAAA,SAAiBA,EAAE,CAACwD,SAApB;AAAA,CAAtB;;AACA,SAASG,qBAAT,CAA+B3D,EAA/B;AACE,MAAM6C,aAAa,GAAGnD,gBAAgB,CAACM,EAAD,CAAtC;;AACA,MAAI,CAAC6C,aAAa,CAACe,OAAnB,EAA4B;AAC1Bf,IAAAA,aAAa,CAACe,OAAd,GAAwB7D,2BAA2B,CACjDC,EADiD,EAEjD,OAFiD,EAGjD0D,aAHiD,EAIjDH,aAJiD,EAKjD/B,mBALiD,CAAnD;AAOD;;AACD,SAAOqB,aAAa,CAACe,OAArB;AACD;;AAED,IAAMC,YAAY,GAAG,SAAfA,YAAe,CAACC,QAAD;AAAA,SAAsB,UAAC9D,EAAD;AAAA;;AAAA,+BACzCA,EAAE,CAAC+D,YAAH,CAAgBD,QAAhB,CADyC,+BACZ,IADY;AAAA,GAAtB;AAAA,CAArB;;AAEA,IAAME,YAAY,GAAG,SAAfA,YAAe,CAACF,QAAD;AAAA,SAAsB,UAAC9D,EAAD,EAAcgB,GAAd;AAAA,WACzCA,GAAG,KAAK,IAAR,GAAehB,EAAE,CAACiE,YAAH,CAAgBH,QAAhB,EAA0B9C,GAA1B,CAAf,GAAgDhB,EAAE,CAACyD,eAAH,CAAmBK,QAAnB,CADP;AAAA,GAAtB;AAAA,CAArB;;AAEA,SAASI,yBAAT,CAAmClE,EAAnC,EAAgDZ,IAAhD;AACE,MAAMyD,aAAa,GAAGnD,gBAAgB,CAACM,EAAD,CAAtC;;AACA,MAAI,CAAC6C,aAAa,CAACtD,UAAd,CAAyBH,IAAzB,CAAL,EAAqC;AACnCyD,IAAAA,aAAa,CAACtD,UAAd,CAAyBH,IAAzB,IAAiCW,2BAA2B,CAC1DC,EAD0D,EAE1DZ,IAF0D,EAG1DyE,YAAY,CAACzE,IAAD,CAH8C,EAI1D4E,YAAY,CAAC5E,IAAD,CAJ8C,EAK1D2C,kBAL0D,CAA5D;AAOD;;AACD,SAAOc,aAAa,CAACtD,UAAd,CAAyBH,IAAzB,CAAP;AACD;;AAED,SAAS+E,2BAAT,CAAqCnE,EAArC,EAAkDZ,IAAlD;AACE,MAAMO,OAAO,GAAGZ,QAAQ,CAACc,GAAT,CAAaG,EAAb,CAAhB;AACA,MAAI,CAACL,OAAL,EAAc;;AACd,MAAIP,IAAI,KAAK,MAAb,EAAqB;AAAA;;AACnB,qBAAAO,OAAO,CAACmD,IAAR,4DAAcrC,QAAd,2CAAwB2D,UAAxB;AACA,WAAOzE,OAAO,CAACmD,IAAf;AACD,GAHD,MAGO,IAAI1D,IAAI,KAAK,OAAb,EAAsB;AAAA;;AAC3B,wBAAAO,OAAO,CAACiE,OAAR,+DAAiBnD,QAAjB,2CAA2B2D,UAA3B;AACA,WAAOzE,OAAO,CAACiE,OAAf;AACD,GAHM,MAGA,IAAIxE,IAAI,KAAK,UAAb,EAAyB;AAAA;;AAC9B,yBAAAO,OAAO,CAAC2D,QAAR,gEAAkB7C,QAAlB,2CAA4B2D,UAA5B;AACA,WAAOzE,OAAO,CAAC2D,QAAf;AACD,GAHM,MAGA;AAAA;;AACL,2BAAA3D,OAAO,CAACJ,UAAR,kEAAqBH,IAArB,sEAA4BqB,QAA5B,4CAAsC2D,UAAtC;AACA,WAAOzE,OAAO,CAACJ,UAAR,CAAmBH,IAAnB,CAAP;AACD;AACF;;AAED,IAAIiF,kBAAJ;;AACA,SAAS9C,kBAAT,CAA4BuB,IAA5B;AACE,MAAI,CAACuB,kBAAL,EAAyB;AACvBA,IAAAA,kBAAkB,GAAGlC,QAAQ,CAACmC,aAAT,CAAuB,KAAvB,CAArB;AACD;;AACDD,EAAAA,kBAAkB,CAAC5B,SAAnB,GAA+BK,IAA/B;AACA,SAAOuB,kBAAkB,CAAC5B,SAA1B;AACD;;AAED,SAAS8B,gBAAT,CACEvE,EADF,EAEEZ,IAFF,EAGEiC,CAHF;AAKE,MAAI,CAACA,CAAC,CAAChB,OAAP,EAAgB;AAChBgB,EAAAA,CAAC,CAAChB,OAAF,GAAY,KAAZ;AACA,MAAMW,GAAG,GAAGK,CAAC,CAACd,YAAd;;AACA,MAAI,CAACc,CAAC,CAACpC,SAAF,CAAYuF,MAAjB,EAAyB;AACvBL,IAAAA,2BAA2B,CAACnE,EAAD,EAAKZ,IAAL,CAA3B;AACD;;AACDiC,EAAAA,CAAC,CAACnB,QAAF,CAAWF,EAAX,EAAegB,GAAf;AACD;;AAED,SAASd,QAAT,CAAkBmB,CAAlB,EAAoCrB,EAApC;AACEqB,EAAAA,CAAC,CAACyB,IAAF,IAAUyB,gBAAgB,CAAavE,EAAb,EAAiB,MAAjB,EAAyBqB,CAAC,CAACyB,IAA3B,CAA1B;AACAzB,EAAAA,CAAC,CAACuC,OAAF,IAAaW,gBAAgB,CAAkBvE,EAAlB,EAAsB,OAAtB,EAA+BqB,CAAC,CAACuC,OAAjC,CAA7B;AACAvC,EAAAA,CAAC,CAACiC,QAAF,IAAciB,gBAAgB,CAAiBvE,EAAjB,EAAqB,UAArB,EAAiCqB,CAAC,CAACiC,QAAnC,CAA9B;AACAmB,EAAAA,MAAM,CAACC,IAAP,CAAYrD,CAAC,CAAC9B,UAAd,EAA0B6B,OAA1B,CAAkC,UAAAhC,IAAI;AACpCmF,IAAAA,gBAAgB,CAAkBvE,EAAlB,EAAsBZ,IAAtB,EAA4BiC,CAAC,CAAC9B,UAAF,CAAaH,IAAb,CAA5B,CAAhB;AACD,GAFD;AAGD;;AAED,SAAS8B,aAAT;AACEnC,EAAAA,QAAQ,CAACqC,OAAT,CAAiBlB,QAAjB;AACD;;;AAGD,SAASyE,aAAT,CAAuBC,QAAvB,EAA2CjF,OAA3C;AACE,MAAIC,MAAM,GAA2C,IAArD;;AACA,MAAIgF,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;AAC5BjF,IAAAA,MAAM,GAAGgD,oBAAoB,CAACjD,OAAD,CAA7B;AACD,GAFD,MAEO,IAAIiF,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;AACpCjF,IAAAA,MAAM,GAAG+D,qBAAqB,CAAChE,OAAD,CAA9B;AACD,GAFM,MAEA,IAAIiF,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;AACxCjF,IAAAA,MAAM,GAAGsE,yBAAyB,CAACvE,OAAD,EAAUiF,QAAQ,CAACE,SAAnB,CAAlC;AACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;AACvCjF,IAAAA,MAAM,GAAGyD,wBAAwB,CAAC1D,OAAD,CAAjC;AACD;;AACD,MAAI,CAACC,MAAL,EAAa;AACbA,EAAAA,MAAM,CAACX,SAAP,CAAiB8F,IAAjB,CAAsBH,QAAtB;AACAhF,EAAAA,MAAM,CAACO,cAAP,CAAsBP,MAAtB;AACD;;;AAGD,SAASoF,YAAT,CAAsBJ,QAAtB,EAA0C5E,EAA1C;AACE,MAAIJ,MAAM,GAA2C,IAArD;;AACA,MAAIgF,QAAQ,CAACC,IAAT,KAAkB,MAAtB,EAA8B;AAC5BjF,IAAAA,MAAM,GAAGgD,oBAAoB,CAAC5C,EAAD,CAA7B;AACD,GAFD,MAEO,IAAI4E,QAAQ,CAACC,IAAT,KAAkB,OAAtB,EAA+B;AACpCjF,IAAAA,MAAM,GAAG+D,qBAAqB,CAAC3D,EAAD,CAA9B;AACD,GAFM,MAEA,IAAI4E,QAAQ,CAACC,IAAT,KAAkB,WAAtB,EAAmC;AACxCjF,IAAAA,MAAM,GAAGsE,yBAAyB,CAAClE,EAAD,EAAK4E,QAAQ,CAACE,SAAd,CAAlC;AACD,GAFM,MAEA,IAAIF,QAAQ,CAACC,IAAT,KAAkB,UAAtB,EAAkC;AACvCjF,IAAAA,MAAM,GAAGyD,wBAAwB,CAACrD,EAAD,CAAjC;AACD;;AACD,MAAI,CAACJ,MAAL,EAAa;AACb,MAAMqF,KAAK,GAAGrF,MAAM,CAACX,SAAP,CAAiBiG,OAAjB,CAAyBN,QAAzB,CAAd;AACA,MAAIK,KAAK,KAAK,CAAC,CAAf,EAAkBrF,MAAM,CAACX,SAAP,CAAiBkG,MAAjB,CAAwBF,KAAxB,EAA+B,CAA/B;AAClBrF,EAAAA,MAAM,CAACO,cAAP,CAAsBP,MAAtB;AACD;;;AAGD,SAASwF,kBAAT,CAA4BR,QAA5B;AACE;AACA;AACA,MAAIA,QAAQ,CAACC,IAAT,KAAkB,UAAlB,IAAgCD,QAAQ,CAAC7F,QAAT,CAAkBsG,IAAlB,KAA2B,CAA/D,EAAkE;AAElE,MAAMC,gBAAgB,GAAG,IAAIpG,GAAJ,CAAQ0F,QAAQ,CAAC7F,QAAjB,CAAzB;AACA,MAAMwG,gBAAgB,GAAGpD,QAAQ,CAACqD,gBAAT,CAA0BZ,QAAQ,CAACa,QAAnC,CAAzB;AAEAF,EAAAA,gBAAgB,CAACnE,OAAjB,CAAyB,UAAApB,EAAE;AACzB,QAAI,CAACsF,gBAAgB,CAACI,GAAjB,CAAqB1F,EAArB,CAAL,EAA+B;AAC7B4E,MAAAA,QAAQ,CAAC7F,QAAT,CAAkB4G,GAAlB,CAAsB3F,EAAtB;AACA2E,MAAAA,aAAa,CAACC,QAAD,EAAW5E,EAAX,CAAb;AACD;AACF,GALD;AAMD;;AAED,SAAS4F,cAAT,CAAwBhB,QAAxB;AACEA,EAAAA,QAAQ,CAAC7F,QAAT,CAAkBqC,OAAlB,CAA0B,UAAApB,EAAE;AAAA,WAAIgF,YAAY,CAACJ,QAAD,EAAW5E,EAAX,CAAhB;AAAA,GAA5B;AACA4E,EAAAA,QAAQ,CAAC7F,QAAT,CAAkB8G,KAAlB;AACA5G,EAAAA,SAAS,UAAT,CAAiB2F,QAAjB;AACD;;AAED,SAASkB,qBAAT;AACE7G,EAAAA,SAAS,CAACmC,OAAV,CAAkBgE,kBAAlB;AACD;;;AAGD,IAAI3E,QAAJ;SACgBsF;AACdtF,EAAAA,QAAQ,IAAIA,QAAQ,CAAC2D,UAAT,EAAZ;AACD;SACe4B;AACd,MAAI,OAAO7D,QAAP,KAAoB,WAAxB,EAAqC;;AAErC,MAAI,CAAC1B,QAAL,EAAe;AACbA,IAAAA,QAAQ,GAAG,IAAIC,gBAAJ,CAAqB;AAC9BoF,MAAAA,qBAAqB;AACtB,KAFU,CAAX;AAGD;;AAEDA,EAAAA,qBAAqB;AACrBrF,EAAAA,QAAQ,CAACK,OAAT,CAAiBqB,QAAQ,CAAC8D,eAA1B,EAA2C;AACzC5G,IAAAA,SAAS,EAAE,IAD8B;AAEzCC,IAAAA,OAAO,EAAE,IAFgC;AAGzCC,IAAAA,UAAU,EAAE,KAH6B;AAIzCC,IAAAA,aAAa,EAAE;AAJ0B,GAA3C;AAMD;;AAGDwG,qBAAqB;;AAErB,SAASE,WAAT,CAAqB7E,CAArB;AACE;AACA,MAAI,OAAOc,QAAP,KAAoB,WAAxB,EAAqC,OAAOtD,cAAP;;AAErCI,EAAAA,SAAS,CAAC0G,GAAV,CAActE,CAAd;;AAEA+D,EAAAA,kBAAkB,CAAC/D,CAAD,CAAlB;AACA,SAAO;AACLvC,IAAAA,MAAM,EAAE;AACN8G,MAAAA,cAAc,CAACvE,CAAD,CAAd;AACD;AAHI,GAAP;AAKD;;AAED,SAASyB,IAAT,CACE2C,QADF,EAEEnE,MAFF;AAIE,SAAO4E,WAAW,CAAC;AACjBrB,IAAAA,IAAI,EAAE,MADW;AAEjB9F,IAAAA,QAAQ,EAAE,IAAIG,GAAJ,EAFO;AAGjBoC,IAAAA,MAAM,EAANA,MAHiB;AAIjBmE,IAAAA,QAAQ,EAARA;AAJiB,GAAD,CAAlB;AAMD;;AAED,SAASnC,QAAT,CACEmC,QADF,EAEEnE,MAFF;AAIE,SAAO4E,WAAW,CAAC;AACjBrB,IAAAA,IAAI,EAAE,UADW;AAEjB9F,IAAAA,QAAQ,EAAE,IAAIG,GAAJ,EAFO;AAGjBoC,IAAAA,MAAM,EAANA,MAHiB;AAIjBmE,IAAAA,QAAQ,EAARA;AAJiB,GAAD,CAAlB;AAMD;;AAED,SAAS7B,OAAT,CACE6B,QADF,EAEEnE,MAFF;AAIE,SAAO4E,WAAW,CAAC;AACjBrB,IAAAA,IAAI,EAAE,OADW;AAEjB9F,IAAAA,QAAQ,EAAE,IAAIG,GAAJ,EAFO;AAGjBoC,IAAAA,MAAM,EAANA,MAHiB;AAIjBmE,IAAAA,QAAQ,EAARA;AAJiB,GAAD,CAAlB;AAMD;;AAED,SAASX,SAAT,CACEW,QADF,EAEEX,SAFF,EAGExD,MAHF;AAKE,MAAI,CAAC1C,kBAAkB,CAACuH,IAAnB,CAAwBrB,SAAxB,CAAL,EAAyC,OAAOjG,cAAP;;AAEzC,MAAIiG,SAAS,KAAK,OAAd,IAAyBA,SAAS,KAAK,WAA3C,EAAwD;AACtD,WAAOlB,OAAO,CAAC6B,QAAD,EAAW,UAAAW,UAAU;AACjC,UAAMC,iBAAiB,GAAG/E,MAAM,CAACM,KAAK,CAACC,IAAN,CAAWuE,UAAX,EAAuBtE,IAAvB,CAA4B,GAA5B,CAAD,CAAhC;AACAsE,MAAAA,UAAU,CAACP,KAAX;AACA,UAAI,CAACQ,iBAAL,EAAwB;AACxBA,MAAAA,iBAAiB,CACd5E,KADH,CACS,MADT,EAEGC,MAFH,CAEUC,OAFV,EAGGP,OAHH,CAGW,UAAAkF,CAAC;AAAA,eAAIF,UAAU,CAACT,GAAX,CAAeW,CAAf,CAAJ;AAAA,OAHZ;AAID,KARa,CAAd;AASD;;AAED,SAAOJ,WAAW,CAAC;AACjBrB,IAAAA,IAAI,EAAE,WADW;AAEjBC,IAAAA,SAAS,EAATA,SAFiB;AAGjB/F,IAAAA,QAAQ,EAAE,IAAIG,GAAJ,EAHO;AAIjBoC,IAAAA,MAAM,EAANA,MAJiB;AAKjBmE,IAAAA,QAAQ,EAARA;AALiB,GAAD,CAAlB;AAOD;;AAED,SAASc,WAAT;MACEd,iBAAAA;MACAe,eAAAA;MACA7D,cAAAA;MACWvD,aAAX0F;MACA7C,uBAAAA;MACAC,6BAAAA;;AAEA,MAAI9C,IAAI,KAAK,MAAb,EAAqB;AACnB,QAAIoH,MAAM,KAAK,QAAf,EAAyB;AACvB,aAAO1D,IAAI,CAAC2C,QAAD,EAAW,UAAAzE,GAAG;AAAA,eAAIA,GAAG,IAAI2B,KAAJ,WAAIA,KAAJ,GAAa,EAAb,CAAP;AAAA,OAAd,CAAX;AACD,KAFD,MAEO,IAAI6D,MAAM,KAAK,KAAf,EAAsB;AAC3B,aAAO1D,IAAI,CAAC2C,QAAD,EAAW;AAAA,eAAM9C,KAAN,WAAMA,KAAN,GAAe,EAAf;AAAA,OAAX,CAAX;AACD;AACF,GAND,MAMO,IAAIvD,IAAI,KAAK,OAAb,EAAsB;AAC3B,QAAIoH,MAAM,KAAK,QAAf,EAAyB;AACvB,aAAO5C,OAAO,CAAC6B,QAAD,EAAW,UAAAzE,GAAG;AAC1B,YAAI2B,KAAJ,EAAW3B,GAAG,CAAC2E,GAAJ,CAAQhD,KAAR;AACZ,OAFa,CAAd;AAGD,KAJD,MAIO,IAAI6D,MAAM,KAAK,QAAf,EAAyB;AAC9B,aAAO5C,OAAO,CAAC6B,QAAD,EAAW,UAAAzE,GAAG;AAC1B,YAAI2B,KAAJ,EAAW3B,GAAG,UAAH,CAAW2B,KAAX;AACZ,OAFa,CAAd;AAGD,KAJM,MAIA,IAAI6D,MAAM,KAAK,KAAf,EAAsB;AAC3B,aAAO5C,OAAO,CAAC6B,QAAD,EAAW,UAAAzE,GAAG;AAC1BA,QAAAA,GAAG,CAAC6E,KAAJ;AACA,YAAIlD,KAAJ,EAAW3B,GAAG,CAAC2E,GAAJ,CAAQhD,KAAR;AACZ,OAHa,CAAd;AAID;AACF,GAfM,MAeA,IAAIvD,IAAI,KAAK,UAAb,EAAyB;AAC9B,QAAIoH,MAAM,KAAK,KAAX,IAAoBvE,cAAxB,EAAwC;AACtC,aAAOqB,QAAQ,CAACmC,QAAD,EAAW;AAAA,eAAO;AAC/BvD,UAAAA,oBAAoB,EAApBA,oBAD+B;AAE/BD,UAAAA,cAAc,EAAdA;AAF+B,SAAP;AAAA,OAAX,CAAf;AAID;AACF,GAPM,MAOA;AACL,QAAIuE,MAAM,KAAK,QAAf,EAAyB;AACvB,aAAO1B,SAAS,CAACW,QAAD,EAAWrG,IAAX,EAAiB,UAAA4B,GAAG;AAAA,eAClCA,GAAG,KAAK,IAAR,GAAeA,GAAG,IAAI2B,KAAJ,WAAIA,KAAJ,GAAa,EAAb,CAAlB,GAAqCA,KAArC,WAAqCA,KAArC,GAA8C,EADZ;AAAA,OAApB,CAAhB;AAGD,KAJD,MAIO,IAAI6D,MAAM,KAAK,KAAf,EAAsB;AAC3B,aAAO1B,SAAS,CAACW,QAAD,EAAWrG,IAAX,EAAiB;AAAA,eAAMuD,KAAN,WAAMA,KAAN,GAAe,EAAf;AAAA,OAAjB,CAAhB;AACD,KAFM,MAEA,IAAI6D,MAAM,KAAK,QAAf,EAAyB;AAC9B,aAAO1B,SAAS,CAACW,QAAD,EAAWrG,IAAX,EAAiB;AAAA,eAAM,IAAN;AAAA,OAAjB,CAAhB;AACD;AACF;;AACD,SAAOP,cAAP;AACD;;AAeD,YAAe;AACbiE,EAAAA,IAAI,EAAJA,IADa;AAEbc,EAAAA,OAAO,EAAPA,OAFa;AAGbkB,EAAAA,SAAS,EAATA,SAHa;AAIbxB,EAAAA,QAAQ,EAARA,QAJa;AAKbiD,EAAAA,WAAW,EAAXA;AALa,CAAf;;;;;"}