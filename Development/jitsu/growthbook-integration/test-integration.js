#!/usr/bin/env node

const axios = require('axios');

const API_BASE = 'http://localhost:3001';

async function testIntegration() {
  console.log('🧪 Testing Jitsu + GrowthBook Integration\n');

  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health endpoint...');
    const health = await axios.get(`${API_BASE}/health`);
    console.log('✅ Health:', health.data);
    console.log();

    // Test 2: Get features
    console.log('2️⃣ Testing features endpoint...');
    const features = await axios.get(`${API_BASE}/features`);
    console.log('✅ Features:', JSON.stringify(features.data, null, 2));
    console.log();

    // Test 3: Evaluate feature for US user
    console.log('3️⃣ Testing feature evaluation for US user...');
    const usEvaluation = await axios.post(`${API_BASE}/evaluate`, {
      userId: 'user_us_123',
      featureKey: 'welcome-message',
      attributes: { country: 'US', premium: false }
    });
    console.log('✅ US User Evaluation:', usEvaluation.data);
    console.log();

    // Test 4: Evaluate feature for premium user
    console.log('4️⃣ Testing feature evaluation for premium user...');
    const premiumEvaluation = await axios.post(`${API_BASE}/evaluate`, {
      userId: 'user_premium_456',
      featureKey: 'button-color',
      attributes: { country: 'CA', premium: true }
    });
    console.log('✅ Premium User Evaluation:', premiumEvaluation.data);
    console.log();

    // Test 5: Track experiment
    console.log('5️⃣ Testing experiment tracking...');
    const tracking = await axios.post(`${API_BASE}/track-experiment`, {
      userId: 'user_experiment_789',
      experimentKey: 'homepage-redesign',
      variationId: 'variant-b',
      attributes: { source: 'web', device: 'desktop' }
    });
    console.log('✅ Experiment Tracking:', tracking.data);
    console.log();

    // Test 6: Multiple evaluations to simulate real usage
    console.log('6️⃣ Simulating multiple user interactions...');
    const users = [
      { id: 'user1', country: 'US', premium: false },
      { id: 'user2', country: 'CA', premium: true },
      { id: 'user3', country: 'UK', premium: false },
      { id: 'user4', country: 'US', premium: true }
    ];

    for (const user of users) {
      await axios.post(`${API_BASE}/evaluate`, {
        userId: user.id,
        featureKey: 'welcome-message',
        attributes: user
      });

      await axios.post(`${API_BASE}/evaluate`, {
        userId: user.id,
        featureKey: 'button-color',
        attributes: user
      });
    }
    console.log('✅ Simulated 8 feature evaluations for 4 users');
    console.log();

    console.log('🎉 All tests completed successfully!');
    console.log('📊 Check the server logs to see the events that would be sent to Jitsu');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response:', error.response.data);
    }
  }
}

// Run tests
testIntegration();
