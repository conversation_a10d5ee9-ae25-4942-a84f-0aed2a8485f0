const express = require('express');
const { engine } = require('express-handlebars');
const cors = require('cors');
const path = require('path');
const axios = require('axios');
const { Pool } = require('pg');
// const { ClickHouse } = require('clickhouse'); // Disabled for now
const moment = require('moment');

require('dotenv').config();

const app = express();
const port = process.env.PORT || 3002;

// Configure Handlebars
app.engine('handlebars', engine({
  defaultLayout: 'main',
  layoutsDir: path.join(__dirname, 'views/layouts'),
  partialsDir: path.join(__dirname, 'views/partials'),
  helpers: {
    eq: function (a, b) {
      return a === b;
    },
    formatDate: (date) => moment(date).format('YYYY-MM-DD HH:mm:ss'),
    formatNumber: (num) => new Intl.NumberFormat().format(num),
    json: (obj) => JSON.stringify(obj, null, 2)
  }
}));
app.set('view engine', 'handlebars');
app.set('views', path.join(__dirname, 'views'));

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, 'public')));

// Database connections
const pgPool = new Pool({
  host: process.env.POSTGRES_HOST,
  port: process.env.POSTGRES_PORT,
  database: process.env.POSTGRES_DB,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
});

// ClickHouse connection disabled for now
const clickhouse = {
  query: () => ({ toPromise: () => Promise.resolve([]) })
};

// Simple auth middleware
const auth = (req, res, next) => {
  const auth = req.headers.authorization;
  if (!auth || !auth.startsWith('Basic ')) {
    res.set('WWW-Authenticate', 'Basic realm="Admin Panel"');
    return res.status(401).send('Authentication required');
  }
  
  const credentials = Buffer.from(auth.slice(6), 'base64').toString().split(':');
  const username = credentials[0];
  const password = credentials[1];
  
  if (username === process.env.ADMIN_USERNAME && password === process.env.ADMIN_PASSWORD) {
    next();
  } else {
    res.set('WWW-Authenticate', 'Basic realm="Admin Panel"');
    return res.status(401).send('Invalid credentials');
  }
};

// Routes
app.get('/', auth, async (req, res) => {
  try {
    // Get system stats
    const stats = await getSystemStats();
    res.render('dashboard', { 
      title: 'Jitsu Admin Dashboard',
      stats
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.render('error', { error: error.message });
  }
});

app.get('/events', auth, async (req, res) => {
  try {
    const events = await getRecentEvents();
    res.render('events', { 
      title: 'Recent Events',
      events
    });
  } catch (error) {
    console.error('Events error:', error);
    res.render('error', { error: error.message });
  }
});

app.get('/features', auth, async (req, res) => {
  try {
    const features = await getFeatureFlags();
    res.render('features', { 
      title: 'Feature Flags',
      features
    });
  } catch (error) {
    console.error('Features error:', error);
    res.render('error', { error: error.message });
  }
});

app.get('/users', auth, async (req, res) => {
  try {
    const users = await getActiveUsers();
    res.render('users', { 
      title: 'Active Users',
      users
    });
  } catch (error) {
    console.error('Users error:', error);
    res.render('error', { error: error.message });
  }
});

app.get('/api/stats', auth, async (req, res) => {
  try {
    const stats = await getSystemStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Helper functions
async function getSystemStats() {
  try {
    // Check integration API
    let integrationStatus = 'down';
    try {
      const response = await axios.get(`${process.env.INTEGRATION_API_URL}/health`, { timeout: 5000 });
      integrationStatus = response.data.status === 'ok' ? 'up' : 'down';
    } catch (e) {
      integrationStatus = 'down';
    }

    // Check databases
    let pgStatus = 'down';
    try {
      await pgPool.query('SELECT 1');
      pgStatus = 'up';
    } catch (e) {
      pgStatus = 'down';
    }

    let chStatus = 'down';
    try {
      await clickhouse.query('SELECT 1').toPromise();
      chStatus = 'up';
    } catch (e) {
      chStatus = 'down';
    }

    return {
      integration: integrationStatus,
      postgres: pgStatus,
      clickhouse: chStatus,
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  } catch (error) {
    console.error('Error getting system stats:', error);
    return {
      integration: 'unknown',
      postgres: 'unknown',
      clickhouse: 'unknown',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      error: error.message
    };
  }
}

async function getRecentEvents() {
  // Mock events for demo - in real implementation would query ClickHouse
  return [
    {
      id: 1,
      event: 'feature_evaluated',
      user_id: 'user123',
      feature_key: 'welcome-message',
      result: 'Welcome!',
      timestamp: new Date(Date.now() - 1000 * 60 * 5).toISOString()
    },
    {
      id: 2,
      event: 'experiment_viewed',
      user_id: 'user456',
      experiment_id: 'homepage-test',
      variation_id: 'variant-b',
      timestamp: new Date(Date.now() - 1000 * 60 * 10).toISOString()
    }
  ];
}

async function getFeatureFlags() {
  try {
    const response = await axios.get(`${process.env.INTEGRATION_API_URL}/features`);
    return response.data.features;
  } catch (error) {
    console.error('Error fetching features:', error);
    return {};
  }
}

async function getActiveUsers() {
  // Mock users for demo
  return [
    { id: 'user123', last_seen: new Date(Date.now() - 1000 * 60 * 5).toISOString(), events_count: 15 },
    { id: 'user456', last_seen: new Date(Date.now() - 1000 * 60 * 10).toISOString(), events_count: 8 },
    { id: 'user789', last_seen: new Date(Date.now() - 1000 * 60 * 30).toISOString(), events_count: 23 }
  ];
}

app.listen(port, () => {
  console.log(`🚀 Jitsu Admin Panel running on http://localhost:${port}`);
  console.log(`📊 Username: ${process.env.ADMIN_USERNAME}`);
  console.log(`🔑 Password: ${process.env.ADMIN_PASSWORD}`);
});
