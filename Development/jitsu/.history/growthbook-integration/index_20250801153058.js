const express = require('express');
const cors = require('cors');
const { GrowthBook } = require('@growthbook/growthbook');
const axios = require('axios');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// GrowthBook instance
let growthbook;

// Initialize GrowthBook
async function initializeGrowthBook() {
  try {
    console.log('Initializing GrowthBook with API key:', process.env.GROWTHBOOK_API_KEY?.substring(0, 10) + '...');

    // Initialize GrowthBook without fetching features first (for demo purposes)
    growthbook = new GrowthBook({
      apiHost: process.env.GROWTHBOOK_API_HOST,
      clientKey: process.env.GROWTHBOOK_API_KEY,
      features: {
        // Demo features for testing
        'welcome-message': {
          defaultValue: 'Welcome!',
          rules: [
            {
              condition: { country: 'US' },
              value: 'Welcome to our US site!'
            }
          ]
        },
        'button-color': {
          defaultValue: 'blue',
          rules: [
            {
              condition: { premium: true },
              value: 'gold'
            }
          ]
        }
      },
      trackingCallback: (experiment, result) => {
        console.log('Experiment tracked:', experiment.key, result);
        // Send experiment data to Jitsu
        sendToJitsu({
          event: 'experiment_viewed',
          experiment_id: experiment.key,
          variation_id: result.variationId,
          user_id: result.userId,
          timestamp: new Date().toISOString()
        });
      }
    });

    console.log('GrowthBook initialized successfully with demo features');
  } catch (error) {
    console.error('Failed to initialize GrowthBook:', error.message);

    // Initialize with minimal config for demo
    growthbook = new GrowthBook({
      features: {},
      trackingCallback: (experiment, result) => {
        console.log('Experiment tracked:', experiment.key, result);
      }
    });
  }
}

// Send data to Jitsu
async function sendToJitsu(data) {
  try {
    await axios.post(`${process.env.JITSU_INGEST_URL}/api/v1/event`, data, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.JITSU_API_KEY}`
      }
    });
    console.log('Event sent to Jitsu:', data.event);
  } catch (error) {
    console.error('Failed to send event to Jitsu:', error.message);
  }
}

// Routes
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

app.get('/features', async (req, res) => {
  try {
    if (!growthbook) {
      return res.status(500).json({ error: 'GrowthBook not initialized' });
    }

    const features = growthbook.getFeatures();
    res.json({ features });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/evaluate', async (req, res) => {
  try {
    if (!growthbook) {
      return res.status(500).json({ error: 'GrowthBook not initialized' });
    }

    const { userId, attributes, featureKey } = req.body;

    // Set user attributes
    growthbook.setAttributes({
      id: userId,
      ...attributes
    });

    // Evaluate feature
    const result = growthbook.getFeatureValue(featureKey, null);

    // Send evaluation event to Jitsu
    await sendToJitsu({
      event: 'feature_evaluated',
      feature_key: featureKey,
      user_id: userId,
      result: result,
      attributes: attributes,
      timestamp: new Date().toISOString()
    });

    res.json({
      featureKey,
      result,
      userId,
      attributes
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/track-experiment', async (req, res) => {
  try {
    const { experimentKey, userId, variationId, attributes } = req.body;

    // Send experiment tracking to Jitsu
    await sendToJitsu({
      event: 'experiment_tracked',
      experiment_key: experimentKey,
      variation_id: variationId,
      user_id: userId,
      attributes: attributes,
      timestamp: new Date().toISOString()
    });

    res.json({ success: true, message: 'Experiment tracked successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start server
app.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);
  await initializeGrowthBook();
});
