# 🚀 Jitsu Admin Panel

Адміністративна панель для управління інтеграцією Jitsu + GrowthBook.

## 📋 Функції

### 🎯 Dashboard
- Моніторинг статусу всіх сервісів
- Перевірка підключення до баз даних
- Системна інформація та uptime
- Швидкі дії та посилання

### 📊 Events
- Перегляд останніх подій
- Фільтрація за типом події
- Деталі про feature flags та експерименти
- Real-time оновлення

### 🏁 Feature Flags
- Перегляд всіх feature flags з GrowthBook
- Тестування feature flags
- Перегляд правил та умов
- Інтерактивне тестування з різними атрибутами

### 👥 Users
- Список активних користувачів
- Статистика активності
- Деталі про взаємодії користувачів
- Географічний розподіл

## 🔐 Доступ

- **URL**: http://localhost:3002
- **Username**: `admin`
- **Password**: `jitsu_admin_2024`

## 🛠️ Технології

- **Backend**: Node.js + Express
- **Template Engine**: Handlebars
- **Frontend**: Bootstrap 5 + Font Awesome
- **Database**: PostgreSQL + ClickHouse
- **Authentication**: Basic Auth

## 📁 Структура

```
jitsu-admin/
├── server.js              # Основний сервер
├── package.json           # Залежності
├── .env                   # Конфігурація
├── views/
│   ├── layouts/
│   │   └── main.handlebars # Основний шаблон
│   ├── dashboard.handlebars # Головна сторінка
│   ├── events.handlebars   # Сторінка подій
│   ├── features.handlebars # Feature flags
│   ├── users.handlebars    # Користувачі
│   └── error.handlebars    # Сторінка помилок
└── public/
    ├── css/
    │   └── admin.css       # Стилі
    └── js/
        └── admin.js        # JavaScript
```

## 🚀 Запуск

1. **Встановити залежності**:
```bash
npm install
```

2. **Налаштувати змінні середовища** (`.env`):
```env
PORT=3002
ADMIN_USERNAME=admin
ADMIN_PASSWORD=jitsu_admin_2024
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_PASSWORD=postgres_secure_2024
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=8123
CLICKHOUSE_PASSWORD=clickhouse_secure_2024
INTEGRATION_API_URL=http://localhost:3001
```

3. **Запустити сервер**:
```bash
npm start
```

4. **Відкрити в браузері**: http://localhost:3002

## 🔗 API Endpoints

### GET /
Головна сторінка dashboard

### GET /events
Сторінка подій

### GET /features
Сторінка feature flags

### GET /users
Сторінка користувачів

### GET /api/stats
API для отримання статистики системи

## 🎨 Особливості UI

- **Responsive Design**: Адаптивний дизайн для всіх пристроїв
- **Real-time Updates**: Автоматичне оновлення кожні 30 секунд
- **Interactive Testing**: Можливість тестувати feature flags прямо з інтерфейсу
- **Status Indicators**: Кольорові індикатори статусу сервісів
- **Copy to Clipboard**: Швидке копіювання коду та конфігурацій

## 🔧 Налаштування

### Зміна порту
```env
PORT=3003
```

### Зміна облікових даних
```env
ADMIN_USERNAME=your_username
ADMIN_PASSWORD=your_secure_password
```

### Підключення до інших баз даних
```env
POSTGRES_HOST=your_postgres_host
POSTGRES_PORT=5432
POSTGRES_PASSWORD=your_password
```

## 📈 Моніторинг

Адмінка автоматично перевіряє:
- ✅ Статус Integration API (http://localhost:3001)
- ✅ Підключення до PostgreSQL
- ✅ Підключення до ClickHouse
- ✅ Uptime сервера

## 🔒 Безпека

- Basic Authentication для доступу
- Захищені API endpoints
- Валідація всіх вхідних даних
- CORS налаштування

## 🚀 Розширення

Для додавання нових функцій:

1. Створити новий route в `server.js`
2. Додати відповідний Handlebars шаблон
3. Оновити навігацію в `main.handlebars`
4. Додати стилі в `admin.css`

## 📞 Підтримка

Адмінка інтегрована з основною системою Jitsu + GrowthBook та надає повний контроль над інтеграцією.
