## [8.0.3](https://github.com/express-handlebars/express-handlebars/compare/v8.0.2...v8.0.3) (2025-04-23)


### Bug Fixes

* **deps:** update dependency glob to ^11.0.2 ([#982](https://github.com/express-handlebars/express-handlebars/issues/982)) ([def7c02](https://github.com/express-handlebars/express-handlebars/commit/def7c0270601c07c5ee5e56fd09a4ac43a19397b))

## [8.0.2](https://github.com/express-handlebars/express-handlebars/compare/v8.0.1...v8.0.2) (2025-04-17)


### Bug Fixes

* **deps:** update dependency glob to ^11.0.1 ([#928](https://github.com/express-handlebars/express-handlebars/issues/928)) ([b4b9cd3](https://github.com/express-handlebars/express-handlebars/commit/b4b9cd332232136c5fee7155253a5152c7107cc3))

## [8.0.1](https://github.com/express-handlebars/express-handlebars/compare/v8.0.0...v8.0.1) (2024-08-07)


### Bug Fixes

* remove module type ([#857](https://github.com/express-handlebars/express-handlebars/issues/857)) ([efca370](https://github.com/express-handlebars/express-handlebars/commit/efca37028ba2af18310400e2f8be87842e17d6b1))

# [8.0.0](https://github.com/express-handlebars/express-handlebars/compare/v7.1.3...v8.0.0) (2024-08-07)


### Bug Fixes

* require node v20 ([#854](https://github.com/express-handlebars/express-handlebars/issues/854)) ([cb0358e](https://github.com/express-handlebars/express-handlebars/commit/cb0358e15f574e438f40726f9a1683e1c9052522))


### BREAKING CHANGES

* minimum node v20

## [7.1.3](https://github.com/express-handlebars/express-handlebars/compare/v7.1.2...v7.1.3) (2024-06-19)


### Bug Fixes

* **deps:** update dependency glob to ^10.4.2 ([#782](https://github.com/express-handlebars/express-handlebars/issues/782)) ([676a537](https://github.com/express-handlebars/express-handlebars/commit/676a537cb5d1b84b8efe8881a8629e2106a65f25))

## [7.1.2](https://github.com/express-handlebars/express-handlebars/compare/v7.1.1...v7.1.2) (2023-08-08)


### Bug Fixes

* use types from handlebars for helpers ([#617](https://github.com/express-handlebars/express-handlebars/issues/617)) ([bc38da4](https://github.com/express-handlebars/express-handlebars/commit/bc38da4199cdc450dd84537c0515da475ef0d6ad))

## [7.1.1](https://github.com/express-handlebars/express-handlebars/compare/v7.1.0...v7.1.1) (2023-08-02)


### Bug Fixes

* **deps:** update dependency handlebars to ^4.7.8 ([#616](https://github.com/express-handlebars/express-handlebars/issues/616)) ([54ef900](https://github.com/express-handlebars/express-handlebars/commit/54ef9006ad1dd425a166bd4f1fdd08aa0911dc19))

# [7.1.0](https://github.com/express-handlebars/express-handlebars/compare/v7.0.7...v7.1.0) (2023-07-20)


### Features

* add resetCache ([#554](https://github.com/express-handlebars/express-handlebars/issues/554)) ([868e9b4](https://github.com/express-handlebars/express-handlebars/commit/868e9b4ac9690de5000385c1fecdef858cf8d504))

## [7.0.7](https://github.com/express-handlebars/express-handlebars/compare/v7.0.6...v7.0.7) (2023-04-15)


### Bug Fixes

* **deps:** update dependency glob to ^10.1.0 ([#555](https://github.com/express-handlebars/express-handlebars/issues/555)) ([196c925](https://github.com/express-handlebars/express-handlebars/commit/196c9255716856ae741a9c6672821ff8e2aeb2a9))

## [7.0.6](https://github.com/express-handlebars/express-handlebars/compare/v7.0.5...v7.0.6) (2023-04-12)


### Bug Fixes

* replace backslash in partials with forward slash ([#553](https://github.com/express-handlebars/express-handlebars/issues/553)) ([2ecd784](https://github.com/express-handlebars/express-handlebars/commit/2ecd7840cb16d0e5013469b84183211e02212053))

## [7.0.5](https://github.com/express-handlebars/express-handlebars/compare/v7.0.4...v7.0.5) (2023-04-11)


### Bug Fixes

* **deps:** update dependency glob to v10 ([#551](https://github.com/express-handlebars/express-handlebars/issues/551)) ([7817150](https://github.com/express-handlebars/express-handlebars/commit/7817150a1c06b9cb50826574a2e2127d5730c268))

## [7.0.4](https://github.com/express-handlebars/express-handlebars/compare/v7.0.3...v7.0.4) (2023-03-23)


### Bug Fixes

* **deps:** update dependency graceful-fs to ^4.2.11 ([#531](https://github.com/express-handlebars/express-handlebars/issues/531)) ([570e73d](https://github.com/express-handlebars/express-handlebars/commit/570e73da862694ee3b760c3616eb23ace7b0d4af))

## [7.0.3](https://github.com/express-handlebars/express-handlebars/compare/v7.0.2...v7.0.3) (2023-03-23)


### Bug Fixes

* **deps:** update dependency glob to ^9.3.2 ([#526](https://github.com/express-handlebars/express-handlebars/issues/526)) ([2aa9c4a](https://github.com/express-handlebars/express-handlebars/commit/2aa9c4aa58cf43d25ae7dd4b21110523451ebaac))

## [7.0.2](https://github.com/express-handlebars/express-handlebars/compare/v7.0.1...v7.0.2) (2023-03-09)


### Bug Fixes

* **deps:** update dependency glob to ^9.2.1 ([#520](https://github.com/express-handlebars/express-handlebars/issues/520)) ([16a9bc3](https://github.com/express-handlebars/express-handlebars/commit/16a9bc356ba333586bb1626d2402d9aa7df948fd))

## [7.0.1](https://github.com/express-handlebars/express-handlebars/compare/v7.0.0...v7.0.1) (2023-03-01)


### Bug Fixes

* **deps:** update dependency glob to ^9.1.0 ([#518](https://github.com/express-handlebars/express-handlebars/issues/518)) ([635922c](https://github.com/express-handlebars/express-handlebars/commit/635922c746c76b0f36fe7061fcecc7c9f64d3b1b))
* fix cannot use import ([0f45477](https://github.com/express-handlebars/express-handlebars/commit/0f45477b822a9d7814e1d05c493e4b827279717c))

# [7.0.0](https://github.com/express-handlebars/express-handlebars/compare/v6.0.7...v7.0.0) (2023-03-01)


### Bug Fixes

* **deps:** update dependency glob to v9 ([#514](https://github.com/express-handlebars/express-handlebars/issues/514)) ([3b08bbb](https://github.com/express-handlebars/express-handlebars/commit/3b08bbb05fe195a855a090fadc11fc183f5c2292))
* minimum Node v16 ([#516](https://github.com/express-handlebars/express-handlebars/issues/516)) ([86da3b2](https://github.com/express-handlebars/express-handlebars/commit/86da3b229b9c3bdf1c0e5924a796199a861ec260))


### BREAKING CHANGES

* minimum node version is v16

## [6.0.7](https://github.com/express-handlebars/express-handlebars/compare/v6.0.6...v6.0.7) (2023-01-25)


### Bug Fixes

* **deps:** update dependency glob to ^8.1.0 ([#489](https://github.com/express-handlebars/express-handlebars/issues/489)) ([1bb2a2f](https://github.com/express-handlebars/express-handlebars/commit/1bb2a2f3dae7148afc5468bc916f6abe08381937))

## [6.0.6](https://github.com/express-handlebars/express-handlebars/compare/v6.0.5...v6.0.6) (2022-05-13)


### Bug Fixes

* **deps:** update dependency glob to ^8.0.2 ([8202ea1](https://github.com/express-handlebars/express-handlebars/commit/8202ea19fb6e4354edd05dc457d2f3a14a5c29d9))

## [6.0.5](https://github.com/express-handlebars/express-handlebars/compare/v6.0.4...v6.0.5) (2022-04-11)


### Bug Fixes

* **deps:** update dependency glob to v8 ([4025b58](https://github.com/express-handlebars/express-handlebars/commit/4025b58534b794863b2f51dcdc779d347a46c4a6))

## [6.0.4](https://github.com/express-handlebars/express-handlebars/compare/v6.0.3...v6.0.4) (2022-04-06)


### Bug Fixes

* **deps:** update dependency graceful-fs to ^4.2.10 ([2d6e89c](https://github.com/express-handlebars/express-handlebars/commit/2d6e89c219b11000125f7bc2630f6ddaf241987d))

## [6.0.3](https://github.com/express-handlebars/express-handlebars/compare/v6.0.2...v6.0.3) (2022-03-03)


### Bug Fixes

* allow false for defaultLayout ([#303](https://github.com/express-handlebars/express-handlebars/issues/303)) ([d6180fe](https://github.com/express-handlebars/express-handlebars/commit/d6180fe7ad8ab74e60f58b4ced1b6d6af2d68c42))
* **deps:** update dependency graceful-fs to ^4.2.9 ([#271](https://github.com/express-handlebars/express-handlebars/issues/271)) ([ea0f1f5](https://github.com/express-handlebars/express-handlebars/commit/ea0f1f563488d67202d7d6067116a4fe67eddf18))

## [6.0.2](https://github.com/express-handlebars/express-handlebars/compare/v6.0.1...v6.0.2) (2021-11-25)


### Bug Fixes

* fix typescript in strict mode ([6833d8d](https://github.com/express-handlebars/express-handlebars/commit/6833d8dd4532e45790e04940b646e33f5fd07429))

## [6.0.1](https://github.com/express-handlebars/express-handlebars/compare/v6.0.0...v6.0.1) (2021-11-13)


### Bug Fixes

* fix types ([f4de857](https://github.com/express-handlebars/express-handlebars/commit/f4de8577d5ad4510f4c5286cdee300dd27c6abfc))
* remove default export ([a7f38a1](https://github.com/express-handlebars/express-handlebars/commit/a7f38a1d3127d63450b10b3f3539e3ce8131b677))
* update examples ([1b1f5f7](https://github.com/express-handlebars/express-handlebars/commit/1b1f5f7b818985d433f6dc0398f7866c62b6cdea))

# [6.0.0](https://github.com/express-handlebars/express-handlebars/compare/v5.3.5...v6.0.0) (2021-11-13)


### Features

* rewrite in typescript ([188d3c4](https://github.com/express-handlebars/express-handlebars/commit/188d3c48526499143b7e1787accd230150a200d3))


### BREAKING CHANGES

* Change minimum node version to 12

## [5.3.5](https://github.com/express-handlebars/express-handlebars/compare/v5.3.4...v5.3.5) (2021-11-13)


### Bug Fixes

* update deps ([b516cff](https://github.com/express-handlebars/express-handlebars/commit/b516cff30ba3de90db02b3a3682c9ffbcfb10091))

## [5.3.4](https://github.com/express-handlebars/express-handlebars/compare/v5.3.3...v5.3.4) (2021-09-23)


### Bug Fixes

* **deps:** update dependency glob to ^7.2.0 ([15c77f5](https://github.com/express-handlebars/express-handlebars/commit/15c77f5e7cf31168942adaee8d021870719d9cd8))

## [5.3.3](https://github.com/express-handlebars/express-handlebars/compare/v5.3.2...v5.3.3) (2021-08-05)


### Bug Fixes

* **deps:** update dependency graceful-fs to ^4.2.7 ([94a4073](https://github.com/express-handlebars/express-handlebars/commit/94a4073bbea4591b57ea5e3cdae03c8fd861d50e))

## [5.3.2](https://github.com/express-handlebars/express-handlebars/compare/v5.3.1...v5.3.2) (2021-05-06)


### Bug Fixes

* **deps:** update dependency glob to ^7.1.7 ([8222f00](https://github.com/express-handlebars/express-handlebars/commit/8222f0015805b1287f62a1c66747a7f831a976db))

## [5.3.1](https://github.com/express-handlebars/express-handlebars/compare/v5.3.0...v5.3.1) (2021-05-04)


### Bug Fixes

* add note about security ([78c47a2](https://github.com/express-handlebars/express-handlebars/commit/78c47a235c4ad7bc2674bddd8ec2721567ed8c72))

# [5.3.0](https://github.com/express-handlebars/express-handlebars/compare/v5.2.1...v5.3.0) (2021-03-30)


### Features

* Add partialsDir.rename option ([#151](https://github.com/express-handlebars/express-handlebars/issues/151)) ([1a6771b](https://github.com/express-handlebars/express-handlebars/commit/1a6771b0f9a3db1cbd516faf79cb5e20a779e456))

## [5.2.1](https://github.com/express-handlebars/express-handlebars/compare/v5.2.0...v5.2.1) (2021-02-16)


### Bug Fixes

* **deps:** update dependency handlebars to ^4.7.7 ([1930523](https://github.com/express-handlebars/express-handlebars/commit/1930523103e6c97a3f3e41d6e7b5d6dc329c66f9))

# [5.2.0](https://github.com/express-handlebars/express-handlebars/compare/v5.1.0...v5.2.0) (2020-10-23)


### Features

* allow views to be an array ([a9f4aaa](https://github.com/express-handlebars/express-handlebars/commit/a9f4aaabd657221236b7321a4f87df7c9eb9a1bd))

# [5.1.0](https://github.com/express-handlebars/express-handlebars/compare/v5.0.0...v5.1.0) (2020-07-16)


### Features

* add encoding option ([9e516c3](https://github.com/express-handlebars/express-handlebars/commit/9e516c382269b3ab586a6ab0dbd586b3c23110c4))

# [5.0.0](https://github.com/express-handlebars/express-handlebars/compare/v4.0.6...v5.0.0) (2020-07-06)


### Bug Fixes

* update code to es2015+ ([e5a08ee](https://github.com/express-handlebars/express-handlebars/commit/e5a08eed844f177b0f365f882a20c7b229715bdd))
* update node support ([ea30d53](https://github.com/express-handlebars/express-handlebars/commit/ea30d531b2f458c37f65b50bddc504180e774f8f))


### BREAKING CHANGES

* Drop support for node versions below v10

## [4.0.6](https://github.com/express-handlebars/express-handlebars/compare/v4.0.5...v4.0.6) (2020-07-06)


### Bug Fixes

* add runtimeOptions ([b64284f](https://github.com/express-handlebars/express-handlebars/commit/b64284f6f6eab2d184671736c33fc45df5b26246))

## [4.0.5](https://github.com/express-handlebars/express-handlebars/compare/v4.0.4...v4.0.5) (2020-07-03)


### Bug Fixes

* overwrite past settings.views ([c27f1b0](https://github.com/express-handlebars/express-handlebars/commit/c27f1b0e8dcf2be974584861433cfb01a10ce1f6))
* renderView returns promise when no callback given ([c39ed87](https://github.com/express-handlebars/express-handlebars/commit/c39ed87f2478ed64211821a6ffe1dca7212fb21b))

## [4.0.4](https://github.com/express-handlebars/express-handlebars/compare/v4.0.3...v4.0.4) (2020-04-29)


### Bug Fixes

* **deps:** update dependency graceful-fs to ^4.2.4 ([c01661b](https://github.com/express-handlebars/express-handlebars/commit/c01661be5193ea77d9914b71aedcb71d6ad4ab92))

## [4.0.3](https://github.com/express-handlebars/express-handlebars/compare/v4.0.2...v4.0.3) (2020-04-05)


### Bug Fixes

* **deps:** update dependency handlebars to ^4.7.6 ([2aa29ab](https://github.com/express-handlebars/express-handlebars/commit/2aa29ab29d5db9becccb5690a6fdef4a46055906))

## [4.0.2](https://github.com/express-handlebars/express-handlebars/compare/v4.0.1...v4.0.2) (2020-04-03)


### Bug Fixes

* **deps:** update dependency handlebars to ^4.7.5 ([#6](https://github.com/express-handlebars/express-handlebars/issues/6)) ([e597254](https://github.com/express-handlebars/express-handlebars/commit/e59725426cd6c6ab261127fd96065f30009ea1e1))
