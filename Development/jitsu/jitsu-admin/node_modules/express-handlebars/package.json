{"name": "express-handlebars", "description": "A Handlebars view engine for Express which doesn't suck.", "version": "8.0.3", "homepage": "https://github.com/express-handlebars/express-handlebars", "keywords": ["express", "express3", "handlebars", "view", "layout", "partials", "templates"], "author": "<PERSON> <<EMAIL>> (http://ericf.me/)", "repository": {"type": "git", "url": "git://github.com/express-handlebars/express-handlebars.git"}, "bugs": {"url": "https://github.com/express-handlebars/express-handlebars/issues"}, "engines": {"node": ">=22.15.0"}, "dependencies": {"glob": "^11.0.2", "graceful-fs": "^4.2.11", "handlebars": "^4.7.8"}, "main": "dist/index.js", "directories": {"example": "examples"}, "devDependencies": {"@eslint/js": "^9.25.1", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.1", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@types/glob": "^8.1.0", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "eslint": "^9.25.1", "globals": "^16.0.0", "jest-cli": "^29.7.0", "semantic-release": "^24.2.3", "ts-jest": "^29.3.2", "typescript": "^5.8.3", "typescript-eslint": "^8.31.0"}, "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", "@semantic-release/github", "@semantic-release/git"]}, "scripts": {"test": "jest --verbose", "test:cover": "jest --coverage", "lint": "eslint", "build": "tsc"}, "license": "BSD-3-<PERSON><PERSON>"}