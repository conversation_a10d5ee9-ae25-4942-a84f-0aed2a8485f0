"use strict";
/*
 * Copyright (c) 2014, Yahoo Inc. All rights reserved.
 * Copyrights licensed under the New BSD License.
 * See the accompanying LICENSE file for terms.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExpressHandlebars = void 0;
exports.create = create;
exports.engine = engine;
const express_handlebars_1 = require("./express-handlebars");
exports.ExpressHandlebars = express_handlebars_1.default;
function create(config = {}) {
    return new express_handlebars_1.default(config);
}
function engine(config = {}) {
    return create(config).engine;
}
//# sourceMappingURL=index.js.map