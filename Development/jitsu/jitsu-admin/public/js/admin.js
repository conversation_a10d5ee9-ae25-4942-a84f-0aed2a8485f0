// Admin Panel JavaScript

// Global utilities
window.AdminPanel = {
    // Show loading state
    showLoading: function(element) {
        if (element) {
            element.classList.add('loading');
        }
    },
    
    // Hide loading state
    hideLoading: function(element) {
        if (element) {
            element.classList.remove('loading');
        }
    },
    
    // Show toast notification
    showToast: function(message, type = 'info') {
        const toastContainer = document.getElementById('toast-container') || this.createToastContainer();
        const toast = this.createToast(message, type);
        toastContainer.appendChild(toast);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            toast.remove();
        }, 5000);
    },
    
    // Create toast container if it doesn't exist
    createToastContainer: function() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    },
    
    // Create individual toast
    createToast: function(message, type) {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // Initialize Bootstrap toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        return toast;
    },
    
    // Format date for display
    formatDate: function(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString();
    },
    
    // Format numbers with commas
    formatNumber: function(num) {
        return new Intl.NumberFormat().format(num);
    },
    
    // Copy text to clipboard
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(() => {
            this.showToast('Copied to clipboard!', 'success');
        }).catch(() => {
            this.showToast('Failed to copy to clipboard', 'danger');
        });
    },
    
    // Make API request with error handling
    apiRequest: async function(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            });
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            return await response.json();
        } catch (error) {
            this.showToast(`API Error: ${error.message}`, 'danger');
            throw error;
        }
    }
};

// Auto-refresh functionality
let autoRefreshInterval;

function startAutoRefresh(intervalSeconds = 30) {
    stopAutoRefresh(); // Clear any existing interval
    
    autoRefreshInterval = setInterval(() => {
        // Only refresh if the page is visible
        if (!document.hidden) {
            location.reload();
        }
    }, intervalSeconds * 1000);
    
    AdminPanel.showToast(`Auto-refresh enabled (${intervalSeconds}s)`, 'info');
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
        AdminPanel.showToast('Auto-refresh disabled', 'info');
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + R: Refresh page
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        location.reload();
    }
    
    // Ctrl/Cmd + H: Go to dashboard
    if ((e.ctrlKey || e.metaKey) && e.key === 'h') {
        e.preventDefault();
        window.location.href = '/';
    }
});

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Add copy buttons to code blocks
    document.querySelectorAll('code, pre').forEach(function(codeBlock) {
        if (codeBlock.textContent.length > 10) {
            const copyButton = document.createElement('button');
            copyButton.className = 'btn btn-sm btn-outline-secondary position-absolute';
            copyButton.style.top = '5px';
            copyButton.style.right = '5px';
            copyButton.innerHTML = '<i class="fas fa-copy"></i>';
            copyButton.onclick = () => AdminPanel.copyToClipboard(codeBlock.textContent);
            
            const wrapper = document.createElement('div');
            wrapper.className = 'position-relative';
            codeBlock.parentNode.insertBefore(wrapper, codeBlock);
            wrapper.appendChild(codeBlock);
            wrapper.appendChild(copyButton);
        }
    });
    
    // Add status indicators
    updateStatusIndicators();
});

// Update status indicators with real-time data
async function updateStatusIndicators() {
    try {
        const stats = await AdminPanel.apiRequest('/api/stats');
        
        // Update status badges
        document.querySelectorAll('[data-status]').forEach(element => {
            const service = element.getAttribute('data-status');
            const status = stats[service];
            
            element.className = element.className.replace(/bg-\w+/, '');
            element.classList.add(status === 'up' ? 'bg-success' : 'bg-danger');
            element.textContent = status;
        });
        
    } catch (error) {
        console.error('Failed to update status indicators:', error);
    }
}

// Page-specific functionality
if (window.location.pathname === '/') {
    // Dashboard specific code
    document.addEventListener('DOMContentLoaded', function() {
        // Start auto-refresh for dashboard
        startAutoRefresh(30);
    });
}

// Export for global use
window.startAutoRefresh = startAutoRefresh;
window.stopAutoRefresh = stopAutoRefresh;
window.updateStatusIndicators = updateStatusIndicators;
