<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-flag me-2"></i>
                    Feature Flags
                </h5>
                <div>
                    <a href="http://localhost:3001" target="_blank" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-external-link-alt me-1"></i>
                        Test Features
                    </a>
                    <button class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>
                        Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                {{#if features}}
                <div class="row">
                    {{#each features}}
                    <div class="col-md-6 mb-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="card-title mb-0">
                                    <i class="fas fa-flag me-2"></i>
                                    {{@key}}
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <strong>Default Value:</strong>
                                    <span class="badge bg-secondary ms-2">{{defaultValue}}</span>
                                </div>
                                
                                {{#if rules}}
                                <div class="mb-3">
                                    <strong>Rules:</strong>
                                    {{#each rules}}
                                    <div class="border rounded p-2 mt-2 bg-light">
                                        <div class="row">
                                            <div class="col-6">
                                                <small class="text-muted">Condition:</small><br>
                                                <code>{{json condition}}</code>
                                            </div>
                                            <div class="col-6">
                                                <small class="text-muted">Value:</small><br>
                                                <span class="badge bg-success">{{value}}</span>
                                            </div>
                                        </div>
                                    </div>
                                    {{/each}}
                                </div>
                                {{else}}
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    No conditional rules defined
                                </div>
                                {{/if}}
                                
                                <div class="d-grid">
                                    <button class="btn btn-outline-primary btn-sm" onclick="testFeature('{{@key}}')">
                                        <i class="fas fa-play me-1"></i>
                                        Test Feature
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{/each}}
                </div>
                {{else}}
                <div class="text-center py-5">
                    <i class="fas fa-flag fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No feature flags found</h5>
                    <p class="text-muted">Feature flags from GrowthBook will appear here.</p>
                    <a href="http://localhost:3001/features" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt me-2"></i>
                        View API Response
                    </a>
                </div>
                {{/if}}
            </div>
        </div>
    </div>
</div>

<!-- Feature Testing Modal -->
<div class="modal fade" id="testFeatureModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Feature Flag</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="testFeatureForm">
                    <div class="mb-3">
                        <label class="form-label">Feature Key</label>
                        <input type="text" class="form-control" id="featureKey" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">User ID</label>
                        <input type="text" class="form-control" id="userId" value="test-user-123">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">User Attributes (JSON)</label>
                        <textarea class="form-control" id="userAttributes" rows="4">{"country": "US", "premium": false}</textarea>
                    </div>
                </form>
                <div id="testResult" class="mt-3"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="executeFeatureTest()">
                    <i class="fas fa-play me-1"></i>
                    Test Feature
                </button>
            </div>
        </div>
    </div>
</div>

<script>
function testFeature(featureKey) {
    document.getElementById('featureKey').value = featureKey;
    document.getElementById('testResult').innerHTML = '';
    new bootstrap.Modal(document.getElementById('testFeatureModal')).show();
}

async function executeFeatureTest() {
    const featureKey = document.getElementById('featureKey').value;
    const userId = document.getElementById('userId').value;
    const attributesText = document.getElementById('userAttributes').value;
    
    try {
        const attributes = JSON.parse(attributesText);
        
        const response = await fetch('http://localhost:3001/evaluate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                userId: userId,
                featureKey: featureKey,
                attributes: attributes
            })
        });
        
        const result = await response.json();
        
        document.getElementById('testResult').innerHTML = `
            <div class="alert alert-success">
                <h6>Test Result:</h6>
                <pre>${JSON.stringify(result, null, 2)}</pre>
            </div>
        `;
    } catch (error) {
        document.getElementById('testResult').innerHTML = `
            <div class="alert alert-danger">
                <h6>Error:</h6>
                <p>${error.message}</p>
            </div>
        `;
    }
}
</script>
