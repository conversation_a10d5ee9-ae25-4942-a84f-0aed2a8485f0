<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-users me-2"></i>
                    Active Users
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                </button>
            </div>
            <div class="card-body">
                {{#if users.length}}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>User ID</th>
                                <th>Last Seen</th>
                                <th>Events Count</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{#each users}}
                            <tr>
                                <td>
                                    <code>{{id}}</code>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{formatDate last_seen}}
                                    </small>
                                </td>
                                <td>
                                    <span class="badge bg-primary">
                                        {{formatNumber events_count}}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-circle me-1"></i>
                                        Active
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-outline-info" onclick="viewUserDetails('{{id}}')">
                                        <i class="fas fa-eye me-1"></i>
                                        View
                                    </button>
                                </td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
                {{else}}
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No active users</h5>
                    <p class="text-muted">Users will appear here as they interact with the system.</p>
                    <a href="http://localhost:3001" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt me-2"></i>
                        Generate Test Activity
                    </a>
                </div>
                {{/if}}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    User Statistics
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-12 mb-3">
                        <h4 class="text-primary">{{users.length}}</h4>
                        <small class="text-muted">Total Active Users</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Activity Timeline
                </h6>
            </div>
            <div class="card-body">
                <small class="text-muted">Last 24 hours</small>
                <div class="progress mt-2">
                    <div class="progress-bar bg-success" style="width: 75%"></div>
                </div>
                <small class="text-muted">75% activity rate</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-globe me-2"></i>
                    Geographic Distribution
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <span class="badge bg-primary me-2">US</span>
                    <small>60%</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-secondary me-2">CA</span>
                    <small>25%</small>
                </div>
                <div class="mb-2">
                    <span class="badge bg-info me-2">UK</span>
                    <small>15%</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Details Modal -->
<div class="modal fade" id="userDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="userDetailsContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewUserDetails(userId) {
    document.getElementById('userDetailsContent').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>User Information</h6>
                <table class="table table-sm">
                    <tr>
                        <td><strong>User ID:</strong></td>
                        <td><code>${userId}</code></td>
                    </tr>
                    <tr>
                        <td><strong>Status:</strong></td>
                        <td><span class="badge bg-success">Active</span></td>
                    </tr>
                    <tr>
                        <td><strong>First Seen:</strong></td>
                        <td>2024-08-01 10:30:00</td>
                    </tr>
                    <tr>
                        <td><strong>Last Activity:</strong></td>
                        <td>2024-08-01 13:30:00</td>
                    </tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Recent Activity</h6>
                <div class="list-group">
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">Feature Evaluated</h6>
                            <small>5 mins ago</small>
                        </div>
                        <p class="mb-1">welcome-message → "Welcome!"</p>
                    </div>
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">Experiment Viewed</h6>
                            <small>10 mins ago</small>
                        </div>
                        <p class="mb-1">homepage-test → variant-b</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    new bootstrap.Modal(document.getElementById('userDetailsModal')).show();
}
</script>
