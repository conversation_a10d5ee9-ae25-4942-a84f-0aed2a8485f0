<div class="row">
    <!-- System Status Cards -->
    <div class="col-md-3 mb-4">
        <div class="card">
            <div class="card-body text-center">
                {{#if (eq stats.integration 'up')}}
                <i class="fas fa-plug fa-2x text-success mb-2"></i>
                {{else}}
                <i class="fas fa-plug fa-2x text-danger mb-2"></i>
                {{/if}}
                <h5 class="card-title">Integration API</h5>
                <p class="card-text">
                    {{#if (eq stats.integration 'up')}}
                    <span class="badge bg-success">{{stats.integration}}</span>
                    {{else}}
                    <span class="badge bg-danger">{{stats.integration}}</span>
                    {{/if}}
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-database fa-2x {{#if (eq stats.postgres 'up')}}text-success{{else}}text-danger{{/if}} mb-2"></i>
                <h5 class="card-title">PostgreSQL</h5>
                <p class="card-text">
                    <span class="badge {{#if (eq stats.postgres 'up')}}bg-success{{else}}bg-danger{{/if}}">
                        {{stats.postgres}}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-bar fa-2x {{#if (eq stats.clickhouse 'up')}}text-success{{else}}text-danger{{/if}} mb-2"></i>
                <h5 class="card-title">ClickHouse</h5>
                <p class="card-text">
                    <span class="badge {{#if (eq stats.clickhouse 'up')}}bg-success{{else}}bg-danger{{/if}}">
                        {{stats.clickhouse}}
                    </span>
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-4">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                <h5 class="card-title">Uptime</h5>
                <p class="card-text">
                    <span class="badge bg-info">
                        {{formatNumber stats.uptime}}s
                    </span>
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Quick Actions -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/events" class="btn btn-outline-primary">
                        <i class="fas fa-stream me-2"></i>
                        View Recent Events
                    </a>
                    <a href="/features" class="btn btn-outline-success">
                        <i class="fas fa-flag me-2"></i>
                        Manage Feature Flags
                    </a>
                    <a href="/users" class="btn btn-outline-info">
                        <i class="fas fa-users me-2"></i>
                        View Active Users
                    </a>
                    <a href="http://localhost:3001" target="_blank" class="btn btn-outline-warning">
                        <i class="fas fa-external-link-alt me-2"></i>
                        Open Integration Demo
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Information
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>Last Updated:</strong></td>
                        <td>{{formatDate stats.timestamp}}</td>
                    </tr>
                    <tr>
                        <td><strong>Node.js Version:</strong></td>
                        <td>{{process.version}}</td>
                    </tr>
                    <tr>
                        <td><strong>Environment:</strong></td>
                        <td>
                            <span class="badge bg-secondary">Development</span>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>Integration URL:</strong></td>
                        <td>
                            <a href="http://localhost:3001" target="_blank">
                                http://localhost:3001
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Real-time Stats -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Real-time Monitoring
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshStats()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                </button>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Integration Status:</strong> 
                    The Jitsu + GrowthBook integration is running and ready to process events.
                    All core services are operational.
                </div>
                
                {{#if stats.error}}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> {{stats.error}}
                </div>
                {{/if}}
            </div>
        </div>
    </div>
</div>

<script>
async function refreshStats() {
    try {
        const response = await fetch('/api/stats');
        const stats = await response.json();
        location.reload(); // Simple refresh for now
    } catch (error) {
        console.error('Failed to refresh stats:', error);
    }
}

// Auto-refresh every 30 seconds
setInterval(refreshStats, 30000);
</script>
