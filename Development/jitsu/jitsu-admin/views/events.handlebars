<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-stream me-2"></i>
                    Recent Events
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="location.reload()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                </button>
            </div>
            <div class="card-body">
                {{#if events.length}}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Event Type</th>
                                <th>User ID</th>
                                <th>Details</th>
                                <th>Timestamp</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{#each events}}
                            <tr>
                                <td>
                                    {{#if (eq event 'feature_evaluated')}}
                                    <span class="badge bg-primary">
                                        <i class="fas fa-flag me-1"></i>
                                        Feature Evaluated
                                    </span>
                                    {{else if (eq event 'experiment_viewed')}}
                                    <span class="badge bg-success">
                                        <i class="fas fa-flask me-1"></i>
                                        Experiment Viewed
                                    </span>
                                    {{else}}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-circle me-1"></i>
                                        {{event}}
                                    </span>
                                    {{/if}}
                                </td>
                                <td>
                                    <code>{{user_id}}</code>
                                </td>
                                <td>
                                    {{#if feature_key}}
                                    <strong>Feature:</strong> {{feature_key}}<br>
                                    <strong>Result:</strong> <code>{{result}}</code>
                                    {{/if}}
                                    {{#if experiment_id}}
                                    <strong>Experiment:</strong> {{experiment_id}}<br>
                                    <strong>Variation:</strong> <code>{{variation_id}}</code>
                                    {{/if}}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{formatDate timestamp}}
                                    </small>
                                </td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>
                {{else}}
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No events found</h5>
                    <p class="text-muted">Events will appear here as users interact with feature flags and experiments.</p>
                    <a href="http://localhost:3001" target="_blank" class="btn btn-primary">
                        <i class="fas fa-external-link-alt me-2"></i>
                        Test Integration
                    </a>
                </div>
                {{/if}}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Event Types
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>Feature Evaluated:</strong> When a feature flag is checked<br>
                    <strong>Experiment Viewed:</strong> When a user sees an A/B test variant<br>
                    <strong>Experiment Tracked:</strong> When experiment participation is recorded
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cog me-2"></i>
                    Event Configuration
                </h6>
            </div>
            <div class="card-body">
                <p><strong>Real-time Processing:</strong> ✅ Enabled</p>
                <p><strong>Storage:</strong> ClickHouse Database</p>
                <p><strong>Retention:</strong> 90 days</p>
                <p><strong>Batch Size:</strong> 1000 events</p>
            </div>
        </div>
    </div>
</div>
